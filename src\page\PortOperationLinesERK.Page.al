page 60004 "Port Operation Lines ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Port Operation Lines';
    PageType = List;
    SourceTable = "Port Operation Line ERK";
    UsageCategory = ReportsAndAnalysis;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                {
                }
                field("Revenue Amount (ACY)"; Rec."Revenue Amount (ACY)")
                {
                }
                field("Expense Amount (ACY)"; Rec."Expense Amount (ACY)")
                {
                }
                field("Parent Load Type"; Rec."Parent Load Type")
                {
                }
                field("Sub Load Type"; Rec."Sub Load Type")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Bill-to Customer Name"; Rec."Bill-to Customer Name")
                {
                }
                field("Load Item No."; Rec."Load Item No.")
                {
                }
                field("Total Entry Quantity"; Rec."Total Entry Quantity")
                {
                }
                field("First Entry Date"; Rec."First Entry Date")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field("Total Exit Quantity"; Rec."Total Exit Quantity")
                {
                }
                field("Remaining Quantity"; Rec."Remaining Quantity")
                {
                }
                field("Total Scrap Quantity"; Rec."Total Scrap Quantity")
                {
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                }
                field("Total Transfer Quantity"; Rec."Total Transfer Quantity")
                {
                }
                field("Load Owner No."; Rec."Load Owner No.")
                {
                }
                field("Load Owner Name"; Rec."Load Owner Name")
                {
                }
            }
        }
    }
}
