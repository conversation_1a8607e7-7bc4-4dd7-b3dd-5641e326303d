pageextension 60044 "Sales CrMemo Ext. ERK" extends "Sales Credit Memo"
{
    layout
    {
        addlast(General)
        {
            field("Invoice Type INF ERK"; Rec."Invoice Type INF ERK")
            {
                ApplicationArea = All;
            }
            field("EBA Status ERK"; Rec."EBA Status ERK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the EBA Status field.';
            }
            field("PDFExistERK ERK"; Rec.GetPDFFileData())
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the PDF Exist field.';
                Caption = 'PDF File';
                Editable = false;


                trigger OnAssistEdit()
                begin
                    Message(Rec.GetPDFFileData());
                end;
            }
        }
    }

    actions
    {
        addafter("&Credit Memo")
        {
            action("ImportInvoicePDFFileERK ERK")
            {
                ApplicationArea = All;
                Caption = 'Import Invoice PDF File';
                Image = Document;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Imports the invoice PDF file.';

                trigger OnAction()
                var
                    EBAIntegrationMngt: Codeunit "EBA Integration Mngt. ERK";
                begin
                    EBAIntegrationMngt.ImportInvoiceViewSales(Rec);
                end;
            }
        }
    }
}