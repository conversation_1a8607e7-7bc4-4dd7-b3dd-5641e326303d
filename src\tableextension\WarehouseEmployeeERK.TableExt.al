tableextension 60034 "Warehouse Employee ERK" extends "Warehouse Employee"
{
    fields
    {
        field(60000; "Discharge ERK"; Boolean)
        {
            Caption = 'Discharge';
            ToolTip = 'Specifies whether the employee is allowed to perform the Discharge operation.';
        }
        field(60001; "Loading ERK"; Boolean)
        {
            Caption = 'Loading';
            ToolTip = 'Specifies whether the employee is allowed to perform the Loading operation.';
        }
        field(60002; "Addressing ERK"; Boolean)
        {
            Caption = 'Addressing';
            ToolTip = 'Specifies whether the employee is allowed to perform the Addressing operation.';
        }
        field(60003; "Customs Exit ERK"; Boolean)
        {
            Caption = 'Customs Exit';
            ToolTip = 'Specifies whether the employee is allowed to perform the Customs Exit operation.';
        }
        field(60004; "Transfer ERK"; Boolean)
        {
            Caption = 'Transfer';
            ToolTip = 'Specifies whether the employee is allowed to perform the Transfer operation.';
        }
        field(60005; "PDI Exit ERK"; Boolean)
        {
            Caption = 'PDI Exit';
            ToolTip = 'Specifies whether the employee is allowed to perform the PDI Exit operation.';
        }
        field(60006; "Nav Exit ERK"; Boolean)
        {
            Caption = 'Nav Exit';
            ToolTip = 'Specifies whether the employee is allowed to perform the Nav Exit operation.';
        }
        field(60007; "Vehicle Entry ERK"; Boolean)
        {
            Caption = 'Vehicle Entry';
            ToolTip = 'Specifies whether the employee is allowed to perform the Vehicle Entry operation.';
        }
        field(60008; "Dealer Dispatch ERK"; Boolean)
        {
            Caption = 'Dealer Dispatch';
            ToolTip = 'Specifies whether the employee is allowed to perform the Dealer Dispatch operation.';
        }
        field(60009; "PDI Entry ERK"; Boolean)
        {
            Caption = 'PDI Entry';
            ToolTip = 'Specifies whether the employee is allowed to perform the PDI Entry operation.';
        }
        field(60010; "Damage Exit ERK"; Boolean)
        {
            Caption = 'Damage Exit';
            ToolTip = 'Specifies whether the employee is allowed to perform the Damage Exit operation.';
        }
        field(60011; "Wash ERK"; Boolean)
        {
            Caption = 'Wash';
            ToolTip = 'Specifies whether the employee is allowed to perform the Wash operation.';
        }
        field(60012; "Stock Taking ERK"; Boolean)
        {
            Caption = 'Stock-Taking';
            ToolTip = 'Specifies whether the employee is allowed to perform the Stock-Taking operation.';
        }
        field(60013; "Grupage Dealer Dispatch ERK"; Boolean)
        {
            Caption = 'Grupage Dealer Dispatch';
            ToolTip = 'Specifies whether the employee is allowed to perform the Grupage Dealer Dispatch operation.';
        }
        field(60014; "Dispatch Preparation ERK"; Boolean)
        {
            Caption = 'Dispatch Preparation';
            ToolTip = 'Specifies whether the employee is allowed to perform the Dispatch Preparation operation.';
        }
        field(60015; "Multiple Reading Allowed ERK"; Boolean)
        {
            Caption = 'Multiple Reading Allowed';
            ToolTip = 'Specifies whether the employee is allowed to perform the Multiple Reading Allowed operation.';
        }
        field(60016; "External Company ERK"; Text[100])
        {
            Caption = 'External Company';
            ToolTip = 'Specifies the value of the External Company field.';
        }

    }
}
