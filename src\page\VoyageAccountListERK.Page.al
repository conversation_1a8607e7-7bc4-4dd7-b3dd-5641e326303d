page 60029 "Voyage Account List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Voyage Account List';
    PageType = List;
    SourceTable = "Voyage Account ERK";
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Voyage Account Card ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field(Name; Rec.Name)
                {
                }
                field("Name 2"; Rec."Name 2")
                {
                }
                field(Address; Rec.Address)
                {
                }
                field("Address 2"; Rec."Address 2")
                {
                }
                field("Post Code"; Rec."Post Code")
                {
                }
                field(City; Rec.City)
                {
                }
                field("Country/Region Code"; Rec."Country/Region Code")
                {
                }
                field("Phone No."; Rec."Phone No.")
                {
                }
                field("Car Carrier Related"; Rec."Car Carrier Related")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}
