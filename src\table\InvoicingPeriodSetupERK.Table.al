table 60079 "Invoicing Period Setup ERK"
{
    Caption = 'Invoicing Period Setup';
    DataClassification = CustomerContent;
    DrillDownPageId = "Invoicing Period Setup List";
    LookupPageId = "Invoicing Period Setup List";

    fields
    {
        field(1; Code; Code[10])
        {
            Caption = 'Code';
            NotBlank = true;
            ToolTip = 'Specifies a unique code to identify this invoicing period setup.';
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies a description of the invoicing period.';
        }
        field(3; "Invoicing Period"; DateFormula)
        {
            Caption = 'Invoicing Period';
            NotBlank = true;
            ToolTip = 'Specifies the date formula that defines the invoicing period (e.g., 1M for monthly, 3M for quarterly).';
        }
    }
    keys
    {
        key(PK; Code)
        {
            Clustered = true;
        }
    }
}
