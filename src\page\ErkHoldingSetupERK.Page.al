page 60000 "Erk Holding Setup ERK"
{
    PageType = Card;
    SourceTable = "Erk Holding Setup ERK";
    Caption = 'Erk Holding Setup';
    InsertAllowed = false;
    DeleteAllowed = false;
    UsageCategory = Administration;
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            group(DredgeSetup)
            {
                Caption = 'Dredge Setup';

                field("Dredge Header Nos"; Rec."Dredge Header Nos.")
                {
                    ApplicationArea = CustomsOperationERK;
                }
            }
            group(RDMSetup)
            {
                Caption = 'RDM Setup';

                field("Dimension Code For Export"; Rec."Dimension Code For Export")
                {
                    ApplicationArea = ExportManagementERK;
                }
                field("Export No. Series"; Rec."Export No. Series")
                {
                    ApplicationArea = ExportManagementERK;
                }
            }
            group(RoRoSetup)
            {
                Caption = 'Ro-Ro Setup';

                field("Empty Trip No Series"; Rec."Empty Trip No Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Voyage No. Series"; Rec."Voyage No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("MGO Item No."; Rec."MGO Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("IFO Item No."; Rec."IFO Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("HSFO Item No."; Rec."HSFO Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("LNG Item No."; Rec."LNG Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Consumption Customer No."; Rec."Consumption Customer No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Department Dimension Code"; Rec."Department Dimension Code")
                {
                    ApplicationArea = ErkPortERK;
                }
                // field("Ro-Ro Dimension Value Code"; Rec."Ro-Ro Dimension Value Code")
                // {
                //     ToolTip = 'Specifies the value of the Ro-Ro Dimension Value Code field.';
                //     ApplicationArea = ErkPortERK;
                // }
                field("Hire Item No."; Rec."Hire Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
            }
            group(CarCarrierSetup)
            {
                Caption = 'Car Carrier Setup';

                field("Voyage Account No. Series"; Rec."Voyage Account No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Car Carrier Order Nos"; Rec."Car Carrier Order Nos")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Car Carrier No. Series"; Rec."Car Carrier No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Ferry Car Carrier Nos"; Rec."Ferry Car Carrier Nos")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Road Transport Nos"; Rec."Road Transport Nos")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Ballast No. Series"; Rec."Ballast No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Ferry Ballast No. Series"; Rec."Ferry Ballast No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Trip No. Series"; Rec."Trip No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Item No. for Vehicles"; Rec."Item No. for Vehicles")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Ferry Fuel Item No."; Rec."Ferry Fuel Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Est. Revenue Item No."; Rec."Est. Revenue Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Est. Fuel Item No."; Rec."Est. Fuel Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Est. Hire Item No."; Rec."Est. Hire Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Est. Other Cost Item No."; Rec."Est. Other Cost Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Date Filter for Revenue/Expense"; Rec."Date Filter for Rev./Exp.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Temp. Traffic Doc. No. Series"; Rec."Temp. Traffic Doc. No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
            }
            group(PDISetup)
            {
                Caption = 'PDI Setup';

                field("Vehicle Transfer No. Series"; Rec."Vehicle Transfer No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("PDI No. Series"; Rec."PDI No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
            }
            group(PortOperationsSetup)
            {
                Caption = 'Port Operation Setup';
                field("Port Operation Contract Nos"; Rec."Port Operation Contract Nos")
                {
                }

                field("Port Operation No. Series"; Rec."Port Operation No. Series")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Port Service Nos"; Rec."Port Service Nos")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Logistics Dept. Value Code"; Rec."Logistics Dept. Value Code")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Def. Cons. Loc. for Logistics"; Rec."Def. Cons. Loc. for Logistics")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Def. Cons. Bin for Logistics"; Rec."Def. Cons. Bin for Logistics")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Load Process Jnl. Template"; Rec."Load Process Jnl. Template")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Load Process Jnl. Batch"; Rec."Load Process Jnl. Batch")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Load Item No."; Rec."Load Item No.")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Load Transfer Template Name"; Rec."Load Transfer Template Name")
                {
                    ApplicationArea = ErkPortERK;
                }
                field("Load Transfer Batch Name"; Rec."Load Transfer Batch Name")
                {
                    ApplicationArea = ErkPortERK;
                }

            }
            group(CustomsOperationSetup)
            {
                Caption = 'Customs Operation Setup';

                field("Stamp Tax Item No."; Rec."Stamp Tax Item No.")
                {
                    ApplicationArea = CustomsOperationERK;
                }
                field("Customs Operation No. Series"; Rec."Customs Operation No. Series")
                {
                    ApplicationArea = CustomsOperationERK;
                }
            }
            group("Inb. E-Invoice")
            {
                Caption = 'Inbound E-Invoice';
                field("Inb. E-Inv Vendor Filter"; Rec."Inb. E-Inv Vendor Filter")
                {

                }
                field("Excluded VPG Code for EBA"; Rec."Excluded VPG Code for EBA")
                {
                    trigger OnLookup(var Text: Text): Boolean
                    var
                        VendorPostingGroup: Record "Vendor Posting Group";
                        GetSelectionFilter: Codeunit SelectionFilterManagement;
                        VendorPostingGroups: Page "Vendor Posting Groups";
                        RecRef: RecordRef;
                    begin
                        VendorPostingGroups.LookupMode := true;
                        if VendorPostingGroups.RunModal() = Action::LookupOK then begin
                            VendorPostingGroups.SetSelectionFilter(VendorPostingGroup);
                            RecRef.GetTable(VendorPostingGroup);
                            Rec."Excluded VPG Code for EBA" := CopyStr(GetSelectionFilter.GetSelectionFilter(RecRef, VendorPostingGroup.FieldNo(Code)), 1, MaxStrLen(Rec."Excluded VPG Code for EBA"));
                        end
                    end;
                }
            }

        }
    }
    actions
    {
        area(Processing)
        {
            //         action(BlockAllCustomers)
            //         {
            //             ApplicationArea = All;
            //             Caption = 'Block All Customers';
            //             Promoted = true;
            //             PromotedCategory = Process;
            //             PromotedIsBig = true;
            //             Image = Customer;
            //             ToolTip = 'Executes the Block All Customers action.';
            //             PromotedOnly = true;
            //             trigger OnAction()
            //             var
            //                 Customer: Record Customer;
            //             begin
            //                 Customer.ModifyAll(Blocked, Customer.Blocked::All, true);
            //                 Message('Total of %1 Customers has been blocked.', Customer.Count());
            //             end;
            //         }
            //         action(BlockAllVendors)
            //         {
            //             ApplicationArea = All;
            //             Caption = 'Block All Vendors';
            //             Promoted = true;
            //             PromotedCategory = Process;
            //             PromotedIsBig = true;
            //             Image = Vendor;
            //             ToolTip = 'Executes the Block All Vendors action.';
            //             PromotedOnly = true;
            //             trigger OnAction()
            //             var
            //                 Vendor: Record Vendor;
            //             begin
            //                 Vendor.ModifyAll(Blocked, Vendor.Blocked::All, true);
            //                 Message('Total of %1 Vendors has been blocked.', Vendor.Count());
            //             end;
            //         }
            //         action(ProcessAllVehicleTransferLines)
            //         {
            //             ApplicationArea = All;
            //             Caption = 'Process All Vehicle Transfer Lines';
            //             Promoted = true;
            //             PromotedCategory = Process;
            //             PromotedIsBig = true;
            //             Image = Item;
            //             ToolTip = 'Executes the Process All Vehicle Transfer Lines action.';
            //             PromotedOnly = true;
            //             Visible = false;
            //             trigger OnAction()
            //             var
            //                 VehicleTransferLine: Record "Vehicle Transfer Line ERK";
            //             begin
            //                 VehicleTransferLine.SetRange("Processed", false);
            //                 VehicleTransferLine.ModifyAll(Processed, true, false);
            //                 VehicleTransferLine.ModifyAll("Processed By", UserId, false);
            //                 VehicleTransferLine.ModifyAll("Processed At", CurrentDateTime, false);
            //                 Message('Total of %1 Lines has been processed.', VehicleTransferLine.Count());
            //             end;
            //         }
            // action(RenameAllItems)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Rename All Items';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = Replan;
            //     ToolTip = 'Executes the Rename All Items action.';
            //     trigger OnAction()
            //     var
            //         Item: Record Item;
            //         //Item2: Record Item;
            //         ConfirmManagement: Codeunit "Confirm Management"; // Declare a variable of type Confirm Management
            //         OldItemNo: Code[20];
            //         ItemCount: Integer;
            //         MyDialog: Dialog; // Declare a variable of type Dialog
            //         ProgressText: Text; // Declare a variable to store the progress text
            //         ProgressValue: Integer; // Declare a variable to store the progress value
            //         Question: Text; // Declare a variable to store the question
            //         Answer: Boolean; // Declare a variable to store the answer
            //     begin
            //         Item.SetFilter("No. 2", '<>%1', '');
            //         Item.SetCurrentKey(Description);
            //         Item.FindSet(true);
            //         ProgressText := 'Renaming items #1#####/@2@'; // Define the progress text with placeholders for the progress value and the total items count
            //         ProgressValue := 0; // Initialize the progress value
            //         Question := 'Do you want to rename %1 items?';
            //         Question := StrSubstNo(Question, Item.Count());
            //         Answer := ConfirmManagement.GetResponseOrDefault(Question, true); // Call the Confirm method with the question and the default response
            //         if Answer then begin
            //             ItemCount := Item.Count();
            //             MyDialog.Open(ProgressText, ProgressValue, ItemCount); // Open the dialog window with the text and the value
            //             repeat
            //                 OldItemNo := Item."No.";
            //                 Item.Get(Item."No.");
            //                 Item.Rename(Item."No. 2");
            //                 Item."No. 2" := OldItemNo;
            //                 Item.Modify(false);
            //                 ProgressValue := ProgressValue + 1; // Increment the progress value
            //                 MyDialog.Update(); // Update the dialog window with the new value
            //             until Item.Next() = 0;
            //             MyDialog.Close(); // Close the dialog window when the loop is done
            //             Message('Total of %1 Items has been renamed.', Item.Count());
            //         end
            //         else
            //             Message('No items were renamed.'); // Inform the user that the process was canceled
            //     end;
            // action(UpdateCarCarrierLedgerEntries)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Update Car Carrier Ledger Entries';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = UpdateXML;
            //     PromotedOnly = true;
            //     ToolTip = 'Executes the Update Car Carrier Ledger Entries action.';
            //     trigger OnAction()
            //     begin
            //         UpdateDocumentLineNoAndDocumentDetailLineNoCarCarrierLedgerEntryAppVersion230047();
            //         // UpdateShipNoAndShipNameCarCarrierLedgerEntryAppVersion230047();
            //         // UpdatePortDescriptionsCarCarrierLedgerEntryAppVersion230047();
            //     end;
            // }
            // action(FixSerialNoLocations)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Fix Serial No. Locations';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = UpdateXML;
            //     PromotedOnly = true;
            //     ToolTip = 'Executes the Update Car Carrier Ledger Entries action.';
            //     trigger OnAction()
            //     begin
            //         FixSerialNoInformationCardCUrrentLocationAndBinCodes();
            //     end;
            // }
            // action(AnkesReportTest)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Direct Print Test';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = UpdateXML;
            //     PromotedOnly = true;
            //     ToolTip = 'Executes the Ankes Report Test action.';
            //     Visible = false;
            //     trigger OnAction()
            //     // var
            //     //     EMailManagement: Codeunit "E-Mail Management ERK";
            //     begin
            //         // EMailManagement.SendEMail_Ankes(60003);
            //         // Message('Mail gönderimi başarılı.');
            //         //Codeunit.Run(Codeunit::"Printer Test ERK");
            //     end;
            // }

            action(CreateBalastDetailLine)
            {
                Caption = 'Create Balast Car Carrier Detail Line';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Create;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Balast Car Carrier Detail Line action.';
                trigger OnAction()
                var
                    VehicleRevExpManagement: Codeunit "Vehicle Rev./Exp. Management";
                begin
                    VehicleRevExpManagement.CreateCarCarrierDetailForBalastVoyages();
                end;
            }
        }
    }
    // local procedure UpdateDocumentLineNoAndDocumentDetailLineNoCarCarrierLedgerEntryAppVersion230047()
    // var
    //     CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    //     CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
    // begin
    //     CarCarrierLineDetail.SetFilter("Loaded Quantity", '0');
    //     if not CarCarrierLineDetail.FindSet() then
    //         exit;
    //     repeat
    //         CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLineDetail."Document No.");
    //         CarCarrierLedgerEntry.SetRange("Customer No.", CarCarrierLineDetail."Customer No.");
    //         CarCarrierLedgerEntry.SetRange("Loading Port", CarCarrierLineDetail."Loading Port");
    //         CarCarrierLedgerEntry.SetRange("Discharge Port", CarCarrierLineDetail."Discharge Port");
    //         CarCarrierLedgerEntry.ModifyAll("Document Line No.", CarCarrierLineDetail."Document Line No.", false);
    //         CarCarrierLedgerEntry.ModifyAll("Document Line Detail No.", CarCarrierLineDetail."Line No.", false);
    //     until CarCarrierLineDetail.Next() = 0;
    // end;
    // local procedure UpdateShipNoAndShipNameCarCarrierLedgerEntryAppVersion230047()
    // var
    //     CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    //     CarCarrierHeader: Record "Car Carrier Header ERK";
    // begin
    //     // if myAppInfo.DataVersion = Version.Create(23, 0, 0, 47) then begin
    //     CarCarrierLedgerEntry.SetRange("Ship No.", '');
    //     if not CarCarrierLedgerEntry.FindSet(true) then
    //         exit;
    //     repeat
    //         if CarCarrierHeader.Get(CarCarrierLedgerEntry."Document No.") then begin
    //             CarCarrierLedgerEntry."Ship No." := CarCarrierHeader."Ship No.";
    //             CarCarrierLedgerEntry."Ship Name" := CarCarrierHeader."Ship Name";
    //             CarCarrierLedgerEntry.Modify(false);
    //         end;
    //     until CarCarrierLedgerEntry.Next() = 0;
    // end;
    // // end;
    // local procedure UpdatePortDescriptionsCarCarrierLedgerEntryAppVersion230047()
    // var
    //     CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    //     EntryExitPoint: Record "Entry/Exit Point";
    // begin
    //     // if myAppInfo.DataVersion = Version.Create(23, 0, 0, 47) then begin
    //     CarCarrierLedgerEntry.SetRange("Loading to Discharge Desc.", '');
    //     if not CarCarrierLedgerEntry.FindSet(true) then
    //         exit;
    //     repeat
    //         if EntryExitPoint.Get(CarCarrierLedgerEntry."Loading Port") then
    //             CarCarrierLedgerEntry."Loading Port Description" := EntryExitPoint.Description;
    //         if EntryExitPoint.Get(CarCarrierLedgerEntry."Discharge Port") then
    //             CarCarrierLedgerEntry."Discharge Port Description" := EntryExitPoint.Description;
    //         CarCarrierLedgerEntry."Loading to Discharge Desc." := CarCarrierLedgerEntry."Loading Port Description" + ' - ' + CarCarrierLedgerEntry."Discharge Port Description";
    //         CarCarrierLedgerEntry.Modify(false);
    //     until CarCarrierLedgerEntry.Next() = 0;
    // end;
    // // end;
    procedure FixSerialNoInformationCardCUrrentLocationAndBinCodes()
    var
        SerialNoInformation: Record "Serial No. Information";
        VehicleTransferLedgerEntry: Record "Vehicle Transfer Ledger Entry";
        FixedCount: Integer;
    begin
        SerialNoInformation.SetRange("Current Location Code ERK", 'GEMILER');
        if not SerialNoInformation.FindSet() then
            exit;
        repeat
            VehicleTransferLedgerEntry.SetRange("Serial No.", SerialNoInformation."Serial No.");
            if VehicleTransferLedgerEntry.FindLast() then begin
                SerialNoInformation."Current Location Code ERK" := VehicleTransferLedgerEntry."To Location Code";
                SerialNoInformation."Current Bin Code ERK" := VehicleTransferLedgerEntry."To Bin Code";
                SerialNoInformation.Modify(false);
                FixedCount += 1;
            end;
        until SerialNoInformation.Next() = 0;
        Message('%1 Serial No. Information records fixed.', FixedCount);
    end;

    trigger OnOpenPage()
    begin
        Rec.InsertIfNotExists();
    end;
}
