page 60146 "Port Service Lines ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Port Service Lines';
    PageType = List;
    SourceTable = "Port Service Line ERK";
    UsageCategory = Lists;
    Editable = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                    Editable = false;
                }
                field("Line No."; Rec."Line No.")
                {
                    Editable = false;
                }
                field(Type; Rec.Type)
                {
                }
                field("Source No."; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Unit Price"; Rec."Unit Price")
                {
                }
                field("VAT Product Posting Group"; Rec."VAT Product Posting Group")
                {
                }
                field("Line Amount"; Rec."Line Amount")
                {
                }
                field("Line Amount (ACY)"; Rec."Line Amount (ACY)")
                {
                }
                field("Invoice Currency Code"; Rec."Invoice Currency Code")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Invoice No."; Rec."Invoice No.")
                {
                }
                field("Posted Invoice No."; Rec."Posted Invoice No.")
                {
                }
                field("External Document No."; Rec."External Document No.")
                {
                }
                field("Is Cancelled"; Rec."Is Cancelled")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies when the record was created.';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(CreateSalesInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Sales Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = NewSalesInvoice;
                ToolTip = 'Creates a sales invoice from the selected lines.';

                trigger OnAction()
                var
                    PortServiceLine: Record "Port Service Line ERK";
                // PortServiceManagement: Codeunit "Port Service Management ERK";
                begin
                    CurrPage.SetSelectionFilter(PortServiceLine);
                    // PortServiceManagement.CreateSalesInvoiceFromLines(PortServiceLine);
                    Message('Create Sales Invoice functionality will be implemented.');
                end;
            }

            action(CreatePurchaseInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Purchase Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NewPurchaseInvoice;
                ToolTip = 'Creates a purchase invoice from the selected lines.';

                trigger OnAction()
                var
                    PortServiceLine: Record "Port Service Line ERK";
                // PortServiceManagement: Codeunit "Port Service Management ERK";
                begin
                    CurrPage.SetSelectionFilter(PortServiceLine);
                    // PortServiceManagement.CreatePurchaseInvoiceFromLines(PortServiceLine);
                    Message('Create Purchase Invoice functionality will be implemented.');
                end;
            }
        }
    }
}