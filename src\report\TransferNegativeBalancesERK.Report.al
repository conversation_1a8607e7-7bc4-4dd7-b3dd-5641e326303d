report 60021 "Transfer Negative Balances ERK"
{
    Caption = 'Transfer Negative Balances';
    ProcessingOnly = true;
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;

    dataset
    {
        dataitem(Customer; Customer)
        {
            DataItemTableView = sorting("No.") where("Transfer Reverse Amount ERK" = const(true));

            trigger OnAfterGetRecord()
            begin
                ProcessCustomer(Customer."No.", TransferDate);
            end;
        }

        dataitem(Vendor; Vendor)
        {
            DataItemTableView = sorting("No.") where("Transfer Reverse Amount ERK" = const(true));

            trigger OnAfterGetRecord()
            begin
                ProcessVendor(Vendor."No.", TransferDate);
            end;
        }
    }

    requestpage
    {
        SaveValues = true;

        layout
        {
            area(Content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field(TransferDateField; TransferDate)
                    {
                        ApplicationArea = All;
                        Caption = 'Transfer Date';
                        ToolTip = 'Specifies the date to check for negative balances.';
                    }
                }
            }
        }

        trigger OnOpenPage()
        begin
            if TransferDate = 0D then
                TransferDate := WorkDate();
        end;
    }

    var
        GenJournalLine: Record "Gen. Journal Line";
        TranferNegBalSetup: Record "Transfer Neg. Balance ERK";
        GeneralJournalPage: Page "General Journal";
        TransferDate: Date;
        LineNo: Integer;
        // CustomerNegativeBalanceMsg: Label 'Customer %1 has negative balance of %2 on %3.', Comment = '%1 is the customer number, %2 is the balance amount, and %3 is the date.';
        JournalLinesCreatedMsg: Label 'Journal lines have been created.';
        SetupMissingErr: Label 'Transfer Negative Balance Setup is missing. Please complete the setup before running this report.';
    // VendorNegativeBalanceMsg: Label 'Vendor %1 has negative balance of %2 on %3.', Comment = '%1 is the vendor number, %2 is the balance amount, and %3 is the date.';

    trigger OnPreReport()
    begin
        if not TranferNegBalSetup.Get() then
            Error(SetupMissingErr);

        LineNo := 10000;
    end;

    local procedure ProcessCustomer(CustomerNo: Code[20]; ProcessDate: Date)
    var
        CustomerBalance: Decimal;
    begin
        CustomerBalance := GetCustomerBalance(CustomerNo, ProcessDate);

        // Only process customers with negative balances
        if CustomerBalance >= 0 then
            exit;

        //Message(CustomerNegativeBalanceMsg, CustomerNo, Abs(CustomerBalance), ProcessDate);

        // Create journal entry for transfer
        CreateJournalLine(
            GenJournalLine."Account Type"::Customer,
            CustomerNo,
            ProcessDate,
            GenJournalLine."Document Type"::" ",
            Abs(CustomerBalance),
            TranferNegBalSetup."Customer Balance Account No.");

        // Create journal entry for transfer back
        CreateJournalLine(
            GenJournalLine."Account Type"::"G/L Account",
            TranferNegBalSetup."Customer Balance Account No.",
            CalcDate('<+1D>', ProcessDate),
            GenJournalLine."Document Type"::" ",
            Abs(CustomerBalance),
            CustomerNo);
    end;

    local procedure ProcessVendor(VendorNo: Code[20]; ProcessDate: Date)
    var
        VendorBalance: Decimal;
    begin
        VendorBalance := GetVendorBalance(VendorNo, ProcessDate);

        // Only process vendors with negative balances
        if VendorBalance <= 0 then
            exit;

        //Message(VendorNegativeBalanceMsg, VendorNo, Abs(VendorBalance), ProcessDate);

        // Create journal entry for transfer
        CreateJournalLine(
            GenJournalLine."Account Type"::Vendor,
            VendorNo,
            ProcessDate,
            GenJournalLine."Document Type"::" ",
            Abs(VendorBalance),
            TranferNegBalSetup."Vendor Balance Account No.");

        // Create journal entry for transfer back
        CreateJournalLine(
            GenJournalLine."Account Type"::"G/L Account",
            TranferNegBalSetup."Vendor Balance Account No.",
            CalcDate('<+1D>', ProcessDate),
            GenJournalLine."Document Type"::" ",
            Abs(VendorBalance),
            VendorNo);
    end;

    local procedure GetCustomerBalance(CustomerNo: Code[20]; BalanceAsOfDate: Date): Decimal
    var
        Customer: Record Customer;
    begin
        Customer.Get(CustomerNo);
        Customer.SetFilter("Date Filter", '..%1', BalanceAsOfDate);
        Customer.CalcFields("Balance (LCY)");
        exit(Customer."Balance (LCY)");
    end;

    local procedure GetVendorBalance(VendorNo: Code[20]; BalanceAsOfDate: Date): Decimal
    var
        Vendor: Record Vendor;
    begin
        Vendor.Get(VendorNo);
        Vendor.SetFilter("Date Filter", '..%1', BalanceAsOfDate);
        Vendor.CalcFields("Balance (LCY)");
        exit(Vendor."Balance (LCY)");
    end;

    local procedure CreateJournalLine(AccountType: Enum "Gen. Journal Account Type"; AccountNo: Code[20]; PostingDate: Date; DocumentType: Enum "Gen. Journal Document Type"; Amount: Decimal; BalAccountNo: Code[20])
    var
        LastGenJournalLine: Record "Gen. Journal Line";
    begin
        GenJournalLine.Init();
        GenJournalLine."Journal Template Name" := TranferNegBalSetup."Journal Template Name";
        GenJournalLine."Journal Batch Name" := TranferNegBalSetup."Journal Batch Name";

        // Find the last journal line to use for SetupNewLine
        LastGenJournalLine.Reset();
        LastGenJournalLine.SetRange("Journal Template Name", GenJournalLine."Journal Template Name");
        LastGenJournalLine.SetRange("Journal Batch Name", GenJournalLine."Journal Batch Name");
        if LastGenJournalLine.FindLast() then
            LineNo := LastGenJournalLine."Line No." + 10000
        else
            LineNo := 10000;

        GenJournalLine."Line No." := LineNo;

        // Use SetupNewLine to set default values
        GenJournalLine.SetUpNewLine(LastGenJournalLine, 0, true);

        // Override with our specific values
        GenJournalLine.Validate("Account Type", AccountType);
        GenJournalLine.Validate("Account No.", AccountNo);
        GenJournalLine.Validate("Posting Date", PostingDate);
        //GenJournalLine."VAT Reporting Date" := PostingDate;  // Set VAT Date equal to Posting Date
        GenJournalLine.Validate("Document Type", DocumentType);


        //GenJournalLine.Description := Description;
        GenJournalLine.Amount := Amount;

        case AccountType of
            GenJournalLine."Account Type"::Customer,
            GenJournalLine."Account Type"::Vendor:
                GenJournalLine."Bal. Account Type" := GenJournalLine."Bal. Account Type"::"G/L Account";
            GenJournalLine."Account Type"::"G/L Account":
                if AccountNo = TranferNegBalSetup."Customer Balance Account No." then
                    GenJournalLine."Bal. Account Type" := GenJournalLine."Bal. Account Type"::Customer
                else
                    GenJournalLine."Bal. Account Type" := GenJournalLine."Bal. Account Type"::Vendor;
        end;

        GenJournalLine.Validate("Bal. Account No.", BalAccountNo);
        GenJournalLine.Insert(true);
    end;

    trigger OnPostReport()
    begin
        Message(JournalLinesCreatedMsg);

        // Open the General Journal page with filters for the created journal entries
        GenJournalLine.Reset();
        GenJournalLine.SetRange("Journal Template Name", TranferNegBalSetup."Journal Template Name");
        GenJournalLine.SetRange("Journal Batch Name", TranferNegBalSetup."Journal Batch Name");

        if GenJournalLine.FindSet() then begin
            GeneralJournalPage.SetTableView(GenJournalLine);
            GeneralJournalPage.SetRecord(GenJournalLine);
            GeneralJournalPage.Run();
        end;
    end;
}