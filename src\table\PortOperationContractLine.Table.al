table 60072 "Port Operation Contract Line"
{
    Caption = 'Port Operation Contract Line';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the number of the port operation contract.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the line number of the port operation contract.';
            AllowInCustomizations = Always;
        }
        field(3; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the number of the port operation contract.';
            TableRelation = Item."No." where(Type = const(Service));
            trigger OnValidate()
            begin
                "Variant Code" := '';
                Description := ErkHoldingBasicFunctions.GetItemVariantDescription("No.", "Variant Code");
            end;
        }
        field(4; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the variant code of the port operation contract.';
            TableRelation = "Item Variant".Code where("Item No." = field("No."));
            trigger OnValidate()
            begin
                Description := ErkHoldingBasicFunctions.GetItemVariantDescription("No.", "Variant Code");
            end;
        }
        field(5; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the description of the port operation contract.';
        }
        field(6; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            ToolTip = 'Specifies the currency code of the port operation contract.';
            TableRelation = Currency.Code;
        }
        field(7; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            ToolTip = 'Specifies the unit of measure code of the port operation contract.';
            TableRelation = "Item Unit of Measure".Code where("Item No." = field("No."));
        }
        field(8; "Unit Price"; Decimal)
        {
            Caption = 'Unit Price';
            ToolTip = 'Specifies the unit price of the port operation contract.';
        }
        field(9; "Parent Load Type"; Code[20])
        {
            Caption = 'Parent Load Type';
            ToolTip = 'Specifies the parent load type of the port operation contract.';
            TableRelation = "Parent Load Type ERK".Code;
        }
        field(10; "Invoicing Period"; DateFormula)
        {
            Caption = 'Invoicing Period';
            ToolTip = 'Specifies the value of the Invoicing Period field.';

            trigger OnLookup()
            var
                InvoicingPeriodSetup: Record "Invoicing Period Setup ERK";
            begin
                if Page.RunModal(Page::"Invoicing Period Setup List", InvoicingPeriodSetup) = Action::LookupOK then begin
                    Validate("Invoicing Period Code", InvoicingPeriodSetup.Code);
                    Validate("Invoicing Period", InvoicingPeriodSetup."Invoicing Period");
                end;
            end;
        }
        field(11; "Sub Load Type"; Code[20])
        {
            Caption = 'Sub Load Type';
            ToolTip = 'Specifies the sub load type of the port operation contract.';
            TableRelation = "Sub Load Type ERK";
        }
        field(12; "Processed Load"; Code[10])
        {
            Caption = 'Processed Load';
            ToolTip = 'Specifies the processed load of the port operation contract.';
            TableRelation = "Item Variant".Code where("Item No." = const('HZMT0052'));
        }
        field(13; "Starting Date"; Date)
        {
            Caption = 'Starting Date';
            ToolTip = 'Specifies the starting date of the contract price validity.';
        }
        field(14; "Ending Date"; Date)
        {
            Caption = 'Ending Date';
            ToolTip = 'Specifies the ending date of the contract price validity.';

            trigger OnValidate()
            begin
                if "Ending Date" <> 0D then
                    if "Starting Date" > "Ending Date" then
                        Error(EndDateBeforeStartDateErr);
            end;
        }
        field(15; "Invoicing Period Code"; Code[10])
        {
            Caption = 'Invoicing Period Code';
            TableRelation = "Invoicing Period Setup ERK".Code;
            AllowInCustomizations = Always;

            trigger OnValidate()
            var
                InvoicingPeriodSetup: Record "Invoicing Period Setup ERK";
            begin
                if "Invoicing Period Code" <> '' then begin
                    InvoicingPeriodSetup.Get("Invoicing Period Code");
                    Validate("Invoicing Period", InvoicingPeriodSetup."Invoicing Period");
                end;
            end;
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        EndDateBeforeStartDateErr: Label 'Ending Date cannot be before Starting Date.';
}