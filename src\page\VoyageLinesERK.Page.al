page 60020 "Voyage Lines ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Voyage Lines';
    PageType = List;
    SourceTable = "Voyage Line ERK";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Voyage No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Loader/Exporter No."; Rec."Loader/Exporter No.")
                {
                }
                field("Loader/Exporter Name"; Rec."Loader/Exporter Name")
                {
                }
                field("Shipment Method Code"; Rec."Shipment Method Code")
                {
                }
                field("Shipper Ship-to Code"; Rec."Shipper Ship-to Code")
                {
                }
                field("Shipper Ship-to Name"; Rec."Shipper Ship-to Name")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}
