﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="ExportNo">
          <DataField>ExportNo</DataField>
        </Field>
        <Field Name="No">
          <DataField>No</DataField>
        </Field>
        <Field Name="ConcanatedContainerNo">
          <DataField>ConcanatedContainerNo</DataField>
        </Field>
        <Field Name="CustomerName">
          <DataField>CustomerName</DataField>
        </Field>
        <Field Name="TotalBoxQuantity">
          <DataField>TotalBoxQuantity</DataField>
        </Field>
        <Field Name="TotalNetWeightKG">
          <DataField>TotalNetWeightKG</DataField>
        </Field>
        <Field Name="TotalNetWeightKGFormat">
          <DataField>TotalNetWeightKGFormat</DataField>
        </Field>
        <Field Name="TotalGrossWeightKG">
          <DataField>TotalGrossWeightKG</DataField>
        </Field>
        <Field Name="TotalGrossWeightKGFormat">
          <DataField>TotalGrossWeightKGFormat</DataField>
        </Field>
        <Field Name="ConsigneeShiptoAddress_ExportHeaderERK">
          <DataField>ConsigneeShiptoAddress_ExportHeaderERK</DataField>
        </Field>
        <Field Name="ConsigneeShiptoAddress2_ExportHeaderERK">
          <DataField>ConsigneeShiptoAddress2_ExportHeaderERK</DataField>
        </Field>
        <Field Name="ConsigneeShiptoCity_ExportHeaderERK">
          <DataField>ConsigneeShiptoCity_ExportHeaderERK</DataField>
        </Field>
        <Field Name="ConsigneeShiptoCode_ExportHeaderERK">
          <DataField>ConsigneeShiptoCode_ExportHeaderERK</DataField>
        </Field>
        <Field Name="ConsigneeShiptoCountry_ExportHeaderERK">
          <DataField>ConsigneeShiptoCountry_ExportHeaderERK</DataField>
        </Field>
        <Field Name="ConsigneeShiptoCounty_ExportHeaderERK">
          <DataField>ConsigneeShiptoCounty_ExportHeaderERK</DataField>
        </Field>
        <Field Name="ConsigneeShiptoName_ExportHeaderERK">
          <DataField>ConsigneeShiptoName_ExportHeaderERK</DataField>
        </Field>
        <Field Name="ConsigneeShiptoName2_ExportHeaderERK">
          <DataField>ConsigneeShiptoName2_ExportHeaderERK</DataField>
        </Field>
        <Field Name="NotifyShiptoAddress_ExportHeaderERK">
          <DataField>NotifyShiptoAddress_ExportHeaderERK</DataField>
        </Field>
        <Field Name="NotifyShiptoAddress2_ExportHeaderERK">
          <DataField>NotifyShiptoAddress2_ExportHeaderERK</DataField>
        </Field>
        <Field Name="NotifyShiptoCity_ExportHeaderERK">
          <DataField>NotifyShiptoCity_ExportHeaderERK</DataField>
        </Field>
        <Field Name="NotifyShiptoCode_ExportHeaderERK">
          <DataField>NotifyShiptoCode_ExportHeaderERK</DataField>
        </Field>
        <Field Name="NotifyShiptoCountryRegion_ExportHeaderERK">
          <DataField>NotifyShiptoCountryRegion_ExportHeaderERK</DataField>
        </Field>
        <Field Name="NotifyShiptoCounty_ExportHeaderERK">
          <DataField>NotifyShiptoCounty_ExportHeaderERK</DataField>
        </Field>
        <Field Name="NotifyShiptoName_ExportHeaderERK">
          <DataField>NotifyShiptoName_ExportHeaderERK</DataField>
        </Field>
        <Field Name="NotifyShiptoName2_ExportHeaderERK">
          <DataField>NotifyShiptoName2_ExportHeaderERK</DataField>
        </Field>
        <Field Name="LoadingDate_ExportHeaderERK">
          <DataField>LoadingDate_ExportHeaderERK</DataField>
        </Field>
        <Field Name="ExportGrossWeight">
          <DataField>ExportGrossWeight</DataField>
        </Field>
        <Field Name="ExportGrossWeightFormat">
          <DataField>ExportGrossWeightFormat</DataField>
        </Field>
        <Field Name="ExportNo_ContainerLineERK">
          <DataField>ExportNo_ContainerLineERK</DataField>
        </Field>
        <Field Name="ContainerNo_ContainerLineERK">
          <DataField>ContainerNo_ContainerLineERK</DataField>
        </Field>
        <Field Name="LineNo_ContainerLineERK">
          <DataField>LineNo_ContainerLineERK</DataField>
        </Field>
        <Field Name="ItemNo_ContainerLineERK">
          <DataField>ItemNo_ContainerLineERK</DataField>
        </Field>
        <Field Name="VariantCode_ContainerLineERK">
          <DataField>VariantCode_ContainerLineERK</DataField>
        </Field>
        <Field Name="Description_ContainerLineERK">
          <DataField>Description_ContainerLineERK</DataField>
        </Field>
        <Field Name="Specification_ContainerLineERK">
          <DataField>Specification_ContainerLineERK</DataField>
        </Field>
        <Field Name="Quantity_ContainerLineERK">
          <DataField>Quantity_ContainerLineERK</DataField>
        </Field>
        <Field Name="Quantity_ContainerLineERKFormat">
          <DataField>Quantity_ContainerLineERKFormat</DataField>
        </Field>
        <Field Name="UnitofMeasureCode_ContainerLineERK">
          <DataField>UnitofMeasureCode_ContainerLineERK</DataField>
        </Field>
        <Field Name="BoxQuantity_ContainerLineERK">
          <DataField>BoxQuantity_ContainerLineERK</DataField>
        </Field>
        <Field Name="PaletteQuantity_ContainerLineERK">
          <DataField>PaletteQuantity_ContainerLineERK</DataField>
        </Field>
        <Field Name="PackageType_ContainerLineERK">
          <DataField>PackageType_ContainerLineERK</DataField>
        </Field>
        <Field Name="GrossWeightKG_ContainerLineERK">
          <DataField>GrossWeightKG_ContainerLineERK</DataField>
        </Field>
        <Field Name="GrossWeightKG_ContainerLineERKFormat">
          <DataField>GrossWeightKG_ContainerLineERKFormat</DataField>
        </Field>
        <Field Name="NetWeightKG_ContainerLineERK">
          <DataField>NetWeightKG_ContainerLineERK</DataField>
        </Field>
        <Field Name="NetWeightKG_ContainerLineERKFormat">
          <DataField>NetWeightKG_ContainerLineERKFormat</DataField>
        </Field>
        <Field Name="EDocumentPackagingTypeName_ContainerLineERK">
          <DataField>EDocumentPackagingTypeName_ContainerLineERK</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>