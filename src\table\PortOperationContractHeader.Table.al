table 60070 "Port Operation Contract Header"
{
    DataClassification = CustomerContent;
    Caption = 'Port Operation Contract Header';
    DrillDownPageId = "Port Operation Contracts ERK";
    LookupPageId = "Port Operation Contracts ERK";
    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the number of the port operation contract.';
            DataClassification = SystemMetadata;
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Port Operation Contract Nos", xRec."No.");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            ToolTip = 'Specifies the customer number.';
            TableRelation = Customer;
        }
        field(3; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            ToolTip = 'Specifies the name of the customer.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Customer No.")));
        }
        field(4; "Starting Date"; Date)
        {
            Caption = 'Starting Date';
            ToolTip = 'Specifies the starting date of the port operation contract.';
        }
        field(5; "Ending Date"; Date)
        {
            Caption = 'Ending Date';
            ToolTip = 'Specifies the ending date of the port operation contract.';
        }
        field(6; Active; Boolean)
        {
            Caption = 'Active';
            ToolTip = 'Specifies if the contract is active.';


        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            ToolTip = 'Specifies the number series to be used for the port operation contract.';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Always;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    fieldgroups
    {
        fieldgroup(DropDown; "No.", "Customer Name")
        {
        }
    }

    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Port Operation Contract Nos");
            "No. Series" := ErkHoldingSetup."Port Operation Contract Nos";
            if NoSeries.AreRelated(ErkHoldingSetup."Port Operation Contract Nos", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;
    end;
}