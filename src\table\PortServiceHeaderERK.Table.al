table 60077 "Port Service Header ERK"
{
    Caption = 'Port Service Header';
    DataClassification = CustomerContent;
    DrillDownPageId = "Port Service List ERK";
    LookupPageId = "Port Service List ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the document number.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Port Service Nos");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Starting Date"; Date)
        {
            Caption = 'Starting Date';
            ToolTip = 'Specifies the starting date.';
        }
        field(3; "Ending Date"; Date)
        {
            Caption = 'Ending Date';
            ToolTip = 'Specifies the ending date.';
        }
        field(4; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            ToolTip = 'Specifies the ship name.';
        }
        field(5; "Bill-to Customer No."; Code[20])
        {
            Caption = 'Bill-to Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the bill-to customer number.';
        }
        field(6; "Bill-to Customer Name"; Text[100])
        {
            Caption = 'Bill-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Bill-to Customer No.")));
            ToolTip = 'Specifies the bill-to customer name.';
        }
        field(7; "Load Owner Name"; Text[100])
        {
            Caption = 'Load Owner Name';
            ToolTip = 'Specifies the load owner name.';
        }
        field(8; "Load Type"; Code[50])
        {
            Caption = 'Load Type';
            TableRelation = "Load Type ERK";
            ToolTip = 'Specifies the load type.';
        }
        field(9; "Parent Load Type"; Code[20])
        {
            Caption = 'Parent Load Type';
            TableRelation = "Parent Load Type ERK";
            ToolTip = 'Specifies the parent load type.';
        }
        field(10; "Port Name"; Text[100])
        {
            Caption = 'Port Name';
            ToolTip = 'Specifies the port name.';
        }
        field(11; Completed; Boolean)
        {
            Caption = 'Completed';
            ToolTip = 'Specifies whether the service is completed.';
        }
        field(12; "Shortcut Dimension Code 1"; Code[20])
        {
            Caption = 'Shortcut Dimension Code 1';
            TableRelation = "Dimension Value".Code;
            ToolTip = 'Specifies the shortcut dimension code.';
        }
        field(13; "Declared Quantity (Tonnage)"; Decimal)
        {
            Caption = 'Declared Quantity (Tonnage)';
            DecimalPlaces = 0 : 3;
            ToolTip = 'Specifies the declared quantity in tonnage.';
        }
        field(14; "Actual Quantity (Tonnage)"; Decimal)
        {
            Caption = 'Actual Quantity (Tonnage)';
            DecimalPlaces = 0 : 3;
            ToolTip = 'Specifies the actual quantity in tonnage.';
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Port Service Nos");
            "No. Series" := ErkHoldingSetup."Port Service Nos";
            if NoSeries.AreRelated(ErkHoldingSetup."Port Service Nos", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;
    end;

    trigger OnDelete()
    var
        PortServiceLine: Record "Port Service Line ERK";
    begin
        PortServiceLine.SetRange("Document No.", Rec."No.");
        PortServiceLine.DeleteAll(true);
    end;
}