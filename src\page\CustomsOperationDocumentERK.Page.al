page 60053 "Customs Operation Document ERK"
{
    ApplicationArea = All;
    Caption = 'Customs Operation Document';
    PageType = Document;
    SourceTable = "Customs Operation Header ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                {
                }
                field("Bill-To Customer Name"; Rec."Bill-To Customer Name")
                {
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                }
                field("Declaration Type"; Rec."Declaration Type")
                {
                }
                field("Regstration Date"; Rec."Registration Date")
                {
                }
                field("Stamp Tax Line No."; Rec."Stamp Tax Line No.")
                {
                }
                field(MRN; Rec.MRN)
                {
                }
                field("Truck License Plate"; Rec."Truck License Plate")
                {
                }
                field("Trailer License Plate"; Rec."Trailer License Plate")
                {
                }
                field("Container No."; Rec."Container No.")
                {
                }
                field("Truck Carnet No."; Rec."Truck Carnet No.")
                {
                }
                field("Exit Customs Code"; Rec."Exit Customs Code")
                {
                }
                field("Exit Customs Description"; Rec."Exit Customs")
                {
                    Editable = false;
                }
                field(Sender; Rec.Sender)
                {
                }
                field(Receiver; Rec.Receiver)
                {
                }
                field("Customer Representative"; Rec."Customer Representative")
                {
                }
                field("Unposted Sales Invoice No."; Rec."Unposted Sales Invoice No.")
                {
                }
                field("Posted Sales Invoice No."; Rec."Posted Sales Invoice No.")
                {
                }
                field("E-Invoice No."; Rec."E-Invoice No.")
                {
                }
                field("Invoicing Group Code"; Rec."Invoicing Group Code")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
            part(Lines; "Customs Operation Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateSalesInvoiceFromSelectedLines)
            {
                Caption = 'Create Sales Invoice From Selected Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = CreateDocument;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Sales Invoice From Selected Lines action.';

                trigger OnAction()
                var
                    CustomsOperationHeader: Record "Customs Operation Header ERK";
                begin
                    CurrPage.SetSelectionFilter(CustomsOperationHeader);
                    CustomsOperationManagement.CreateSalesInvoiceFromSelectedCustomsOperationHeaders(CustomsOperationHeader);
                end;
            }
        }
    }
    var
        CustomsOperationManagement: Codeunit "Customs Operation Management";
}
