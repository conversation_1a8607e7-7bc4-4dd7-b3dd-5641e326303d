reportextension 60002 "Blanket Purchase Order ERK" extends "Blanket Purchase Order"
{
    dataset
    {
        add("Purchase Header")
        {
            column(RemittoNameERK; ErkHoldingBasicFunctions.GetRemittoNameFromRemittoCode("Purchase Header"."Buy-from Vendor No.", CopyStr("Purchase Header"."Remit-to Code", 1, 10)))
            {
            }
            column(RequestedReceiptDateText_PurchaseHeaderERK; "Requested Receipt Text ERK")
            {
            }
            column(PaymentTermsCode_PurchaseHeaderERK; "Payment Terms Code")
            {
            }
        }
        modify("Purchase Header")
        {
            trigger OnAfterAfterGetRecord()
            begin
                FormatDocumentFields("Purchase Header");
            end;
        }
        add(CopyLoop)
        {
            column(TotalTextERK; TotalText)
            {
            }
        }
        add(RoundLoop)
        {
            column(BrandCodeERK_PurchaseLineERK; ExportManagement.GetBrandFromItemAndVariantCode("Purchase Line"."No.", "Purchase Line"."Variant Code"))
            {
            }
            column(SpecialFeatureCodeERK_PurchaseLineERK; ExportManagement.GetSpecificationFromItemAndVariantCode("Purchase Line"."No.", "Purchase Line"."Variant Code"))
            {
            }
            column(DirectUnitCostERK; "Purchase Line"."Direct Unit Cost")
            {
            }
            column(LineAmountERK; "Purchase Line"."Line Amount")
            {
            }
        }
    }
    local procedure FormatDocumentFields(PurchaseHeader: Record "Purchase Header")
    begin
        FormatDocument.SetTotalLabels(PurchaseHeader."Currency Code", TotalText, TotalInclVATText, TotalExclVATText);
        // FormatDocument.SetPurchaser(SalesPurchPerson, "Purchaser Code", PurchaserText);
        // FormatDocument.SetPaymentTerms(PaymentTerms, "Payment Terms Code", "Language Code");
        // FormatDocument.SetPaymentTerms(PrepmtPaymentTerms, "Prepmt. Payment Terms Code", "Language Code");
        // FormatDocument.SetShipmentMethod(ShipmentMethod, "Shipment Method Code", "Language Code");
        // ReferenceText := FormatDocument.SetText("Your Reference" <> '', FieldCaption("Your Reference"));
        // VATNoText := FormatDocument.SetText("VAT Registration No." <> '', FieldCaption("VAT Registration No."));
    end;

    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        ExportManagement: Codeunit "Export Management ERK";
        TotalText: Text[50];
        TotalInclVATText: Text[50];
        TotalExclVATText: Text[50];
}
