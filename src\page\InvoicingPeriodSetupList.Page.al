page 60149 "Invoicing Period Setup List"
{
    ApplicationArea = All;
    Caption = 'Invoicing Period Setup';
    PageType = List;
    SourceTable = "Invoicing Period Setup ERK";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(Control1)
            {
                field(Code; Rec.Code)
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Invoicing Period"; Rec."Invoicing Period")
                {
                }
            }
        }
    }
}
