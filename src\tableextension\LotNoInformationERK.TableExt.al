tableextension 60010 "Lot No. Information ERK" extends "Lot No. Information"
{
    fields
    {
        field(60000; "Load Owner No. ERK"; Code[20])
        {
            Caption = 'Load Owner No.';
            TableRelation = Customer;
            ToolTip = 'Specifies the value of the Load Owner No. field.';
        }
        // field(60001; "Load Owner Name ERK"; Text[100])
        // {
        //     Caption = 'Load Owner Name';
        // }
        field(60001; "Load Owner Name ERK"; Text[100])
        {
            Caption = 'Load Owner Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Load Owner No. ERK")));
            ToolTip = 'Specifies the value of the Load Owner Name field.';
        }
        field(60002; "Bill-to Customer No. ERK"; Code[20])
        {
            Caption = 'Bill-to Customer No.';
            TableRelation = Customer;
            ToolTip = 'Specifies the value of the Bill-to Customer No. field.';
        }
        // field(60003; "Bill-to Customer Name ERK"; Text[100])
        // {
        //     Caption = 'Bill-to Customer Name';
        // }
        field(60003; "Bill-to Customer Name ERK"; Text[100])
        {
            Caption = 'Load Owner Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Bill-to Customer No. ERK")));
            ToolTip = 'Specifies the value of the Bill-to Customer Name field.';
        }
        field(60004; "Package Type ERK"; Code[10])
        {
            Caption = 'Package Type';
            ToolTip = 'Specifies the value of the Package Type field.';
        }
        field(60005; "Package Count ERK"; Decimal)
        {
            Caption = 'Package Count';
            ToolTip = 'Specifies the value of the Package Count field.';
        }
        // field(60006; "Location Code ERK"; Code[10])
        // {
        //     Caption = 'Location Code';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Item Ledger Entry"."Location Code" where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Lot No." = field("Lot No."), Open = const(true)));
        // }
        field(60007; "Operation No. ERK"; Code[20])
        {
            Caption = 'Operation No.';
            ToolTip = 'Specifies the value of the Operation No. field.';
        }
        field(60008; "Operation Line No. ERK"; Integer)
        {
            Caption = 'Operation Line No.';
            ToolTip = 'Specifies the value of the Operation Line No. field.';
        }
    }
}
