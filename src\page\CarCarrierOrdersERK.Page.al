page 60079 "Car Carrier Orders ERK"
{
    ApplicationArea = All;
    Caption = 'Car Carrier Orders';
    PageType = List;
    SourceTable = "Car Carrier Order Header ERK";
    UsageCategory = Documents;
    CardPageId = "Car Carrier Order ERK";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ShowMandatory = true;
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Order Date"; Rec."Order Date")
                {
                    ShowMandatory = true;
                }
                field(Type; Rec."Type")
                {
                    ShowMandatory = true;
                }
                field("Contract No."; Rec."Contract No.")
                {
                }
                field("Your Reference"; Rec."Your Reference")
                {
                }
                field("Loading Port Code"; Rec."Loading Port Code")
                {
                    ShowMandatory = true;
                }
                field("Loading Port Description"; Rec."Loading Port Description")
                {
                }
                field("Discharge Port Code"; Rec."Discharge Port Code")
                {
                    ShowMandatory = true;
                }
                field("Discharge Port Description"; Rec."Discharge Port Description")
                {
                }
            }
        }
    }
}
