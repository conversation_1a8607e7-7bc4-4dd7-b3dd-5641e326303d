POST https://login.microsoftonline.com/erkholding.com/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
&client_id=72ada03e-2bda-4193-baa9-6b40f667f1b7
&Scope=https%3A%2F%2Fapi.businesscentral.dynamics.com%2F.default
&client_secret=****************************************

GET https://api.businesscentral.dynamics.com/v2.0/ERKPORT_Dev2/api/infotek/eh/v1.0/companies(e3e1b984-0a94-ee11-be36-0022480d05f5)/EBApurchaseOrdersERK
Authorization: Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

POST https://api.businesscentral.dynamics.com/v2.0/ERKPORT_Dev2/api/infotek/eh/v1.0/companies(e3e1b984-0a94-ee11-be36-0022480d05f5)/EBApurchaseOrdersERK
Authorization: Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Content-type: application/json

{
    "orderDate": "2025-01-01",
    "postingDate": "2025-01-01",
    "vendorNumber": "320.02.0097", 
    "payToVendorNumber": "320.02.0097",
    "shipToName": "",
    "shipToContact": "",
    "shipToAddressLine1": "",
    "shipToAddressLine2": "",
    "shipToCity": "",
    "shipToCountry": "",
    "shipToState": "",
    "shipToPostCode": "",
    "shortcutDimension1Code": "", 
    "shortcutDimension2Code": "", 
    "buyFromAddressLine1": "",
    "buyFromAddressLine2": "",
    "buyFromCity": "",
    "buyFromCountry": "",
    "buyFromState": "",
    "buyFromPostCode": "",
    "currencyCode": "",
    "pricesIncludeTax": false,
    "yourReference": "abc123"
    
}


//66ccee13-1e19-f011-9af4-6045bd6b69ef

GET https://api.businesscentral.dynamics.com/v2.0/ERKPORT_Dev2/api/infotek/eh/v1.0/companies(e3e1b984-0a94-ee11-be36-0022480d05f5)/EBApurchaseOrdersERK(66ccee13-1e19-f011-9af4-6045bd6b69ef)/EBApurchaseOrderLinesERK
Authorization: Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


POST https://api.businesscentral.dynamics.com/v2.0/ERKPORT_Dev2/api/infotek/eh/v1.0/companies(e3e1b984-0a94-ee11-be36-0022480d05f5)/EBApurchaseOrdersERK(66ccee13-1e19-f011-9af4-6045bd6b69ef)/EBApurchaseOrderLinesERK
Authorization: Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Content-type: application/json

{
    
    "documentNo": "ALSP25-000328",
    "lineType" : "Item",
    "sequence" : 10000,
    "lineObjectNumber" : "1000",
    "unitOfMeasureCode": "ADET"

}



PATCH https://api.businesscentral.dynamics.com/v2.0/ERKPORT_Dev2/api/infotek/eh/v2.0/companies(e3e1b984-0a94-ee11-be36-0022480d05f5)/purchaseOrdersERK(d4509e60-be16-f011-9af4-6045bd6b69ef)
Authorization: Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Content-Type: application/json
If-Match: *

{

    "yourReferenceERK": "AAAAAAAA"

}