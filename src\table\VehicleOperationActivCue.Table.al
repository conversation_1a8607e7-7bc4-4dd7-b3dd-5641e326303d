table 60032 "Vehicle Operation Activ. Cue"
{
    Caption = 'Vehicle Operation Activities Cue';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "Discharge Documents - Open"; Integer)
        {
            Caption = 'Discharge Documents - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Header ERK" where("Operation Type" = const(Discharge),
                                                                     Completed = const(false),
                                                                     Released = const(true),
                                                                     "Location Filter" = field("Location Filter")));
            ToolTip = 'Specifies the value of the Discharge Documents - Open field.';
        }
        field(3; "Customs Exit Docs. - Released"; Integer)
        {
            Caption = 'Customs Exit Documents - Released';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Header ERK" where("Operation Type" = const("Customs Exit"),
                                                                     Completed = const(false),
                                                                     Released = const(true),
                                                                     "Location Filter" = field("Location Filter")));
            ToolTip = 'Specifies the value of the Customs Exit Documents - Open field.';
        }
        field(4; "Loading Documents - Open"; Integer)
        {
            Caption = 'Loading Documents - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Header ERK" where("Operation Type" = const(Loading),
                                                                     Completed = const(false),
                                                                     Released = const(true),
                                                                     "Location Filter" = field("Location Filter")));
            ToolTip = 'Specifies the value of the Loading Documents - Open field.';
        }
        field(5; "Transfer Documents - Open"; Integer)
        {
            Caption = 'Transfer Documents - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Header ERK" where("Operation Type" = const(Transfer),
                                                                     Completed = const(false),
                                                                     Released = const(true),
                                                                     "Location Filter" = field("Location Filter")));
            ToolTip = 'Specifies the value of the Transfer Documents - Open field.';
        }
        field(6; "Wash Documents - Open"; Integer)
        {
            Caption = 'Wash Documents - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Header ERK" where("Operation Type" = const(Wash),
                                                                     Completed = const(false),
                                                                     Released = const(true),
                                                                     "Location Filter" = field("Location Filter")));
            ToolTip = 'Specifies the value of the Wash Documents - Open field.';
        }
        field(7; "PDI Entry Documents - Released"; Integer)
        {
            Caption = 'PDI Entry Documents - Released';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Header ERK" where("Operation Type" = const("PDI Entry"),
                                                                     Completed = const(false),
                                                                     Released = const(true),
                                                                     "Location Filter" = field("Location Filter")));
            ToolTip = 'Specifies the value of the PDI Entry Documents - Open field.';
        }
        field(8; "Dispatch Prep. - Released"; Integer)
        {
            Caption = 'Dispatch Preparation - Released';
            ToolTip = 'Specifies the number of Vehicle Transfer records where the Operation Type is Dispatch Preparation, the Completed field is false, and the Released field is true.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Header ERK" where("Operation Type" = const("Dispatch Preparation"),
                                                                     Completed = const(false),
                                                                     Released = const(true),
                                                                     "Location Filter" = field("Location Filter")));
        }
        field(9; "Nav Entry - Released"; Integer)
        {
            Caption = 'Nav Entry - Released';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Header ERK" where("Operation Type" = const("Nav Entry"),
                                                                     Completed = const(false),
                                                                     Released = const(true),
                                                                     "Lines Locked" = const(true),
                                                                     "Location Filter" = field("Location Filter")));
            ToolTip = 'Specifies the value of the Nav Entry - Released field.';
        }
        field(10; "PDI Documents - Open"; Integer)
        {
            Caption = 'PDI Documents - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("PDI Header ERK" where(Saved = const(false)));
            ToolTip = 'Specifies the value of the PDI Documents - Open field.';
        }
        field(11; "Vehicles in Perron Bins"; Integer)
        {
            Caption = 'Vehicles in Perron Bins';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count(Bin where("Is Perron ERK" = const(true), "Vehicle Quantity ERK" = filter(> 0), "Location Code" = field("Location Filter")));
            ToolTip = 'Specifies the number of perron bins that contain vehicles.';
        }
        field(12; "Location Filter"; Text[250])
        {
            Caption = 'Location Filter';
            FieldClass = FlowFilter;
            ToolTip = 'Specifies the location filter for the cue fields.';
        }
    }
    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }
}
