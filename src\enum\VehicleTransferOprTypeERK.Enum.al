enum 60006 "Vehicle Transfer Opr. Type ERK"
{
    Extensible = true;

    value(0; " ")
    {
        Caption = ' ', Locked = true;
    }
    value(1; Discharge)
    {
        Caption = 'Discharge';
    }
    value(2; Loading)
    {
        Caption = 'Loading';
    }
    value(3; Addressing)
    {
        Caption = 'Addressing';
    }
    value(4; "Customs Exit")
    {
        Caption = 'Customs Exit';
    }
    value(5; Transfer)
    {
        Caption = 'Transfer';
    }
    value(6; "PDI Exit")
    {
        Caption = 'PDI Exit';
    }
    value(7; "Nav Exit")
    {
        Caption = 'Nav Exit';
    }
    value(8; "Vehicle Entry")
    {
        Caption = 'Vehicle Entry';
    }
    value(9; "Dealer Dispatch")
    {
        Caption = 'Dealer Dispatch';
    }
    value(10; "PDI Entry")
    {
        Caption = 'PDI Entry';
    }
    value(11; "Damage Exit")
    {
        Caption = 'Damage Exit';
    }
    value(12; Wash)
    {
        Caption = 'Wash';
    }
    value(13; "Stock-Taking")
    {
        Caption = 'Stock-Taking';
    }
    value(14; "Grupage Dealer Dispatch")
    {
        Caption = 'Grupage Dealer Dispatch';
    }
    value(15; "Dispatch Preparation")
    {
        Caption = 'Dispatch Preparation';
    }
    value(16; "Nav Entry")
    {
        Caption = 'Nav Entry';
    }
}
