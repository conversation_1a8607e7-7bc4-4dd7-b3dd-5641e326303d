page 60057 "PDI Check List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'PDI Check List';
    PageType = List;
    SourceTable = "PDI Check List ERK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field(Code; Rec.Code)
                {
                    Style = Strong; // This sets the font style to bold
                    StyleExpr = Rec."Parent Code"; // This sets the condition for applying the style
                }
                field(Description; Rec.Description)
                {
                    Style = Strong; // This sets the font style to bold
                    StyleExpr = Rec."Parent Code"; // This sets the condition for applying the style
                }
                field("Parent Code"; Rec."Parent Code")
                {
                    Style = Strong; // This sets the font style to bold
                    StyleExpr = Rec."Parent Code"; // This sets the condition for applying the style
                }
                field("Parent Code Value"; Rec."Parent Code Value")
                {
                }
                field("Default Result"; Rec."Default Result")
                {
                }
            }
        }
    }
}
