pageextension 60033 "Bins ERK" extends Bins
{
    layout
    {
        addlast(Control1)
        {
            field("Default Loading/Discharge ERK"; Rec."Default Discharge ERK")
            {
                ApplicationArea = All;
            }
            field("Vehicle Quantity ERK"; Rec."Vehicle Quantity ERK")
            {
                ApplicationArea = All;
            }
            field("Vehicle Capacity ERK"; Rec."Vehicle Capacity ERK")
            {
                ApplicationArea = All;
            }
            field("Civil Area ERK"; Rec."Civil Area ERK")
            {
                ApplicationArea = All;
            }
            field("PDI Bin ERK"; Rec."PDI Bin ERK")
            {
                ApplicationArea = All;
            }
            field("In-Transit Bin ERK"; Rec."In-Transit Bin ERK")
            {
                ApplicationArea = All;
            }
            field("Customs Exit ERK"; Rec."Customs Exit ERK")
            {
                ApplicationArea = All;
            }
            field("Default Loading ERK"; Rec."Default Loading ERK")
            {
                ApplicationArea = All;
            }
            field("Nav Area ERK"; Rec."Nav Area ERK")
            {
                ApplicationArea = All;
            }
            field("Visible for Nav Entry ERK"; Rec."Visible for Nav Entry ERK")
            {
                ApplicationArea = All;
            }
            field("PDI Area ERK"; Rec."PDI Area ERK")
            {
                ApplicationArea = All;
            }
            field("Transfer ERK"; Rec."Transfer ERK")
            {
                ApplicationArea = All;
            }
            field("Dealer ERK"; Rec."Vis. for Dealer Dispatch ERK")
            {
                ApplicationArea = All;
            }
            field("Hide in Addressing ERK"; Rec."Hide in Addressing ERK")
            {
                ApplicationArea = All;
            }
            field("Damage Exit ERK"; Rec."Visible for Damage Exit ERK")
            {
                ApplicationArea = All;
            }
            field("PDI Entry ERK"; Rec."Visible for PDI Entry ERK")
            {
                ApplicationArea = All;
            }
            field("Visible for Wash ERK"; Rec."Visible for Wash ERK")
            {
                ApplicationArea = All;
            }
            field("Vehicle Entry ERK"; Rec."Vehicle Entry ERK")
            {
                ApplicationArea = All;
            }
            field("Mandatory From Bin For PDI ERK"; Rec."Mandatory From Bin For PDI ERK")
            {
                ApplicationArea = All;
            }
            field("Man. From Bin For Nav Exit ERK"; Rec."Man. From Bin For Nav Exit ERK")
            {
                ApplicationArea = All;
            }
            field("Man Frm Bin Dealer Dispch. ERK"; Rec."Man Frm Bin Dealer Dispch. ERK")
            {
                ApplicationArea = All;
            }
            field("Man. From Bin Dmg. Exit ERK"; Rec."Man. From Bin Dmg. Exit ERK")
            {
                ApplicationArea = All;
            }
            field("Visible for Dispatch Prep. ERK"; Rec."Visible for Dispatch Prep. ERK")
            {
                ApplicationArea = All;
            }
            field("Is Perron ERK"; Rec."Is Perron ERK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addfirst("&Bin")
        {
            action("TransferSelectedBinsToXXBin ERK")
            {
                ApplicationArea = All;
                Image = TransferReceipt;
                Caption = 'Transfer Selected Bins to XX Bin';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Transfer Selected Bins to XX Bin action.';

                trigger OnAction()
                var
                    Bin: Record Bin;
                begin
                    CurrPage.SetSelectionFilter(Bin);
                    VehicleTransferManagement.TransferSelectedBinsToXXBin(Bin);
                end;
            }
        }
    }
    var
        VehicleTransferManagement: Codeunit "Vehicle Transfer Management";
}
