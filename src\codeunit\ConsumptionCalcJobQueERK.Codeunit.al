codeunit 60017 "Consumption Calc. Job Que ERK"
{
    trigger OnRun()
    begin
        CalculateCarCarrierConsumptions();
        CalculateRoRoConsumptions();
        CalculatePortOperationConsumptions();
    end;

    local procedure CalculatePortOperationConsumptions()
    var
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
        ValueEntry: Record "Value Entry";
        SalesInvoiceLine: Record "Sales Invoice Line";
        QtyPerUnitOfMeasureCode: Decimal;
    begin
        PortOperationLineDetail.SetRange(Type, PortOperationLineDetail.Type::Consumption);
        if not PortOperationLineDetail.FindSet() then
            exit;
        repeat
            ValueEntry.SetRange("Document No.", PortOperationLineDetail."Invoice No.");
            ValueEntry.SetRange("Global Dimension 2 Code", PortOperationLineDetail."Document No.");
            ValueEntry.SetRange("Item No.", PortOperationLineDetail."No.");
            ValueEntry.SetRange("Variant Code", PortOperationLineDetail."Variant Code");
            ValueEntry.CalcSums("Cost per Unit (ACY)", "Cost per Unit");
            SalesInvoiceLine.SetRange("Document No.", PortOperationLineDetail."Invoice No.");
            SalesInvoiceLine.SetRange("Shortcut Dimension 2 Code", PortOperationLineDetail."Document No.");
            SalesInvoiceLine.SetRange("No.", PortOperationLineDetail."No.");
            SalesInvoiceLine.SetRange("Variant Code", PortOperationLineDetail."Variant Code");
            if SalesInvoiceLine.FindFirst() then
                QtyPerUnitOfMeasureCode := SalesInvoiceLine."Qty. per Unit of Measure"
            else
                QtyPerUnitOfMeasureCode := 1;
            PortOperationLineDetail.Validate("Currency Code", '');
            PortOperationLineDetail."Unit Price" := ValueEntry."Cost per Unit" * QtyPerUnitOfMeasureCode;
            PortOperationLineDetail."Line Amount" := PortOperationLineDetail."Unit Price" * PortOperationLineDetail.Quantity;
            PortOperationLineDetail."Line Amount (ACY)" := ValueEntry."Cost per Unit (ACY)" * QtyPerUnitOfMeasureCode * PortOperationLineDetail.Quantity;
            PortOperationLineDetail.Modify(false);
        until PortOperationLineDetail.Next() = 0;
    end;

    local procedure CalculateRoRoConsumptions()
    var
        VoyageExpense: Record "Voyage Expense ERK";
    begin
        VoyageExpense.SetRange("Consumption Line", true);
        if not VoyageExpense.FindSet(true) then
            exit;
        repeat
            CalculateConsumptionCostFromVoyageExpense(VoyageExpense);
        until VoyageExpense.Next() = 0;
    end;

    procedure CalculateConsumptionCostFromVoyageExpense(var VoyageExpense: Record "Voyage Expense ERK")
    var
        ValueEntry: Record "Value Entry";
        SalesInvoiceLine: Record "Sales Invoice Line";
        QtyPerUnitOfMeasureCode: Decimal;
    begin
        if not VoyageExpense."Consumption Line" then
            exit;
        ValueEntry.SetRange("Document No.", VoyageExpense."Purchase Invoice No.");
        ValueEntry.SetRange("Global Dimension 2 Code", VoyageExpense."Document No.");
        ValueEntry.SetRange("Item No.", VoyageExpense."No.");
        ValueEntry.SetRange("Variant Code", VoyageExpense."Variant Code");
        ValueEntry.CalcSums("Cost per Unit (ACY)", "Cost per Unit");
        SalesInvoiceLine.SetRange("Document No.", VoyageExpense."Purchase Invoice No.");
        SalesInvoiceLine.SetRange("Shortcut Dimension 2 Code", VoyageExpense."Document No.");
        SalesInvoiceLine.SetRange("No.", VoyageExpense."No.");
        SalesInvoiceLine.SetRange("Variant Code", VoyageExpense."Variant Code");
        if SalesInvoiceLine.FindFirst() then
            QtyPerUnitOfMeasureCode := SalesInvoiceLine."Qty. per Unit of Measure"
        else
            QtyPerUnitOfMeasureCode := 1;
        VoyageExpense.Validate("Currency Code", '');
        VoyageExpense."Unit Cost" := ValueEntry."Cost per Unit" * QtyPerUnitOfMeasureCode;
        VoyageExpense."Line Amount" := VoyageExpense."Unit Cost" * VoyageExpense.Quantity;
        VoyageExpense."Line Amount (ACY)" := ValueEntry."Cost per Unit (ACY)" * QtyPerUnitOfMeasureCode * VoyageExpense.Quantity;
        VoyageExpense.Modify(false);
    end;

    local procedure CalculateCarCarrierConsumptions()
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        CarCarrRevenueExpense.SetRange("Type", CarCarrRevenueExpense.Type::Consumption);
        CarCarrRevenueExpense.SetFilter("Posted Invoice No.", '<>%1', '');
        if not CarCarrRevenueExpense.FindSet() then
            exit;
        repeat
            CalculateConsumptionCostFromCarCarrierRevExp(CarCarrRevenueExpense);
        until CarCarrRevenueExpense.Next() = 0;
    end;

    local procedure CalculateConsumptionCostFromCarCarrierRevExp(var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    var
        ValueEntry: Record "Value Entry";
        SalesInvoiceLine: Record "Sales Invoice Line";
        QtyPerUnitOfMeasureCode: Decimal;
    begin
        ValueEntry.SetRange("Document No.", CarCarrRevenueExpense."Posted Invoice No.");
        ValueEntry.SetRange("Global Dimension 2 Code", CarCarrRevenueExpense."Document No.");
        ValueEntry.SetRange("Item No.", CarCarrRevenueExpense."No.");
        ValueEntry.SetRange("Variant Code", CarCarrRevenueExpense."Variant Code");
        ValueEntry.CalcSums("Cost per Unit (ACY)", "Cost per Unit");
        SalesInvoiceLine.SetRange("Document No.", CarCarrRevenueExpense."Posted Invoice No.");
        SalesInvoiceLine.SetRange("Shortcut Dimension 2 Code", CarCarrRevenueExpense."Document No.");
        SalesInvoiceLine.SetRange("No.", CarCarrRevenueExpense."No.");
        SalesInvoiceLine.SetRange("Variant Code", CarCarrRevenueExpense."Variant Code");
        if SalesInvoiceLine.FindFirst() then
            QtyPerUnitOfMeasureCode := SalesInvoiceLine."Qty. per Unit of Measure"
        else
            QtyPerUnitOfMeasureCode := 1;
        CarCarrRevenueExpense.Validate("Currency Code", '');
        //CarCarrRevenueExpense.Validate("Unit Price/Cost", ValueEntry."Cost per Unit" * QtyPerUnitOfMeasureCode);
        CarCarrRevenueExpense."Unit Price/Cost" := ValueEntry."Cost per Unit" * QtyPerUnitOfMeasureCode;
        CarCarrRevenueExpense."Line Amount" := CarCarrRevenueExpense."Unit Price/Cost" * CarCarrRevenueExpense.Quantity;
        CarCarrRevenueExpense."Line Amount (ACY)" := ValueEntry."Cost per Unit (ACY)" * QtyPerUnitOfMeasureCode * CarCarrRevenueExpense.Quantity;
        CarCarrRevenueExpense.Validate("Consumption Amount Calculated", true);
        CarCarrRevenueExpense.Modify(false);
    end;
}
