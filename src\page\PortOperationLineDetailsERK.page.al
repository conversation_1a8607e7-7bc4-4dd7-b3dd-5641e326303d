page 60034 "Port Operation Line DetailsERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Port Operation Line Details';
    PageType = List;
    SourceTable = "Port Operation Line Detail ERK";
    UsageCategory = Lists;
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Declaration No."; Rec."Declaration No.")
                {
                    StyleExpr = StyleTxt;
                }
                field("Document No."; Rec."Document No.")
                {
                    StyleExpr = StyleTxt;
                    trigger OnDrillDown()
                    var
                        PortOperation: Record "Port Operation Header ERK";
                    begin
                        begin
                            Rec.TestField("Document No.");
                            if PortOperation.Get(Rec."Document No.") then
                                PageManagement.PageRun(PortOperation);
                        end;
                    end;
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                    StyleExpr = StyleTxt;
                }
                field("Line No."; Rec."Line No.")
                {
                    StyleExpr = StyleTxt;
                }
                field(Type; Rec."Type")
                {
                    StyleExpr = StyleTxt;
                }
                field("Operation Date"; Rec."Operation Date")
                {
                    StyleExpr = StyleTxt;
                }
                field("No."; Rec."No.")
                {
                    StyleExpr = StyleTxt;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    StyleExpr = StyleTxt;
                }
                field("Parent Load Type"; Rec."Parent Load Type")
                {
                    StyleExpr = StyleTxt;
                }
                field("Load Type"; Rec."Sub Load Type")
                {
                    StyleExpr = StyleTxt;
                }
                field("Processed Load"; Rec."Processed Load")
                {
                    StyleExpr = StyleTxt;
                }
                field(Description; Rec.Description)
                {
                    StyleExpr = StyleTxt;
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                    StyleExpr = StyleTxt;
                }
                field("Department Name"; Rec."Department Name")
                {
                    StyleExpr = StyleTxt;
                }
                field("Location Code"; Rec."Location Code")
                {
                    StyleExpr = StyleTxt;
                }
                field("Bin Code"; Rec."Bin Code")
                {
                    StyleExpr = StyleTxt;
                }
                field("Consumption Location Code"; Rec."Consumption Location Code")
                {
                    StyleExpr = StyleTxt;
                }
                field("Consumption Bin Code"; Rec."Consumption Bin Code")
                {
                    StyleExpr = StyleTxt;
                }
                field("Regime Type"; Rec."Regime Type")
                {
                    StyleExpr = StyleTxt;
                }
                field("VAT Prod. Posting Group"; Rec."VAT Prod. Posting Group")
                {
                    StyleExpr = StyleTxt;
                    Editable = not ((Rec.Type = Rec.Type::"Load Entry") or (Rec.Type = Rec.Type::"Load Exit") or (Rec.Type = Rec.Type::"Load Transfer"));
                }
                field("VAT With. Prod. Posting Group"; Rec."VAT With. Prod. Posting Group")
                {
                    StyleExpr = StyleTxt;
                    Editable = not ((Rec.Type = Rec.Type::"Load Entry") or (Rec.Type = Rec.Type::"Load Exit") or (Rec.Type = Rec.Type::"Load Transfer"));
                }
                field(Quantity; Rec.Quantity)
                {
                    StyleExpr = StyleTxt;
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    StyleExpr = StyleTxt;
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    StyleExpr = StyleTxt;
                    Editable = not ((Rec.Type = Rec.Type::"Load Entry") or (Rec.Type = Rec.Type::"Load Exit") or (Rec.Type = Rec.Type::"Load Transfer"));
                }
                field("Unit Price"; Rec."Unit Price")
                {
                    StyleExpr = StyleTxt;
                    Editable = not ((Rec.Type = Rec.Type::"Load Entry") or (Rec.Type = Rec.Type::"Load Exit") or (Rec.Type = Rec.Type::"Load Transfer"));
                }
                field("Duration (Days)"; Rec."Duration (Days)")
                {
                }
                field("Invoice Comment"; Rec."Invoice Comment")
                {
                    StyleExpr = StyleTxt;
                    Editable = not ((Rec.Type = Rec.Type::"Load Entry") or (Rec.Type = Rec.Type::"Load Exit") or (Rec.Type = Rec.Type::"Load Transfer"));
                }
                field("Line Amount"; Rec."Line Amount")
                {
                    StyleExpr = StyleTxt;
                }
                field("Line Amount (ACY)"; Rec."Line Amount (ACY)")
                {
                    StyleExpr = StyleTxt;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    StyleExpr = StyleTxt;
                }
                field("Next Invoice Date"; Rec."Next Invoice Date")
                {
                    StyleExpr = StyleTxt;
                    Editable = not ((Rec.Type = Rec.Type::"Load Entry") or (Rec.Type = Rec.Type::"Load Exit") or (Rec.Type = Rec.Type::"Load Transfer"));
                }
                field("Next Invoice Period"; Rec."Next Invoice Period")
                {
                    StyleExpr = StyleTxt;
                    Editable = not ((Rec.Type = Rec.Type::"Load Entry") or (Rec.Type = Rec.Type::"Load Exit") or (Rec.Type = Rec.Type::"Load Transfer"));
                }
                field("Invoice No."; Rec."Invoice No.")
                {
                    StyleExpr = StyleTxt;

                    trigger OnDrillDown()
                    var
                        SalesHeader: Record "Sales Header";
                        SalesInvoiceHeader: Record "Sales Invoice Header";
                        PurchaseHeader: Record "Purchase Header";
                        PurchInvHeader: Record "Purch. Inv. Header";
                    begin
                        Rec.TestField("Invoice No.");
                        if (Rec.Type = Rec.Type::Revenue) or (Rec.Type = Rec.Type::Consumption) then begin
                            if SalesInvoiceHeader.Get(Rec."Invoice No.") then //Page.Run(Page::"Posted Sales Invoice", SalesInvoiceHeader)
                                PageManagement.PageRun(SalesInvoiceHeader)
                            else
                                if SalesHeader.Get(SalesHeader."Document Type"::Invoice, Rec."Invoice No.") then //Page.Run(Page::"Sales Invoice", SalesHeader)
                                    PageManagement.PageRun(SalesHeader)
                        end
                        else
                            if Rec.Type = Rec.Type::Expense then
                                if PurchInvHeader.Get(Rec."Invoice No.") then //Page.Run(Page::"Posted Purchase Invoice", PurchInvHeader)
                                    PageManagement.PageRun(PurchInvHeader)
                                else
                                    if PurchaseHeader.Get(PurchaseHeader."Document Type"::Invoice, Rec."Invoice No.") then //Page.Run(Page::"Purchase Invoice", PurchaseHeader);
                                        PageManagement.PageRun(PurchaseHeader);
                    end;
                }
                field("Is Cancelled"; Rec."Is Cancelled")
                {
                    StyleExpr = StyleTxt;
                }
                field("External Document No."; Rec."External Document No.")
                {
                    StyleExpr = StyleTxt;
                    Editable = not ((Rec.Type = Rec.Type::"Load Entry") or (Rec.Type = Rec.Type::"Load Exit") or (Rec.Type = Rec.Type::"Load Transfer"));
                }
                field("Source No."; Rec."Source No.")
                {
                    StyleExpr = StyleTxt;
                    Editable = (Rec.Type <> Rec.Type::"Load Transfer");
                }
                field("Source Name"; Rec."Source Name")
                {
                    StyleExpr = StyleTxt;
                }
                field("Transfer Customer Name"; Rec."Transfer Customer Name")
                {
                    StyleExpr = StyleTxt;
                }
                // field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                // {
                //     ToolTip = 'Specifies the value of the Bill-to Customer No. field.';
                // }
                // field("Bill-to Customer Name"; Rec."Bill-to Customer Name")
                // {
                //     ToolTip = 'Specifies the value of the Bill-to Customer Name field.';
                // }
                field(Processed; Rec.Processed)
                {
                    StyleExpr = StyleTxt;
                    Visible = false;
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                    StyleExpr = StyleTxt;
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                    StyleExpr = StyleTxt;
                }
                field("Load Item No."; Rec."Load Item No.")
                {
                    StyleExpr = StyleTxt;
                    Visible = false;
                }
                field("Ship No."; Rec."Ship No.")
                {
                    StyleExpr = StyleTxt;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateSalesInvoiceForSelectedLines)
            {
                ApplicationArea = All;
                Caption = 'Create Sales Invoice For Selected Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = CreateDocument;
                ToolTip = 'Executes the Create Sales Invoice For Selected Lines action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    PortOperationLineDetail: Record "Port Operation Line Detail ERK";
                begin
                    CurrPage.SetSelectionFilter(PortOperationLineDetail);
                    PortOperationManagement.CreateNewSalesInvoiceFromSelectedLines(PortOperationLineDetail);
                end;
            }
            action(CreatePurchaseInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Purchase Invoice For Selected Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NewDocument;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Purchase Invoice action.';

                trigger OnAction()
                var
                    PortOperationLineDetail: Record "Port Operation Line Detail ERK";
                begin
                    CurrPage.SetSelectionFilter(PortOperationLineDetail);
                    PortOperationManagement.CreatePurchaseInvoiceFromPortOperationLineDetails(PortOperationLineDetail);
                end;
            }
            action(LoadProcess)
            {
                ApplicationArea = All;
                Caption = 'Load Process';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = WorkCenterLoad;
                ToolTip = 'Executes the Load Process action.';

                trigger OnAction()
                var
                    InvalidTypeErr: Label 'Invalid Type for Load Process';
                begin
                    case Rec.Type of
                        Rec.Type::"Load Entry":
                            ;//Nothing to do here yet.
                        //PortOperationManagement.LoadEntryExit(Rec);
                        Rec.Type::"Load Exit":
                            ;
                        //PortOperationManagement.LoadEntryExit(Rec);
                        Rec.Type::"Load Transfer":
                            PortOperationManagement.PopulateLoadTransferPopUpFromPortOperationLineDetail(Rec);
                        else
                            Error(InvalidTypeErr);
                    end;
                end;
            }
            action(ConsumeSelectedLines)
            {
                ApplicationArea = All;
                Caption = 'Consume Selected Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ConsumptionJournal;
                ToolTip = 'Executes the Consume Selected Lines action.';

                trigger OnAction()
                var
                    PortOperationLineDetail: Record "Port Operation Line Detail ERK";
                begin
                    CurrPage.SetSelectionFilter(PortOperationLineDetail);
                    PortOperationManagement.CreateAndPostConsumptionInvoice(PortOperationLineDetail);
                end;
            }
            action(CreateNewRevenueLineFromSelectedLine)
            {
                ApplicationArea = All;
                Caption = 'Create New Revenue Line From Selected Line';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Line;
                ToolTip = 'Executes the Create New Revenue Line From Selected Line action.';

                trigger OnAction()
                begin
                    PortOperationManagement.CreateNewRevenueLineFromPortOperationLineDetail(Rec);
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        StyleTxt := Format(SetStyle());
    end;

    procedure SetStyle() Style: PageStyle
    begin
        case Rec.Type of
            "Voyage Line Detail Type ERK"::Expense:
                exit(PageStyle::Unfavorable);
            "Voyage Line Detail Type ERK"::Revenue:
                exit(PageStyle::Favorable);
            "Voyage Line Detail Type ERK"::Consumption:
                exit(PageStyle::Attention);
            "Voyage Line Detail Type ERK"::Scrap:
                exit(PageStyle::Ambiguous);
            "Voyage Line Detail Type ERK"::"Load Exit":
                exit(PageStyle::Strong);
            "Voyage Line Detail Type ERK"::"Load Entry":
                exit(PageStyle::StrongAccent);
        end;
    end;

    var
        PortOperationManagement: Codeunit "Port Operation Management ERK";
        PageManagement: Codeunit "Page Management";
        StyleTxt: Text;
}
