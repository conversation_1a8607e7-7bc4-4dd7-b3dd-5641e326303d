page 60023 "Voyage Line Detail List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Voyage Line Details';
    PageType = List;
    SourceTable = "Voyage Line Detail ERK";
    UsageCategory = Lists;
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field(Type; Rec."Type")
                {
                    ValuesAllowed = 0, 1, 2;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Unit Price"; Rec."Unit Price")
                {
                }
                field("Line Amount"; Rec."Line Amount")
                {
                }
                field("Line Amount (ACY)"; Rec."Line Amount (ACY)")
                {
                }
                // field("Line Amount (ACY)"; ExportManagement.ConvertAmountToACY(Rec."Posting Date", Rec."Currency Code", Rec."Line Amount"))
                // {
                //     Caption = 'Line Amount (ACY)';
                //     ToolTip = 'Specifies the value of the Line Amount (ACY) field.';
                // }
                field("Invoice No."; Rec."Invoice No.")
                {
                    trigger OnDrillDown()
                    var
                        SalesHeader: Record "Sales Header";
                        SalesInvoiceHeader: Record "Sales Invoice Header";
                        PurchaseHeader: Record "Purchase Header";
                        PurchInvHeader: Record "Purch. Inv. Header";
                    begin
                        Rec.TestField("Invoice No.");
                        if Rec.Type = Rec.Type::Revenue then begin
                            if SalesInvoiceHeader.Get(Rec."Invoice No.") then
                                PageManagement.PageRun(SalesInvoiceHeader)
                            else
                                if SalesHeader.Get(SalesHeader."Document Type"::Invoice, Rec."Invoice No.") then
                                    PageManagement.PageRun(SalesHeader)
                        end
                        else
                            if Rec.Type = Rec.Type::Expense then
                                if PurchInvHeader.Get(Rec."Invoice No.") then
                                    PageManagement.PageRun(PurchInvHeader)
                                else
                                    if PurchaseHeader.Get(PurchaseHeader."Document Type"::Invoice, Rec."Invoice No.") then
                                        PageManagement.PageRun(PurchaseHeader);
                    end;
                }
                field("Is Cancelled"; Rec."Is Cancelled")
                {
                }
                field(Posted; Rec.Posted)
                {
                }
                field("Invoice Line No."; Rec."Invoice Line No.")
                {
                }
                field("Vendor No."; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                field("External Document No."; Rec."External Document No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateSalesInvoiceForSelectedLines)
            {
                ApplicationArea = All;
                Caption = 'Create Sales Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = CreateDocument;
                ToolTip = 'Executes the Create Sales Invoice For Selected Lines action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    VoyageLineDetail: Record "Voyage Line Detail ERK";
                begin
                    CurrPage.SetSelectionFilter(VoyageLineDetail);
                    VoyageMangement.CreateNewSalesInvoiceFromSelectedLines(VoyageLineDetail);
                end;
            }
            action(CreatePurchaseInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Purchase Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NewDocument;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Purchase Invoice action.';

                trigger OnAction()
                var
                    VoyageLineDetail: Record "Voyage Line Detail ERK";
                begin
                    CurrPage.SetSelectionFilter(VoyageLineDetail);
                    VoyageMangement.CreatePurchaseInvoiceFromVoyageLineDetail(VoyageLineDetail);
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        Rec.Validate("Line Amount (ACY)", ExportManagement.ConvertAmountToACY(Rec."Posting Date", Rec."Currency Code", Rec."Line Amount"));
        Rec.Modify(false);
    end;

    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        ExportManagement: Codeunit "Export Management ERK";
        PageManagement: Codeunit "Page Management";
}
