report 60017 "Vendor Turnover Report ERK"
{
    ApplicationArea = All;
    Caption = 'Vendor Turnover Report';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(PurchInvHeader; "Purch. Inv. Header")
        {
            column(BuyfromVendorNo; "Buy-from Vendor No.")
            {
            }
            column(BuyfromVendorName; "Buy-from Vendor Name")
            {
            }
            column(BuyfromVendorName2; "Buy-from Vendor Name 2")
            {
            }
            column(VendorInvoiceNo; "Vendor Invoice No.")
            {
            }
            column(PostingDate; "Posting Date")
            {
            }
            column(Amount; Amount)
            {
            }
            column(CurrencyCode; "Currency Code")
            {
            }
            column(PaymentTermsCode; "Payment Terms Code")
            {
            }
            dataitem("Purch. Inv. Line"; "Purch. Inv. Line")
            {
                DataItemLink = "Document No." = field("No.");
                column(LocationCode_PurchInvLine; "Location Code")
                {
                }
                column(Description_PurchInvLine; Description)
                {
                }
                column(Description2_PurchInvLine; "Description 2")
                {
                }
                column(ShortcutDimension1Code_PurchInvLine; "Shortcut Dimension 1 Code")
                {
                }
                column(LineAmount_PurchInvLine; "Line Amount")
                {
                }
                column(DocumentNo_PurchInvLine; "Document No.")
                {
                }
            }
        }
        dataitem("Purch. Cr. Memo Hdr."; "Purch. Cr. Memo Hdr.")
        {
            column(CrBuyfromVendorNo; "Buy-from Vendor No.")
            {
            }
            column(CrBuyfromVendorName; "Buy-from Vendor Name")
            {
            }
            column(CrBuyfromVendorName2; "Buy-from Vendor Name 2")
            {
            }
            column(CrVendorInvoiceNo; "Vendor Cr. Memo No.")
            {
            }
            column(CrPostingDate; "Posting Date")
            {
            }
            column(CrAmount; Amount)
            {
            }
            column(CrCurrencyCode; "Currency Code")
            {
            }
            column(CrPaymentTermsCode; "Payment Terms Code")
            {
            }
            dataitem("Purch. Cr. Memo Line"; "Purch. Cr. Memo Line")
            {
                DataItemLink = "Document No." = field("No.");
                column(CrLocationCode_PurchInvLine; "Location Code")
                {
                }
                column(CrDescription_PurchInvLine; Description)
                {
                }
                column(CrDescription2_PurchInvLine; "Description 2")
                {
                }
                column(CrShortcutDimension1Code_PurchInvLine; "Shortcut Dimension 1 Code")
                {
                }
                column(DocumentNo_PurchCrMemoLine; "Document No.")
                {
                }
                column(LineAmount_PurchCrMemoLine; "Line Amount")
                {
                }
            }
        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(Processing)
    //         {
    //         }
    //     }
    // }
}