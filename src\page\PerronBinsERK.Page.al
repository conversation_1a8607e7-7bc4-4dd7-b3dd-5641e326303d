page 60141 "Perron Bins ERK"
{
    ApplicationArea = All;
    Caption = 'Perron Bins';
    PageType = List;
    SourceTable = Bin;
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("Bin Groupage Info"; BinGroupageInfo)
                {
                    Caption = 'Bin Groupage Information';
                    ToolTip = 'Specifies all groupage numbers for vehicles in this bin.';
                    Editable = false;
                }
                field("Location Code"; Rec."Location Code")
                {
                    ToolTip = 'Specifies the location code.';
                }
                field(Code; Rec.Code)
                {
                    Caption = 'Bin Code';
                    ToolTip = 'Specifies the bin code.';
                }
                field("Vehicle Quantity"; Rec."Vehicle Quantity ERK")
                {
                    ToolTip = 'Specifies the vehicle quantity for this bin.';
                    trigger OnDrillDown()
                    var
                        SerialNoInformation: Record "Serial No. Information";
                    begin
                        SerialNoInformation.SetRange("Current Location Code ERK", Rec."Location Code");
                        SerialNoInformation.SetRange("Current Bin Code ERK", Rec.Code);
                        Page.Run(Page::"Serial No. Information Perron", SerialNoInformation);
                    end;
                }

            }
        }
    }

    var
        BinGroupageInfo: Text;

    trigger OnAfterGetRecord()
    var
        SerialNoInfo: Record "Serial No. Information";
        GroupageList: Text;
        TempGroupageList: List of [Text];
        CurrentGroupage: Text;
    begin
        GroupageList := '';
        Clear(TempGroupageList);

        // Get all serial numbers in this bin
        SerialNoInfo.SetRange("Current Location Code ERK", Rec."Location Code");
        SerialNoInfo.SetRange("Current Bin Code ERK", Rec.Code);
        if SerialNoInfo.FindSet() then
            repeat
                CurrentGroupage := SerialNoInfo."Grupage No. ERK";
                if CurrentGroupage <> '' then
                    // Add only if not already in the list
                    if not TempGroupageList.Contains(CurrentGroupage) then
                        TempGroupageList.Add(CurrentGroupage);
            until SerialNoInfo.Next() = 0;

        // Build the display string
        foreach CurrentGroupage in TempGroupageList do begin
            if GroupageList <> '' then
                GroupageList += ', ';
            GroupageList += CurrentGroupage;
        end;

        BinGroupageInfo := GroupageList;
    end;
}