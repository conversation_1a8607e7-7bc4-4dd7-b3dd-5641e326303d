page 60012 "Export Ledger Entries ERK"
{
    ApplicationArea = ExportManagementERK;
    Caption = 'Export Ledger Entries';
    PageType = List;
    SourceTable = "Export Ledger Entry ERK";
    UsageCategory = Lists;

    //Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Type"; Rec."Document Type")
                {
                }
                field("External Document No."; Rec."External Document No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field(Amount; Rec.Amount)
                {
                }
                field("Amount (LCY)"; Rec."Amount (LCY)")
                {
                }
                field("Amount (ACY)"; Rec."Amount (ACY)")
                {
                }
                field("Remaining Amout"; Rec."Remaining Amout")
                {
                }
                field("Remaining Amout (LCY)"; Rec."Remaining Amout (LCY)")
                {
                }
                field("Remaining Amout (ACY)"; Rec."Remaining Amout (ACY)")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Currency Exchange Rate"; Rec."Currency Exchange Rate")
                {
                }
                field("Source Cust. Ledger Entry No."; Rec."Source Cust. Ledger Entry No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field("Assignable Amount"; Rec."Assignable Amount")
                {
                }
                field("Assigned Amount"; Rec."Assigned Amount")
                {
                }
                field("Blanket Sales Order No."; Rec."Blanket Sales Order No.")
                {
                }
                field("Source Export Ledg. Entry No."; Rec."Source Export Ledg. Entry No.")
                {
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }
}
