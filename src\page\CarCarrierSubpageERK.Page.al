page 60026 "Car Carrier Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Car Carrier Subpage';
    PageType = ListPart;
    SourceTable = "Car Carrier Line ERK";
    AutoSplitKey = true;
    UsageCategory = Lists;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Line No."; Rec."Line No.")
                {
                    Editable = false;
                }
                field("Departure Port"; Rec."Departure Port")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Departure Port Description"; Rec."Departure Port Description")
                {
                }
                field("Departure Date"; Rec."Departure Date-Time")
                {
                    ToolTip = 'Specifies the value of the Departure Date field.';
                }
                field("Arrival Port"; Rec."Arrival Port")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Arrival Port Description"; Rec."Arrival Port Description")
                {
                }
                field("Arrival Date"; Rec."Arrival Date-Time")
                {
                    ToolTip = 'Specifies the value of the Arrival Date field.';
                }
                field("Loaded Quantity"; Rec."Loaded Quantity")
                {
                }
                field("Discharged Quantity"; Rec."Discharged Quantity")
                {
                }
                // field("Fuel Consumption Qty. (IFO)"; Rec."Fuel Ship Cons. Qty. (IFO)")
                // {
                //     ToolTip = 'Specifies the value of the Fuel Consumption Qty. (IFO) field.';
                // }
                // field("Fuel Consumption Qty. (MGO)"; Rec."Fuel Ship Cons. Qty. (MGO)")
                // {
                //     ToolTip = 'Specifies the value of the Fuel Consumption Qty. (MGO) field.';
                // }
                // field("Departure Port Fuel Qty. (IFO)"; Rec."Departure Port Fuel Qty. (IFO)")
                // {
                //     ToolTip = 'Specifies the value of the Departure Port Fuel Qty. (IFO) field.';
                // }
                // field("Departure Port Fuel Qty. (MGO)"; Rec."Departure Port Fuel Qty. (MGO)")
                // {
                //     ToolTip = 'Specifies the value of the Departure Port Fuel Qty. (MGO) field.';
                // }
                field("COSP ROB (IFO)"; Rec."COSP ROB (IFO)")
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("COSP ROB (MGO)"; Rec."COSP ROB (MGO)")
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("COSP ROB (HSFO)"; Rec."COSP ROB (HSFO)")
                {
                }
                field("COSP ROB (LNG)"; Rec."COSP ROB (LNG)")
                {
                    ToolTip = 'Specifies the value of the COSP ROB (LNG) field.';
                }
                field("EOSP ROB (IFO)"; Rec."EOSP ROB (IFO)")
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("EOSP ROB (MGO)"; Rec."EOSP ROB (MGO)")
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("EOSP ROB (HSFO)"; Rec."EOSP ROB (HSFO)")
                {
                }
                field("EOSP ROB (LNG)"; Rec."EOSP ROB (LNG)")
                {
                    ToolTip = 'Specifies the value of the EOSP ROB (LNG) field.';
                }
                field("All Fast ROB (IFO)"; Rec."All Fast ROB (IFO)")
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("All Fast ROB (MGO)"; Rec."All Fast ROB (MGO)")
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("All Fast ROB (HSFO)"; Rec."All Fast ROB (HSFO)")
                {
                }
                field("All Fast ROB (LNG)"; Rec."All Fast ROB (LNG)")
                {
                    ToolTip = 'Specifies the value of the All Fast ROB (LNG) field.';
                }
                field("Fueling (IFO)"; Rec."Fueling (IFO)")
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("Fueling (MGO)"; Rec."Fueling (MGO)")
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("Fueling (HSFO)"; Rec."Fueling (HSFO)")
                {
                }
                field("Fueling (LNG)"; Rec."Fueling (LNG)")
                {
                    ToolTip = 'Specifies the value of the Fueling (LNG) field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Load)
            {
                Caption = 'Load Vehicles';
                Image = SalesShipment;
                RunObject = page "Car Carrier Line Details ERK";
                RunPageLink = "Document No." = field("Document No."), "Document Line No." = field("Line No.");
                ToolTip = 'Executes the Car Carrier Line Details action.';
            }
            action(Discharge)
            {
                Caption = 'Discharge Vehicles';
                Image = ExportShipment;
                RunObject = page "Car Carrier Line Details ERK";
                RunPageLink = "Document No." = field("Document No."), "Discharge Port" = field("Arrival Port");
                ToolTip = 'Executes the Discharge Vehicles action.';
            }
        }
    }
}
