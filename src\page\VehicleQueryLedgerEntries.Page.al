page 60067 "Vehicle Query Ledger Entries"
{
    ApplicationArea = All;
    Caption = 'Vehicle Query Ledger Entries';
    PageType = List;
    SourceTable = "Vehicle Query Ledger Entry ERK";
    UsageCategory = History;
    ModifyAllowed = false;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Customs Declaration No."; Rec."Customs Declaration No.")
                {
                }
                field("Customs Declaration Line No."; Rec."Customs Declaration Line No.")
                {
                }
                field("Summary Declaration No."; Rec."Summary Declaration No.")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }
}
