page 60078 "Report Sending Setup ERK"
{
    ApplicationArea = All;
    Caption = 'Report Sending Setup';
    PageType = List;
    SourceTable = "Report Sending Setup ERK";
    UsageCategory = Administration;
    DelayedInsert = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Report ID"; Rec."Report ID")
                {
                }
                field("Report Name"; Rec."Report Name")
                {
                }
                field("E-Mail Address"; Rec."E-Mail Address")
                {
                }
                field("Company Name"; Rec."Company Name")
                {
                }
                field(Parameters; Rec.Parameters)
                {
                }

                field("Report Export Type"; Rec."Report Export Type")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(SetReportParameters)
            {
                Caption = 'Set Report Parameters';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Set Report Parameters action.';
                Image = TestReport;
                PromotedOnly = true;

                trigger OnAction()
                begin
                    Rec.Parameters := CopyStr(Report.RunRequestPage(Rec."Report ID", Rec.Parameters), 1, MaxStrLen(Rec.Parameters));
                end;
            }
            action(TestEmailForSelectedLines)
            {
                Caption = 'Test E-Mail for Report No. 120';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Test E-Mail for Selected Lines action.';
                Image = SendEmailPDF;
                PromotedOnly = true;
                Visible = false;

                trigger OnAction()
                begin
                    EMailManagement.SendEMail_120_AgedAccountReceivables(120);
                end;
            }
        }
    }
    var
        EMailManagement: Codeunit "E-Mail Management ERK";
}
