page 60088 "Car Carrier Order Lines ERK"
{
    ApplicationArea = All;
    Caption = 'Car Carrier Order Lines';
    PageType = List;
    SourceTable = "Car Carrier Order Line ERK";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                    trigger OnDrillDown()
                    var
                        CarCarrierOrderHeader: Record "Car Carrier Order Header ERK";
                    begin
                        if CarCarrierOrderHeader.Get(Rec."Document No.") then
                            PageManagement.PageRun(CarCarrierOrderHeader);
                    end;
                }
                field("Your Reference"; Rec."Your Reference")
                {
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Order Status"; Rec."Order Status")
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("Loading Port Code"; Rec."Loading Port Code")
                {
                }
                field("Loading Post Description"; Rec."Loading Post Description")
                {
                }
                field("Discharge Port Code"; Rec."Discharge Port Code")
                {
                }
                field("Discharge Post Description"; Rec."Discharge Post Description")
                {
                }
                field("Transshipment Allowed"; Rec."Transshipment Allowed")
                {
                }
                field("Transshipment Port"; Rec."Transshipment Port")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Load Type"; Rec."Load Type")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Pre-Load Quantity"; Rec."Pre-Load Quantity")
                {
                }
                field("Planned Car Carrier No."; Rec."Planned Car Carrier No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Brand Code"; Rec."Brand Code")
                {
                }
                field("Model Code"; Rec."Model Code")
                {
                }
                field("Height (cm)"; Rec."Height (cm)")
                {
                }
                field("Width (cm)"; Rec."Width (cm)")
                {
                }
                field("Length (cm)"; Rec."Length (cm)")
                {
                }
                field("Unit Gross Weight (kg)"; Rec."Unit Gross Weight (kg)")
                {
                }
                field("Unit Volume (m3)"; Rec."Unit Volume (m3)")
                {
                }
                field("Unit Area (m2)"; Rec."Unit Area (m2)")
                {
                }
                field("Line Area (m2)"; Rec."Line Area (m2)")
                {
                }
                field("Line Volume (m3)"; Rec."Line Volume (m3)")
                {
                }
                field("Line Gross Weight (kg)"; Rec."Line Gross Weight (kg)")
                {
                }
            }
        }
    }
    var
        PageManagement: Codeunit "Page Management";
}
