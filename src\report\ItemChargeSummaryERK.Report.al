report 60004 "Item Charge Summary ERK"
{
    ApplicationArea = All;
    Caption = 'Item Charge Summary';
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem(ValueEntry; "Value Entry")
        {
            DataItemTableView = where("Item Charge No." = filter(<> ''));

            column(DocumentNo; "Document No.")
            {
            }
            column(ItemChargeNo; "Item Charge No.")
            {
            }
            column(GlobalDimension1Code; "Global Dimension 1 Code")
            {
            }
            column(PostingDate; "Posting Date")
            {
            }
            column(SourceNo; "Source No.")
            {
            }
            column(SourceName; ErkHoldingBasicFunctions.GetVendorNameFromVendorNo("Source No."))
            {
            }
            column(CostAmountNonInvtblACY; "Cost Amount (Non-Invtbl.)(ACY)")
            {
            }
            column(CostAmountNonInvtbl; "Cost Amount (Non-Invtbl.)")
            {
            }
            column(ExternalDocumentNo; "External Document No.")
            {
            }
            column(Applied_Currency_Code_INF; "Applied Currency Code INF")
            {
            }
            column(Applied_Currency_Factor_INF; "Applied Currency Factor INF")
            {
            }
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
