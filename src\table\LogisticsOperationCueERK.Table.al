table 60076 "Logistics Operation Cue ERK"
{
    Caption = 'Logistics Operation Cue';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "Port Ops Due Today"; Integer)
        {
            Caption = 'Port Operations Due Today';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Port Operation Line Detail ERK" where(
                "Posting Date" = field("Date Filter Today"),
                "External Document No." = filter(''),
                "Invoice No." = filter(''),
                "Is Cancelled" = const(false)));
            ToolTip = 'Specifies port operations with invoice due today that have not been invoiced yet.';
        }
        field(3; "Port Ops Due This Week"; Integer)
        {
            Caption = 'Port Operations Due This Week';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Port Operation Line Detail ERK" where(
                "Next Invoice Date" = field("Date Filter Week"),
                "External Document No." = filter(''),
                "Invoice No." = filter(''),
                "Is Cancelled" = const(false)));
            ToolTip = 'Specifies port operations with invoice due this week that have not been invoiced yet.';
        }
        field(10; "Date Filter Today"; Date)
        {
            Caption = 'Date Filter Today';
            Editable = false;
            FieldClass = FlowFilter;
        }
        field(11; "Date Filter Week"; Date)
        {
            Caption = 'Date Filter Week';
            Editable = false;
            FieldClass = FlowFilter;
        }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }
}