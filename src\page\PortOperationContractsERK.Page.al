page 60125 "Port Operation Contracts ERK"
{
    ApplicationArea = All;
    Caption = 'Port Operation Contracts';
    PageType = List;
    SourceTable = "Port Operation Contract Header";
    UsageCategory = Lists;
    CardPageId = "Port Operation Contract ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Starting Date"; Rec."Starting Date")
                {
                }
                field("Ending Date"; Rec."Ending Date")
                {
                }
                field(Active; Rec.Active)
                {
                }
            }
        }
    }
}