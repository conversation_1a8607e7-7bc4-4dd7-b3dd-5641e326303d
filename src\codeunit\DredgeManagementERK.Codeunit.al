codeunit 60100 "Dredge Management ERK"
{
    // Add business logic specific to Dredge operations
    // Similar to CarCarrierManagement codeunit

    procedure CreatePurchaseInvoice(var DredgeLine: Record "Dredge Line ERK")
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        DredgeHeader: Record "Dredge Header ERK";
        xDredgeLine: Record "Dredge Line ERK";
        Item: Record Item;
        SuccesMsg: Label 'Purchase Invoice Created: %1', Comment = '%1="Purchase Header"."No."';
        TypeErr: Label 'Type must be Expense';
        PurchaseLineNo: Integer;
    begin
        DredgeLine.FindSet(true);
        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Invoice;
        PurchaseHeader.Insert(true);
        PurchaseHeader.Validate("Buy-from Vendor No.", DredgeLine."Source No.");
        PurchaseHeader.Validate("Vendor Invoice No.", DredgeLine."External Document No");
        PurchaseHeader.Validate("Posting Date", DredgeLine."Posting Date");
        PurchaseHeader.Validate("Currency Code", DredgeLine."Currency Code");
        PurchaseHeader.Modify(true);
        xDredgeLine := DredgeLine;

        repeat
            DredgeLine.TestField("External Document No");
            DredgeLine.TestField("Unposted Invoice No.", '');
            DredgeLine.TestField("Posted Invoice No.", '');

            if not (DredgeLine.Type in [DredgeLine.Type::Expense, DredgeLine.Type::"Expected Expense"]) then
                Error(TypeErr);

            Item.Get(DredgeLine."No.");
            if Item.IsVariantMandatory() then
                DredgeLine.TestField("Variant Code");

            DredgeHeader.Get(DredgeLine."Document No.");
            VoyageMangement.CreateDimensionValueCode(DredgeHeader."No.");

            DredgeLine.TestField("Currency Code", xDredgeLine."Currency Code");
            DredgeLine.TestField("Source No.", xDredgeLine."Source No.");
            DredgeLine.TestField("External Document No", xDredgeLine."External Document No");
            DredgeLine.TestField("Posting Date", xDredgeLine."Posting Date");

            PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
            PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
            if PurchaseLine.FindLast() then
                PurchaseLineNo := PurchaseLine."Line No." + 10000
            else
                PurchaseLineNo := 10000;

            Clear(PurchaseLine);
            PurchaseLine.Init();
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Line No." := PurchaseLineNo;
            PurchaseLine.Insert(true);
            PurchaseLine.Validate("Type", PurchaseLine."Type"::Item);
            PurchaseLine.Validate("No.", DredgeLine."No.");
            PurchaseLine.Validate("Variant Code", DredgeLine."Variant Code");
            PurchaseLine.Validate(Quantity, DredgeLine.Quantity);
            PurchaseLine.Validate("Unit of Measure Code", DredgeLine."Unit of Measure");
            PurchaseLine.Validate("Direct Unit Cost", DredgeLine."Unit Price/Cost");
            PurchaseLine.Validate("Shortcut Dimension 1 Code", DredgeHeader."Department Code");
            PurchaseLine.Validate("Shortcut Dimension 2 Code", DredgeHeader."No.");
            PurchaseLine.Modify(true);

            DredgeLine."Unposted Invoice No." := PurchaseHeader."No.";
            DredgeLine.Modify(false);
            xDredgeLine := DredgeLine;
        until DredgeLine.Next() = 0;

        Message(SuccesMsg, PurchaseHeader."No.");
    end;

    procedure CreateSalesInvoice(var DredgeLine: Record "Dredge Line ERK")
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        DredgeHeader: Record "Dredge Header ERK";
        xDredgeLine: Record "Dredge Line ERK";
        Item: Record Item;
        SuccesMsg: Label 'Sales Invoice Created: %1', Comment = '%1="Sales Header"."No."';
        TypeErr: Label 'Type must be Revenue or Expected Revenue';
        SalesLineNo: Integer;
    begin
        DredgeLine.FindSet(true);
        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", DredgeLine."Source No.");
        SalesHeader.Validate("External Document No.", DredgeLine."External Document No");
        SalesHeader.Validate("Posting Date", DredgeLine."Posting Date");
        SalesHeader.Validate("Currency Code", DredgeLine."Currency Code");
        SalesHeader.Modify(true);
        xDredgeLine := DredgeLine;

        repeat
            DredgeLine.TestField("Unposted Invoice No.", '');
            DredgeLine.TestField("Posted Invoice No.", '');

            if not (DredgeLine.Type in [DredgeLine.Type::Revenue, DredgeLine.Type::"Expected Revenue"]) then
                Error(TypeErr);

            Item.Get(DredgeLine."No.");
            if Item.IsVariantMandatory() then
                DredgeLine.TestField("Variant Code");

            DredgeHeader.Get(DredgeLine."Document No.");
            VoyageMangement.CreateDimensionValueCode(DredgeHeader."No.");

            DredgeLine.TestField("Currency Code", xDredgeLine."Currency Code");
            DredgeLine.TestField("Source No.", xDredgeLine."Source No.");
            DredgeLine.TestField("Posting Date", xDredgeLine."Posting Date");

            SalesLine.SetRange("Document Type", SalesHeader."Document Type");
            SalesLine.SetRange("Document No.", SalesHeader."No.");
            if SalesLine.FindLast() then
                SalesLineNo := SalesLine."Line No." + 10000
            else
                SalesLineNo := 10000;

            Clear(SalesLine);
            SalesLine.Init();
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Line No." := SalesLineNo;
            SalesLine.Insert(true);
            SalesLine.Validate("Type", SalesLine."Type"::Item);
            SalesLine.Validate("No.", DredgeLine."No.");
            SalesLine.Validate("Variant Code", DredgeLine."Variant Code");
            SalesLine.Validate(Quantity, DredgeLine.Quantity);
            SalesLine.Validate("Unit of Measure Code", DredgeLine."Unit of Measure");
            SalesLine.Validate("Shortcut Dimension 1 Code", DredgeHeader."Department Code");
            SalesLine.Validate("Shortcut Dimension 2 Code", DredgeHeader."No.");
            SalesLine.Validate("Unit Price", DredgeLine."Unit Price/Cost");
            SalesLine.Modify(true);

            DredgeLine."Unposted Invoice No." := SalesHeader."No.";
            DredgeLine.Modify(false);
            xDredgeLine := DredgeLine;
        until DredgeLine.Next() = 0;

        Message(SuccesMsg, SalesHeader."No.");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure OnDeleteOnBeforeTestStatusOpen(var SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    begin
        ClearUnpostedInvoiceNoFieldOnDredgeLines(SalesLine);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure OnDeleteOnBeforeTestStatusOpen_PurchaseLine(var PurchaseLine: Record "Purchase Line"; var IsHandled: Boolean)
    begin
        ClearUnpostedInvoiceNoFieldOnDredgeLinesForPurchase(PurchaseLine);
    end;

    local procedure ClearUnpostedInvoiceNoFieldOnDredgeLines(var SalesLine: Record "Sales Line")
    var
        DredgeLine: Record "Dredge Line ERK";
    begin
        if SalesLine."Shortcut Dimension 2 Code" = '' then
            exit;
        DredgeLine.SetRange("Unposted Invoice No.", SalesLine."Document No.");
        if DredgeLine.IsEmpty() then
            exit;

        Message('%1 Dredge Lines Unposted Invoice Nos has been cleared.', DredgeLine.Count());
        DredgeLine.ModifyAll("Unposted Invoice No.", '', false);
    end;

    local procedure ClearUnpostedInvoiceNoFieldOnDredgeLinesForPurchase(var PurchaseLine: Record "Purchase Line")
    var
        DredgeLine: Record "Dredge Line ERK";
    begin
        if PurchaseLine."Shortcut Dimension 2 Code" = '' then
            exit;
        DredgeLine.SetRange("Unposted Invoice No.", PurchaseLine."Document No.");
        if DredgeLine.IsEmpty() then
            exit;
        DredgeLine.ModifyAll("Unposted Invoice No.", '', false);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting(var SalesHeader: Record "Sales Header"; var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; var SkipDelete: Boolean; CommitIsSuppressed: Boolean; EverythingInvoiced: Boolean; var TempSalesLineGlobal: Record "Sales Line" temporary)
    var
        SalesLine: Record "Sales Line";
        DredgeLine: Record "Dredge Line ERK";
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if SalesLine.IsEmpty() then
            exit;
        DredgeLine.SetRange("Unposted Invoice No.", SalesHeader."No.");
        DredgeLine.ModifyAll("Posted Invoice No.", SalesInvoiceHeader."No.", false);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting_PurchasePost(var PurchaseHeader: Record "Purchase Header"; var PurchInvHeader: Record "Purch. Inv. Header"; var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var SkipDelete: Boolean; CommitIsSupressed: Boolean; var TempPurchLine: Record "Purchase Line" temporary; var TempPurchLineGlobal: Record "Purchase Line" temporary; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    var
        PurchaseLine: Record "Purchase Line";
        DredgeLine: Record "Dredge Line ERK";
    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetRange(Type, PurchaseLine.Type::Item);
        if PurchaseLine.IsEmpty() then
            exit;
        DredgeLine.SetRange("Unposted Invoice No.", PurchaseHeader."No.");
        DredgeLine.ModifyAll("Posted Invoice No.", PurchInvHeader."No.", false);
    end;

    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
}