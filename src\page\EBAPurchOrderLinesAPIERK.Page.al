//gerekirse açılacak //Yıldız Özdemir

// page 60145 "EBA Purch Order Lines API ERK"
// {
//     DelayedInsert = true;
//     APIVersion = 'v1.0';
//     EntityCaption = 'Purchase Order Line';
//     EntitySetCaption = 'Purchase Order Lines';
//     PageType = API;
//     ODataKeyFields = SystemId;
//     EntityName = 'EBApurchaseOrderLineERK';
//     EntitySetName = 'EBApurchaseOrderLinesERK';
//     SourceTable = "Purchase Line";
//     SourceTableTemporary = true;
//     Extensible = false;
//     APIPublisher = 'infotek';
//     APIGroup = 'eh';

//     layout
//     {
//         area(Content)
//         {
//             repeater(Group)
//             {
//                 field(id; Rec.SystemId)
//                 {
//                     Caption = 'Id';
//                     Editable = false;
//                 }
//                 field(documentNo; Rec."Document No.")
//                 {
//                     Caption = 'Document No.';
//                 }
//                 field(sequence; Rec."Line No.")
//                 {
//                     Caption = 'Sequence';


//                 }
//                 field(lineType; Rec."Type")
//                 {
//                     Caption = 'Type';
//                 }


//                 field(lineObjectNumber; Rec."No.")
//                 {
//                     Caption = 'Line Object No.';


//                 }
//                 field(description; Rec.Description)
//                 {
//                     Caption = 'Description';


//                 }
//                 field(description2; Rec."Description 2")
//                 {
//                     Caption = 'Description 2';

//                 }

//                 field(unitOfMeasureCode; Rec."Unit of Measure Code")
//                 {
//                     Caption = 'Unit Of Measure Code';


//                 }
//                 field(quantity; Rec.Quantity)
//                 {
//                     Caption = 'Quantity';


//                 }
//                 field(directUnitCost; Rec."Direct Unit Cost")
//                 {
//                     Caption = 'Direct Unit Cost';


//                 }
//                 field(discountAmount; Rec."Line Discount Amount")
//                 {
//                     Caption = 'Discount Amount';


//                 }
//                 field(discountPercent; Rec."Line Discount %")
//                 {
//                     Caption = 'Discount Percent';


//                 }
//                 field(expectedReceiptDate; Rec."Expected Receipt Date")
//                 {
//                     Caption = 'Expected Receipt Date';
//                 }
//                 field(receivedQuantity; Rec."Quantity Received")
//                 {
//                     Caption = 'Received Quantity';

//                 }
//                 field(invoicedQuantity; Rec."Quantity Invoiced")
//                 {
//                     Caption = 'Invoiced Quantity';


//                 }
//                 field(invoiceQuantity; Rec."Qty. to Invoice")
//                 {
//                     Caption = 'Invoice Quantity';


//                 }
//                 field(receiveQuantity; Rec."Qty. to Receive")
//                 {
//                     Caption = 'Ship Quantity';


//                 }

//             }
//         }
//     }

//     actions
//     {
//     }

//     trigger OnNewRecord(BelowxRec: Boolean)
//     begin
//         Rec."Document Type" := Rec."Document Type"::Order;

//     end;


// }