table 60059 "Car Carrier Order Vehicle ERK"
{
    Caption = 'Car Carrier Order Vehicle';
    DataClassification = CustomerContent;
    LookupPageId = "Car Carrier Order Vehicles ERK";
    DrillDownPageId = "Car Carrier Order Vehicles ERK";

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            ToolTip = 'Specifies the value of the Entry No. field.';
        }
        field(2; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(3; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the value of the Document Line No. field.';
        }
        field(4; "Document Line Detail No."; Integer)
        {
            Caption = 'Document Line Detail No.';
            ToolTip = 'Specifies the value of the Document Line Detail No. field.';
        }
        field(5; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(6; "Pre-Load"; Boolean)
        {
            Caption = 'Pre-Load';
            ToolTip = 'Specifies the value of the Pre-Load field.';
        }
        field(7; "Final Load"; Boolean)
        {
            Caption = 'Final Load';
            ToolTip = 'Specifies the value of the Final Load field.';
        }
        field(8; "Car Carrier No."; Code[20])
        {
            Caption = 'Car Carrier No.';
            ToolTip = 'Specifies the value of the Car Carrier No. field.';
        }
        field(9; "Car Carrier Line No."; Integer)
        {
            Caption = 'Car Carrier Line No.';
            ToolTip = 'Specifies the value of the Car Carrier Line No. field.';
        }
        field(10; "Car Carrier Line Detail No."; Integer)
        {
            Caption = 'Car Carrier Line Detail No.';
            ToolTip = 'Specifies the value of the Car Carrier Line Detail No. field.';
        }
        field(11; "Load Date-Time"; DateTime)
        {
            Caption = 'Load Date-Time';
            ToolTip = 'Specifies the value of the Load Date-Time field.';
        }
        field(12; "Discharge Date-Time"; DateTime)
        {
            Caption = 'Discharge Date-Time';
            ToolTip = 'Specifies the value of the Discharge Date-Time field.';
        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
        key(SK; "Serial No.")
        {
            Unique = true;
        }
    }
    trigger OnInsert()
    var
        CarCarrierOrderVehicle: Record "Car Carrier Order Vehicle ERK";
    begin
        if Rec.IsTemporary() then
            exit;
        if CarCarrierOrderVehicle.FindLast() then
            Rec."Entry No." := CarCarrierOrderVehicle."Entry No." + 1
        else
            Rec."Entry No." := 1;
    end;
}
