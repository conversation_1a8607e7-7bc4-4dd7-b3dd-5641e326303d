table 60101 "Dredge Line ERK"
{
    Caption = 'Dredge Line';
    DataClassification = CustomerContent;
    DrillDownPageId = "Dredge Lines ERK";
    LookupPageId = "Dredge Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            TableRelation = "Dredge Header ERK"."No.";
            ToolTip = 'Specifies the document number this line belongs to.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the line number within the document.';
        }
        field(3; Type; Enum "Dredge Line Type ERK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the type of the dredge line.';
            trigger OnValidate()
            begin
                // Clear fields that don't apply to the new type
                if Type <> xRec.Type then begin
                    "Source No." := '';
                    "Source Name" := '';
                    "No." := '';
                    Description := '';
                    "Variant Code" := '';
                    "Unit of Measure" := '';
                    Quantity := 0;
                    "Unit Price/Cost" := 0;
                    Amount := 0;
                    "Amount ACY" := 0;
                end;
            end;
        }
        field(4; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = if (Type = filter(Expense | "Expected Expense")) Vendor
            else if (Type = filter(Revenue | "Expected Revenue")) Customer;
            ToolTip = 'Specifies the source (customer or vendor) for this line.';
            trigger OnValidate()
            var
                Vendor: Record Vendor;
                Customer: Record Customer;
            begin
                if "Source No." <> xRec."Source No." then
                    case Type of
                        Type::Expense, Type::"Expected Expense":
                            if Vendor.Get("Source No.") then begin
                                "Source Name" := Vendor.Name;
                                "Currency Code" := Vendor."Currency Code";
                            end else
                                "Source Name" := '';
                        Type::Revenue, Type::"Expected Revenue":
                            if Customer.Get("Source No.") then begin
                                "Source Name" := Customer.Name;
                                "Currency Code" := Customer."Currency Code";
                            end else
                                "Source Name" := '';
                    end;
            end;
        }
        field(5; "Source Name"; Text[100])
        {
            Caption = 'Source Name';
            Editable = false;
            ToolTip = 'Specifies the name of the source.';
        }
        field(6; "No."; Code[20])
        {
            Caption = 'No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the item number.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if "No." <> xRec."No." then begin
                    if Item.Get("No.") then begin
                        Description := Item.Description;
                        "Unit of Measure" := Item."Base Unit of Measure";
                    end else begin
                        Description := '';
                        "Unit of Measure" := '';
                    end;
                    "Variant Code" := '';
                end;
            end;
        }
        field(7; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("No."));
            ToolTip = 'Specifies the variant of the item.';
            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                if "Variant Code" <> xRec."Variant Code" then
                    if ItemVariant.Get("No.", "Variant Code") then
                        Description := ItemVariant.Description
                    else
                        if "No." <> '' then begin
                            Item.Get("No.");
                            Description := Item.Description;
                        end;
            end;
        }
        field(8; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the description of the item.';
        }
        field(9; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the quantity for this line.';
            trigger OnValidate()
            begin
                Validate(Amount, Quantity * "Unit Price/Cost");
            end;
        }
        field(10; "Unit of Measure"; Code[10])
        {
            Caption = 'Unit of Measure';
            TableRelation = if ("No." = filter(<> '')) "Item Unit of Measure".Code where("Item No." = field("No."))
            else
            "Unit of Measure";
            ToolTip = 'Specifies the unit of measure for the item.';
        }
        field(11; "Unit Price/Cost"; Decimal)
        {
            Caption = 'Unit Price/Cost';
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the unit price or cost amount.';
            trigger OnValidate()
            begin
                Validate(Amount, Quantity * "Unit Price/Cost");
            end;
        }
        field(12; Amount; Decimal)
        {
            Caption = 'Amount';
            ToolTip = 'Specifies the total amount for this line.';
            trigger OnValidate()
            begin
                "Amount ACY" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", Amount);
            end;
        }
        field(13; "Amount ACY"; Decimal)
        {
            Caption = 'Amount ACY';
            Editable = false;
            ToolTip = 'Specifies the amount in additional currency.';
        }
        field(14; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the currency code for the amount.';
            trigger OnValidate()
            begin
                if "Currency Code" <> xRec."Currency Code" then
                    "Amount ACY" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", Amount);
            end;
        }
        field(15; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the posting date for this line.';
            trigger OnValidate()
            begin
                if "Posting Date" <> xRec."Posting Date" then
                    "Amount ACY" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", Amount);
            end;
        }
        field(16; "Operation Date"; Date)
        {
            Caption = 'Operation Date';
            ToolTip = 'Specifies the operation date for this line.';
        }
        field(17; "External Document No"; Code[35])
        {
            Caption = 'External Document No';
            ToolTip = 'Specifies the external document number.';
        }
        field(18; "Unposted Invoice No."; Code[20])
        {
            Caption = 'Unposted Invoice No.';
            Editable = false;
            ToolTip = 'Specifies the unposted invoice number associated with this line.';
        }
        field(19; "Posted Invoice No."; Code[20])
        {
            Caption = 'Posted Invoice No.';
            Editable = false;
            ToolTip = 'Specifies the posted invoice number associated with this line.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Document Line No.")
        {
            Clustered = true;
        }
        key(SK; "Document No.", Type)
        {
        }
    }

    trigger OnInsert()
    var
        DredgeLine: Record "Dredge Line ERK";
    begin
        DredgeLine.SetRange("Document No.", "Document No.");
        if DredgeLine.FindLast() then
            "Document Line No." := DredgeLine."Document Line No." + 10000
        else
            "Document Line No." := 10000;
    end;

    trigger OnModify()
    begin
        if "Unposted Invoice No." <> '' then
            Error(CannotModifyDocumentWithInvoiceErr, "Document No.", "Document Line No.", "Unposted Invoice No.");

        if "Posted Invoice No." <> '' then
            Error(CannotModifyDocumentWithPostedInvoiceErr, "Document No.", "Document Line No.", "Posted Invoice No.");
    end;

    trigger OnDelete()
    begin
        if "Unposted Invoice No." <> '' then
            Error(CannotDeleteDocumentWithInvoiceErr, "Document No.", "Document Line No.", "Unposted Invoice No.");

        if "Posted Invoice No." <> '' then
            Error(CannotDeleteDocumentWithPostedInvoiceErr, "Document No.", "Document Line No.", "Posted Invoice No.");
    end;

    var
        Item: Record Item;
        ExportManagement: Codeunit "Export Management ERK";
        CannotModifyDocumentWithInvoiceErr: Label 'You cannot modify line %2 in document %1 because it is associated with unposted invoice %3.', Comment = '%1 = Document No., %2 = Document Line No., %3 = Unposted Invoice No.';
        CannotModifyDocumentWithPostedInvoiceErr: Label 'You cannot modify line %2 in document %1 because it is associated with posted invoice %3.', Comment = '%1 = Document No., %2 = Document Line No., %3 = Posted Invoice No.';
        CannotDeleteDocumentWithInvoiceErr: Label 'You cannot delete line %2 in document %1 because it is associated with unposted invoice %3.', Comment = '%1 = Document No., %2 = Document Line No., %3 = Unposted Invoice No.';
        CannotDeleteDocumentWithPostedInvoiceErr: Label 'You cannot delete line %2 in document %1 because it is associated with posted invoice %3.', Comment = '%1 = Document No., %2 = Document Line No., %3 = Posted Invoice No.';
}
