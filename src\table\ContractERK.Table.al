table 60064 "Contract ERK"
{
    Caption = 'Contract';
    DataClassification = CustomerContent;
    DrillDownPageId = "Contracts ERK";
    LookupPageId = "Contracts ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            NotBlank = true;
            ToolTip = 'Specifies the value of the No. field.';
        }
        field(2; "Version No."; Integer)
        {
            Caption = 'Version No.';
            ToolTip = 'Specifies the value of the Version No. field.';
        }
        field(3; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            TableRelation = "Ship ERK";
            ToolTip = 'Specifies the value of the Ship No. field.';
            trigger OnValidate()
            begin
                CalcFields("Ship Name");
            end;
        }
        field(4; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Ship ERK".Name where("No." = field("Ship No.")));
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(5; "Starting Date-Time"; DateTime)
        {
            Caption = 'Starting Date-Time';
            ToolTip = 'Specifies the value of the Starting Date-Time field.';
        }
        field(6; "Ending Date-Time"; DateTime)
        {
            Caption = 'Ending Date-Time';
            ToolTip = 'Specifies the value of the Ending Date-Time field.';
        }
        field(7; "Daily Hire"; Decimal)
        {
            Caption = 'Daily Hire';
            ToolTip = 'Specifies the value of the Daily Hire field.';
            trigger OnValidate()
            begin
                Rec."Hourly Hire" := Rec."Daily Hire" / 24;
            end;
        }
        field(8; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        field(9; "Pay-to Vendor No."; Code[20])
        {
            Caption = 'Pay-to Vendor No.';
            TableRelation = Vendor."No.";
            ToolTip = 'Specifies the value of the Pay-to Vendor No. field.';
            trigger OnValidate()
            begin
                CalcFields("Pay-to Vendor Name");
            end;
        }
        field(10; "Owner No."; Code[20])
        {
            Caption = 'Owner No.';
            TableRelation = Vendor."No.";
            ToolTip = 'Specifies the value of the Owner No. field.';
            trigger OnValidate()
            begin
                CalcFields("Owner Name")
            end;
        }
        field(11; Active; Boolean)
        {
            Caption = 'Active';
            ToolTip = 'Specifies the value of the Active field.';
            trigger OnValidate()
            var
                Contract: Record "Contract ERK";
                Ship: Record "Ship ERK";
                MultipleActiveContactsErr: Label 'Vessel Name: %1 has more than one active contract.', Comment = '%1="Contract ERK"."Ship Name"';
                ShipCardUpdatedMsg: Label 'Ship Cards contract information has been updated.';
                ShipCardClearedMsg: Label 'Ship Cards contract information has been cleared.';
            begin
                if Rec.Active then begin
                    Contract.SetRange("Ship No.", Rec."Ship No.");
                    Contract.SetRange(Active, true);
                    Rec.CalcFields("Ship Name");
                    if not Contract.IsEmpty() then
                        Error(MultipleActiveContactsErr, Rec."Ship Name");

                    Ship.Get(Rec."Ship No.");
                    Ship.Validate("Contract Start Date", Rec."Starting Date-Time".Date());
                    Ship.Validate("Contract End Date", Rec."Ending Date-Time".Date());
                    Ship.Validate("Contract Unit Cost", Rec."Daily Hire");
                    Ship.Validate("Contract Currency Code", Rec."Currency Code");
                    Ship.Modify(true);

                    Message(ShipCardUpdatedMsg);
                end
                else begin
                    Ship.Get(Rec."Ship No.");
                    Ship.Validate("Contract Start Date", 0D);
                    Ship.Validate("Contract End Date", 0D);
                    Ship.Validate("Contract Unit Cost", 0);
                    Ship.Validate("Contract Currency Code", '');
                    Ship.Modify(true);

                    Message(ShipCardClearedMsg);
                end;
            end;
        }
        field(12; "Weather Limit (BF/DG)"; Code[10])
        {
            Caption = 'Weather Limit (BF/DG)';
            ToolTip = 'Specifies the value of the Weather Limit (BF/DG) field.';
        }
        field(13; "Hire Period"; DateFormula)
        {
            Caption = 'Hire Period';
            ToolTip = 'Specifies the value of the Hire Period field.';
        }
        field(14; "Invoice Period"; DateFormula)
        {
            Caption = 'Invoice Period';
            ToolTip = 'Specifies the value of the Invoice Period field.';
        }
        field(15; "Contract Ending Date-Time"; DateTime)
        {
            Caption = 'Contract Ending Date-Time';
            ToolTip = 'Specifies the value of the Contract Ending Date-Time field.';
        }
        field(16; "Extend Option"; Enum "Contract Extend Option ERK")
        {
            Caption = 'Extend Option';
            ToolTip = 'Specifies the value of the Extend Option field.';
        }
        field(17; "Pay-to Vendor Name"; Text[100])
        {
            Caption = 'Pay-to Vendor Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Pay-to Vendor No.")));
            ToolTip = 'Specifies the value of the Pay-to Vendor Name field.';
        }
        field(18; "Owner Name"; Text[100])
        {
            Caption = 'Owner Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Owner No.")));
            ToolTip = 'Specifies the value of the Owner Name field.';
        }
        field(19; "Hourly Hire"; Decimal)
        {
            Caption = 'Hourly Hire';
            ToolTip = 'Specifies the value of the Hourly Hire field.';
        }
        field(20; "Description S. Consumption"; Decimal)
        {
            Caption = 'Description S. Consumption';
            ToolTip = 'Specifies the value of the Description S. Consumption field.';
        }
        field(21; "Description Speed"; Decimal)
        {
            Caption = 'Description Speed';
            ToolTip = 'Specifies the value of the Description Speed field.';
        }
    }
    keys
    {
        key(PK; "No.", "Version No.")
        {
            Clustered = true;
        }
    }
}
