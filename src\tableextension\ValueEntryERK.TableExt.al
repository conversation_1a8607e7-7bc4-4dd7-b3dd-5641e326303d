tableextension 60026 "Value Entry ERK" extends "Value Entry"
{
    fields
    {
        field(60000; "ILE Entry Type ERK"; Enum "Item Ledger Entry Type")
        {
            Caption = 'ILE Entry Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Ledger Entry"."Entry Type" where("Entry No." = field("Item Ledger Entry No.")));
            ToolTip = 'Specifies the value of the ILE Entry Type field.';
        }
    }
}
