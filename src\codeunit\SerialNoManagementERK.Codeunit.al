codeunit 60007 "Serial No. Management ERK"
{
    procedure MarkTSE(var TempSerialNoInformation: Record "Serial No. Information")
    var
        SerialNoInformation: Record "Serial No. Information";
        ConfirmQst: Label '%1 Serial No. will be updated for TSE. Do you want to continue?', Comment = '%1 = No. of Serial No. to be updated';
        TSEUpdatedMsg: Label '%1 Serial No. updated for TSE.', Comment = '%1="Serial No. Information".Count()';
        ConfirmText: Text;
    begin
        ConfirmText := StrSubstNo(ConfirmQst, TempSerialNoInformation.Count());
        if not ConfirmManagement.GetResponseOrDefault(ConfirmText, true) then
            exit;
        if TempSerialNoInformation.FindSet(false) then
            repeat
                if SerialNoInformation.Get(TempSerialNoInformation."Item No.", TempSerialNoInformation."Variant Code", TempSerialNoInformation."Serial No.") then begin
                    SerialNoInformation.Validate("TSE ERK", true);
                    SerialNoInformation.Modify(true);
                end;
            until TempSerialNoInformation.Next() = 0;
        Message(TSEUpdatedMsg, TempSerialNoInformation.Count());
    end;

    procedure MarkCommercialBlockage(var TempSerialNoInformation: Record "Serial No. Information")
    var
        SerialNoInformation: Record "Serial No. Information";
        ConfirmQst: Label '%1 Serial No. will be updated for Commercial Blockage. Do you want to continue?', Comment = '%1 = No. of Serial No. to be updated';
        CommercialBlockageUpdatedMsg: Label '%1 Serial No. updated for Commercial Blockage.', Comment = '%1="Serial No. Information".Count()';
        ConfirmText: Text;
    begin
        ConfirmText := StrSubstNo(ConfirmQst, TempSerialNoInformation.Count());
        if not ConfirmManagement.GetResponseOrDefault(ConfirmText, true) then
            exit;
        if TempSerialNoInformation.FindSet(false) then
            repeat
                if SerialNoInformation.Get(TempSerialNoInformation."Item No.", TempSerialNoInformation."Variant Code", TempSerialNoInformation."Serial No.") then begin
                    SerialNoInformation.Validate("Commercial Blockage ERK", true);
                    SerialNoInformation.Modify(true);
                end;
            until TempSerialNoInformation.Next() = 0;
        Message(CommercialBlockageUpdatedMsg, TempSerialNoInformation.Count());
    end;

    procedure MarkNavigation(var TempSerialNoInformation: Record "Serial No. Information")
    var
        SerialNoInformation: Record "Serial No. Information";
        ConfirmQst: Label '%1 Serial No. will be updated for Navigation. Do you want to continue?', Comment = '%1 = No. of Serial No. to be updated';
        NavigationUpdatedMsg: Label '%1 Serial No. updated for Navigation.', Comment = '%1="Serial No. Information".Count()';
        ConfirmText: Text;
    begin
        ConfirmText := StrSubstNo(ConfirmQst, TempSerialNoInformation.Count());
        if not ConfirmManagement.GetResponseOrDefault(ConfirmText, true) then
            exit;
        if TempSerialNoInformation.FindSet(false) then
            repeat
                if SerialNoInformation.Get(TempSerialNoInformation."Item No.", TempSerialNoInformation."Variant Code", TempSerialNoInformation."Serial No.") then begin
                    SerialNoInformation.Validate("Nav. Process Required ERK", true);
                    SerialNoInformation.Modify(true);
                end;
            until TempSerialNoInformation.Next() = 0;
        Message(NavigationUpdatedMsg, TempSerialNoInformation.Count());
    end;

    procedure UpdateSerialNoInformations(var TempSerialNoInformation: Record "Serial No. Information")
    var
        SerialNoInformation: Record "Serial No. Information";
        ConfirmQst: Label '%1 Serial No. will be updated. Do you want to continue?', Comment = '%1 = No. of Serial No. to be updated';
        SerialNoUpdatedMsg: Label '%1 Serial No. updated.', Comment = '%1="Serial No. Information".Count()';
        ConfirmText: Text;
    begin
        ConfirmText := StrSubstNo(ConfirmQst, TempSerialNoInformation.Count());
        if not ConfirmManagement.GetResponseOrDefault(ConfirmText, true) then
            exit;
        if TempSerialNoInformation.FindSet(false) then
            repeat
                if SerialNoInformation.Get(TempSerialNoInformation."Item No.", TempSerialNoInformation."Variant Code", TempSerialNoInformation."Serial No.") then
                    UpdateSerialNoInformationFields(TempSerialNoInformation, SerialNoInformation);
            until TempSerialNoInformation.Next() = 0;
        Message(SerialNoUpdatedMsg, TempSerialNoInformation.Count());
    end;

    procedure UpdateSerialNoInformationFields(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        UpdateManufacturerCode(TempSerialNoInformation, SerialNoInformation);
        UpdateEngineID(TempSerialNoInformation, SerialNoInformation);
        UpdateFuelType(TempSerialNoInformation, SerialNoInformation);
        UpdateColourName(TempSerialNoInformation, SerialNoInformation);
        UpdateCarlineCode(TempSerialNoInformation, SerialNoInformation);
        UpdateModelVersion(TempSerialNoInformation, SerialNoInformation);
        UpdateFootprint(TempSerialNoInformation, SerialNoInformation);
        UpdateGrossWeight(TempSerialNoInformation, SerialNoInformation);
        UpdateCustomsDeclarationNo(TempSerialNoInformation, SerialNoInformation);
        UpdateCustomsDecLineNo(TempSerialNoInformation, SerialNoInformation);
        UpdateCustomsRegistrationDate(TempSerialNoInformation, SerialNoInformation);
        UpdateBlocked(TempSerialNoInformation, SerialNoInformation);
        UpdateGrupageInformation(TempSerialNoInformation, SerialNoInformation);
        UpdateSummaryDeclarationNo(TempSerialNoInformation, SerialNoInformation);
        UpdateBillofLadingNo(TempSerialNoInformation, SerialNoInformation);
        UpdateTruckPlate(TempSerialNoInformation, SerialNoInformation);
        AddNoteToRecord(TempSerialNoInformation, SerialNoInformation);

        OnAfterUpdateSerialNoInformationFieldsOnBeforeModifySerialNoInformation(TempSerialNoInformation, SerialNoInformation);

        SerialNoInformation.Modify(true);
    end;

    local procedure AddNoteToRecord(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    var
        RecordLink: Record "Record Link";
        RecordLinkManagement: Codeunit "Record Link Management";
        RecRef: RecordRef;
    begin
        if TempSerialNoInformation."Note to Add ERK" = '' then
            exit;

        RecRef.GetTable(SerialNoInformation);

        RecordLink.Init();
        RecordLink."Record ID" := RecRef.RecordId();
        RecordLink.Type := RecordLink.Type::Note;
        RecordLink.Created := CurrentDateTime();
        RecordLink."User ID" := CopyStr(UserId(), 1, MaxStrLen(RecordLink."User ID"));
        RecordLink.Company := CopyStr(CompanyName(), 1, MaxStrLen(RecordLink.Company));

        RecordLinkManagement.WriteNote(RecordLink, TempSerialNoInformation."Note to Add ERK");
        RecordLink.Insert(true);
    end;

    [BusinessEvent(true)]
    local procedure OnAfterUpdateSerialNoInformationFieldsOnBeforeModifySerialNoInformation(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
    end;

    local procedure UpdateGrupageInformation(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Grupage No. ERK" <> '' then
            SerialNoInformation.Validate("Grupage No. ERK", TempSerialNoInformation."Grupage No. ERK");
        if TempSerialNoInformation."Grupage Date ERK" <> 0D then
            SerialNoInformation.Validate("Grupage Date ERK", TempSerialNoInformation."Grupage Date ERK");
        if TempSerialNoInformation."Grupage Ship-to Name ERK" <> '' then
            SerialNoInformation.Validate("Grupage Ship-to Name ERK", TempSerialNoInformation."Grupage Ship-to Name ERK");
        if TempSerialNoInformation."Grupage Ship-to City ERK" <> '' then
            SerialNoInformation.Validate("Grupage Ship-to City ERK", TempSerialNoInformation."Grupage Ship-to City ERK");
        if TempSerialNoInformation."Grupage Ship-to Address ERK" <> '' then
            SerialNoInformation.Validate("Grupage Ship-to Address ERK", TempSerialNoInformation."Grupage Ship-to Address ERK");
        if TempSerialNoInformation."Grupage Location Code ERK" <> '' then
            SerialNoInformation.Validate("Grupage Location Code ERK", TempSerialNoInformation."Grupage Location Code ERK");
        if TempSerialNoInformation."Grupage Bin Code ERK" <> '' then
            SerialNoInformation.Validate("Grupage Bin Code ERK", TempSerialNoInformation."Grupage Bin Code ERK");
        if TempSerialNoInformation."Print Grupage Label ERK" then
            SerialNoInformation.Validate("Print Grupage Label ERK", TempSerialNoInformation."Print Grupage Label ERK");
    end;

    procedure UpdateBlocked(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation.Blocked then
            SerialNoInformation.Validate(Blocked, TempSerialNoInformation.Blocked);
    end;

    procedure UpdateManufacturerCode(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Brand Code ERK" <> '' then
            SerialNoInformation."Brand Code ERK" := TempSerialNoInformation."Brand Code ERK";
    end;

    procedure UpdateEngineID(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Engine ID ERK" <> '' then
            SerialNoInformation.Validate("Engine ID ERK", TempSerialNoInformation."Engine ID ERK");
    end;

    procedure UpdateFuelType(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Fuel Type ERK" <> SerialNoInformation."Fuel Type ERK"::" " then
            SerialNoInformation.Validate("Fuel Type ERK", TempSerialNoInformation."Fuel Type ERK");
    end;

    procedure UpdateColourName(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Colour Name ERK" <> '' then
            SerialNoInformation.Validate("Colour Name ERK", TempSerialNoInformation."Colour Name ERK");
    end;

    procedure UpdateBillofLadingNo(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Bill of Lading No. ERK" <> '' then
            SerialNoInformation.Validate("Bill of Lading No. ERK", TempSerialNoInformation."Bill of Lading No. ERK");
    end;

    procedure UpdateCarlineCode(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Model Code ERK" <> '' then
            SerialNoInformation.Validate("Model Code ERK", TempSerialNoInformation."Model Code ERK");
    end;

    procedure UpdateModelVersion(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Model Version ERK" <> '' then
            SerialNoInformation.Validate("Model Version ERK", TempSerialNoInformation."Model Version ERK");
    end;

    procedure UpdateFootprint(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Footprint (m2) ERK" <> 0 then
            SerialNoInformation.Validate("Footprint (m2) ERK", TempSerialNoInformation."Footprint (m2) ERK");
    end;

    procedure UpdateGrossWeight(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Gross Weight (KG) ERK" <> 0 then
            SerialNoInformation.Validate("Gross Weight (KG) ERK", TempSerialNoInformation."Gross Weight (KG) ERK");
    end;

    procedure UpdateCustomsDeclarationNo(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Customs Declaration No. ERK" <> '' then
            SerialNoInformation.Validate("Customs Declaration No. ERK", TempSerialNoInformation."Customs Declaration No. ERK");
    end;

    procedure UpdateCustomsDecLineNo(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Customs Dec. Line No. ERK" <> 0 then
            SerialNoInformation.Validate("Customs Dec. Line No. ERK", TempSerialNoInformation."Customs Dec. Line No. ERK");
    end;

    procedure UpdateCustomsRegistrationDate(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Customs Registration Date ERK" <> 0D then
            SerialNoInformation.Validate("Customs Registration Date ERK", TempSerialNoInformation."Customs Registration Date ERK");
    end;

    procedure UpdateSummaryDeclarationNo(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Summary Declaration No. ERK" <> '' then
            SerialNoInformation.Validate("Summary Declaration No. ERK", TempSerialNoInformation."Summary Declaration No. ERK");
    end;

    procedure UpdateTruckPlate(var TempSerialNoInformation: Record "Serial No. Information"; var SerialNoInformation: Record "Serial No. Information")
    begin
        if TempSerialNoInformation."Truck Plate ERK" <> '' then
            SerialNoInformation.Validate("Truck Plate ERK", TempSerialNoInformation."Truck Plate ERK");
    end;

    procedure CalculateVolumeFromSerialNoInformation(var SerialNoInformation: Record "Serial No. Information")
    var
        Model: Record "Model ERK";
    begin
        if SerialNoInformation."Model Code ERK" = '' then
            exit;
        Model.Get(SerialNoInformation."Model Code ERK");
        SerialNoInformation.Validate("Volume (m3) ERK", Model."Height (mm)" * Model."Width (mm)" * Model."Length (mm)" / 1000000000);
        SerialNoInformation.Modify(true);
    end;

    procedure CalculateAreaFromSerialNoInformation(var SerialNoInformation: Record "Serial No. Information")
    var
        Model: Record "Model ERK";
    begin
        if SerialNoInformation."Model Code ERK" = '' then
            exit;
        Model.Get(SerialNoInformation."Model Code ERK");
        SerialNoInformation.Validate("Footprint (m2) ERK", Model."Length (mm)" * Model."Width (mm)" / 1000000);
        SerialNoInformation.Modify(true);
    end;

    procedure CalcualteGrossWeightFromSerialNoInformation(var SerialNoInformation: Record "Serial No. Information")
    var
        Model: Record "Model ERK";
    begin
        if SerialNoInformation."Model Code ERK" = '' then
            exit;
        Model.Get(SerialNoInformation."Model Code ERK");
        SerialNoInformation.Validate("Gross Weight (KG) ERK", Model."Gross Weight (kg)");
        SerialNoInformation.Modify(true);
    end;

    procedure CalculateAreaAndVolumeFromModel(var Model: Record "Model ERK")
    begin
        Model.Validate("Area (m2)", Model."Length (mm)" * Model."Width (mm)" / 1000000);
        Model.Validate("Volume (m3)", Model."Height (mm)" * Model."Width (mm)" * Model."Length (mm)" / 1000000000);
    end;

    var
        ConfirmManagement: Codeunit "Confirm Management";
}
