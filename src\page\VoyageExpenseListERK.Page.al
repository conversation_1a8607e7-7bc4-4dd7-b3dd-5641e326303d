page 60028 "Voyage Expense List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Voyage Expenses';
    PageType = List;
    SourceTable = "Voyage Expense ERK";
    UsageCategory = Lists;
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                    QuickEntry = false;
                }
                // field("Department Code"; Rec."Department Code")
                // {
                //     ToolTip = 'Specifies the value of the Department Code field.';
                // }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    QuickEntry = false;
                }
                field("Unit Price"; Rec."Unit Cost")
                {
                }
                field("Line Amount"; Rec."Line Amount")
                {
                    QuickEntry = false;
                }
                // field(LineAmountACY; ExportManagement.ConvertAmountToACY(Rec."Posting Date", Rec."Currency Code", Rec."Line Amount"))
                // {
                //     Caption = 'Line Amount (ACY)';
                //     ToolTip = 'Specifies the value of the Line Amount (ACY) field.';
                // }
                field("Line Amount (ACY)"; Rec."Line Amount (ACY)")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    QuickEntry = false;
                    Editable = not Rec."Consumption Line";
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Vendor No."; Rec."Vendor No.")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Vendor Name"; Rec."Vendor Name")
                {
                }
                field("Purchase Invoice No."; Rec."Purchase Invoice No.")
                {
                    trigger OnDrillDown()
                    var
                        PurchaseHeader: Record "Purchase Header";
                        PurchInvHeader: Record "Purch. Inv. Header";
                        SalesHeader: Record "Sales Header";
                        SalesInvoiceHeader: Record "Sales Invoice Header";
                    begin
                        Rec.TestField("Purchase Invoice No.");
                        if PurchInvHeader.Get(Rec."Purchase Invoice No.") then
                            PageManagement.PageRun(PurchInvHeader)
                        else
                            if PurchaseHeader.Get(PurchaseHeader."Document Type"::Invoice, Rec."Purchase Invoice No.") then
                                PageManagement.PageRun(PurchaseHeader)
                            else
                                if SalesInvoiceHeader.Get(Rec."Purchase Invoice No.") then
                                    PageManagement.PageRun(SalesInvoiceHeader)
                                else
                                    if SalesHeader.Get(SalesHeader."Document Type"::Invoice, Rec."Purchase Invoice No.") then
                                        PageManagement.PageRun(SalesHeader);
                    end;
                }
                field("Purchase Invoice Line No."; Rec."Purchase Invoice Line No.")
                {
                }
                field("Is Cancelled"; Rec."Is Cancelled")
                {
                }
                field(Posted; Rec.Posted)
                {
                }
                field("Vendor Invoice No."; Rec."Vendor Invoice No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field("Consumption Line"; Rec."Consumption Line")
                {
                    Editable = false;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateInvoiceFromSelectedLines)
            {
                ApplicationArea = All;
                Caption = 'Create Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NewDocument;
                ToolTip = 'Executes the Create Invoice action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    VoyageExpense: Record "Voyage Expense ERK";
                begin
                    CurrPage.SetSelectionFilter(VoyageExpense);
                    VoyageMangement.CreatePurchaseInvoiceFromVoyageExpense(VoyageExpense);
                end;
            }
        }
    }
    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        //ExportManagement: Codeunit "Export Management ERK";
        PageManagement: Codeunit "Page Management";
}
