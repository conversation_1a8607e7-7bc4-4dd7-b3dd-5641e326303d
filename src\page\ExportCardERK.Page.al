page 60001 "Export Card ERK"
{
    ApplicationArea = All;
    Caption = 'Export Card';
    PageType = Card;
    SourceTable = "Export Header ERK";
    UsageCategory = None;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field(Completed; Rec.Completed)
                {
                }
                field("Loading Date"; Rec."Loading Date")
                {
                }
                field("Estimated Time of Delivery"; Rec."Estimated Time of Delivery")
                {
                }
                field("Document Date"; Rec."Document Date")
                {
                }
                field("Bill of Lading No."; Rec."Bill of Lading No.")
                {
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                // field("Customs Declaration No."; Rec."Customs Declaration No.")
                // {
                //     ToolTip = 'Specifies the value of the Customs Declaration No. field.';
                //     Editable = false;
                // }
                field("Estimated Time of Departure"; Rec."Estimated Time of Departure")
                {
                }
                field("Load Description"; Rec."Load Description")
                {
                }
                field("Container Quantity"; Rec."Container Quantity")
                {
                }
                group(Sales)
                {
                    Caption = 'Sales';

                    field("Blanket Sales Order No."; Rec."Blanket Sales Order No.")
                    {
                    }
                    field("Sales Order No."; Rec."Sales Order No.")
                    {
                    }
                    field("E-Export No."; Rec."E-Export No.")
                    {
                        trigger OnDrillDown()
                        var
                            EInvoiceHeader: Record "E-Invoice Header INF";
                        begin
                            EInvoiceHeader.SetRange("E-Invoice No.", Rec."E-Export No.");
                            if EInvoiceHeader.FindFirst() then
                                Page.Run(Page::"E-Invoice INF", EInvoiceHeader);
                        end;
                    }
                    field("Posted Sales Invoice No."; Rec."Posted Sales Invoice No.")
                    {
                    }
                    field("Actual Export Date FlowField"; Rec."Actual Export Date FlowField")
                    {
                    }
                    field("Customs Declaration No. FF"; Rec."Customs Declaration No. FF")
                    {
                    }
                    // field("MCT Actual Export Date"; Rec."MCT Actual Export Date")
                    // {
                    //     ToolTip = 'Specifies the value of the MCT Actual Export Date field.';
                    //     Editable = false;
                    // }
                    field("Customer No."; Rec."Customer No.")
                    {
                    }
                    field("Customer Name"; Rec."Customer Name")
                    {
                    }
                    field("Blanket Order Date"; Rec."Blanket Order Date")
                    {
                    }
                    field("Sales Due Date"; Rec."Sales Due Date")
                    {
                    }
                    field("Sales Shipment Method Code"; Rec."Sales Shipment Method Code")
                    {
                    }
                    field("Sales Payment Method Code"; Rec."Sales Payment Method Code")
                    {
                    }
                }
            }
            part(Lines; "Export Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                //Editable = false;
            }
            group("Notify/Consingnee")
            {
                Caption = 'Notify/Consingnee';

                group(Notify)
                {
                    Caption = 'Notify';

                    field("Notify Ship-to Code"; Rec."Notify Ship-to Code")
                    {
                    }
                    field("Notify Ship-to Name"; Rec."Notify Ship-to Name")
                    {
                    }
                    field("Notify Ship-to Name 2"; Rec."Notify Ship-to Name 2")
                    {
                    }
                    field("Notify Ship-to Address"; Rec."Notify Ship-to Address")
                    {
                    }
                    field("Notify Ship-to Address 2"; Rec."Notify Ship-to Address 2")
                    {
                    }
                    field("Notify Ship-to City"; Rec."Notify Ship-to City")
                    {
                    }
                    field("Notify Ship-to County"; Rec."Notify Ship-to County")
                    {
                    }
                    field("Notify Ship-to Country/Region"; Rec."Notify Ship-to Country/Region")
                    {
                    }
                }
                group(Consignee)
                {
                    Caption = 'Consignee';

                    field("Consignee Ship-to Code"; Rec."Consignee Ship-to Code")
                    {
                    }
                    field("Consignee Ship-to Name"; Rec."Consignee Ship-to Name")
                    {
                    }
                    field("Consignee Ship-to Name 2"; Rec."Consignee Ship-to Name 2")
                    {
                    }
                    field("Consignee Ship-to Address"; Rec."Consignee Ship-to Address")
                    {
                    }
                    field("Consignee Ship-to Address 2"; Rec."Consignee Ship-to Address 2")
                    {
                    }
                    field("Consignee Ship-to City"; Rec."Consignee Ship-to City")
                    {
                    }
                    field("Consignee Ship-to County"; Rec."Consignee Ship-to County")
                    {
                    }
                    field("Consignee Ship-to Country"; Rec."Consignee Ship-to Country")
                    {
                    }
                }
            }
            group(Shipment)
            {
                Caption = 'Shipment';

                field("Country of Departure"; Rec."Country of Departure")
                {
                }
                field("Port of Departure"; Rec."Port of Departure")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Shipping Agent"; Rec."Shipping Agent")
                {
                }
                field("Country of Arrival"; Rec."Country of Arrival")
                {
                }
                field("Port of Arrival"; Rec."Port of Arrival")
                {
                }
                field("Transport Method"; Rec."Transport Method")
                {
                }
            }
            group(FinancialInformation)
            {
                Caption = 'Financial Information';

                field("Sales Currency Code"; Rec."Sales Currency Code")
                {
                    ToolTip = 'Specifies the value of the Sales Currency Code field.';
                }
                field("Total Sales Amount"; Rec."Total Sales Amount")
                {
                    ToolTip = 'Specifies the value of the Total Sales Amount field.';
                }
                field("Payment Amount"; Rec."Payment Amount")
                {
                }
                field(RemainingAmount; ExportManagement.CalculateExportRemainingAmount(Rec."No."))
                {
                    Caption = 'Remaining Amount';
                    ToolTip = 'Specifies the value of the Remaining Amount field.';
                }
                field("Curr. Exchange Rate Date (ACY)"; Rec."Curr. Exchange Rate Date (ACY)")
                {
                }
                field("Currency Exchange Rate (ACY)"; Rec."Currency Exchange Rate (ACY)")
                {
                }
                field(TotalSalesAmountACY; ExportManagement.ConvertAmountToACY(Rec."Curr. Exchange Rate Date (ACY)", Rec."Sales Currency Code", Rec."Total Sales Amount"))
                {
                    Caption = 'Total Sales Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Sales Amount (ACY) field.';
                }
                field(TotalPurchaseAmountACY; ExportManagement.CalculateTotalPurchaseCostACY(Rec))
                {
                    Caption = 'Total Purchase Amount (ACY);';
                    ToolTip = 'Specifies the value of the Total Purchase Amount (ACY); field.';
                }
                field(TotalItemChargeAmountACY; ExportManagement.CalculateTotalItemChargeAmountACY(Rec))
                {
                    Caption = 'Total Item Charge Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Item Charge Amount (ACY) field.';
                }
                field(ProfitACY; ExportManagement.ConvertAmountToACY(Rec."Curr. Exchange Rate Date (ACY)", Rec."Sales Currency Code", Rec."Total Sales Amount") - ExportManagement.CalculateTotalPurchaseCostACY(Rec) - ExportManagement.CalculateTotalItemChargeAmountACY(Rec))
                {
                    Caption = 'Profit (ACY)';
                    ToolTip = 'Specifies the value of the Profit (ACY) field.';
                }
            }
            group(BankInformation)
            {
                Caption = 'Bank Information';

                field("Bank Account Name"; Rec."Bank Account Name")
                {
                }
                field("Branch Name"; Rec."Branch Name")
                {
                }
                field(IBAN; Rec.IBAN)
                {
                }
                field("SWIFT Code"; Rec."SWIFT Code")
                {
                }
                field("Sales Payment Terms Code"; Rec."Sales Payment Terms Code")
                {
                }
            }
        }
    }
    actions
    {
        area(Navigation)
        {
            action(ViewItemCharges)
            {
                Caption = 'View Item Charges';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = ValueLedger;
                ToolTip = 'Executes the View Item Charges action.';

                trigger OnAction()
                var
                    ValueEntry: Record "Value Entry";
                begin
                    ValueEntry.SetRange("Global Dimension 1 Code", Rec."No.");
                    ValueEntry.SetFilter("Item Charge No.", '<>%1', '');
                    Page.Run(Page::"Value Entries", ValueEntry);
                end;
            }
            action(ItemChargeSummary)
            {
                Caption = 'Item Charge Summary', Comment = 'TRK="Madde Gider Özeti';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Report;
                ToolTip = 'Executes the Item Charge Summary action.';

                trigger OnAction()
                var
                    ValueEntry: Record "Value Entry";
                begin
                    ValueEntry.SetRange("Global Dimension 1 Code", Rec."No.");
                    Report.Run(Report::"Item Charge Summary ERK", true, true, ValueEntry);
                end;
            }
            action(PurchaseOrders)
            {
                Caption = 'Purchase Orders';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Purchase;
                ToolTip = 'Executes the Purchase Orders action.';

                trigger OnAction()
                begin
                    Page.Run(Page::"Purchase Order List");
                end;
            }
            action(BlanketPurchaseOrders)
            {
                Caption = 'Blanket Purchase Orders';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Purchase;
                ToolTip = 'Executes the Blanket Purchase Orders action.';

                trigger OnAction()
                begin
                    Page.Run(Page::"Blanket Purchase Orders");
                end;
            }
            action(Post)
            {
                Caption = 'P&ost';
                Image = PostOrder;
                PromotedCategory = Process;
                ShortcutKey = 'F9';
                ToolTip = 'Finalize the document or journal by posting the amounts and quantities to the related accounts in your company books.';
                PromotedIsBig = true;
                PromotedOnly = true;
                Promoted = true;
                AboutTitle = 'Posting the order';
                AboutText = 'Posting will ship or invoice the quantities on the order, or both. **Post** and **Send** can save the order as a file, print it, or attach it to an email, all in one go.';

                trigger OnAction()
                var
                    SalesHeader: Record "Sales Header";
                    CurrencyExchangeRate: Record "Currency Exchange Rate";
                    GeneralLedgerSetup: Record "General Ledger Setup";
                    SuccesfullMsg: Label 'Export has been shipped succesfully.';
                begin
                    GeneralLedgerSetup.Get();
                    GeneralLedgerSetup.TestField("Additional Reporting Currency");
                    SalesHeader.Get(SalesHeader."Document Type"::Order, Rec."Sales Order No.");
                    SalesHeader.Validate("Posting Date", Rec."Loading Date");
                    SalesHeader.Validate("External Document No.", Rec."No.");
                    SalesHeader.Ship := true;
                    Codeunit.Run(Codeunit::"Sales-Post", SalesHeader);
                    Rec."Currency Exchange Rate (ACY)" := 1 / CurrencyExchangeRate.ExchangeRate(SalesHeader."Posting Date", GeneralLedgerSetup."Additional Reporting Currency");
                    Rec."Curr. Exchange Rate Date (ACY)" := SalesHeader."Posting Date";
                    Message(SuccesfullMsg);
                end;
            }
            action(ContainerLinst)
            {
                Caption = 'View Container List';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PutAway;
                ToolTip = 'Executes the View Container List action.';

                trigger OnAction()
                var
                    ContainerHeader: Record "Container Header ERK";
                begin
                    ContainerHeader.SetRange("Export No.", Rec."No.");
                    Page.Run(Page::"Container List ERK", ContainerHeader);
                end;
            }
            action(ApplyPayment)
            {
                Caption = 'Apply Payment';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ApplyEntries;
                ToolTip = 'Executes the Apply Payment action.';

                trigger OnAction()
                var
                    ExportLedgerEntry: Record "Export Ledger Entry ERK";
                    ApplyPayment: Page "Apply Payment ERK";
                begin
                    ExportLedgerEntry.SetRange("Customer No.", Rec."Customer No.");
                    ExportLedgerEntry.SetFilter("Blanket Sales Order No.", '%1|%2', Rec."Blanket Sales Order No.", '');
                    ApplyPayment.GetExportRecord(Rec);
                    ApplyPayment.SetTableView(ExportLedgerEntry);
                    ApplyPayment.Run();
                end;
            }
            action(CommercialInvoice)
            {
                Caption = 'Commercial Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Print;
                ToolTip = 'Executes the Commercial Invoice action.';

                trigger OnAction()
                begin
                    Rec.SetRecFilter();
                    //ExportManagement.SetGlobalLayoutCode('Commercial Invoice ERK');
                    Report.Run(Report::"Commercial Invoice ERK", true, true, Rec);
                end;
            }
            action(ShippingOrder)
            {
                Caption = 'Shipping Order';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReleaseShipment;
                ToolTip = 'Executes the Shipping Order action.';

                trigger OnAction()
                var
                    ContainerHeader: Record "Container Header ERK";
                begin
                    ContainerHeader.SetRange("Export No.", Rec."No.");
                    //Rec.SetRecFilter();
                    //ExportManagement.SetGlobalLayoutCode('Konşimento Talimatı ERK');
                    Report.Run(Report::"Shipping Order ERK", true, true, ContainerHeader);
                end;
            }
            action(PackingList)
            {
                Caption = 'Packing List';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PrintReport;
                ToolTip = 'Executes the Packing List action.';

                trigger OnAction()
                var
                    ContainerHeader: Record "Container Header ERK";
                begin
                    ContainerHeader.SetRange("Export No.", Rec."No.");
                    Report.Run(Report::"Packing List ERK", true, true, ContainerHeader)
                end;
            }
            action(RelatedItemChargeLines)
            {
                Caption = 'Related Item Charge Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Related Item Charge Lines action.';
                Image = Line;

                trigger OnAction()
                var
                    PurchInvLine: Record "Purch. Inv. Line";
                    PostedPurchaseInvoiceLines: Page "Posted Purchase Invoice Lines";
                begin
                    PurchInvLine.SetRange(Type, PurchInvLine.Type::"Charge (Item)");
                    PurchInvLine.SetRange("Shortcut Dimension 1 Code", Rec."No.");
                    PostedPurchaseInvoiceLines.SetTableView(PurchInvLine);
                    PostedPurchaseInvoiceLines.Run();
                end;
            }
        }
    }
    var
        ExportManagement: Codeunit "Export Management ERK";
}
