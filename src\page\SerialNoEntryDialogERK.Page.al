page 60148 "Serial No. Entry Dialog ERK"
{
    Caption = 'Serial Number Entry';
    PageType = StandardDialog;
    ApplicationArea = All;
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            field(SerialNoField; SerialNo)
            {
                Caption = 'Serial No.';
                ToolTip = 'Specifies enter the Serial No. to view attachments for.';
            }
        }
    }

    var
        SerialNo: Code[50];

    procedure GetSerialNo(): Code[50]
    begin
        exit(SerialNo);
    end;
}