tableextension 60006 "Cust. Ledger Entry ERK" extends "Cust. Ledger Entry"
{
    fields
    {
        field(60000; "Assignable Amount ERK"; Decimal)
        {
            Caption = 'Assignable Amount';
            Editable = false;
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Assignable Amount field.';
        }
        field(60001; "Amount to Assign ERK"; Decimal)
        {
            Caption = 'Amount to Assign';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Amount to Assign field.';
        }
        field(60002; "Erk Holding Intercompany ERK"; Boolean)
        {
            Caption = 'Erk Holding Intercompany';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer."Erk Holding Intercompany ERK" where("No." = field("Customer No.")));
            ToolTip = 'Specifies the value of the Erk Holding Intercompany field.';
        }
    }
}
