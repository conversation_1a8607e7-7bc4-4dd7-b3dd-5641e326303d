page 60104 "Pending App. Custom Dec. Que."
{
    ApplicationArea = All;
    Caption = 'Pending Approval Custom Declaration Queue';
    PageType = List;
    SourceTable = "Vehicle Query Ledger Entry ERK";
    SourceTableView = where(Status = const("Approval Pending"));
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Customs Declaration No."; Rec."Customs Declaration No.")
                {
                }
                field("Customs Declaration Line No."; Rec."Customs Declaration Line No.")
                {
                }
                field("Summary Declaration No."; Rec."Summary Declaration No.")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Approve)
            {
                Caption = 'Approve';
                Image = Approval;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Approve action.';

                trigger OnAction()
                var
                    VehicleQueryLedgerEntry: Record "Vehicle Query Ledger Entry ERK";
                    ConfirmationlMsg: Label 'Declaration Queries will be approved are you sure ?';
                    SuccessMsg: Label 'Declaration Queries has been approved';
                begin
                    if ConfirmManagement.GetResponseOrDefault(ConfirmationlMsg, true) then begin
                        CurrPage.SetSelectionFilter(VehicleQueryLedgerEntry);
                        VehicleQueryLedgerEntry.ModifyAll(Status, VehicleQueryLedgerEntry.Status::Approved, true);
                        Message(SuccessMsg)
                    end;
                end;
            }
        }
    }
    var
        ConfirmManagement: Codeunit "Confirm Management";
}