query 60007 "Car Carrier API ERK"
{
    Caption = 'Car Carrier API';
    APIGroup = 'erkHoldingCustomization';
    APIPublisher = 'infotek';
    APIVersion = 'v1.0';
    EntityName = 'carCarrierReport';
    EntitySetName = 'carCarrierReports';
    QueryType = API;
    DataAccessIntent = ReadOnly;

    elements
    {
        dataitem(carCarrierLineDetailERK;
        "Car Carrier Line Detail ERK")
        {
            column(documentNo; "Document No.")
            {
            }
            column(year; Year)
            {
            }
            column(month; Month)
            {
            }
            column(week; Week)
            {
            }
            column(customerNo; "Customer No.")
            {
            }
            column(customerName; "Customer Name")
            {
            }
            column(loadingPort; "Loading Port")
            {
            }
            column(loadingPortDescription; "Loading Port Description")
            {
            }
            column(dischargePort; "Discharge Port")
            {
            }
            column(dischargePortDescription; "Discharge Port Description")
            {
            }
            column(shipName; "Ship Name")
            {
            }
            column(totalSalesACY; "Total Sales (ACY)")
            {
            }
            column(totalProfitACY; "Total Profit (ACY)")
            {
            }
            column(profitability; Profitability)
            {
            }
            column(voyageEndingDateTime; "Voyage Ending Date-Time")
            {
                ColumnFilter = voyageEndingDateTime = filter(<> '');
            }
        }
    }
    trigger OnBeforeOpen()
    begin
    end;
}
