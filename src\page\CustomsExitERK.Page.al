page 60062 "Customs Exit ERK"
{
    ApplicationArea = All;
    Caption = 'Field Operation';
    PageType = StandardDialog;
    SourceTable = "Vehicle Operation ERK";
    UsageCategory = Tasks;
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("Operation Type"; Rec."Operation Type")
                {
                    ValuesAllowed = 0, 1, 2, 4, 10, 16;
                }
                // field("To Location Code"; Rec."To Location Code")
                // {
                //     ToolTip = 'Specifies the value of the To Location Code field.';
                //     QuickEntry = false;
                // }
                // field("To Bin Code"; Rec."To Bin Code")
                // {
                //     ToolTip = 'Specifies the value of the To Bin Code field.';
                //     QuickEntry = false;
                // }
                field("Serial No."; Rec."Serial No.")
                {
                    trigger OnValidate()
                    begin
                        LastSuccesfullSerialNo := Rec."Serial No.";
                        Rec."Serial No." := '';
                    end;
                }
                field(LastSuccesfullSerialNo; LastSuccesfullSerialNo)
                {
                    Caption = 'Last Succesfull Serial No.';
                    ToolTip = 'Specifies the value of the Last Succesfull Serial No. field.';
                    Editable = false;
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.Init();
        Rec.Insert(false);
    end;

    var
        LastSuccesfullSerialNo: Code[50];
}
