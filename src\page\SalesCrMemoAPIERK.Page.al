page 60156 "Sales CrMemo API ERK"
{
    PageType = API;
    Caption = 'Sales Credit Memo API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'salesCreditMemo';
    EntitySetName = 'salesCreditMemo';
    SourceTable = "Sales Header";
    SourceTableView = where("Document Type" = filter("Credit Memo"));
    DelayedInsert = true;
    ODataKeyFields = SystemId;


    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {

                field(id; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(documentType; Rec."Document Type")
                {
                    Caption = 'Document Type';
                }
                field(number; Rec."No.")
                {
                    Caption = 'No.';
                }
                field(externalDocNumber; Rec."External Document No.")
                {
                    Caption = 'External Document No.';
                }
                field(postingDate; Rec."Posting Date")
                {
                    Caption = 'Posting Date';
                }
                field(sellToCustomerNumber; Rec."Sell-to Customer No.")
                {
                    Caption = 'Sell-to Customer No.';
                }
                field(sellToCustomerName; Rec."Sell-to Customer Name")
                {
                    Caption = 'Sell-to Customer Name';
                }
                field(sellToCustomerName2; Rec."Sell-to Customer Name 2")
                {
                    Caption = 'Sell-to Customer Name 2';
                }

                field(currencyCode; Rec."Currency Code")
                {
                    Caption = 'Currency Code';
                }
                field(amount; Rec.Amount)
                {
                    Caption = 'Amount';
                }
                field(amountIncludingVAT; Rec."Amount Including VAT")
                {
                    Caption = 'Amount Including VAT';
                }
                field(invoiceTypeINFERK; Rec."Invoice Type INF ERK")
                {
                    Caption = 'Invoice Type';
                }
                field(ebaStatusERK; Rec."EBA Status ERK")
                {
                    Caption = 'EBA Status';
                }
                field(invoiceViewPDFFile; Rec."Invoice View PDF File ERK")
                {
                    Caption = 'Invoice View PDF File';
                }
                field(lastModifiedDateTime; Rec.SystemModifiedAt)
                {
                    Caption = 'SystemModifiedAt';
                }
                field(vatRegistrationNumber; Rec."VAT Registration No.")
                {
                    Caption = 'VAT Registration No.';
                }
                field(personNameINF; Rec."Person Name INF")
                {
                    Caption = 'Person Name';
                }
                field(personSurnameINF; Rec."Person Surname INF")
                {
                    Caption = 'Person Surname';
                }
                field(totalWithheldVATAmountINF; Rec."Total Withheld VAT Amount INF")
                {
                    Caption = 'Total Withheld VAT Amount INF';
                }
                field(paymentTermsCode; Rec."Payment Terms Code")
                {
                    Caption = 'Payment Terms Code';
                }
                field(paymentMethodCode; Rec."Payment Method Code")
                {
                    Caption = 'Payment Method Code';
                }
            }
        }
    }
}