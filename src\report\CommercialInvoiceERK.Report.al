report 60002 "Commercial Invoice ERK"
{
    ApplicationArea = All;
    Caption = 'Commercial Invoice';
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem("Company Information"; "Company Information")
        {
            CalcFields = Picture;

            column(PhoneNo_CompanyInformation; "Phone No.")
            {
            }
            column(VATRegistrationNo_CompanyInformation; "VAT Registration No.")
            {
            }
            column(TaxAreaCodeINF_CompanyInformation; "Tax Area Code INF")
            {
            }
            column(Picture_CompanyInformation; Picture)
            {
            }
            column(Address_CompanyInformation; Address)
            {
            }
            column(Address2_CompanyInformation; "Address 2")
            {
            }
            column(City_CompanyInformation; City)
            {
            }
            column(County_CompanyInformation; County)
            {
            }
            // column(HomePage_CompanyInformation; "Home Page")
            // {
            // }
            column(EMail_CompanyInformation; "E-Mail")
            {
            }
            column(FaxNo_CompanyInformation; "Fax No.")
            {
            }
        }
        dataitem(ExportHeaderERK; "Export Header ERK")
        {
            column(No; "No.")
            {
            }
            column(EExportNo_ExportHeaderERK; "E-Export No.")
            {
            }
            column(SalesPaymentTermsCode_ExportHeaderERK; "Sales Payment Terms Code")
            {
            }
            column(PaymentMethodDescription; ErkHoldingBasicFunctions.GetTranslatedPaymentTermsDescriptionFromCode("Sales Payment Terms Code", 'ENU'))
            {
            }
            column(BankAccountName_ExportHeaderERK; "Bank Account Name")
            {
            }
            column(BranchName_ExportHeaderERK; "Branch Name")
            {
            }
            column(IBAN_ExportHeaderERK; IBAN)
            {
            }
            column(SWIFTCode_ExportHeaderERK; "SWIFT Code")
            {
            }
            column(BlanketSalesOrderNo; "Blanket Sales Order No.")
            {
            }
            column(CurrencyExchangeRateACY; "Currency Exchange Rate (ACY)")
            {
            }
            column(SalesOrderNo; "Sales Order No.")
            {
            }
            column(CurrExchangeRateDateACY; "Curr. Exchange Rate Date (ACY)")
            {
            }
            column(Completed; Completed)
            {
            }
            column(LoadingDate; "Loading Date")
            {
            }
            column(TotalSalesAmount; "Total Sales Amount")
            {
            }
            column(DocumentDate; "Document Date")
            {
            }
            column(CustomerNo; "Customer No.")
            {
            }
            column(CustomerName; "Customer Name")
            {
            }
            column(BlanketOrderDate; "Blanket Order Date")
            {
            }
            column(SalesDueDate; "Sales Due Date")
            {
            }
            column(SalesShipmentMethodCode; "Sales Shipment Method Code")
            {
            }
            column(SalesPaymentMethodCode; "Sales Payment Method Code")
            {
            }
            column(SalesCurrencyCode; "Sales Currency Code")
            {
            }
            column(NotifyShiptoCode; "Notify Ship-to Code")
            {
            }
            column(ConsigneeShiptoCode; "Consignee Ship-to Code")
            {
            }
            column(NotifyShiptoName; "Notify Ship-to Name")
            {
            }
            column(ConsigneeShiptoName; "Consignee Ship-to Name")
            {
            }
            column(NotifyShiptoName2; "Notify Ship-to Name 2")
            {
            }
            column(ConsigneeShiptoName2; "Consignee Ship-to Name 2")
            {
            }
            column(NotifyShiptoAddress; "Notify Ship-to Address")
            {
            }
            column(ConsigneeShiptoAddress; "Consignee Ship-to Address")
            {
            }
            column(NotifyShiptoAddress2; "Notify Ship-to Address 2")
            {
            }
            column(ConsigneeShiptoAddress2; "Consignee Ship-to Address 2")
            {
            }
            column(NotifyShiptoCity; "Notify Ship-to City")
            {
            }
            column(ConsigneeShiptoCity; "Consignee Ship-to City")
            {
            }
            column(NotifyShiptoCounty; "Notify Ship-to County")
            {
            }
            column(ConsigneeShiptoCounty; "Consignee Ship-to County")
            {
            }
            column(NotifyShiptoCountryRegion; "Notify Ship-to Country/Region")
            {
            }
            column(ConsigneeShiptoCountry; "Consignee Ship-to Country")
            {
            }
            column(NoSeries; "No. Series")
            {
            }
            column(CountryofDeparture; ErkHoldingBasicFunctions.GetCountryRegionNameInCurrentLanguage("Country of Departure"))
            {
            }
            column(PortofDeparture; VoyageMangement.GetEntryExitDescriptionFromEntryExitCode("Port of Departure"))
            {
            }
            column(ShipName; "Ship Name")
            {
            }
            column(ShippingAgent; "Shipping Agent")
            {
            }
            column(CountryofArrival; ErkHoldingBasicFunctions.GetCountryRegionNameInCurrentLanguage("Country of Arrival"))
            {
            }
            column(PortofArrival; VoyageMangement.GetEntryExitDescriptionFromEntryExitCode("Port of Arrival"))
            {
            }
            column(TransportMethod; "Transport Method")
            {
            }
            column(EstimatedTimeofDelivery; "Estimated Time of Delivery")
            {
            }
            column(EstimatedTimeofDeparture; "Estimated Time of Departure")
            {
            }
            column(BillofLadingNo; "Bill of Lading No.")
            {
            }
            column(BookingNo; "Booking No.")
            {
            }
            column(LoadDescription; "Load Description")
            {
            }
            column(PostedSalesInvoiceNo; "Posted Sales Invoice No.")
            {
            }
            column(ExportInvoiceAmount; "Export Invoice Amount")
            {
            }
            column(BlanketSalesOrderAmount; "Blanket Sales Order Amount")
            {
            }
            column(CreationOrder; "Creation Order")
            {
            }
            column(ContainerQuantity; "Container Quantity")
            {
            }
            column(TotalNetWeight; ExportManagement.CalculateExportTotalNetWeight(ExportHeaderERK))
            {
            }
            column(TotalGrossWeight; ExportManagement.CalculateExportTotalGrossWeight(ExportHeaderERK))
            {
            }
            dataitem("Export Line ERK"; "Export Line ERK")
            {
                DataItemLink = "Document No." = field("No.");

                column(BlanketSalesOrderNo_ExportLineERK; "Blanket Sales Order No.")
                {
                }
                column(Brand_ExportLineERK; Brand)
                {
                }
                column(Specification; ExportManagement.GetSpecificationFromItemAndVariantCode("Item No.", "Variant Code"))
                {
                }
                column(UoMDescription; ErkHoldingBasicFunctions.GetTranslatedUnitOfMeasureDescription("Unit of Measure Code", 'ENU'))
                {
                }
                column(TariffNo_ExportLineERK; "Tariff No.")
                {
                }
                column(CountryRegionofOriginCode_ExportLineERK; "Country/Region of Origin Code")
                {
                }
                column(DocumentNo_ExportLineERK; "Document No.")
                {
                }
                column(ItemDescription_ExportLineERK; ExportManagement.GetItemTranslation("Item No.", "Variant Code", 'ENU'))
                {
                }
                column(ItemNo_ExportLineERK; "Item No.")
                {
                }
                column(LineNo_ExportLineERK; "Line No." / 10000)
                {
                }
                column(LoadQuantity_ExportLineERK; "Load Quantity")
                {
                }
                column(PurchaseCurrencyCode_ExportLineERK; "Purchase Currency Code")
                {
                }
                column(PurchaseDate_ExportLineERK; "Purchase Date")
                {
                }
                column(PurchaseDueDate_ExportLineERK; "Purchase Due Date")
                {
                }
                column(PurchaseLineAmount_ExportLineERK; "Purchase Line Amount")
                {
                }
                column(PurchaseReceiptNo_ExportLineERK; "Purchase Receipt No.")
                {
                }
                column(PurchaseShipmentMethodCode_ExportLineERK; "Purchase Shipment Method Code")
                {
                }
                column(Quantity_ExportLineERK; Quantity)
                {
                }
                column(SalesCurrencyCode_ExportLineERK; "Sales Currency Code")
                {
                }
                column(SalesLineAmount_ExportLineERK; "Sales Line Amount")
                {
                }
                column(UnitCost_ExportLineERK; "Unit Cost")
                {
                }
                column(UnitCostLCY_ExportLineERK; "Unit Cost (LCY)")
                {
                }
                column(UnitofMeasureCode_ExportLineERK; "Unit of Measure Code")
                {
                }
                column(UnitPrice_ExportLineERK; "Unit Price")
                {
                }
                column(UnitPriceLCY_ExportLineERK; "Unit Price (LCY)")
                {
                }
                column(VariantCode_ExportLineERK; "Variant Code")
                {
                }
                column(VendorName_ExportLineERK; "Vendor Name")
                {
                }
                column(VendorNo_ExportLineERK; "Vendor No.")
                {
                }
                trigger OnPreDataItem()
                begin
                end;
            }
            dataitem("Container Header ERK"; "Container Header ERK")
            {
                DataItemLink = "Export No." = field("No.");
                CalcFields = "Total Palette Quantity", "Total Box Quantity";

                column(TotalBoxQuantity_ContainerHeaderERK; "Total Box Quantity")
                {
                }
                column(TotalPaletteQuantity_ContainerHeaderERK; "Total Palette Quantity")
                {
                }
                column(No_ContainerHeaderERK; "No.")
                {
                }
                column(ConcanatedContainerNo; ConcanatedContainerNo)
                {
                }
                dataitem("Container Line ERK"; "Container Line ERK")
                {
                    DataItemLink = "Container No." = field("No."), "Export No." = field("Export No.");

                    column(NetWeightKG_ContainerLineERK; "Net Weight (KG)")
                    {
                    }
                    column(GrossWeightKG_ContainerLineERK; "Gross Weight (KG)")
                    {
                    }
                    column(BoxQuantity_ContainerLineERK; "Box Quantity")
                    {
                    }
                    column(PackageType_ContainerLineERK; "Package Type")
                    {
                    }
                    column(PackagingTypeCode_ContainerLineERK; ErkHoldingBasicFunctions.GetEDocumentPackageTypeNameFromCode("Packaging Type Code"))
                    {
                    }
                    column(ExportLineNo_ContainerLineERK; "Export Line No.")
                    {
                    }
                }
                trigger OnAfterGetRecord()
                begin
                    if "Container Header ERK".FindSet() then begin
                        repeat
                            ConcanatedContainerNo += "Container Header ERK"."No." + ', ';
                        until "Container Header ERK".Next() = 0;
                        ConcanatedContainerNo := CopyStr(ConcanatedContainerNo, 1, StrLen(ConcanatedContainerNo) - 2);
                    end;
                end;
            }
            dataitem("Sales Header"; "Sales Header")
            {
                DataItemLink = "No." = field("Sales Order No.");

                column(SelltoAddress_SalesHeader; "Sell-to Address")
                {
                }
                column(SelltoAddress2_SalesHeader; "Sell-to Address 2")
                {
                }
                column(SelltoCity_SalesHeader; "Sell-to City")
                {
                }
                column(SelltoCountryRegionCode_SalesHeader; "Sell-to Country/Region Code")
                {
                }
                column(SelltoCountryRegionName; ErkHoldingBasicFunctions.GetCountryRegionNameInCurrentLanguage("Sell-to Country/Region Code"))
                {
                }
                column(SelltoCounty_SalesHeader; "Sell-to County")
                {
                }
                column(SelltoCustomerName2_SalesHeader; "Sell-to Customer Name 2")
                {
                }
                column(SelltoPostCode_SalesHeader; "Sell-to Post Code")
                {
                }
            }
        }
    }
    var
        ExportManagement: Codeunit "Export Management ERK";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        ConcanatedContainerNo: Text;
}
