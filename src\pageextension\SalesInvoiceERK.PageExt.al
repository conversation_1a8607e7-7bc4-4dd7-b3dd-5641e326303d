pageextension 60063 "Sales Invoice ERK" extends "Sales Invoice"
{
    layout
    {
        addlast(General)
        {
            field("Created From Car Carrier ERK"; Rec."Created From Car Carrier ERK")
            {
                ApplicationArea = All;
                Editable = false;
            }
        }
    }
    actions
    {
        addfirst("&Invoice")
        {
            action("CombinedInvoiceLines ERK")
            {
                ApplicationArea = ErkPortERK;
                Caption = 'Combine Invoice Lines', Comment = 'TRK="Fatura Satırlarını Birleştir"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReverseLines;
                PromotedOnly = true;
                ToolTip = 'Executes the Combine Invoice Lines action.';

                trigger OnAction()
                begin
                    PortOperationManagement.CombineInvoiceLines(Rec);
                end;
            }
            action("CreateInternalVoucher ERK")
            {
                ApplicationArea = All;
                Caption = 'Create Internal Voucher', Comment = 'TRK="İç Dekont Oluştur"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostedVoucherGroup;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Internal Voucher action.';

                trigger OnAction()
                begin
                    InternalVoucherMngtERK.CreateInternalVoucher(Rec);
                end;
            }


        }
    }
    var
        PortOperationManagement: Codeunit "Port Operation Management ERK";
        InternalVoucherMngtERK: Codeunit "Internal Voucher Mngt. ERK";
}
