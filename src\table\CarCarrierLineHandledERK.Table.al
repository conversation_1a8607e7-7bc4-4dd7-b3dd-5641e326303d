table 60068 "Car Carrier Line Handled ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Car Carrier Line Handled';
    LookupPageId = "Car Carrier Line Handled ERK";
    DrillDownPageId = "Car Carrier Line Handled ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            TableRelation = "Car Carrier Header ERK"."No.";
            ToolTip = 'Specifies the value of the Document No. field.';
            AllowInCustomizations = Never;
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document Line No. field.';
            AllowInCustomizations = Never;
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Line No. field.';
            AllowInCustomizations = Never;
        }
        field(4; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            ToolTip = 'Specifies the value of the Customer No. field.';
        }

        field(5; Week; Integer)
        {
            Caption = 'Week';
            ToolTip = 'Specifies the value of the Week field.';
        }
        field(6; Month; Integer)
        {
            Caption = 'Month';
            ToolTip = 'Specifies the value of the Month field.';
        }
        field(7; Year; Integer)
        {
            Caption = 'Year';
            ToolTip = 'Specifies the value of the Year field.';
        }
        field(8; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(9; "Loading Port"; Code[10])
        {
            Caption = 'Loading Port';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Loading Port field.';
        }
        field(10; "Loading Port Description"; Text[100])
        {
            Caption = 'Loading Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Loading Port")));
            ToolTip = 'Specifies the value of the Loading Port Description field.';
        }
        field(11; "Discharge Port"; Code[10])
        {
            Caption = 'Discharge Port';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Discharge Port field.';
        }
        field(12; "Discharge Port Description"; Text[100])
        {
            Caption = 'Discharge Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Discharge Port")));
            ToolTip = 'Specifies the value of the Discharge Port Description field.';
        }
        field(13; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ship Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(14; "Total Sales (ACY)"; Decimal)
        {
            Caption = 'Total Sales (ACY)';
            ToolTip = 'Specifies the value of the Total Sales (ACY) field.';
        }
        field(15; "Total Profit (ACY)"; Decimal)
        {
            Caption = 'Total Profit (ACY)';
            ToolTip = 'Specifies the value of the Total Profit (ACY) field.';
        }
        field(16; Profitability; Decimal)
        {
            Caption = 'Profitability';
            ToolTip = 'Specifies the value of the Profitability field.';
        }

        field(17; Handled; Boolean)
        {
            Caption = 'Handled';
            ToolTip = 'Specifies the value of the Handled field.';
            AllowInCustomizations = Never;
        }
    }

    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
        }
    }

    internal procedure InsertNewRecord(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    var
    begin
        Clear(Rec);
        Rec.Init();
        Rec."Document No." := CarCarrierLineDetail."Document No.";
        Rec."Document Line No." := CarCarrierLineDetail."Document Line No.";
        Rec."Line No." := CarCarrierLineDetail."Line No.";
        Rec.Year := CarCarrierLineDetail.Year;
        Rec.Month := CarCarrierLineDetail.Month;
        Rec.Week := CarCarrierLineDetail.Week;
        Rec.Validate("Customer No.", CarCarrierLineDetail."Customer No.");
        CarCarrierLineDetail.CalcFields("Customer Name");
        Rec."Customer Name" := CarCarrierLineDetail."Customer Name";
        Rec.Validate("Loading Port", CarCarrierLineDetail."Loading Port");
        Rec.Validate("Discharge Port", CarCarrierLineDetail."Discharge Port");
        Rec."Total Sales (ACY)" := CarCarrierLineDetail."Total Sales (ACY)";
        Rec."Total Profit (ACY)" := CarCarrierLineDetail."Total Profit (ACY)";
        Rec.Profitability := CarCarrierLineDetail.Profitability;
        Rec.Handled := false;
        Rec.Insert(false);
    end;

}