query 60005 "Car Carrier Line Report ERK"
{
    APIGroup = 'erkHoldingCustomization';
    APIPublisher = 'infotek';
    APIVersion = 'v1.0';
    EntityName = 'carCarrierLineReport';
    EntitySetName = 'carCarrierLineReports';
    QueryType = API;
    Caption = 'Car Carrier Line Report';
    DataAccessIntent = ReadOnly;

    elements
    {
        dataitem(carCarrierLineReportERK;
        "Car Carrier Line Report ERK")
        {
            column(entryNo; "Entry No.")
            {
            }
            column(cost; Cost)
            {
            }
            column(duration; Duration)
            {
            }
            column(fillRateCubicMetres; "Fill Rate Cubic Metres")
            {
            }
            column(fillRateSquareMetres; "Fill Rate Square Metres")
            {
            }
            column(fromDate; "From Date")
            {
            }
            column(income; Income)
            {
            }
            column(loadCubicMetres; "Load Cubic Metres")
            {
            }
            column(loadSquareMetres; "Load Square Metres")
            {
            }
            column(netLoadM2; "Net Load(m2)")
            {
            }
            column(netLoadM3; "Net Load(m3)")
            {
            }
            column(portVoyage; "Port Voyage")
            {
            }
            column(profit; Profit)
            {
            }
            column(profitability; Profitability)
            {
            }
            column(shipName; "Ship Name")
            {
            }
            column(systemCreatedAt; SystemCreatedAt)
            {
            }
            column(systemCreatedBy; SystemCreatedBy)
            {
            }
            column(systemId; SystemId)
            {
            }
            column(systemModifiedAt; SystemModifiedAt)
            {
            }
            column(systemModifiedBy; SystemModifiedBy)
            {
            }
            column(toDate; "To Date")
            {
            }
            column(unloadCubicMetres; "Unload Cubic Metres")
            {
            }
            column(unloadSquareMetres; "Unload Square Metres")
            {
            }
            column(waitErk; Wait_Erk)
            {
            }
            column(waitOther; Wait_Other)
            {
            }
            column(cluster; Cluster)
            {
            }
        }
    }
    trigger OnBeforeOpen()
    begin
    end;
}
