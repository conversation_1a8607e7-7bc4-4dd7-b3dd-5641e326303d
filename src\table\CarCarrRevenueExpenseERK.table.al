table 60038 "Car Carr. Revenue/Expense ERK"
{
    Caption = 'Car Carrier Revenue/Expense';
    DataClassification = CustomerContent;
    DrillDownPageId = "Car Carr. Revenue/Expenses ERK";
    LookupPageId = "Car Carr. Revenue/Expenses ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
            TableRelation = "Car Carrier Header ERK"."No.";
            ToolTip = 'Specifies the value of the Document No. field.';
            // trigger OnValidate()
            // var
            //     CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
            //     NewLineNo: Integer;
            // begin
            //     if (Rec."Line No." <> 0) and (Rec."Document No." = '') then begin
            //         CarCarrRevenueExpense.SetRange("Document No.", Rec."Document No.");
            //         if CarCarrRevenueExpense.FindLast() then
            //             NewLineNo := CarCarrRevenueExpense."Line No." + 10000
            //         else
            //             NewLineNo := 10000;

            //         if NewLineNo <> Rec."Line No." then
            //             Rec.Rename(Rec."Document No.", NewLineNo);
            //     end;
            // end;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "No."; Code[20])
        {
            Caption = 'No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
                Ship: Record "Ship ERK";
                CarCarrierHeader: Record "Car Carrier Header ERK";
            begin
                Rec."Variant Code" := '';
                Rec.TestField(Type);
                Rec.Validate("Currency Code", '');
                if Item.Get("No.") then begin
                    Rec.Description := Item.Description;
                    Rec."UoM Code" := Item."Base Unit of Measure";
                    Rec."VAT Prod. Posting Group" := Item."VAT Prod. Posting Group";
                    ErkHoldingSetup.GetRecordOnce();
                    if Rec.Type = Rec.Type::Consumption then
                        if Rec."No." = ErkHoldingSetup."Hire Item No." then begin
                            CarCarrierHeader.Get(Rec."Document No.");
                            Ship.Get(CarCarrierHeader."Ship No.");
                            Rec.Validate("Unit Price/Cost", Ship."Contract Unit Cost" / 24);
                            Rec.Validate("Currency Code", Ship."Contract Currency Code");
                        end
                        else
                            Rec."Unit Price/Cost" := Item."Last Direct Cost";
                end else begin
                    Rec.Description := '';
                    Rec."UoM Code" := '';
                    Rec."VAT Prod. Posting Group" := '';
                end;
            end;
        }
        field(4; Type; Enum "Voyage Line Detail Type ERK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
            trigger OnValidate()
            begin
                ValidatePostingDateWithCreationDateFilter();

                Rec.TestField("Source No.", '');
                Rec.TestField("No.", '');
                case Rec.Type of
                    Rec.Type::Consumption:
                        begin
                            ErkHoldingSetup.GetRecordOnce();
                            ErkHoldingSetup.TestField("Consumption Customer No.");
                            Rec.Validate("Source No.", ErkHoldingSetup."Consumption Customer No.");
                        end;
                end;
            end;
        }
        field(6; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(7; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                if ItemVariant.Get("No.", "Variant Code") then
                    Rec.Description := ItemVariant.Description;
            end;
        }
        field(5; "UoM Code"; Code[10])
        {
            Caption = 'UoM Code';
            TableRelation = "Item Unit of Measure".Code where("Item No." = field("No."));
            ToolTip = 'Specifies the value of the UoM Code field.';
        }
        field(8; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", Quantity * "Unit Price/Cost");
            end;
        }
        field(9; "Unit Price/Cost"; Decimal)
        {
            Caption = 'Unit Price/Cost';
            ToolTip = 'Specifies the value of the Unit Price/Cost field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", Quantity * "Unit Price/Cost");
            end;
        }
        field(10; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Currency Code field.';
            trigger OnValidate()
            begin
                "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", "Line Amount");
            end;
        }
        field(11; "Line Amount"; Decimal)
        {
            Caption = 'Line Amount';
            Editable = false;
            ToolTip = 'Specifies the value of the Line Amount field.';
            trigger OnValidate()
            begin
                "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", "Line Amount");
            end;
        }
        field(12; "Line Amount (ACY)"; Decimal)
        {
            Caption = 'Line Amount (ACY)';
            Editable = false;
            ToolTip = 'Specifies the value of the Line Amount (ACY) field.';
        }
        field(13; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = if (Type = filter(Expense | "Expected Expense" | "Estimated Expense" | "Realized Expense - No Proforma")) Vendor
            else if (Type = filter(Revenue | Consumption | "Expected Revenue" | "Estimated Revenue")) Customer;
            ToolTip = 'Specifies the value of the Source No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
                Vendor: Record Vendor;
            begin
                case Type of
                    "Voyage Line Detail Type ERK"::Revenue,
                    "Voyage Line Detail Type ERK"::Consumption,
                    "Voyage Line Detail Type ERK"::"Expected Revenue",
                    "Voyage Line Detail Type ERK"::"Estimated Revenue":
                        begin
                            Customer.Get("Source No.");
                            Rec."Source Name" := Customer.Name;
                            Rec.Validate("Currency Code", Customer."Currency Code");
                        end;
                    "Voyage Line Detail Type ERK"::Expense, "Voyage Line Detail Type ERK"::"Expected Expense", "Voyage Line Detail Type ERK"::"Estimated Expense", "Voyage Line Detail Type ERK"::"Realized Expense - No Proforma":
                        begin
                            Vendor.Get("Source No.");
                            Rec."Source Name" := Vendor.Name;
                            Rec.Validate("Currency Code", Vendor."Currency Code");
                        end;
                end;
            end;
        }
        field(14; "Source Name"; Text[100])
        {
            Caption = 'Source Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Name field.';
        }
        field(15; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
            trigger OnValidate()
            var
                SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
                SerialNoACYUpdatedMsg: Label '%1 Serial Nos Amount (ACY) has been updated.', Comment = '%1=Count';
            begin
                "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", "Line Amount");

                Rec.CalcFields("Distrubuted Quantity");
                if ("Posting Date" <> 0D) and (Rec."Posting Date" <> xRec."Posting Date") and ("Distrubuted Quantity" <> 0) then begin
                    SerialNoRevenueExpense.SetRange("Document No.", Rec."Document No.");
                    SerialNoRevenueExpense.SetRange("Car Carrier Rev/Exp Line No.", Rec."Line No.");
                    SerialNoRevenueExpense.FindSet(true);
                    repeat
                        SerialNoRevenueExpense."Amount (ACY)" := ExportManagement.ConvertAmountToACY(Rec."Posting Date", SerialNoRevenueExpense."Currency Code", SerialNoRevenueExpense.Amount);
                        SerialNoRevenueExpense.Modify(true);
                    until SerialNoRevenueExpense.Next() = 0;
                    Message(SerialNoACYUpdatedMsg, SerialNoRevenueExpense.Count());
                end;
            end;
        }
        field(16; "External Document No."; Code[35])
        {
            Caption = 'External Document No.';
            ToolTip = 'Specifies the value of the External Document No. field.';
        }
        field(17; "Unposted Invoice No."; Code[20])
        {
            Caption = 'Unposted Invoice No.';
            ToolTip = 'Specifies the value of the Unposted Invoice No. field.';
            trigger OnLookup()
            var
                SalesHeader: Record "Sales Header";
                PurchaseHeader: Record "Purchase Header";
            begin
                if SalesHeader.Get(SalesHeader."Document Type"::Invoice, "Unposted Invoice No.") then
                    PageManagement.PageRun(SalesHeader);
                if PurchaseHeader.Get(PurchaseHeader."Document Type"::Invoice, "Unposted Invoice No.") then
                    PageManagement.PageRun(PurchaseHeader);
            end;
        }
        field(18; "Posted Invoice No."; Code[20])
        {
            Caption = 'Posted Invoice No.';
            ToolTip = 'Specifies the value of the Posted Invoice No. field.';
            trigger OnLookup()
            var
                SalesInvoiceHeader: Record "Sales Invoice Header";
                PurchInvHeader: Record "Purch. Inv. Header";
            begin
                if SalesInvoiceHeader.Get("Posted Invoice No.") then
                    PageManagement.PageRun(SalesInvoiceHeader);
                if PurchInvHeader.Get("Posted Invoice No.") then
                    PageManagement.PageRun(PurchInvHeader);
            end;
        }
        field(19; "Distrubuted Quantity"; Integer)
        {
            Caption = 'Distrubuted Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Serial No. Revenue/Expense ERK" where("Document No." = field("Document No."), "Car Carrier Rev/Exp Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Distrubuted Quantity field.';
        }
        field(20; "Port Code"; Code[10])
        {
            Caption = 'Port Code';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Port Code field.';
        }
        field(21; "Port Description"; Text[100])
        {
            Caption = 'Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Port Code")));
            ToolTip = 'Specifies the value of the Port Description field.';
        }
        field(22; Notes; Text[1024])
        {
            Caption = 'Notes';
            ToolTip = 'Specifies the value of the Notes field.';
        }
        field(23; "Your Reference"; Code[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
        }
        field(24; "Consumption Amount Calculated"; Boolean)
        {
            Caption = 'Consumption Amount Calculated';
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Consumption Amount Calculated field.';
        }
        field(25; "Source Proforma Line No."; Integer)
        {
            Caption = 'Source Proforma Line No.';
            AllowInCustomizations = Always;
            Editable = false;
            ToolTip = 'Specifies the value of the Source Proforma Line No. field.';
        }
        field(26; "Source Car Carrier Order No."; Code[20])
        {
            Caption = 'Source Car Carrier Order No.';
            TableRelation = "Car Carrier Order Header ERK"."No.";
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Source Car Carrier Order No. field.';
        }
        field(27; "Source Car Carr.Order Line No."; Integer)
        {
            Caption = 'Source Car Carrier Order Line No.';
            TableRelation = "Car Carrier Order Line ERK"."Line No." where("Document No." = field("Source Car Carrier Order No."));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Source Car Carrier Order Line No. field.';
        }
        field(28; "Ship No."; Code[50])
        {
            Caption = 'Ship No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ship No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship No. field.';
        }
        field(29; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ship Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(30; "Car Carrier Starting Date-Time"; DateTime)
        {
            Caption = 'Car Carrier Starting Date-Time';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Starting Date-Time" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Car Carrier Starting Date-Time field.';
        }
        field(31; "Car Carrier Ending Date-Time"; DateTime)
        {
            Caption = 'Car Carrier Ending Date-Time';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ending Date-Time" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Car Carrier Ending Date-Time field.';
        }
        field(32; "Source Realized Line No."; Integer)
        {
            Caption = 'Source Realized Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Realized Line No. field.';
        }
        field(33; Status; Enum "Car Carrier Rev./Exp. Status")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
        }
        field(34; "Planned Starting Date"; Date)
        {
            Caption = 'Planned Starting Date';
            ToolTip = 'Specifies the planned starting date of the car carrier.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Planned Starting Date" where("No." = field("Document No.")));
        }
        field(35; "Planned Ending Date"; Date)
        {
            Caption = 'Planned Ending Date';
            ToolTip = 'Specifies the planned ending date of the car carrier.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Planned Ending Date" where("No." = field("Document No.")));
        }
        field(99; "VAT Prod. Posting Group"; Code[20])
        {
            Caption = 'VAT Prod. Posting Group';
            TableRelation = "VAT Product Posting Group";
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        CarCarrRevenueExpense.SetRange("Document No.", Rec."Document No.");
        if CarCarrRevenueExpense.FindLast() then
            Rec."Line No." := CarCarrRevenueExpense."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnModify()
    begin
        ValidatePostingDateWithCreationDateFilter();

        Rec.TestField("Unposted Invoice No.", '');
        Rec.TestField("Posted Invoice No.", '');

        case true of
            Rec.Type = Rec.Type::" ",
            Rec."Source No." = '',
            Rec."No." = '',
            Rec.Quantity = 0,
            Rec."UoM Code" = '',
            Rec."Unit Price/Cost" = 0,
            Rec."Line Amount" = 0,
            Rec."Line Amount (ACY)" = 0:
                Rec.Validate(Status, Rec.Status::Draft);
            else
                Rec.Validate(Status, Rec.Status::Ready);
        end;
    end;

    trigger OnDelete()
    begin
        ValidatePostingDateWithCreationDateFilter();

        Rec.CalcFields("Distrubuted Quantity");
        Rec.TestField("Distrubuted Quantity", 0);
        Rec.TestField("Unposted Invoice No.", '');
        Rec.TestField("Posted Invoice No.", '');
    end;

    local procedure ValidatePostingDateWithCreationDateFilter()
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
        CarrierDateBeforeSetupDateErr: Label 'You cannot create, modify or delete a record for a completed voyage before %1.', Comment = '%1 = Date Filter for Rev./Exp.';
    begin
        ErkHoldingSetup.GetRecordOnce();

        // If Creation Date Filter is empty, no validation is needed
        if ErkHoldingSetup."Date Filter for Rev./Exp." = 0D then
            exit;

        // Get the Car Carrier Header to access the Ending Date-Time
        if not CarCarrierHeader.Get(Rec."Document No.") then
            exit;

        // Only validate for Completed voyages
        if CarCarrierHeader.Status <> CarCarrierHeader.Status::Completed then
            exit;

        // Check if Car Carrier's Ending Date-Time is not before the setup date
        if CarCarrierHeader."Ending Date-Time" <> 0DT then
            if CarCarrierHeader."Ending Date-Time".Date() < ErkHoldingSetup."Date Filter for Rev./Exp." then
                Error(CarrierDateBeforeSetupDateErr, ErkHoldingSetup."Date Filter for Rev./Exp.");
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        ExportManagement: Codeunit "Export Management ERK";
        PageManagement: Codeunit "Page Management";
}
