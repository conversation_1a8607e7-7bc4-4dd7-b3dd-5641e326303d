page 60127 "VTLE - <PERSON><PERSON>a ERK"
{
    ApplicationArea = All;
    Caption = 'VTLE - Yalova';
    PageType = List;
    SourceTable = "Vehicle Transfer Ledger Entry";
    UsageCategory = Lists;
    Editable = false;
    SourceTableView = where("From Location Code" = filter('YALOVA 1|YALOVA 2'));

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                // field("Entry No."; Rec."Entry No.")
                // {
                // }
                field("From Location Code"; Rec."From Location Code")
                {
                }
                field("From Bin Code"; Rec."From Bin Code")
                {
                }
                field("To Location Code"; Rec."To Location Code")
                {
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                }
                field("Operation Date-Time"; Rec."Operation Date-Time")
                {
                }
                field("Operation Type"; Rec."Operation Type")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                // field("Document No."; Rec."Document No.")
                // {
                // }
                // field("Document Line No."; Rec."Document Line No.")
                // {
                // }
                field("From Bin Description"; Rec."From Bin Description")
                {
                }
                field("To Bin Description"; Rec."To Bin Description")
                {
                }
                // field("Shipping Agent Code"; Rec."Shipping Agent Code")
                // {
                // }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the System Created At field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the System Created By field.';
                }
            }
        }
    }
}