table 60007 "Container Line ERK"
{
    Caption = 'Container Line';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Export No."; Code[20])
        {
            Caption = 'Export No.';
            AllowInCustomizations = Never;
        }
        field(2; "Container No."; Code[20])
        {
            Caption = 'Container No.';
            AllowInCustomizations = Never;
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Never;
        }
        field(4; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                Rec.TestField("Container No.");
                if Item.Get("Item No.") then begin
                    Description := Item.Description;
                    "Unit of Measure Code" := Item."Base Unit of Measure";
                end;
            end;
        }
        field(5; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            Editable = false;
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                if ItemVariant.Get("Item No.", "Variant Code") then
                    Description := ItemVariant.Description;
            end;
        }
        field(6; Description; Text[100])
        {
            Caption = 'Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(7; Specification; Code[10])
        {
            Caption = 'Specification';
            TableRelation = "Packaging Type ERK" where(Factor = filter('>0'));
            ToolTip = 'Specifies the value of the Specification field.';
        }
        field(8; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(9; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(10; "Box Quantity"; Integer)
        {
            Caption = 'Box Quantity';
            ToolTip = 'Specifies the value of the Box Quantity field.';
            trigger OnValidate()
            var
                PackagingType: Record "Packaging Type ERK";
            begin
                PackagingType.Get(Specification);
                "Net Weight (KG)" := "Box Quantity" * PackagingType.Factor;
                if Rec."Unit of Measure Code" = 'KG' then
                    Quantity := "Net Weight (KG)";
            end;
        }
        field(11; "Palette Quantity"; Integer)
        {
            Caption = 'Palette Quantity';
            ToolTip = 'Specifies the value of the Palette Quantity field.';
        }
        field(12; "Package Type"; Code[10])
        {
            Caption = 'Package Type';
            Editable = false;
            AllowInCustomizations = Never;
        }
        field(13; "Gross Weight (KG)"; Decimal)
        {
            Caption = 'Gross Weight (KG)';
            ToolTip = 'Specifies the value of the Gross Weight field.';
        }
        field(14; "Net Weight (KG)"; Decimal)
        {
            Caption = 'Net Weight (KG)';
            ToolTip = 'Specifies the value of the Net Weight (KG) field.';
        }
        field(15; "Packaging Type Code"; Code[10])
        {
            Caption = 'Packaging Type Code';
            TableRelation = "E-Document Packaging Type INF";
            ToolTip = 'Specifies the value of the Packaging Type Code field.';
        }
        field(16; "Export Line No."; Integer)
        {
            Caption = 'Export Line No.';
            AllowInCustomizations = Never;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Export Line ERK"."Line No." where("Item No." = field("Item No."), "Variant Code" = field("Variant Code")));
        }
    }
    keys
    {
        key(PK; "Export No.", "Container No.", "Line No.")
        {
            Clustered = true;
            SumIndexFields = "Box Quantity";
        }
    }
}
