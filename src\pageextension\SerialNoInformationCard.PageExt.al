pageextension 60030 "Serial No. Information Card" extends "Serial No. Information Card"
{
    layout
    {
        modify(Inventory)
        {
            Visible = false;
        }
        addlast(content)
        {
            group("VehicleInformation ERK")
            {
                Caption = 'Vehicle Information';

                field("Current Location Code ERK"; Rec."Current Location Code ERK")
                {
                    ApplicationArea = All;
                }
                field("Current Bin Code ERK"; Rec."Current Bin Code ERK")
                {
                    ApplicationArea = All;
                }
                field("Colour Name ERK"; Rec."Colour Name ERK")
                {
                    ApplicationArea = All;
                }
                field("Customs Dec. Line No. ERK"; Rec."Customs Dec. Line No. ERK")
                {
                    ApplicationArea = All;
                }
                field("Customs Declaration No. ERK"; Rec."Customs Declaration No. ERK")
                {
                    ApplicationArea = All;
                }
                field("Customs Registration Date ERK"; Rec."Customs Registration Date ERK")
                {
                    ApplicationArea = All;
                }
                field("Summary Declaration No. ERK"; Rec."Summary Declaration No. ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Summary Declaration No. field.';
                }
                field("Bill of Lading No. ERK"; Rec."Bill of Lading No. ERK")
                {
                    ApplicationArea = All;
                }
                field("Engine ID ERK"; Rec."Engine ID ERK")
                {
                    ApplicationArea = All;
                }
                field("Footprint (m2) ERK"; Rec."Footprint (m2) ERK")
                {
                    ApplicationArea = All;
                }
                field("Volume (m3) ERK"; Rec."Volume (m3) ERK")
                {
                    ApplicationArea = All;
                }
                field("Fuel Type ERK"; Rec."Fuel Type ERK")
                {
                    ApplicationArea = All;
                }
                field("Gross Weight (KG) ERK"; Rec."Gross Weight (KG) ERK")
                {
                    ApplicationArea = All;
                }
                field("Manufacturer Code ERK"; Rec."Brand Code ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Manufacturer Code field.';
                }
                field("Model Group ERK"; Rec."Model Code ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Model Group field.';
                }
                field("Model Version ERK"; Rec."Model Version ERK")
                {
                    ApplicationArea = All;
                }
                field("TSE ERK"; Rec."TSE ERK")
                {
                    ApplicationArea = All;
                }
                field("Nav. Process Required ERK"; Rec."Nav. Process Required ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Nav. Process Required field.';
                }
                field("Nav Upload Succesful ERK"; Rec."Nav Upload Succesful ERK")
                {
                    ApplicationArea = All;
                }
                field("Commercial Blockage ERK"; Rec."Commercial Blockage ERK")
                {
                    ApplicationArea = All;
                }
                field("Car Carrier Ledger Entries ERK"; Rec."Car Carrier Ledger Entries ERK")
                {
                    ApplicationArea = All;
                }
                field("Vehicle Trns Ledg. Entries ERK"; Rec."Vehicle Trns Ledg. Entries ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Vehicle Trns Ledg. Entries field.';
                }
                field("Temp. Traffic Document No. ERK"; Rec."Temp. Traffic Document No. ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Temp. Traffic Document No. field.';
                }
                field("Temp. License Plate No. ERK"; Rec."Temp. License Plate No. ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Temp. License Plate No. field.';
                }
                field("Temp. Driver Full Name ERK"; Rec."Temp. Driver Full Name ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Temp. Driver Full Name field.';
                }
            }
            group("GrupageInfo ERK")
            {
                Caption = 'Grupage Information';

                field("Grupage No. ERK"; Rec."Grupage No. ERK")
                {
                    ApplicationArea = All;
                }
                field("Grupage Date ERK"; Rec."Grupage Date ERK")
                {
                    ApplicationArea = All;
                }
                field("Truck Plate ERK"; Rec."Truck Plate ERK")
                {
                    ApplicationArea = All;
                }
                field("Trailer Plate ERK"; Rec."Trailer Plate ERK")
                {
                    ApplicationArea = All;
                }
                field("Driver Full Name ERK"; Rec."Driver Full Name ERK")
                {
                    ApplicationArea = All;
                }
                field("Grupage Location Code ERK"; Rec."Grupage Location Code ERK")
                {
                    ApplicationArea = All;
                }
                field("Grupage Bin Code ERK"; Rec."Grupage Bin Code ERK")
                {
                    ApplicationArea = All;
                }
                field("Grupage Ship-to Name ERK"; Rec."Grupage Ship-to Name ERK")
                {
                    ApplicationArea = All;
                }
                field("Grupage Ship-to Address ERK"; Rec."Grupage Ship-to Address ERK")
                {
                    ApplicationArea = All;
                }
                field("Grupage Ship-to City ERK"; Rec."Grupage Ship-to City ERK")
                {
                    ApplicationArea = All;
                }
                field("Print Grupage Label ERK"; Rec."Print Grupage Label ERK")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Print Grupage Label field.';
                }
            }
        }
        addfirst(factboxes)
        {
            part("Attached Documents"; "Doc. Attachment List Factbox")
            {
                Caption = 'Attachments';
                ApplicationArea = All;
                SubPageLink = "Table ID" = const(Database::"Serial No. Information"), "No." = field("Serial No.");
            }
        }
    }
    actions
    {
        addfirst(processing)
        {
            action("Print Temporary Traffic Document ERK")
            {
                ApplicationArea = All;
                Caption = 'Print Temporary Traffic Document';
                Image = Print;
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Print Temporary Traffic Document action.';
                trigger OnAction()
                begin
                    Rec.SetRecFilter();
                    Report.Run(Report::"Temporary Traffic Document ERK", true, true, Rec);
                end;
            }
            action("Attachments ERK")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                Image = Attach;
                ToolTip = 'Add a file as an attachment. You can attach images as well as documents.';

                trigger OnAction()
                var
                    DocumentAttachmentDetails: Page "Document Attachment Details";
                    RecRef: RecordRef;
                begin
                    RecRef.GetTable(Rec);
                    DocumentAttachmentDetails.OpenForRecRef(RecRef);
                    DocumentAttachmentDetails.RunModal();
                end;
            }
        }
    }
}
