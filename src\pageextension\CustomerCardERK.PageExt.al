pageextension 60014 "Customer Card ERK" extends "Customer Card"
{
    layout
    {
        addafter("Salesperson Code")
        {
            field("Salesperson Name ERK"; Rec."Salesperson Name ERK")
            {
                ApplicationArea = All;
            }
        }
        addlast(content)
        {
            group("Erk ERK")
            {
                Caption = 'Erk';

                field("Bank Account No. ERK"; Rec."Company Bank Account Code ERK")
                {
                    ApplicationArea = All;
                }
                field("Bank Account Name ERK"; Rec."Company Bank Account Name ERK")
                {
                    ApplicationArea = All;
                }
                field("Erk Holding Intercompany ERK"; Rec."Erk Holding Intercompany ERK")
                {
                    ApplicationArea = All;
                }
                field("Transfer Reverse Amount ERK"; Rec."Transfer Reverse Amount ERK")
                {
                    ApplicationArea = All;
                }
            }
        }
        addafter("Tax Area Code INF")
        {
            field("Tax Area Description ERK"; Rec."Tax Area Description ERK")
            {
                ApplicationArea = All;
            }
        }
        modify("Tax Area Code INF")
        {
            trigger OnAfterValidate()
            begin
                CurrPage.Update();
            end;
        }
        modify(Name)
        {
            ShowMandatory = true;
        }
        modify(Address)
        {
            ShowMandatory = true;
        }
        modify(City)
        {
            ShowMandatory = true;
        }
        // modify("Phone No.")
        // {
        //     ShowMandatory = true;
        // }
        modify("Customer Posting Group")
        {
            ShowMandatory = true;
        }
        modify("Payment Terms Code")
        {
            ShowMandatory = true;
        }
        modify("Country/Region Code")
        {
            ShowMandatory = true;
        }
        // modify("Payment Method Code")
        // {
        //     ShowMandatory = true;
        // }
        modify("VAT Registration No.")
        {
            ShowMandatory = true;
        }
        modify("Gen. Bus. Posting Group")
        {
            ShowMandatory = true;
        }
        modify("Post Code")
        {
            ShowMandatory = true;
        }
        modify(County)
        {
            ShowMandatory = true;
        }
        // modify("E-Mail")
        // {
        //     ShowMandatory = true;
        // }
        modify("Tax Area Code")
        {
            ShowMandatory = true;
        }
        modify("VAT Bus. Posting Group")
        {
            ShowMandatory = true;
        }
        modify("Partner Type")
        {
            ShowMandatory = true;
        }
        modify("E-Invoice Profile ID INF")
        {
            ShowMandatory = true;
        }
    }
}
