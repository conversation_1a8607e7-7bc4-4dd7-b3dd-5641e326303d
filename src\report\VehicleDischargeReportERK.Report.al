report 60007 "Vehicle Discharge Report ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle Discharge Report';
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem(VehicleTransferLedgerEntry; "Vehicle Transfer Ledger Entry")
        {
            DataItemTableView = where("Operation Type" = const(Discharge));
            RequestFilterFields = "Operation Date-Time", "Document No.";

            column(SerialNo; "Serial No.")
            {
            }
            column(CustomerName; CustomerName)
            {
            }
            column(ShipperName; ShipperName)
            {
            }
            column(ConsigneeName; ConsigneeName)
            {
            }
            column(BillToCustomerName; BillToCustomerName)
            {
            }
            dataitem("Serial No. Information"; "Serial No. Information")
            {
                DataItemLink = "Serial No." = field("Serial No.");

                column(Gross_Weight__KG__ERK; "Gross Weight (KG) ERK")
                {
                }
                column(Customs_Declaration_No__ERK; "Customs Declaration No. ERK")
                {
                }
                column(Customs_Dec__Line_No__ERK; "Customs Dec. Line No. ERK")
                {
                }
                column(Customs_Registration_Date_ERK; "Customs Registration Date ERK")
                {
                }
            }
            trigger OnAfterGetRecord()
            begin
                PopulateAdditionalInformationFromVehicleTransferLedgerEntry();
            end;
        }
    }
    local procedure PopulateAdditionalInformationFromVehicleTransferLedgerEntry()
    var
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
    begin
        if VehicleTransferHeader.Get(VehicleTransferLedgerEntry."Document No.") then begin
            CarCarrierLedgerEntry.SetRange("Document No.", VehicleTransferHeader."Car Carrier No.");
            CarCarrierLedgerEntry.SetRange("Serial No.", VehicleTransferLedgerEntry."Serial No.");
            if CarCarrierLedgerEntry.FindFirst() then
                if CarCarrierLineDetail.Get(CarCarrierLedgerEntry."Document No.", CarCarrierLedgerEntry."Document Line No.", CarCarrierLedgerEntry."Document Line Detail No.") then begin
                    CarCarrierLineDetail.CalcFields("Customer Name", "Shipper Name", "Consignee Name", "Bill-to Customer Name");
                    CustomerName := CarCarrierLineDetail."Customer Name";
                    ShipperName := CarCarrierLineDetail."Shipper Name";
                    ConsigneeName := CarCarrierLineDetail."Consignee Name";
                    BillToCustomerName := CarCarrierLineDetail."Bill-to Customer Name";
                end;
        end;
    end;

    var
        ShipperName: Text[100];
        ConsigneeName: Text[100];
        CustomerName: Text[100];
        BillToCustomerName: Text[100];
    // requestpage
    // {
    //     layout
    //     {
    //         area(content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(processing)
    //         {
    //         }
    //     }
    // }
}
