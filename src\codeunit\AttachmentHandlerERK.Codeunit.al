codeunit 60028 "Attachment Handler ERK"
{
    procedure OpenAttachmentsForSerialNo(SerialNo: Code[50]): Boolean
    var
        SerialNoInformation: Record "Serial No. Information";
        DocumentAttachmentDetails: Page "Document Attachment Details";
        RecRef: RecordRef;
        NoSerialNoFoundMsg: Label 'No Serial No. Information record found for Serial No. %1.', Comment = '%1 = Serial No.';
    begin
        if SerialNo = '' then
            exit(false);

        SerialNoInformation.SetRange("Serial No.", SerialNo);
        if not SerialNoInformation.FindFirst() then begin
            Message(NoSerialNoFoundMsg, SerialNo);
            exit(false);
        end;

        RecRef.GetTable(SerialNoInformation);
        DocumentAttachmentDetails.OpenForRecRef(RecRef);
        DocumentAttachmentDetails.RunModal();

        exit(true);
    end;

    procedure ShowSerialNoAttachmentDialog()
    var
        SerialNoEntryDialog: Page "Serial No. Entry Dialog ERK";
        SerialNo: Code[50];
    begin
        if SerialNoEntryDialog.RunModal() = Action::OK then begin
            SerialNo := SerialNoEntryDialog.GetSerialNo();
            if SerialNo <> '' then
                OpenAttachmentsForSerialNo(SerialNo);
        end;
    end;
}