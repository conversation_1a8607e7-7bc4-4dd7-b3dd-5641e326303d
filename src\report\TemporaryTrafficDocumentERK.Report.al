report 60015 "Temporary Traffic Document ERK"
{
    ApplicationArea = All;
    Caption = 'Temporary Traffic Document';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(SerialNoInformation; "Serial No. Information")
        {
            column(SerialNo; "Serial No.")
            {
            }
            column(EngineIDERK; "Engine ID ERK")
            {
            }
            column(BrandCodeERK; "Brand Code ERK")
            {
            }
            column(ModelCodeERK; "Model Code ERK")
            {
            }
            column(ModelVersionERK; "Model Version ERK")
            {
            }
            column(ColourNameERK; "Colour Name ERK")
            {
            }
            column(GrossWeightKGERK; "Gross Weight (KG) ERK")
            {
            }
            column(TempDriverFullNameERK_SerialNoInformation; "Temp. Driver Full Name ERK")
            {
            }
            column(TempLicensePlateNoERK_SerialNoInformation; "Temp. License Plate No. ERK")
            {
            }
            column(TempTrafficDocumentNoERK_SerialNoInformation; "Temp. Traffic Document No. ERK")
            {
            }
            dataitem(DriverInformationERK; "Driver Information ERK")
            {
                DataItemLink = "Driver Full Name" = field("Temp. Driver Full Name ERK");

                column(DocumentNo_DriverInformationERK; "Document No.")
                {
                }
                column(ValidUntil_DriverInformationERK; "Document Date")
                {
                }
            }
            dataitem("Temporary License Plate ERK"; "Temporary License Plate ERK")
            {
                DataItemLink = "No." = field("Temp. License Plate No. ERK");

                column(VoucherNo_TemporaryLicensePlateERK; "Voucher No.")
                {
                }
                column(EndDate_TemporaryLicensePlateERK; "End Date")
                {
                }
                column(StartDate_TemporaryLicensePlateERK; "Start Date")
                {
                }
                column(PolicyNo_TemporaryLicensePlateERK; "Policy No.")
                {
                }
                column(PolicyStartDate_TemporaryLicensePlateERK; "Policy Start Date")
                {
                }
                column(PolicyEndDate_TemporaryLicensePlateERK; "Policy End Date")
                {
                }
            }
        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(Processing)
    //         {
    //         }
    //     }
    // }
}