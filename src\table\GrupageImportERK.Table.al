table 60054 "Grupage Import ERK"
{
    Caption = 'Grupage Import';
    DataClassification = CustomerContent;
    LookupPageId = "Grupage Import Worksheet ERK";
    DrillDownPageId = "Grupage Import Worksheet ERK";

    fields
    {
        field(1; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            NotBlank = false;
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(2; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(3; "Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Operation Type';
            InitValue = "Grupage Dealer Dispatch";
            ToolTip = 'Specifies the value of the Operation Type field.';
        }
        field(4; "To Location Code"; Code[10])
        {
            Caption = 'To Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the To Location Code field.';
        }
        field(5; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("To Location Code"));
            ToolTip = 'Specifies the value of the To Bin Code field.';
        }
        field(6; "Shipping Agent Code"; Code[10])
        {
            Caption = 'Shipping Agent Code';
            TableRelation = "Shipping Agent".Code;
            ToolTip = 'Specifies the shipping agent responsible for transporting the vehicle.';
        }
        field(7; "Grupage Date"; Date)
        {
            Caption = 'Grupage Date';
            ToolTip = 'Specifies the date of the Grupage shipment.';
        }
        field(8; "Grupage Ship-to Name"; Text[100])
        {
            Caption = 'Grupage Ship-to Name';
            ToolTip = 'Specifies the name of the Grupage shipment recipient.';
        }
        field(9; "Grupage Ship-to City"; Code[35])
        {
            Caption = 'Grupage Ship-to City';
            TableRelation = "Post Code".City;
            ToolTip = 'Specifies the city of the Grupage shipment destination.';
        }
        field(10; "Grupage Ship-to Address"; Text[250])
        {
            Caption = 'Grupage Ship-to Address';
            ToolTip = 'Specifies the address of the Grupage shipment destination.';
        }
    }
    keys
    {
        key(PK; "Serial No.")
        {
            Clustered = true;
        }
    }
}
