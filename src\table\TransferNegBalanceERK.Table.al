table 60075 "Transfer Neg. Balance ERK"
{
    Caption = 'Transfer Negative Balance Setup';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "Customer Balance Account No."; Code[20])
        {
            Caption = 'Customer Balance Account No.';
            TableRelation = "G/L Account";
            ToolTip = 'Specifies the G/L account for transferring negative customer balances.';
        }
        field(3; "Vendor Balance Account No."; Code[20])
        {
            Caption = 'Vendor Balance Account No.';
            TableRelation = "G/L Account";
            ToolTip = 'Specifies the G/L account for transferring negative vendor balances.';
        }
        field(4; "Journal Template Name"; Code[10])
        {
            Caption = 'Journal Template Name';
            TableRelation = "Gen. Journal Template";
            ToolTip = 'Specifies the journal template for transfer entries.';
        }
        field(5; "Journal Batch Name"; Code[10])
        {
            Caption = 'Journal Batch Name';
            TableRelation = "Gen. Journal Batch".Name where("Journal Template Name" = field("Journal Template Name"));
            ToolTip = 'Specifies the journal batch for transfer entries.';
        }
    }

    keys
    {
        key(Key1; "Primary Key")
        {
            Clustered = true;
        }
    }
}