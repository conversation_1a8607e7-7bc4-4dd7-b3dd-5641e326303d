codeunit 60009 "PDI Management ERK"
{
    procedure CreatePDILinesFromPDICheckList(PDIHeader: Record "PDI Header ERK")
    var
        PDILine: Record "PDI Line ERK";
        PDICheckList: Record "PDI Check List ERK";
    begin
        PDICheckList.FindSet(false);
        repeat
            PDILine.Init();
            PDILine."Document No." := PDIHeader."No.";
            PDILine.Insert(true);
            PDILine.Code := PDICheckList.Code;
            PDILine.Description := PDICheckList.Description;
            PDILine."Parent Code" := PDICheckList."Parent Code Value";
            if PDICheckList."Parent Code" then
                PDILine.Indent := 0
            else
                PDILine.Indent := 1;
            PDILine.Result := PDICheckList."Default Result";
            PDILine.Modify(false);
        until PDICheckList.Next() = 0;
    end;

    procedure CreatePDIDocument(VehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        PDIHeader: Record "PDI Header ERK";
    begin
        if not ShouldCreatePDIDocument(VehicleTransferLine) then
            exit;

        PDIHeader.Init();
        PDIHeader.Insert(true);
        PDIHeader.Validate("Serial No.", VehicleTransferLine."Serial No.");
        PDIHeader.Modify(false);

        CreatePDILinesFromPDICheckList(PDIHeader);
    end;

    procedure ShouldCreatePDIDocument(VehicleTransferLine: Record "Vehicle Transfer Line ERK"): Boolean
    var
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        Location: Record Location;
        Bin: Record Bin;
    begin
        if not VehicleTransferHeader.Get(VehicleTransferLine."Document No.") then
            exit(false);

        if VehicleTransferHeader."Operation Type" <> VehicleTransferLine."Operation Type"::"PDI Entry" then
            exit(false);

        if not VehicleTransferLine.Processed then
            exit(false);

        Location.Get(VehicleTransferLine."To Location Code");
        if not Location."Create PDI Document ERK" then
            exit(false);

        Bin.Get(VehicleTransferLine."To Location Code", VehicleTransferLine."To Bin Code");
        if not Bin."PDI Bin ERK" then
            exit(false);

        exit(true);
    end;

    procedure SavePDIDocument(var PDIHeader: Record "PDI Header ERK")
    var
        PDILine: Record "PDI Line ERK";
        OpenLinesExistErr: Label 'PDI Document cannot be closed because there are still open PDI Lines.';
    begin
        if PDIHeader.Saved then
            exit; // Already saved, nothing to do

        PDIHeader.TestField("Responsible Name");
        PDIHeader.TestField("Line No.");
        PDIHeader.TestField("Brand Code");
        PDIHeader.TestField("Model Code");
        PDIHeader.TestField("Color Name");
        PDIHeader.TestField("Fuel Type");
        PDIHeader.TestField("To Bin Code");

        PDILine.SetRange("Document No.", PDIHeader."No.");
        PDILine.SetFilter(Result, '%1', '');
        if not PDILine.IsEmpty() then
            Error(OpenLinesExistErr);

        PDIHeader.Validate(Saved, true);
        PDIHeader.Modify(true);
    end;

    procedure CreateAndSendPDIExitVehicleTransferDocument(PDIHeader: Record "PDI Header ERK")
    var
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        SerialNoInformation: Record "Serial No. Information";
    begin
        SerialNoInformation.SetRange("Serial No.", PDIHeader."Serial No.");
        SerialNoInformation.FindFirst();
        if SerialNoInformation."Current Bin Code ERK" = PDIHeader."To Bin Code" then
            exit;

        VehicleTransferHeader.Init();
        VehicleTransferHeader.Insert(true);
        VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::"PDI Exit");
        VehicleTransferHeader.Validate("To Location Code", PDIHeader."Location Code");
        VehicleTransferHeader.Validate("To Bin Code", PDIHeader."To Bin Code");
        VehicleTransferHeader.Validate("Created From PDI Document", true);
        VehicleTransferHeader.Modify(true);

        VehicleTransferHeader.Validate("Serial No.", PDIHeader."Serial No.");

        Message('Vehicle has been sent to %1', VehicleTransferHeader."To Bin Code");
    end;
}
