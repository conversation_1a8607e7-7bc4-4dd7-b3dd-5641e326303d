codeunit 60014 "Document Attachment Mngmt. ERK"
{
    SingleInstance = true;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Document Attachment Mgmt", OnAfterTableHasNumberFieldPrimaryKey, '', false, false)]
    local procedure C_DocumentAttachmentMgmt_OnAfterTableHasNumberFieldPrimaryKey(TableNo: Integer; var Result: Boolean; var FieldNo: Integer)
    begin
        case TableNo of
            Database::"Car Carrier Header ERK":
                begin
                    FieldNo := 1;
                    Result := true;
                end;
            Database::"Serial No. Information":
                begin
                    FieldNo := 3;
                    Result := true;
                end;
            Database::"Car Carrier Line Detail ERK":
                begin
                    FieldNo := 1;
                    Result := true;
                end;
            Database::"Car Carrier Order Header ERK":
                begin
                    FieldNo := 1;
                    Result := true;
                end;
            Database::"Car Carrier Order Line ERK":
                begin
                    FieldNo := 1;
                    Result := true;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Document Attachment Mgmt", OnAfterTableHasDocTypePrimaryKey, '', false, false)]
    local procedure C_DocumentAttachmentMgmt_OnAfterTableHasDocTypePrimaryKey(TableNo: Integer; var Result: Boolean; var FieldNo: Integer)
    begin
        case TableNo of
        // If you have any tables with document type as part of their primary key, add them here
        end;
    end;

    [EventSubscriber(ObjectType::Page, Page::"Doc. Attachment List Factbox", OnAfterGetRecRefFail, '', false, false)]
    local procedure OnAfterGetRecRefFail(DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef)
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
        SerialNoInformation: Record "Serial No. Information";
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        CarCarrierOrderHeader: Record "Car Carrier Order Header ERK";
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
    begin
        case DocumentAttachment."Table ID" of
            Database::"Car Carrier Header ERK":
                begin
                    RecRef.Open(Database::"Car Carrier Header ERK");
                    if CarCarrierHeader.Get(DocumentAttachment."No.") then
                        RecRef.GetTable(CarCarrierHeader);
                end;
            Database::"Serial No. Information":
                begin
                    RecRef.Open(Database::"Serial No. Information");
                    SerialNoInformation.SetRange("Serial No.", DocumentAttachment."No.");
                    if SerialNoInformation.FindFirst() then
                        RecRef.GetTable(SerialNoInformation);
                end;
            Database::"Car Carrier Line Detail ERK":
                begin
                    RecRef.Open(Database::"Car Carrier Line Detail ERK");
                    if CarCarrierLineDetail.Get(DocumentAttachment."No.", DocumentAttachment."Line No.", DocumentAttachment."Line Detail No. ERK") then
                        RecRef.GetTable(CarCarrierLineDetail);
                end;
            Database::"Car Carrier Order Header ERK":
                begin
                    RecRef.Open(Database::"Car Carrier Order Header ERK");
                    if CarCarrierOrderHeader.Get(DocumentAttachment."No.") then
                        RecRef.GetTable(CarCarrierOrderHeader);
                end;
            Database::"Car Carrier Order Line ERK":
                begin
                    RecRef.Open(Database::"Car Carrier Order Line ERK");
                    if CarCarrierOrderLine.Get(DocumentAttachment."No.", DocumentAttachment."Line No.") then
                        RecRef.GetTable(CarCarrierOrderLine);
                end;
        end;
    end;

    // [EventSubscriber(ObjectType::Page, Page::"Doc. Attachment List Factbox", OnAfterGetRecRefFail, '', false, false)]
    // local procedure OnAfterGetRecRefFail(DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef)
    // var
    //     FieldRef: FieldRef;
    //     RecNo: Code[20];
    //     RecLineNo: Integer;
    //     RecLineDetailNo: Integer;
    // begin
    //     case RecRef.Number of
    //         Database::"Car Carrier Header ERK":
    //             begin
    //                 FieldRef := RecRef.Field(1);
    //                 RecNo := FieldRef.Value;
    //                 DocumentAttachment.SetRange("No.", RecNo);
    //             end;
    //         Database::"Serial No. Information":
    //             begin
    //                 FieldRef := RecRef.Field(3);
    //                 RecNo := FieldRef.Value;
    //                 DocumentAttachment.SetRange("No.", RecNo);
    //             end;
    //         Database::"Car Carrier Line Detail ERK":
    //             begin
    //                 FieldRef := RecRef.Field(1);
    //                 RecNo := FieldRef.Value;
    //                 FieldRef := RecRef.Field(2);
    //                 RecLineNo := FieldRef.Value;
    //                 FieldRef := RecRef.Field(3);
    //                 RecLineDetailNo := FieldRef.Value;
    //                 DocumentAttachment.SetRange("No.", RecNo);
    //                 DocumentAttachment.SetRange("Line No.", RecLineNo);
    //                 DocumentAttachment.SetRange("Line Detail No. ERK", RecLineDetailNo);
    //             end;
    //         Database::"Car Carrier Order Header ERK":
    //             begin
    //                 FieldRef := RecRef.Field(1);
    //                 RecNo := FieldRef.Value;
    //                 DocumentAttachment.SetRange("No.", RecNo);
    //             end;
    //         Database::"Car Carrier Order Line ERK":
    //             begin
    //                 FieldRef := RecRef.Field(1);
    //                 RecNo := FieldRef.Value;
    //                 FieldRef := RecRef.Field(2);
    //                 RecLineNo := FieldRef.Value;
    //                 DocumentAttachment.SetRange("No.", RecNo);
    //                 DocumentAttachment.SetRange("Line No.", RecLineNo);
    //             end;
    //     end;
    // end;

    [EventSubscriber(ObjectType::Table, Database::"Document Attachment", OnAfterInitFieldsFromRecRef, '', false, false)]
    local procedure OnAfterInitFieldsFromRecRef(var DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef)
    var
        FieldRef: FieldRef;
        RecNo: Code[20];
        RecLineNo: Integer;
        RecLineDetailNo: Integer;
    begin
        case RecRef.Number() of
            Database::"Car Carrier Header ERK":
                begin
                    FieldRef := RecRef.Field(1);
                    RecNo := FieldRef.Value();
                    DocumentAttachment.Validate("No.", RecNo);
                end;
            Database::"Serial No. Information":
                begin
                    FieldRef := RecRef.Field(3);
                    RecNo := FieldRef.Value();
                    DocumentAttachment.Validate("No.", RecNo);
                end;
            Database::"Car Carrier Line Detail ERK":
                begin
                    FieldRef := RecRef.Field(1);
                    RecNo := FieldRef.Value();
                    FieldRef := RecRef.Field(2);
                    RecLineNo := FieldRef.Value();
                    FieldRef := RecRef.Field(3);
                    RecLineDetailNo := FieldRef.Value();
                    DocumentAttachment.Validate("No.", RecNo);
                    DocumentAttachment.Validate("Line No.", RecLineNo);
                    DocumentAttachment.Validate("Line Detail No. ERK", RecLineDetailNo);
                end;
            Database::"Car Carrier Order Header ERK":
                begin
                    FieldRef := RecRef.Field(1);
                    RecNo := FieldRef.Value();
                    DocumentAttachment.Validate("No.", RecNo);
                end;
            Database::"Car Carrier Order Line ERK":
                begin
                    FieldRef := RecRef.Field(1);
                    RecNo := FieldRef.Value();
                    FieldRef := RecRef.Field(2);
                    RecLineNo := FieldRef.Value();
                    DocumentAttachment.Validate("No.", RecNo);
                    DocumentAttachment.Validate("Line No.", RecLineNo);
                end;
        end;
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Document Attachment Mgmt", OnAfterTransformAttachmentDocumentTypeValue, '', false, false)]
    // local procedure Document_Attachment_Mgmt_OnAfterTransformAttachmentDocumentTypeValue(TableNo: Integer; var AttachmentDocumentType: Enum "Attachment Document Type")
    // begin
    //     case TableNo of
    //         Database::"Car Carrier Order Header ERK":
    //             case AttachmentDocumentType of
    //                 AttachmentDocumentType::Order:
    //                     AttachmentDocumentType := AttachmentDocumentType::"Transport Document";
    //             end;
    //     end;
    // end;
}
