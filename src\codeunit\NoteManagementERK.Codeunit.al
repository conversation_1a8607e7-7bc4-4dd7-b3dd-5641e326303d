codeunit 60016 "Note Management ERK"
{
    procedure RecordRefHasNote(MyRec: RecordRef): Boolean
    var
        RecordLink: Record "Record Link";
    begin
        Clear(RecordLink);
        RecordLink.SetRange("Record ID", MyRec.RecordId());
        RecordLink.SetRange(Type, RecordLink.Type::Note);
        if not RecordLink.IsEmpty() then
            exit(true)
        else
            exit(false);
    end;

    procedure GetNotesForRecordRef(MyRec: RecordRef): Text[250]
    var
        RecordLink: Record "Record Link";
        TypeHelper: Codeunit "Record Link Management";
        Result: Text;
    begin
        Clear(RecordLink);
        Clear(Result);
        RecordLink.SetRange("Record ID", MyRec.RecordId());
        RecordLink.SetRange(Type, RecordLink.Type::Note);
        if RecordLink.FindSet() then
            repeat
                RecordLink.CalcFields(Note);
                Result += TypeHelper.ReadNote(RecordLink);
            until RecordLink.Next() = 0;
        exit(CopyStr(Result, 1, 250));
    end;
}
