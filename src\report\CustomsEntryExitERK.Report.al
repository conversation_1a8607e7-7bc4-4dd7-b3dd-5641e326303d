report 60005 "Customs Entry - Exit ERK"
{
    ApplicationArea = All;
    Caption = 'Customs Entry - Exit';
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem(VehicleTransferLedgerEntry; "Vehicle Transfer Ledger Entry")
        {
            RequestFilterFields = "Operation Date-Time", "Operation Type", "To Location Code", "To Bin Code", "From Location Code";
            DataItemTableView = where("To Bin Code" = filter(<> 'IN-TRANSIT'));

            column(SerialNo; "Serial No.")
            {
            }
            column(OperationType_VehicleTransferLedgerEntry; "Operation Type")
            {
            }
            column(CustomsExitDate_VehicleTransferLedgerEntry; "Operation Date-Time")
            {
            }
            column(CustomsEntryDate; CustomsEntryDate)
            {
            }
            column(ShipName; ShipName)
            {
            }
            trigger OnAfterGetRecord()
            begin
                PopulateEntryInformationFromSerialNo(VehicleTransferLedgerEntry."Serial No.");
            end;
        }
    }
    requestpage
    {
        trigger OnOpenPage()
        begin
            VehicleTransferLedgerEntry.SetRange("Operation Type", VehicleTransferLedgerEntry."Operation Type"::"Customs Exit");
            VehicleTransferLedgerEntry.SetRange("From Location Code", 'HAYDARPASA');
        end;
    }
    procedure PopulateEntryInformationFromSerialNo(SerialNo: Code[50])
    var
        VehicleTransferLedgerEntry: Record "Vehicle Transfer Ledger Entry";
        Ship: Record "Ship ERK";
    begin
        Clear(CustomsEntryDate);
        Clear(ShipName);
        VehicleTransferLedgerEntry.SetRange("Operation Type", VehicleTransferLedgerEntry."Operation Type"::Discharge);
        VehicleTransferLedgerEntry.SetRange("Serial No.", SerialNo);
        if VehicleTransferLedgerEntry.FindFirst() then begin
            CustomsEntryDate := VehicleTransferLedgerEntry."Operation Date-Time";
            if Ship.Get(CopyStr(VehicleTransferLedgerEntry."From Bin Code", 1, MaxStrLen(Ship."No."))) then
                ShipName := Ship.Name;
        end;
    end;

    var
        ShipName: Text[100];
        CustomsEntryDate: DateTime;
}
