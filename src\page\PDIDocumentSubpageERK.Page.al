page 60058 "PDI Document Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'PDI Document Subpage';
    PageType = ListPart;
    SourceTable = "PDI Line ERK";
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                IndentationColumn = Rec.Indent;
                IndentationControls = Description;
                ShowAsTree = true;
                TreeInitialState = ExpandAll;

                field(Code; Rec.Code)
                {
                    Style = Strong;
                    StyleExpr = (Rec.Indent = 0);
                }
                field(Description; Rec.Description)
                {
                    Style = Strong;
                    StyleExpr = (Rec.Indent = 0);
                }
                field(Result; Rec.Result)
                {
                    Style = Strong;
                    StyleExpr = (Rec.Indent = 0);

                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field(Comment; Rec.Comment)
                {
                    Style = Strong;
                    StyleExpr = (Rec.Indent = 0);
                }
            }
        }
    }
}
