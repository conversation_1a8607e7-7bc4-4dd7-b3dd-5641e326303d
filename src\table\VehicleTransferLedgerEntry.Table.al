table 60028 "Vehicle Transfer Ledger Entry"
{
    Caption = 'Vehicle Transfer Ledger Entry';
    DataClassification = CustomerContent;
    LookupPageId = "Vehicle Transfer Ledg. Entries";
    DrillDownPageId = "Vehicle Transfer Ledg. Entries";

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            ToolTip = 'Specifies the value of the Entry No. field.';
        }
        field(2; "From Location Code"; Code[10])
        {
            Caption = 'From Location Code';
            ToolTip = 'Specifies the value of the From Location Code field.';
        }
        field(3; "From Bin Code"; Code[20])
        {
            Caption = 'From Bin Code';
            ToolTip = 'Specifies the value of the From Bin Code field.';
        }
        field(4; "To Location Code"; Code[10])
        {
            Caption = 'To Location Code';
            ToolTip = 'Specifies the value of the To Location Code field.';
        }
        field(5; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            ToolTip = 'Specifies the value of the To Bin Code field.';
        }
        field(6; "Operation Date-Time"; DateTime)
        {
            Caption = 'Operation Date-Time';
            ToolTip = 'Specifies the value of the Operation Date-Time field.';
        }
        // field(7; "Car Carrier No."; Code[20])
        // {
        //     Caption = 'Car Carrier No.';
        // }
        field(8; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        // field(9; "Ship No."; Code[10])
        // {
        //     Caption = 'Ship No.';
        // }
        // field(10; "Vehicle Ledger Entry No."; Integer)
        // {
        //     Caption = 'Vehicle Ledger Entry No.';
        // }
        // field(11; "Ship Name"; Text[100])
        // {
        //     Caption = 'Ship Name';
        // }
        field(7; "Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Operation Type';
            ToolTip = 'Specifies the value of the Operation Type field.';
        }
        field(9; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(10; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the value of the Document Line No. field.';
        }
        field(11; "From Bin Description"; Text[100])
        {
            Caption = 'From Bin Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Bin.Description where("Location Code" = field("From Location Code"), Code = field("From Bin Code")));
            ToolTip = 'Specifies the value of the From Bin Description field.';
        }
        field(12; "To Bin Description"; Text[100])
        {
            Caption = 'To Bin Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Bin.Description where("Location Code" = field("To Location Code"), Code = field("To Bin Code")));
            ToolTip = 'Specifies the value of the To Bin Description field.';
        }
        field(13; "Shipping Agent Code"; Code[10])
        {
            Caption = 'Shipping Agent Code';
            ToolTip = 'Specifies the value of the Shipping Agent Code field.';
        }
        field(14; T1; Boolean)
        {
            Caption = 'T1';
            ToolTip = 'Specifies the value of the T1 field.';
        }
        field(15; "NAV Succesfull"; Boolean)
        {
            Caption = 'NAV Succesfull';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Serial No. Information"."Nav Upload Succesful ERK" where("Serial No." = field("Serial No.")));
            ToolTip = 'Specifies the value of the NAV Succesfull field.';
        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        VehicleTransferLedgerEntry: Record "Vehicle Transfer Ledger Entry";
    begin
        if VehicleTransferLedgerEntry.FindLast() then
            Rec."Entry No." := VehicleTransferLedgerEntry."Entry No." + 1
        else
            Rec."Entry No." := 1;
    end;
}
