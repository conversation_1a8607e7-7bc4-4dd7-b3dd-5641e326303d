table 60065 "Car Carrier Line Report ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Car Carrier Line Report';

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Editable = false;
            Caption = 'Entry No.';
        }
        field(2; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
        }
        field(3; "Port Voyage"; Text[100])
        {
            Caption = 'Port Voyage';
        }
        field(4; "Load Square Metres"; Decimal)
        {
            Caption = 'Load Square Metres';
        }
        field(5; "Unload Square Metres"; Decimal)
        {
            Caption = 'Unload Square Metres';
        }
        field(6; "Load Cubic Metres"; Decimal)
        {
            Caption = 'Load Cubic Metres';
        }
        field(7; "Unload Cubic Metres"; Decimal)
        {
            Caption = 'Unload Cubic Metres';
        }
        field(8; "Net Load(m2)"; Decimal)
        {
            Caption = 'Net Load(2)';
        }
        field(9; "Fill Rate Square Metres"; Decimal)
        {
            Caption = 'Fill Rate Square Metres';
        }
        field(10; "Fill Rate Cubic Metres"; Decimal)
        {
            Caption = 'Fill Rate Cubic Metres';
        }
        field(11; "From Date"; DateTime)
        {
            Caption = 'From Date';
        }
        field(12; "To Date"; DateTime)
        {
            Caption = 'To Date';
        }
        field(13; Duration; Decimal)
        {
            Caption = 'Duration';
        }
        field(14; Profitability; Decimal)
        {
            Caption = 'Profitability';
        }
        field(15; Wait_Erk; Decimal)
        {
            Caption = 'Wait_Erk';
        }
        field(16; Wait_Other; Decimal)
        {
            Caption = 'Wait_Other';
        }
        field(17; Cost; Decimal)
        {
            Caption = 'Cost';
        }
        field(18; Income; Decimal)
        {
            Caption = 'Income';
        }
        field(19; Profit; Decimal)
        {
            Caption = 'Profit';
        }
        field(20; "Net Load(m3)"; Decimal)
        {
            Caption = 'Net Load(m3)';
        }
        field(21; Cluster; Text[100])
        {
            Caption = 'Cluster';
        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
    #region FillCarrCarrierLineReportTable
    procedure FillCarrCarrierLineReportTable()
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
        CarCarrierLineReport: Record "Car Carrier Line Report ERK";
        TypeHelper: Codeunit "Type Helper";
        CarCarrierNotLoadM2PrevValue: Decimal;
        CarCarrierNotLoadM3PrevValue: Decimal;
        LastProceedShip: Text;
    begin
        CarCarrierNotLoadM2PrevValue := 0;
        CarCarrierNotLoadM3PrevValue := 0;
        EntryNo := 0;
        CarCarrierLine.Reset();
        CarCarrierLine.SetCurrentKey("Ship No.", "Ending Date-Time");
        CarCarrierLine.SetFilter("Arrival Date-Time", '<>%1', CreateDateTime(0D, 0T));
        CarCarrierLine.SetFilter("Departure Date-Time", '<>%1', CreateDateTime(0D, 0T));
        CarCarrierLine.SetFilter("Ending Date-Time", '>%1', CreateDateTime(20231231D, 235900T));
        if CarCarrierLine.FindSet() then
            repeat
                EntryNo += 1;
                CarCarrierLine.CalcFields("Ship Name", "Departure Port Description", "Ship No.", "Arrival Port Description");
                Clear(CarCarrierLineReport);
                CarCarrierLineReport.Init();
                CarCarrierLineReport."Entry No." := EntryNo;
                CarCarrierLineReport."Ship Name" := CarCarrierLine."Ship Name";
                CarCarrierLineReport."Port Voyage" := CarCarrierLine."Departure Port Description";
                CarCarrierLineReport."To Date" := CarCarrierLine."Departure Date-Time";
                CarCarrierLineReport."From Date" := FindFromDate(CarCarrierLine);

                if TypeHelper.CompareDateTime(CarCarrierLineReport."From Date", CreateDateTime(0D, 0T)) = 0 then
                    CarCarrierLineReport."From Date" := CarCarrierLine."Departure Date-Time";
                CarCarrierLineReport."Load Square Metres" := CalcLoadSquareMeters(CarCarrierLine);
                CarCarrierLineReport."Unload Square Metres" := CalcUnloadSquareMeters(CarCarrierLine, CarCarrierLine."Departure Port");
                CarCarrierLineReport."Load Cubic Metres" := CalcLoadCubicMeters(CarCarrierLine);
                CarCarrierLineReport."Unload Cubic Metres" := CalcUnloadCubicMeters(CarCarrierLine, CarCarrierLine."Departure Port");
                CarCarrierLineReport."Net Load(m2)" := 0;
                CarCarrierLineReport."Net Load(m3)" := 0;
                CarCarrierLineReport."Fill Rate Square Metres" := 0;
                CarCarrierLineReport."Fill Rate Cubic Metres" := 0;
                CarCarrierLineReport.Wait_Erk := (CarCarrierLineReport."To Date" - CarCarrierLineReport."From Date") / 86400000;

                CarCarrierLineReport.Profitability := 0;
                CarCarrierLineReport.Cluster := CarCarrierLine."Starting Port Cluster Desc.";
                if not MergeCarCarrierLineReport(CarCarrierLine, CarCarrierLineReport, CarCarrierNotLoadM2PrevValue, CarCarrierNotLoadM3PrevValue) then begin
                    CarCarrierLineReport.Insert(true);
                    if LastProceedShip = '' then
                        LastProceedShip := CarCarrierLineReport."Ship Name";
                    if LastProceedShip <> CarCarrierLineReport."Ship Name" then begin
                        CarCarrierNotLoadM2PrevValue := 0;
                        CarCarrierNotLoadM3PrevValue := 0;
                    end;
                    CalcNetLoadM2ForVoyageLine(CarCarrierLineReport, CarCarrierNotLoadM2PrevValue);
                    CalcNetLoadM3ForVoyageLine(CarCarrierLineReport, CarCarrierNotLoadM3PrevValue);
                    LastProceedShip := CarCarrierLineReport."Ship Name";
                    InsertVoyageLine(CarCarrierLine, CarCarrierNotLoadM2PrevValue, CarCarrierNotLoadM3PrevValue);
                    InsertUnloadCarCarrierLine(CarCarrierLine, CarCarrierNotLoadM2PrevValue, CarCarrierNotLoadM3PrevValue);
                end;
            until CarCarrierLine.Next() = 0;
    end;
    #endregion
    #region MergeCarCarrierLineReport
    local procedure MergeCarCarrierLineReport(CarCarrierLine: Record "Car Carrier Line ERK"; CarCarrierLineReport: Record "Car Carrier Line Report ERK"; var CarCarrierNotLoadM2PrevValue: Decimal; var CarCarrierNotLoadM3PrevValue: Decimal): Boolean
    var
        Valuem2: Decimal;
        Valuem3: Decimal;
        FillRateM2: Decimal;
        FillRateM3: Decimal;
    begin
        if (CarCarrierLineReportPrevious."Ship Name" = CarCarrierLineReport."Ship Name") and (CarCarrierLineReportPrevious."Port Voyage" = CarCarrierLineReport."Port Voyage") then begin
            CarCarrierLineReportPrevious."Load Square Metres" := CarCarrierLineReport."Load Square Metres";
            CarCarrierLineReportPrevious."Load Cubic Metres" := CarCarrierLineReport."Load Cubic Metres";
            CarCarrierLineReportPrevious."From Date" := CarCarrierLineReportVoyagePrevious."To Date";
            CarCarrierLineReportPrevious."To Date" := CarCarrierLineReport."To Date";
            CarCarrierLineReportPrevious.Wait_Erk := (CarCarrierLineReportPrevious."To Date" - CarCarrierLineReportPrevious."From Date") / 86400000;
            CarCarrierLineReportPrevious.Modify(true);
            Valuem2 := CarCarrierLineReport."Load Square Metres" - CarCarrierLineReport."Unload Square Metres" + CarCarrierNotLoadM2PrevValue;
            CarCarrierNotLoadM2PrevValue := Valuem2;
            Valuem3 := CarCarrierLineReport."Load Cubic Metres" - CarCarrierLineReport."Unload Cubic Metres" + CarCarrierNotLoadM3PrevValue;
            CarCarrierNotLoadM3PrevValue := Valuem3;

            if CarCarrierNotLoadM2PrevValue <> 0 then
                CarCarrierLineReportVoyagePrevious."Net Load(m2)" := CarCarrierNotLoadM2PrevValue;

            if CarCarrierNotLoadM3PrevValue <> 0 then
                CarCarrierLineReportVoyagePrevious."Net Load(m3)" := CarCarrierNotLoadM3PrevValue;

            GetFillRatesFromShipCard(CarCarrierLine, FillRateM2, FillRateM3);
            if FillRateM2 <> 0 then
                CarCarrierLineReportVoyagePrevious."Fill Rate Square Metres" := CarCarrierLineReportVoyagePrevious."Net Load(m2)" / FillRateM2 * 100;
            if FillRateM3 <> 0 then
                CarCarrierLineReportVoyagePrevious."Fill Rate Cubic Metres" := CarCarrierLineReportVoyagePrevious."Net Load(m3)" / FillRateM3 * 100;
            CarCarrierLineReportVoyagePrevious."From Date" := CarCarrierLineReport."To Date";
            CarCarrierLineReportVoyagePrevious.Modify(true);
            InsertUnloadCarCarrierLine(CarCarrierLine, CarCarrierNotLoadM2PrevValue, CarCarrierNotLoadM3PrevValue);
            exit(true);
        end;
    end;
    #endregion
    #region InsertUnloadCarCarrierLine
    local procedure InsertUnloadCarCarrierLine(CarCarrierLine: Record "Car Carrier Line ERK"; var CarCarrierNotLoadM2PrevValue: Decimal; var CarCarrierNotLoadM3PrevValue: Decimal)
    var
        CarCarrierLine2: Record "Car Carrier Line ERK";
        CarCarrierLineReport: Record "Car Carrier Line Report ERK";
        TypeHelper: Codeunit "Type Helper";
        LastLineFound: Boolean;
    begin
        LastLineFound := false;
        CarCarrierLine.CalcFields("Ship No.");
        CarCarrierLine2.Reset();
        CarCarrierLine2.SetCurrentKey("Ship No.", "Arrival Date-Time");
        CarCarrierLine2.SetRange("Ship No.", CarCarrierLine."Ship No.");
        if CarCarrierLine2.FindLast() then
            if CarCarrierLine.SystemId = CarCarrierLine2.SystemId then
                LastLineFound := true;
        EntryNo += 1;
        Clear(CarCarrierLineReport);
        CarCarrierLineReport.Init();
        CarCarrierLineReport."Entry No." := EntryNo;
        CarCarrierLineReport."Ship Name" := CarCarrierLine."Ship Name";
        CarCarrierLineReport."Port Voyage" := CarCarrierLine."Arrival Port Description";
        if LastLineFound then begin
            CarCarrierLineReport."To Date" := CarCarrierLine."Arrival Date-Time";
            CarCarrierLineReport."From Date" := CarCarrierLine."Arrival Date-Time";
            CarCarrierLineReportVoyagePrevious."To Date" := CarCarrierLineReport."To Date";
            CarCarrierLineReportVoyagePrevious."From Date" := CarCarrierLineReportPrevious."To Date";
            CarCarrierLineReport.Wait_Erk := (CarCarrierLineReport."To Date" - CarCarrierLineReport."From Date") / 86400000;
            CarCarrierLineReportVoyagePrevious.Modify(true);
        end
        else begin
            CarCarrierLineReport."To Date" := CarCarrierLine."Departure Date-Time";
            CarCarrierLineReport."From Date" := FindFromDate(CarCarrierLine);
        end;
        if TypeHelper.CompareDateTime(CarCarrierLineReport."From Date", CreateDateTime(0D, 0T)) = 0 then
            CarCarrierLineReport."From Date" := CarCarrierLine."Departure Date-Time";
        CarCarrierLineReport."Unload Square Metres" := CalcUnloadSquareMeters(CarCarrierLine, CarCarrierLine."Arrival Port");
        CarCarrierLineReport."Unload Cubic Metres" := CalcUnloadCubicMeters(CarCarrierLine, CarCarrierLine."Arrival Port");
        CarCarrierLineReport."Net Load(m2)" := 0;
        CarCarrierLineReport."Net Load(m3)" := 0;
        CarCarrierLineReport."Fill Rate Square Metres" := 0;
        CarCarrierLineReport."Fill Rate Cubic Metres" := 0;
        CarCarrierLineReport.Profitability := 0;
        CarCarrierLineReport.Cluster := CarCarrierLine."Ending Port Cluster Desc.";
        CarCarrierLineReport.Wait_Erk := (CarCarrierLineReport."To Date" - CarCarrierLineReport."From Date") / 86400000;
        CarCarrierLineReport.Insert(true);
        CalcNetLoadM2ForVoyageLine(CarCarrierLineReport, CarCarrierNotLoadM2PrevValue);
        CalcNetLoadM3ForVoyageLine(CarCarrierLineReport, CarCarrierNotLoadM3PrevValue);
        if not LastLineFound then
            InsertVoyageLine(CarCarrierLine, CarCarrierNotLoadM2PrevValue, CarCarrierNotLoadM3PrevValue);
        CarCarrierLineReportPrevious := CarCarrierLineReport;
    end;
    #endregion
    #region InsertVoyageLine
    local procedure InsertVoyageLine(CarCarrierLine: Record "Car Carrier Line ERK"; CarCarrierNetLoadM2PrevValue: Decimal; CarCarrierNetLoadM3PrevValue: Decimal)
    var
        CarCarrierLineReportVoyage: Record "Car Carrier Line Report ERK";
        FillRateM2: Decimal;
        FillRateM3: Decimal;
        VoyageTok: Label 'Voyage', Locked = true;
        CostValue: Decimal;
        IncomeValue: Decimal;
    begin
        EntryNo += 1;
        Clear(CarCarrierLineReportVoyage);
        CarCarrierLineReportVoyage.Init();
        CarCarrierLineReportVoyage."Entry No." := EntryNo;
        CarCarrierLine.CalcFields("Ship Name", "Ship No.");
        CarCarrierLineReportVoyage."Ship Name" := CarCarrierLine."Ship Name";
        CarCarrierLineReportVoyage."Port Voyage" := VoyageTok;
        CarCarrierLineReportVoyage."Load Square Metres" := 0;
        CarCarrierLineReportVoyage."Unload Square Metres" := 0;
        CarCarrierLineReportVoyage."Load Cubic Metres" := 0;
        CarCarrierLineReportVoyage."Unload Cubic Metres" := 0;
        CarCarrierLineReportVoyage."Net Load(m2)" := CarCarrierNetLoadM2PrevValue;
        CarCarrierLineReportVoyage."Net Load(m3)" := CarCarrierNetLoadM3PrevValue;
        GetFillRatesFromShipCard(CarCarrierLine, FillRateM2, FillRateM3);
        if FillRateM2 <> 0 then
            CarCarrierLineReportVoyage."Fill Rate Square Metres" := CarCarrierLineReportVoyage."Net Load(m2)" / FillRateM2 * 100;
        if FillRateM3 <> 0 then
            CarCarrierLineReportVoyage."Fill Rate Cubic Metres" := CarCarrierLineReportVoyage."Net Load(m3)" / FillRateM3 * 100;
        CarCarrierLineReportVoyage."From Date" := CarCarrierLine."Departure Date-Time";
        CarCarrierLineReportVoyage."To Date" := CarCarrierLine."Arrival Date-Time";
        CarCarrierLineReportVoyage.Duration := (CarCarrierLineReportVoyage."To Date" - CarCarrierLineReportVoyage."From Date") / 86400000;
        CarCarrierLineReportVoyage.Profitability := CalcProfitability(CarCarrierLine);
        CalcCostAndIncome(CarCarrierLine, CostValue, IncomeValue);
        CarCarrierLineReportVoyage.Cost := CostValue;
        CarCarrierLineReportVoyage.Income := IncomeValue;
        CarCarrierLineReportVoyage.Profit := IncomeValue - CostValue;
        CarCarrierLineReportVoyage.Insert(true);
        CarCarrierLineReportVoyagePrevious."From Date" := CarCarrierLineReportVoyage."From Date";
        CarCarrierLineReportVoyagePrevious."To Date" := CarCarrierLineReportVoyage."To Date";
        CarCarrierLineReportVoyagePrevious.Duration := (CarCarrierLineReportVoyage."To Date" - CarCarrierLineReportVoyage."From Date") / 86400000;
        if CarCarrierLineReportVoyagePrevious.Modify(true) then;//Nothing to do here yet.
        CarCarrierLineReportVoyagePrevious := CarCarrierLineReportVoyage;
    end;
    #endregion
    #region DeleteAllCarrCarrierLineReportTable
    procedure DeleteAllCarrCarrierLineReportTable()
    var
        CarCarrierLineReport: Record "Car Carrier Line Report ERK";
    begin
        CarCarrierLineReport.Reset();
        CarCarrierLineReport.DeleteAll(false);
    end;
    #endregion
    #region CalcNetLoadM2ForVoyageLine
    local procedure CalcNetLoadM2ForVoyageLine(CarCarrierLineReport: Record "Car Carrier Line Report ERK"; var CarCarrierNetLoadPrevValue: Decimal)
    var
        NetLoadValue: Decimal;
    begin
        NetLoadValue := CarCarrierLineReport."Load Square Metres" - CarCarrierLineReport."Unload Square Metres" + CarCarrierNetLoadPrevValue;
        if NetLoadValue < 0 then
            if CarCarrierLineReportVoyagePrevious."Net Load(m2)" > 0 then
                NetLoadValue := NetLoadValue + CarCarrierLineReportVoyagePrevious."Net Load(m2)";

        CarCarrierNetLoadPrevValue := NetLoadValue;
    end;
    #endregion
    #region CalcNetLoadM3ForVoyageLine
    local procedure CalcNetLoadM3ForVoyageLine(CarCarrierLineReport: Record "Car Carrier Line Report ERK"; var CarCarrierNetLoadM3PrevValue: Decimal)
    var
        NetLoadValue: Decimal;
    begin
        NetLoadValue := CarCarrierLineReport."Load Cubic Metres" - CarCarrierLineReport."Unload Cubic Metres" + CarCarrierNetLoadM3PrevValue;
        if NetLoadValue < 0 then
            if CarCarrierLineReportVoyagePrevious."Net Load(m3)" > 0 then
                NetLoadValue := NetLoadValue + CarCarrierLineReportVoyagePrevious."Net Load(m3)";

        CarCarrierNetLoadM3PrevValue := NetLoadValue;
    end;
    #endregion
    #region GetFillRatesFromShipCard
    local procedure GetFillRatesFromShipCard(CarCarrierLine: Record "Car Carrier Line ERK"; var FillRateM2: Decimal; var FillRateM3: Decimal)
    var
        Ship: Record "Ship ERK";
    begin
        FillRateM2 := 0;
        FillRateM3 := 0;
        if Ship.Get(CarCarrierLine."Ship No.") then begin
            FillRateM2 := Ship."Capacity (m2)";
            FillRateM3 := Ship."Capacity (m3)";
        end;
    end;
    #endregion
    #region CalcProfitability
    local procedure CalcProfitability(CarCarrierLine: Record "Car Carrier Line ERK"): Decimal
    var
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
    begin
        CarCarrierLineDetail.Reset();
        CarCarrierLineDetail.SetRange("Document No.", CarCarrierLine."Document No.");
        CarCarrierLineDetail.SetRange("Document Line No.", CarCarrierLine."Line No.");
        CarCarrierLineDetail.CalcSums("Total Sales (ACY)", "Total Profit (ACY)");
        if CarCarrierLineDetail."Total Sales (ACY)" > 0 then
            exit(CarCarrierLineDetail."Total Profit (ACY)" / CarCarrierLineDetail."Total Sales (ACY)" * 100);
    end;
    #endregion
    #region CalcCostAndIncome
    local procedure CalcCostAndIncome(CarCarrierLine: Record "Car Carrier Line ERK"; var CostValue: Decimal; var IncomeValue: Decimal)
    var
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
    begin
        CarCarrierLineDetail.Reset();
        CarCarrierLineDetail.SetRange("Document No.", CarCarrierLine."Document No.");
        CarCarrierLineDetail.SetRange("Document Line No.", CarCarrierLine."Line No.");
        CarCarrierLineDetail.CalcSums("Total Sales (ACY)", "Total Expense (ACY)");
        CostValue := CarCarrierLineDetail."Total Expense (ACY)";
        IncomeValue := CarCarrierLineDetail."Total Sales (ACY)";
    end;
    #endregion
    #region FindFromDate
    local procedure FindFromDate(CarCarrierLine: Record "Car Carrier Line ERK"): DateTime
    var
        CarCarrierLineForSearch: Record "Car Carrier Line ERK";
    begin
        CarCarrierLineForSearch.Reset();
        CarCarrierLineForSearch.SetCurrentKey("Arrival Date-Time");
        CarCarrierLineForSearch.SetRange("Ship No.", CarCarrierLine."Ship No.");
        CarCarrierLineForSearch.SetFilter("Arrival Date-Time", '<%1 & <>%2', CarCarrierLine."Arrival Date-Time", CreateDateTime(0D, 0T));
        if CarCarrierLineForSearch.FindLast() then
            exit(CarCarrierLineForSearch."Arrival Date-Time");
    end;
    #endregion
    #region CalcLoadSquareMeters
    local procedure CalcLoadSquareMeters(CarCarrierLine: Record "Car Carrier Line ERK"): Decimal
    var
        SerialNoInformation: Record "Serial No. Information";
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        TotalFootPrint: Decimal;
    begin
        CarCarrierLedgerEntry.Reset();
        CarCarrierLedgerEntry.SetCurrentKey("Document No.", "Document Line No.", "Loading Port");
        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLine."Document No.");
        CarCarrierLedgerEntry.SetRange("Document Line No.", CarCarrierLine."Line No.");
        CarCarrierLedgerEntry.SetRange("Loading Port", CarCarrierLine."Departure Port");
        CarCarrierLedgerEntry.SetLoadFields("Serial No.");
        if CarCarrierLedgerEntry.FindSet() then
            repeat
                SerialNoInformation.Reset();
                SerialNoInformation.SetCurrentKey("Serial No.");
                SerialNoInformation.SetRange("Serial No.", CarCarrierLedgerEntry."Serial No.");
                if SerialNoInformation.FindFirst() then
                    TotalFootPrint += SerialNoInformation."Footprint (m2) ERK";
            until CarCarrierLedgerEntry.Next() = 0;
        exit(TotalFootPrint);
    end;
    #endregion
    #region CalcUnloadSquareMeters
    local procedure CalcUnloadSquareMeters(CarCarrierLine: Record "Car Carrier Line ERK"; DischarePort: Code[10]): Decimal
    var
        SerialNoInformation: Record "Serial No. Information";
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        TotalFootPrint: Decimal;
    begin
        CarCarrierLedgerEntry.Reset();
        CarCarrierLedgerEntry.SetCurrentKey("Document No.", "Document Line No.");
        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLine."Document No.");
        CarCarrierLedgerEntry.SetRange("Discharge Port Line No.", CarCarrierLine."Line No.");
        CarCarrierLedgerEntry.SetRange("Discharge Port", DischarePort);
        CarCarrierLedgerEntry.SetFilter("Discharge DateTime", '<>%1', CreateDateTime(0D, 0T));
        CarCarrierLedgerEntry.SetLoadFields("Serial No.");
        if CarCarrierLedgerEntry.FindSet() then
            repeat
                SerialNoInformation.Reset();
                SerialNoInformation.SetCurrentKey("Serial No.");
                SerialNoInformation.SetRange("Serial No.", CarCarrierLedgerEntry."Serial No.");
                if SerialNoInformation.FindFirst() then
                    TotalFootPrint += SerialNoInformation."Footprint (m2) ERK";
            until CarCarrierLedgerEntry.Next() = 0;
        exit(TotalFootPrint);
    end;
    #endregion
    #region CalcLoadCubicMeters
    local procedure CalcLoadCubicMeters(CarCarrierLine: Record "Car Carrier Line ERK"): Decimal
    var
        SerialNoInformation: Record "Serial No. Information";
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        TotalVolume: Decimal;
    begin
        CarCarrierLedgerEntry.Reset();
        CarCarrierLedgerEntry.SetCurrentKey("Document No.", "Document Line No.");
        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLine."Document No.");
        CarCarrierLedgerEntry.SetRange("Document Line No.", CarCarrierLine."Line No.");
        CarCarrierLedgerEntry.SetRange("Loading Port", CarCarrierLine."Departure Port");
        CarCarrierLedgerEntry.SetLoadFields("Serial No.");
        if CarCarrierLedgerEntry.FindSet() then
            repeat
                SerialNoInformation.Reset();
                SerialNoInformation.SetCurrentKey("Serial No.");
                SerialNoInformation.SetRange("Serial No.", CarCarrierLedgerEntry."Serial No.");
                if SerialNoInformation.FindFirst() then
                    TotalVolume += SerialNoInformation."Volume (m3) ERK";
            until CarCarrierLedgerEntry.Next() = 0;
        exit(TotalVolume);
    end;
    #endregion
    #region CalcUnloadCubicMeters
    local procedure CalcUnloadCubicMeters(CarCarrierLine: Record "Car Carrier Line ERK"; DischarePort: Code[10]): Decimal
    var
        SerialNoInformation: Record "Serial No. Information";
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        TotalVolume: Decimal;
    begin
        CarCarrierLedgerEntry.Reset();
        CarCarrierLedgerEntry.SetCurrentKey("Document No.", "Document Line No.");
        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLine."Document No.");
        CarCarrierLedgerEntry.SetRange("Discharge Port Line No.", CarCarrierLine."Line No.");
        CarCarrierLedgerEntry.SetRange("Discharge Port", DischarePort);
        CarCarrierLedgerEntry.SetFilter("Discharge DateTime", '<>%1', CreateDateTime(0D, 0T));
        CarCarrierLedgerEntry.SetLoadFields("Serial No.");
        if CarCarrierLedgerEntry.FindSet() then
            repeat
                SerialNoInformation.Reset();
                SerialNoInformation.SetCurrentKey("Serial No.");
                SerialNoInformation.SetRange("Serial No.", CarCarrierLedgerEntry."Serial No.");
                if SerialNoInformation.FindFirst() then
                    TotalVolume += SerialNoInformation."Volume (m3) ERK";
            until CarCarrierLedgerEntry.Next() = 0;
        exit(TotalVolume);
    end;
    #endregion
    var
        CarCarrierLineReportPrevious: Record "Car Carrier Line Report ERK";
        CarCarrierLineReportVoyagePrevious: Record "Car Carrier Line Report ERK";
        EntryNo: Integer;
}
