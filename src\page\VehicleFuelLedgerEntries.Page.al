page 60068 "Vehicle Fuel Ledger Entries"
{
    ApplicationArea = All;
    Caption = 'Vehicle Fuel Ledger Entries';
    PageType = List;
    SourceTable = "Vehicle Fuel Ledger Entry ERK";
    UsageCategory = History;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Operation Type"; Rec."Operation Type")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Tire Location"; Rec."Tire Location")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Fuel Type"; Rec."Fuel Type")
                {
                }
                field("Quantity (LT)"; Rec."Quantity (LT)")
                {
                }
                field("Card ID"; Rec."Card ID")
                {
                }
                field("Charge Station ID"; Rec."Charge Station ID")
                {
                }
                field(kWh; Rec.kWh)
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; ErkHoldingBasicFunctions.GetUserNameFromSecurityId(Rec.SystemCreatedBy))
                {
                    Caption = 'Created By';
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
