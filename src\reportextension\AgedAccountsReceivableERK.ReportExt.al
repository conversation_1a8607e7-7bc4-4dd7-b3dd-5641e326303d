reportextension 60000 "Aged Accounts Receivable ERK" extends "Aged Accounts Receivable"
{
    dataset
    {
        modify(Customer)
        {
            trigger OnAfterPreDataItem()
            begin
            end;
        }
        add(Customer)
        {
            column(SalespersonCodeERK; Customer."Salesperson Code")
            {
            }
            column(SalespersonNameERK; ErkHoldingBasicFunctions.GetSalesPersonNameFromSalesPersonCode(Customer."Salesperson Code"))
            {
            }
            column(PaymentTermsCodeERK; Customer."Payment Terms Code")
            {
            }
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
