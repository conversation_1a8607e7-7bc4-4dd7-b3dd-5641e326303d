table 60033 "Customs Operation Header ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Customs Operation Header ERK';
    LookupPageId = "Customs Operation Documents";
    DrillDownPageId = "Customs Operation Documents";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                //NoSeries: Codeunit NoSeriesManagement;
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Customs Operation No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Registration Date"; Date)
        {
            Caption = 'Registration Date';
            ToolTip = 'Specifies the value of the Regstration Date field.';
        }
        field(3; "Stamp Tax Line No."; Integer)
        {
            Caption = 'Stamp Tax Line No.';
            BlankZero = false;
            ToolTip = 'Specifies the value of the Stamp Tax Line No. field.';
            trigger OnValidate()
            begin
                if IsTemporary() then
                    exit;
                CustomsOperationManagement.CreateStampTaxLine(Rec);
            end;
        }
        field(4; MRN; Code[20])
        {
            Caption = 'MRN';
            ToolTip = 'Specifies the value of the MRN field.';
        }
        field(5; "Truck License Plate"; Code[20])
        {
            Caption = 'Truck License Plate';
            ToolTip = 'Specifies the value of the Truck License Plate field.';
        }
        field(6; "Trailer License Plate"; Code[20])
        {
            Caption = 'Trailer License Plate';
            ToolTip = 'Specifies the value of the Trailer License Plate field.';
        }
        field(7; "Container No."; Code[20])
        {
            Caption = 'Container No.';
            ToolTip = 'Specifies the value of the Container No. field.';
        }
        field(8; "Exit Customs"; Text[100])
        {
            Caption = 'Exit Customs Description';
            ToolTip = 'Specifies the value of the Exit Customs Description field.';
        }
        field(9; Sender; Text[100])
        {
            Caption = 'Sender';
            ToolTip = 'Specifies the value of the Sender field.';
        }
        field(10; Receiver; Text[100])
        {
            Caption = 'Receiver';
            ToolTip = 'Specifies the value of the Receiver field.';
        }
        field(11; "Customer Representative"; Text[100])
        {
            Caption = 'Customer Representative';
            ToolTip = 'Specifies the value of the Customer Representative field.';
        }
        field(12; "Declaration Type"; Code[20])
        {
            Caption = 'Declaration Type';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Declaration Type field.';
            trigger OnValidate()
            begin
                if IsTemporary() then
                    exit;
                CustomsOperationManagement.CreateCustomsOperationLineForDeclaritionType(Rec);
            end;
        }
        field(13; "Bill-to Customer No."; Code[20])
        {
            Caption = 'Bill-to Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Bill-to Customer No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
            begin
                if Customer.Get("Bill-to Customer No.") then
                    "Bill-To Customer Name" := Customer.Name
                else
                    "Bill-To Customer Name" := '';
            end;
        }
        field(14; "Bill-To Customer Name"; Text[100])
        {
            Caption = 'Bill-To Customer Name';
            ToolTip = 'Specifies the value of the Bill-To Customer Name field.';
        }
        field(15; "Unposted Sales Invoice No."; Code[20])
        {
            Caption = 'Unposted Sales Invoice No.';
            ToolTip = 'Specifies the value of the Unposted Sales Invoice No. field.';
            trigger OnLookup()
            var
                SalesHeader: Record "Sales Header";
            begin
                if SalesHeader.Get(SalesHeader."Document Type"::Invoice, "Unposted Sales Invoice No.") then
                    PageManagement.PageRun(SalesHeader);
            end;
        }
        field(16; "Posted Sales Invoice No."; Code[20])
        {
            Caption = 'Posted Sales Invoice No.';
            ToolTip = 'Specifies the value of the Posted Sales Invoice No. field.';
            trigger OnLookup()
            var
                SalesInvoiceHeader: Record "Sales Invoice Header";
            begin
                if SalesInvoiceHeader.Get("Posted Sales Invoice No.") then
                    PageManagement.PageRun(SalesInvoiceHeader);
            end;
        }
        field(17; "E-Invoice No."; Code[20])
        {
            Caption = 'E-Invoice No.';
            ToolTip = 'Specifies the value of the E-Invoice No. field.';
            trigger OnLookup()
            var
                EInvoiceHeader: Record "E-Invoice Header INF";
            begin
                if EInvoiceHeader.Get(EInvoiceHeader."Document Type"::"Sales Invoice", "E-Invoice No.") then
                    PageManagement.PageRun(EInvoiceHeader);
            end;
        }
        field(18; "Invoicing Group Code"; Code[10])
        {
            Caption = 'Invoicing Group Code';
            ToolTip = 'Specifies the value of the Invoicing Group Code field.';
        }
        field(19; "Total Amount"; Decimal)
        {
            Caption = 'Total Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Customs Operation Line ERK"."Line Amount" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Amount field.';
        }
        field(20; "Truck Carnet No."; Code[20])
        {
            Caption = 'Truck Carnet No. ';
            ToolTip = 'Specifies the value of the Truck Carnet No.  field.';
        }
        field(21; "Shortcut Dimension 1 Code"; Code[20])
        {
            CaptionClass = '1,2,1';
            Caption = 'Shortcut Dimension 1 Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1), Blocked = const(false));
            ToolTip = 'Specifies the value of the Shortcut Dimension 1 Code field.';
            // trigger OnValidate()
            // begin
            //     PortOperationManagement.UpdateCommonOperationField(Rec);
            // end;
        }
        field(22; "Exit Customs Code"; Code[10])
        {
            Caption = 'Exit Customs Code';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Exit Customs Code field.';
            trigger OnValidate()
            var
                EntryExitPoint: Record "Entry/Exit Point";
            begin
                EntryExitPoint.Get("Exit Customs Code");
                Rec."Exit Customs" := EntryExitPoint.Description;
            end;
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        Customer: Record Customer;
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        //NoSeriesManagement: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
    begin
        if not IsTemporary() then
            if "No." = '' then begin
                ErkHoldingSetup.Get();
                ErkHoldingSetup.TestField("Customs Operation No. Series");
                //NoSeriesManagement.InitSeries(ErkHoldingSetup."Customs Operation No. Series", xRec."No. Series", 0D, "No.", "No. Series");
                "No. Series" := ErkHoldingSetup."Customs Operation No. Series";
                if NoSeries.AreRelated(ErkHoldingSetup."Customs Operation No. Series", xRec."No. Series") then
                    "No. Series" := xRec."No. Series";
                "No." := NoSeries.GetNextNo("No. Series");
            end;
        Customer.SetRange(Name, Rec."Bill-To Customer Name");
        if Customer.FindFirst() then
            Rec.Validate("Bill-to Customer No.", Customer."No.");
    end;

    trigger OnDelete()
    var
        CustomsOperationLine: Record "Customs Operation Line ERK";
    begin
        CustomsOperationLine.SetRange("Document No.", Rec."No.");
        CustomsOperationLine.DeleteAll(true);
    end;

    var
        PageManagement: Codeunit "Page Management";
        CustomsOperationManagement: Codeunit "Customs Operation Management";
}
