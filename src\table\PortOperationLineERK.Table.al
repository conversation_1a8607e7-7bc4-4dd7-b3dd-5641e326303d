table 60021 "Port Operation Line ERK"
{
    Caption = 'Port Operation Line';
    DataClassification = CustomerContent;
    DrillDownPageId = "Port Operation Lines ERK";
    LookupPageId = "Port Operation Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            Editable = false;
            AllowInCustomizations = Never;
        }
        field(3; "Bill-to Customer No."; Code[20])
        {
            Caption = 'Bill-to Customer No.';
            TableRelation = Customer;
            ToolTip = 'Specifies the value of the Customer No. field.';
            trigger OnValidate()
            begin
                Rec.Validate("Load Owner No.", Rec."Bill-to Customer No.");
            end;
        }
        field(4; "Revenue Amount (ACY)"; Decimal)
        {
            Caption = 'Revenue Amount (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Port Operation Line Detail ERK"."Line Amount (ACY)" where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = filter(Revenue), "Source No." = filter(<> 'D*'), "Is Cancelled" = filter(false)));
            ToolTip = 'Specifies the value of the Revenue Amount (ACY) field.';
        }
        field(5; "Expense Amount (ACY)"; Decimal)
        {
            Caption = 'Expense Amount (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Port Operation Line Detail ERK"."Line Amount (ACY)" where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = filter(Expense | Consumption), "Source No." = filter(<> 'D*'), "Is Cancelled" = filter(false)));
            ToolTip = 'Specifies the value of the Expense Amount (ACY) field.';
        }
        field(6; "Parent Load Type"; Code[20])
        {
            Caption = 'Parent Load Type';
            TableRelation = "Parent Load Type ERK";
            ToolTip = 'Specifies the value of the Parent Load Type field.';
        }
        field(7; "Sub Load Type"; Code[20])
        {
            Caption = 'Sub Load Type';
            TableRelation = "Sub Load Type ERK";
            ToolTip = 'Specifies the value of the Load Type field.';
        }
        field(8; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Load Item No."));
            ToolTip = 'Specifies the value of the Sub Load Type field.';
        }
        field(9; "Bill-to Customer Name"; Text[100])
        {
            Caption = 'Bill-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Bill-to Customer No.")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(10; "Load Item No."; Code[20])
        {
            Caption = 'Load Item No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Load Item No. field.';
        }
        field(11; "Total Entry Quantity"; Decimal)
        {
            Caption = 'Total Entry Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Port Operation Line Detail ERK".Quantity where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = filter("Load Entry"), Quantity = filter(> 0)));
            ToolTip = 'Specifies the value of the Total Entry Quantity field.';
        }
        field(13; "Total Exit Quantity"; Decimal)
        {
            Caption = 'Total Exit Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Port Operation Line Detail ERK".Quantity where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = filter("Load Exit"), Quantity = filter(< 0)));
            ToolTip = 'Specifies the value of the Total Exit Quantity field.';
        }
        field(14; "Remaining Quantity"; Decimal)
        {
            Caption = 'Remaining Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Port Operation Line Detail ERK".Quantity where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = filter("Load Exit" | "Load Transfer" | "Load Entry" | Scrap)));
            ToolTip = 'Specifies the value of the Remaining Quantity field.';
        }
        field(15; "Total Scrap Quantity"; Decimal)
        {
            Caption = 'Total Scrap Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Port Operation Line Detail ERK".Quantity where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = filter(Scrap), Quantity = filter(< 0)));
            ToolTip = 'Specifies the value of the Total Scrap Quantity field.';
        }
        field(12; Completed; Boolean)
        {
            Caption = 'Completed';
            ToolTip = 'Specifies the value of the Completed field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Remaining Quantity");
                Rec.TestField("Remaining Quantity", 0);
            end;
        }
        field(16; "Shortcut Dimension 1 Code"; Code[20])
        {
            CaptionClass = '1,2,1';
            Caption = 'Shortcut Dimension 1 Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1), Blocked = const(false));
            ToolTip = 'Specifies the value of the Shortcut Dimension 1 Code field.';
        }
        field(17; "Total Transfer Quantity"; Decimal)
        {
            Caption = 'Total Transfer Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Port Operation Line Detail ERK".Quantity where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = filter("Load Transfer")));
            ToolTip = 'Specifies the value of the Total Transfer Quantity field.';
        }
        field(18; "Load Owner No."; Code[20])
        {
            Caption = 'Load Owner No.';
            TableRelation = Customer;
            ToolTip = 'Specifies the value of the Load Owner No. field.';
        }
        field(19; "Load Owner Name"; Text[100])
        {
            Caption = 'Load Owner Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Load Owner No.")));
            ToolTip = 'Specifies the value of the Load Owner Name field.';
        }
        field(20; "First Entry Date"; Date)
        {
            Caption = 'First Entry Date';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Port Operation Line Detail ERK"."Operation Date" where(Type = filter("Load Entry" | "Load Transfer"), "Document No." = field("Document No."), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the First Entry Date field.';
        }
        field(40; "Department Name"; Text[100])
        {
            Caption = 'Department Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Dimension Value".Name where("Global Dimension No." = const(1), Blocked = const(false), Code = field("Shortcut Dimension 1 Code")));
            ToolTip = 'Specifies the value of the Department Name field.';
        }
        field(21; "Contract No."; Code[20])
        {
            Caption = 'Contract No.';
            ToolTip = 'Specifies the value of the Contract No. field.';
            TableRelation = "Port Operation Contract Header";

            trigger OnValidate()
            var
                PortOperationContractMngt: Codeunit "Port Operation Contract Mngt.";
            begin
                // update details using this new contract
                PortOperationContractMngt.UpdateLineDetailsFromContract(Rec, Rec."Contract No.");
            end;
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(Key2; "Bill-to Customer No.", "Variant Code")
        {
        }
    }
    trigger OnDelete()
    var
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
        portOperationLineDetailDeleteErr: Label 'You cannot delete this record because it has Port Operation Line Detail records.';
    begin

        PortOperationLineDetail.SetRange("Document No.", Rec."Document No.");
        PortOperationLineDetail.SetRange("Document Line No.", Rec."Line No.");
        if not PortOperationLineDetail.IsEmpty() then
            Error(portOperationLineDetailDeleteErr);
    end;

    trigger OnInsert()
    var
        PortOperationLine: Record "Port Operation Line ERK";
        PortOperationHeader: Record "Port Operation Header ERK";
    begin
        PortOperationLine.SetRange("Document No.", Rec."Document No.");
        if PortOperationLine.FindLast() then
            Rec."Line No." := PortOperationLine."Line No." + 10000
        else
            Rec."Line No." := 10000;

        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Load Item No.");
        "Load Item No." := ErkHoldingSetup."Load Item No.";

        PortOperationHeader.Get(Rec."Document No.");
        Rec.Validate("Contract No.", PortOperationHeader."Contract No.");
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
}
