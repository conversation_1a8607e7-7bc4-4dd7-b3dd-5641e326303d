query 60000 "Export Header ERK"
{
    Caption = 'Export Header ERK';
    QueryType = Normal;

    elements
    {
        dataitem(ExportHeaderERK;
        "Export Header ERK")
        {
            column(No;
            "No.")
            {
            }
            column(Completed;
            Completed)
            {
            }
            column(ContainerQuantity;
            "Container Quantity")
            {
            }
            column(CurrExchangeRateDateACY;
            "Curr. Exchange Rate Date (ACY)")
            {
            }
            column(CurrencyExchangeRateACY;
            "Currency Exchange Rate (ACY)")
            {
            }
            column(TotalAssignedAmount;
            "Total Assigned Amount")
            {
            }
            column(TotalSalesAmount;
            "Total Sales Amount")
            {
            }
            column(BlanketSalesOrderNo;
            "Blanket Sales Order No.")
            {
            }
            column(SalesOrderNo;
            "Sales Order No.")
            {
            }
            column(LoadingDate;
            "Loading Date")
            {
            }
            column(DocumentDate;
            "Document Date")
            {
            }
            column(CustomerNo;
            "Customer No.")
            {
            }
            column(CustomerName;
            "Customer Name")
            {
            }
            column(BlanketOrderDate;
            "Blanket Order Date")
            {
            }
            column(SalesDueDate;
            "Sales Due Date")
            {
            }
            column(SalesShipmentMethodCode;
            "Sales Shipment Method Code")
            {
            }
            column(SalesPaymentMethodCode;
            "Sales Payment Method Code")
            {
            }
            column(SalesCurrencyCode;
            "Sales Currency Code")
            {
            }
            column(NotifyShiptoCode;
            "Notify Ship-to Code")
            {
            }
            column(ConsigneeShiptoCode;
            "Consignee Ship-to Code")
            {
            }
            column(NotifyShiptoName;
            "Notify Ship-to Name")
            {
            }
            column(ConsigneeShiptoName;
            "Consignee Ship-to Name")
            {
            }
            column(NotifyShiptoName2;
            "Notify Ship-to Name 2")
            {
            }
            column(ConsigneeShiptoName2;
            "Consignee Ship-to Name 2")
            {
            }
            column(NotifyShiptoAddress;
            "Notify Ship-to Address")
            {
            }
            column(ConsigneeShiptoAddress;
            "Consignee Ship-to Address")
            {
            }
            column(NotifyShiptoAddress2;
            "Notify Ship-to Address 2")
            {
            }
            column(ConsigneeShiptoAddress2;
            "Consignee Ship-to Address 2")
            {
            }
            column(NotifyShiptoCity;
            "Notify Ship-to City")
            {
            }
            column(ConsigneeShiptoCity;
            "Consignee Ship-to City")
            {
            }
            column(NotifyShiptoCounty;
            "Notify Ship-to County")
            {
            }
            column(ConsigneeShiptoCounty;
            "Consignee Ship-to County")
            {
            }
            column(NotifyShiptoCountryRegion;
            "Notify Ship-to Country/Region")
            {
            }
            column(ConsigneeShiptoCountry;
            "Consignee Ship-to Country")
            {
            }
            column(NoSeries;
            "No. Series")
            {
            }
            column(CountryofDeparture;
            "Country of Departure")
            {
            }
            column(PortofDeparture;
            "Port of Departure")
            {
            }
            column(ShipName;
            "Ship Name")
            {
            }
            column(ShippingAgent;
            "Shipping Agent")
            {
            }
            column(CountryofArrival;
            "Country of Arrival")
            {
            }
            column(PortofArrival;
            "Port of Arrival")
            {
            }
            column(TransportMethod;
            "Transport Method")
            {
            }
            column(EstimatedTimeofDelivery;
            "Estimated Time of Delivery")
            {
            }
            column(EstimatedTimeofDeparture;
            "Estimated Time of Departure")
            {
            }
            column(BillofLadingNo;
            "Bill of Lading No.")
            {
            }
            column(BookingNo;
            "Booking No.")
            {
            }
            column(LoadDescription;
            "Load Description")
            {
            }
            column(PostedSalesInvoiceNo;
            "Posted Sales Invoice No.")
            {
            }
            column(ExportInvoiceAmount;
            "Export Invoice Amount")
            {
            }
            column(BlanketSalesOrderAmount;
            "Blanket Sales Order Amount")
            {
            }
            column(CreationOrder;
            "Creation Order")
            {
            }
            column(SystemCreatedAt;
            SystemCreatedAt)
            {
            }
            column(SystemCreatedBy;
            SystemCreatedBy)
            {
            }
            column(BankAccountName;
            "Bank Account Name")
            {
            }
            column(BranchName;
            "Branch Name")
            {
            }
            column(EExportNo;
            "E-Export No.")
            {
            }
            column(IBAN;
            IBAN)
            {
            }
            column(SWIFTCode;
            "SWIFT Code")
            {
            }
            column(SalesPaymentTermsCode;
            "Sales Payment Terms Code")
            {
            }
        }
    }
}
