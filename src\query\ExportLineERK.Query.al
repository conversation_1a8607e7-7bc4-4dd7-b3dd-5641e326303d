query 60002 "Export Line ERK"
{
    Caption = 'Export Line ERK';
    QueryType = Normal;

    elements
    {
        dataitem(ExportLineERK;
        "Export Line ERK")
        {
            column(PurchaseDate;
            "Purchase Date")
            {
            }
            column(SalesCurrencyCode;
            "Sales Currency Code")
            {
            }
            column(DocumentNo;
            "Document No.")
            {
            }
            column(LineNo;
            "Line No.")
            {
            }
            column(ItemNo;
            "Item No.")
            {
            }
            column(VariantCode;
            "Variant Code")
            {
            }
            column(ItemDescription;
            "Item Description")
            {
            }
            column(Quantity;
            Quantity)
            {
            }
            column(UnitofMeasureCode;
            "Unit of Measure Code")
            {
            }
            column(UnitPrice;
            "Unit Price")
            {
            }
            column(UnitCost;
            "Unit Cost")
            {
            }
            column(Brand;
            Brand)
            {
            }
            column(PurchaseLineAmount;
            "Purchase Line Amount")
            {
            }
            column(SalesLineAmount;
            "Sales Line Amount")
            {
            }
            column(CountryRegionofOriginCode;
            "Country/Region of Origin Code")
            {
            }
            column(LoadQuantity;
            "Load Quantity")
            {
            }
            column(VendorNo;
            "Vendor No.")
            {
            }
            column(UnitCostLCY;
            "Unit Cost (LCY)")
            {
            }
            column(VendorName;
            "Vendor Name")
            {
            }
            column(UnitPriceLCY;
            "Unit Price (LCY)")
            {
            }
            column(PurchaseDueDate;
            "Purchase Due Date")
            {
            }
            column(PurchaseShipmentMethodCode;
            "Purchase Shipment Method Code")
            {
            }
            column(PurchaseCurrencyCode;
            "Purchase Currency Code")
            {
            }
            column(PurchaseReceiptNo;
            "Purchase Receipt No.")
            {
            }
            column(BlanketSalesOrderNo;
            "Blanket Sales Order No.")
            {
            }
            column(SystemCreatedAt;
            SystemCreatedAt)
            {
            }
            column(SystemCreatedBy;
            SystemCreatedBy)
            {
            }
            column(SystemModifiedAt;
            SystemModifiedAt)
            {
            }
            column(SystemModifiedBy;
            SystemModifiedBy)
            {
            }
        }
    }
    trigger OnBeforeOpen()
    begin
    end;
}
