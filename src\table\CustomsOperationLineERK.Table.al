table 60034 "Customs Operation Line ERK"
{
    Caption = 'Customs Operation Line';
    DataClassification = CustomerContent;
    LookupPageId = "Customs Operation Lines ERK";
    DrillDownPageId = "Customs Operation Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "No."; Code[20])
        {
            Caption = 'No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get("No.") then
                    Description := Item.Description
                else
                    Description := '';
            end;
        }
        field(4; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(5; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
            trigger OnValidate()
            begin
                "Line Amount" := CustomsOperationManagement.CalculateLineAmount(Rec);
            end;
        }
        field(6; "Unit Price"; Decimal)
        {
            Caption = 'Unit Price';
            ToolTip = 'Specifies the value of the Unit Price field.';
            trigger OnValidate()
            begin
                "Line Amount" := CustomsOperationManagement.CalculateLineAmount(Rec);
            end;
        }
        field(7; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
            trigger OnValidate()
            var
                CustomsOperationHeader: Record "Customs Operation Header ERK";
            begin
                CustomsOperationHeader.Get(Rec."Document No.");
                Rec."Unit Price" := CustomsOperationManagement.GetSalesPrice("No.", "Variant Code", CustomsOperationHeader."Bill-to Customer No.", CustomsOperationHeader."Registration Date");
            end;
        }
        field(8; "Line Amount"; Decimal)
        {
            Caption = 'Line Amount';
            Editable = false;
            ToolTip = 'Specifies the value of the Line Amount field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        CustomsOperationLine: Record "Customs Operation Line ERK";
    begin
        CustomsOperationLine.SetRange("Document No.", Rec."Document No.");
        if CustomsOperationLine.FindLast() then
            Rec."Line No." := CustomsOperationLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    var
        CustomsOperationManagement: Codeunit "Customs Operation Management";
}
