table 60049 "Vehicle Rev/Exp. Worksheet Hdr"
{
    Caption = 'Vehicle Rev/Exp. Worksheet Hdr';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            NotBlank = false;
            AllowInCustomizations = Always;
        }
        field(2; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = if (Type = const(Expense)) Vendor
            else if (Type = filter(Revenue | Consumption)) Customer;
            ToolTip = 'Specifies the value of the Customer No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
                Vendor: Record Vendor;
            begin
                if Customer.Get("Source No.") then
                    Rec."Source Name" := Customer.Name
                else
                    if Vendor.Get("Source No.") then
                        Rec."Source Name" := Vendor.Name;
            end;
        }
        field(3; "Source Name"; Text[100])
        {
            Caption = 'Source Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(4; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(5; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency;
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        field(6; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            begin
                Rec.Validate("Variant Code", '');
                Rec.Validate("Port Code", '');
            end;
        }
        field(7; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(8; Type; Enum "Voyage Line Detail Type ERK")
        {
            Caption = 'Type';
            ValuesAllowed = 0, 1, 2;
            ToolTip = 'Specifies the value of the Type field.';
            trigger OnValidate()
            begin
                Rec.Validate("Your Reference", '');
                Rec.Validate("External Document No.", '');
                Rec.Validate("Total Amount", 0);
                Rec.Validate("Source No.", '');
            end;
        }
        field(9; "Your Reference"; Code[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
        }
        field(10; "Port Code"; Code[10])
        {
            Caption = 'Port Code';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Port Code field.';
        }
        field(11; "Port Description"; Text[100])
        {
            Caption = 'Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Port Code")));
            ToolTip = 'Specifies the value of the Port Description field.';
        }
        field(12; "External Document No."; Code[35])
        {
            Caption = 'External Document No.';
            ToolTip = 'Specifies the value of the External Document No. field.';
        }
        field(13; "Total Amount"; Decimal)
        {
            Caption = 'Total Amount';
            ToolTip = 'Specifies the value of the Total Amount field.';
        }
        field(14; "Car Carrier No."; Code[20])
        {
            Caption = 'Car Carrier No.';
            TableRelation = "Car Carrier Header ERK"."No.";
            ToolTip = 'Specifies the value of the Car Carrier No. field.';
        }
        field(15; "Created From Invoice Line"; Boolean)
        {
            Caption = 'Created From Invoice Line';
            ToolTip = 'If the line is created from an invoice line, this field is set to true.';
            Editable = false;
        }
        field(16; "Invoice No."; Code[20])
        {
            Caption = 'Invoice No.';
            ToolTip = 'Specifies the value of the Invoice No. field.';
            Editable = false;
        }
        field(17; "Invoice Line No."; Integer)
        {
            Caption = 'Invoice Line No.';
            ToolTip = 'Specifies the value of the Invoice Line No. field.';
            Editable = false;
        }
        // field(18; "Total Line Amount"; Decimal)
        // {
        //     Caption = 'Total Line Amount';
        //     ToolTip = 'Specifies the value of the Total Line Amount field.';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Vehicle Rev/Exp Worksheet Line"."Unit Amount" where("Document No." = field("No.")));
        // }
        field(18; "Department Code"; Code[20])
        {
            Caption = 'Department Code';
            AllowInCustomizations = Always;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
}
