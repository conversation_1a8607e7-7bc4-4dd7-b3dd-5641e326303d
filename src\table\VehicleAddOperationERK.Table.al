table 60045 "Vehicle Add. Operation ERK"
{
    Caption = 'Vehicle Fuel Operation';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; PK; Code[10])
        {
            Caption = 'PK';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
            trigger OnValidate()
            var
                SerialNoInformation: Record "Serial No. Information";
                VehicleFuelLedgerEntry: Record "Vehicle Fuel Ledger Entry ERK";
                SerialNoFilter: Text;
                RefillQst: Label 'You will refuel the same serial number more than once. Do you want to continue?';
                CancelErr: Label 'You have canceled the operation.';
            begin
                if Rec."Serial No." = '' then
                    exit;
                SerialNoFilter := '*' + Rec."Serial No.";
                SerialNoInformation.SetFilter("Serial No.", SerialNoFilter);
                SerialNoInformation.FindFirst();
                Rec."Serial No." := SerialNoInformation."Serial No.";
                VehicleFuelLedgerEntry.SetRange("Serial No.", Rec."Serial No.");
                VehicleFuelLedgerEntry.SetRange("Operation Type", Rec."Operation Type");
                if not VehicleFuelLedgerEntry.IsEmpty() then
                    if not ConfirmManagement.GetResponseOrDefault(RefillQst, true) then
                        Error(CancelErr);
                Rec.Validate("Fuel Type", SerialNoInformation."Fuel Type ERK");
            end;
        }
        field(3; "Quantity (LT)"; Decimal)
        {
            Caption = 'Quantity (LT)';
            MinValue = 1;
            MaxValue = 5;
            ToolTip = 'Specifies the value of the Quantity (LT) field.';
        }
        field(4; "Fuel Type"; Enum "Fuel Type ERK")
        {
            Caption = 'Fuel Type';
            ToolTip = 'Specifies the value of the Fuel Type field.';
            // trigger OnValidate()
            // begin
            //     Rec.Validate("Serial No.", '');
            //     Rec.Validate("Quantity (LT)", 0);
            //     Rec.Validate("Card ID", '');
            //     Rec.Validate("Charge Station ID", '');
            //     Rec.Validate("kWh", 0);
            // end;
        }
        field(5; "Card ID"; Code[10])
        {
            Caption = 'Card ID';
            TableRelation = "Fuel Card ERK".ID;
            ToolTip = 'Specifies the value of the Card ID field.';
        }
        field(6; "Charge Station ID"; Code[20])
        {
            Caption = 'Charge Station ID';
            TableRelation = "Fuel Station ERK".ID;
            ToolTip = 'Specifies the value of the Charge Station ID field.';
        }
        field(7; kWh; Decimal)
        {
            Caption = 'kWh';
            DecimalPlaces = 2 : 2;
            ToolTip = 'Specifies the value of the kWh field.';
        }
        field(8; "Operation Type"; Enum "Vehicle Add. Operation Type")
        {
            Caption = 'Operation Type';
            ToolTip = 'Specifies the value of the Operation Type field.';
        }
        field(9; "Tire Location"; Enum "Tire Location ERK")
        {
            Caption = 'Tire Location';
            ToolTip = 'Specifies the value of the Tire Location field.';
        }
    }
    keys
    {
        key(PK; PK)
        {
            Clustered = true;
        }
    }
    var
        ConfirmManagement: Codeunit "Confirm Management";
}
