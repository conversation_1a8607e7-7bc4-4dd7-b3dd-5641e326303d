pageextension 60019 "Employee Card ERK" extends "Employee Card"
{
    layout
    {
        // modify(NappAnLAnnualLeaveGroup)
        // {
        //     Visible = false;
        // }
        addbefore("Job Title")
        {
            field("Job Title Code ERK"; Rec."Job Title Code ERK")
            {
                ApplicationArea = All;
            }
        }
        addafter("Job Title")
        {
            field("Position Level Code ERK"; Rec."Position Level Code ERK")
            {
                ApplicationArea = All;
            }
            field("Posititon Level Desc. ERK"; Rec."Posititon Level Desc. ERK")
            {
                ApplicationArea = All;
            }
            field("Global Dimension 1 Code ERK"; Rec."Global Dimension 1 Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Global Dimension 1 Code field.';
            }
            field("Department Code Desc. ERK"; Rec."Department Code Desc. ERK")
            {
                ApplicationArea = All;
            }
            field("Manager No. ERK"; Rec."Manager No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Manager No. field.';
            }
            field("Manager Full Name ERK"; Rec."Manager Full Name ERK")
            {
                ApplicationArea = All;
            }
        }
    }
}
