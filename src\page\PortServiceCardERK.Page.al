page 60145 "Port Service Card ERK"
{
    ApplicationArea = All;
    Caption = 'Port Service Card';
    PageType = Card;
    SourceTable = "Port Service Header ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                field("Starting Date"; Rec."Starting Date")
                {
                }
                field("Ending Date"; Rec."Ending Date")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
            }

            group(CustomerInfo)
            {
                Caption = 'Customer Information';

                field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                {
                }
                field("Bill-to Customer Name"; Rec."Bill-to Customer Name")
                {
                }
                field("Load Owner Name"; Rec."Load Owner Name")
                {
                }
            }

            group(LoadDetails)
            {
                Caption = 'Load Details';

                field("Load Type"; Rec."Load Type")
                {
                }
                field("Parent Load Type"; Rec."Parent Load Type")
                {
                }
                field("Port Name"; Rec."Port Name")
                {
                }
                field("Shortcut Dimension Code 1"; Rec."Shortcut Dimension Code 1")
                {
                }
                field("Declared Quantity (Tonnage)"; Rec."Declared Quantity (Tonnage)")
                {
                }
                field("Actual Quantity (Tonnage)"; Rec."Actual Quantity (Tonnage)")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
            }

            part(Lines; "Port Service Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(CreateSalesInvoice)
            {
                Caption = 'Create Sales Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NewSalesInvoice;
                PromotedOnly = true;
                ToolTip = 'Creates a sales invoice from the port service document.';

                trigger OnAction()
                var
                //PortServiceManagement: Codeunit "Port Service Management ERK";
                begin
                    // PortServiceManagement.CreateSalesInvoiceFromPortService(Rec);
                    Message('Create Sales Invoice functionality will be implemented.');
                end;
            }

            action(PortServiceLines)
            {
                Caption = 'Port Service Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Shipment;
                PromotedOnly = true;
                ToolTip = 'View all port service lines for this document.';
                RunObject = page "Port Service Lines ERK";
                RunPageLink = "Document No." = field("No.");
            }
        }
    }
}