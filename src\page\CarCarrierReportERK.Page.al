page 60089 "Car Carrier Report ERK"
{
    ApplicationArea = All;
    Caption = 'Car Carrier Report';
    PageType = List;
    SourceTable = "Car Carrier Line Detail ERK";
    UsageCategory = ReportsAndAnalysis;
    SourceTableView = where("Document No." = filter('C*')); //where("Voyage Ending Date-Time" = filter(<> ''),
    //"Document No." = filter('C*'));
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field(Year; Rec.Year)
                {
                }
                field(Month; Rec.Month)
                {
                }
                field(Week; Rec.Week)
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Loading Port"; Rec."Loading Port")
                {
                }
                field("Loading Port Description"; Rec."Loading Port Description")
                {
                }
                field("Discharge Port"; Rec."Discharge Port")
                {
                }
                field("Discharge Port Description"; Rec."Discharge Port Description")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field(TotalSalesACY; Rec."Total Sales (ACY)")
                {
                }
                field(TotalProfitACY; Rec."Total Profit (ACY)")
                {
                }
                field(Profitability; Rec.Profitability)
                {
                }

                field(LoadPortCluster; GetLoadPortCluster())
                {
                    Caption = 'LoadPortCluster';
                    ToolTip = 'Specifies the value of the LoadPortCluster field.';
                }
                field(DischargePortCluster; GetDischargePortCluster())
                {
                    Caption = 'LoadPortCluster';
                    ToolTip = 'Specifies the value of the LoadPortCluster field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CalcualteRevenueExpense)
            {
                Caption = 'Calculate Revenue Expense';
                Promoted = true;
                PromotedCategory = Process;
                Image = Calculate;
                ToolTip = 'Executes the Calculate Revenue Expense action.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    VehicleRevExpManagement.CarCarrierReportJobQueue();
                end;
            }
        }
    }


    local procedure GetLoadPortCluster(): Text
    var
        EntryExitPoint: Record "Entry/Exit Point";
    begin
        if Rec."Loading Port" <> '' then
            if EntryExitPoint.Get(Rec."Loading Port") then begin
                EntryExitPoint.CalcFields("Port Cluster Description ERK");
                exit(EntryExitPoint."Port Cluster Description ERK");
            end;
        exit('');
    end;

    local procedure GetDischargePortCluster(): Text
    var
        EntryExitPoint: Record "Entry/Exit Point";
    begin
        if Rec."Loading Port" <> '' then
            if EntryExitPoint.Get(Rec."Discharge Port") then begin
                EntryExitPoint.CalcFields("Port Cluster Description ERK");
                exit(EntryExitPoint."Port Cluster Description ERK");
            end;


        exit('');
    end;

    var
        VehicleRevExpManagement: Codeunit "Vehicle Rev./Exp. Management";
}
