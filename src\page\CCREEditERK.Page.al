page 60155 "CCRE - Edit ERK"
{
    ApplicationArea = All;
    Caption = 'CCRE - Edit';
    PageType = List;
    SourceTable = "Car Carr. Revenue/Expense ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("UoM Code"; Rec."UoM Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit Price/Cost"; Rec."Unit Price/Cost")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Line Amount"; Rec."Line Amount")
                {
                }
                field("Line Amount (ACY)"; Rec."Line Amount (ACY)")
                {
                }
                field("Source No."; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("External Document No."; Rec."External Document No.")
                {
                }
                field("Unposted Invoice No."; Rec."Unposted Invoice No.")
                {
                }
                field("Posted Invoice No."; Rec."Posted Invoice No.")
                {
                }
                field("Distrubuted Quantity"; Rec."Distrubuted Quantity")
                {
                }
                field("Port Code"; Rec."Port Code")
                {
                }
                field("Port Description"; Rec."Port Description")
                {
                }
                field(Notes; Rec.Notes)
                {
                }
                field("Your Reference"; Rec."Your Reference")
                {
                }
                field("Consumption Amount Calculated"; Rec."Consumption Amount Calculated")
                {
                }
                field("Source Proforma Line No."; Rec."Source Proforma Line No.")
                {
                }
                field("Source Car Carrier Order No."; Rec."Source Car Carrier Order No.")
                {
                }
                field("Source Car Carr.Order Line No."; Rec."Source Car Carr.Order Line No.")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Car Carrier Starting Date-Time"; Rec."Car Carrier Starting Date-Time")
                {
                }
                field("Car Carrier Ending Date-Time"; Rec."Car Carrier Ending Date-Time")
                {
                }
                field("Source Realized Line No."; Rec."Source Realized Line No.")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Planned Starting Date"; Rec."Planned Starting Date")
                {
                    ToolTip = 'Specifies the value of the Planned Starting Date field.';
                }
                field("Planned Ending Date"; Rec."Planned Ending Date")
                {
                    ToolTip = 'Specifies the value of the Planned Ending Date field.';
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Clear Doc No. & Reassign Line No.")
            {
                Caption = 'Clear Doc No. & Reassign Line No.';
                ToolTip = 'Clear Document No. field and reassign Line No. for selected records based on existing empty Document No. records.';
                Image = ClearLog;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                trigger OnAction()
                var
                    CarCarrRevenueExpenseERK: Record "Car Carr. Revenue/Expense ERK";
                    CarCarrRevExpRename: Codeunit "Car Carr Rev/Exp Rename ERK";
                    NoRecordsSelectedErr: Label 'No records selected.';
                    ConfirmMsg: Label 'Are you sure you want to clear Document No. and reassign Line No. for %1 selected record(s)?', Comment = '%1=Count of selected records';
                begin
                    CurrPage.SetSelectionFilter(CarCarrRevenueExpenseERK);
                    if CarCarrRevenueExpenseERK.IsEmpty() then
                        Error(NoRecordsSelectedErr);

                    if Confirm(ConfirmMsg, false, CarCarrRevenueExpenseERK.Count()) then begin
                        CarCarrRevExpRename.ClearDocumentNoAndReassignLineNumbers(CarCarrRevenueExpenseERK);
                        CurrPage.Update(false);
                    end;
                end;
            }
            action("Delete Selected")
            {
                Caption = 'Delete Selected';
                ToolTip = 'Delete the selected records without running OnDelete trigger.';
                Image = Delete;
                Visible = false; // Set to true if you want this action to be visible

                trigger OnAction()
                var
                    CarCarrRevenueExpenseERK: Record "Car Carr. Revenue/Expense ERK";
                    NoRecordsSelectedErr: Label 'No records selected.';
                begin
                    CurrPage.SetSelectionFilter(CarCarrRevenueExpenseERK);
                    if CarCarrRevenueExpenseERK.IsEmpty() then
                        Error(NoRecordsSelectedErr);

                    if Confirm('Are you sure you want to delete %1 selected record(s) without running validation?', false, CarCarrRevenueExpenseERK.Count()) then
                        CarCarrRevenueExpenseERK.DeleteAll(false);
                end;
            }

            action("Update Unposted Invoice No.")
            {
                Caption = 'Unposted Invoice No.';
                ToolTip = 'Unposted Invoice No.';
                Image = UpdateDescription;

                trigger OnAction()
                begin
                    UpdateUnpostedInoviceNo();
                end;
            }
        }
    }

    local procedure UpdateUnpostedInoviceNo()
    var
        SalesLine: Record "Sales Line";
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
    begin
        if Rec.Type = Rec.Type::Expense then begin

            PurchaseHeader.Reset();
            PurchaseHeader.SetRange("Pay-to Vendor No.", Rec."Source No.");
            PurchaseHeader.SetRange("Posting Date", Rec."Posting Date");
            PurchaseHeader.SetRange("Document Type", PurchaseHeader."Document Type"::Invoice);
            if PurchaseHeader.FindSet() then
                repeat
                    PurchaseLine.Reset();
                    PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
                    PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
                    PurchaseLine.SetRange("No.", Rec."No.");
                    PurchaseLine.SetRange(Type, SalesLine.Type::Item);
                    PurchaseLine.SetRange("Shortcut Dimension 2 Code", Rec."Document No.");
                    //PurchaseLine.SetRange("Location Code", Rec."Ship No.");
                    //PurchaseLine.SetRange("Bin Code", Rec."Ship No.");
                    PurchaseLine.SetRange("Pay-to Vendor No.", Rec."Source No.");
                    PurchaseLine.SetRange(Quantity, Rec.Quantity);
                    if PurchaseLine.FindFirst() then begin
                        Rec."Unposted Invoice No." := PurchaseLine."Document No.";
                        Rec.Modify(false);
                    end;
                until PurchaseHeader.Next() = 0;

        end else begin

            SalesLine.Reset();
            SalesLine.SetRange("Document Type", SalesLine."Document Type"::Invoice);
            SalesLine.SetRange("Posting Date", Rec."Posting Date");
            SalesLine.SetRange("No.", Rec."No.");
            SalesLine.SetRange(Type, SalesLine.Type::Item);
            SalesLine.SetRange("Shortcut Dimension 2 Code", Rec."Document No.");
            //SalesLine.SetRange("Location Code", Rec."Ship No.");
            //SalesLine.SetRange("Bin Code", Rec."Ship No.");
            SalesLine.SetRange("Bill-to Customer No.", Rec."Source No.");
            SalesLine.SetRange(Quantity, Rec.Quantity);
            if SalesLine.FindFirst() then begin
                Rec."Unposted Invoice No." := SalesLine."Document No.";
                Rec.Modify(false);
            end;
        end;
    end;
}