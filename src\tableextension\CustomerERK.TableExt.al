tableextension 60005 "Customer ERK" extends Customer
{
    fields
    {
        field(60000; "Company Bank Account Code ERK"; Code[20])
        {
            Caption = 'Company Bank Account Code';
            TableRelation = "Bank Account" where("Currency Code" = field("Currency Code"));
            ToolTip = 'Specifies the value of the Bank Account No. field.';
        }
        field(60001; "Company Bank Account Name ERK"; Text[100])
        {
            Caption = 'Company Bank Account Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Bank Account".Name where("No." = field("Company Bank Account Code ERK")));
            ToolTip = 'Specifies the value of the Bank Account Name field.';
        }
        field(60002; "Tax Area Description ERK"; Text[100])
        {
            Caption = 'Tax Area Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Tax Area".Description where(Code = field("Tax Area Code")));
            ToolTip = 'Specifies the value of the Tax Area Description field.';
        }
        field(60003; "Salesperson Name ERK"; Text[50])
        {
            Caption = 'Salesperson Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Salesperson/Purchaser".Name where(Code = field("Salesperson Code")));
            ToolTip = 'Specifies the value of the Salesperson Name field.';
        }
        field(60004; "Erk Holding Intercompany ERK"; Boolean)
        {
            Caption = 'Erk Holding Intercompany';
            ToolTip = 'Specifies the value of the Erk Holding Intercompany field.';
        }
        field(60100; "Transfer Reverse Amount ERK"; Boolean)
        {
            Caption = 'Transfer Reverse Amount';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies whether the customer should be included in the Transfer Negative Balances process.';
        }
    }
}
