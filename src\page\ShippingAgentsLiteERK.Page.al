page 60134 "Shipping Agents Lite ERK"
{
    ApplicationArea = All;
    Caption = 'Shipping Agents Lite';
    PageType = List;
    SourceTable = "Shipping Agent";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Code; Rec.Code)
                {
                    ToolTip = 'Specifies Code.';
                }
                field(Name; Rec.Name)
                {
                    ToolTip = 'Specifies Name.';
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        //Rec.SetCurrentKey(Code);
        Rec.Ascending(false); // Set to false for Z to A sorting
        Rec.FindFirst();
    end;
}