query 60004 "Car Carrier Line API ERK"
{
    Caption = 'Car Carrier Line API';
    APIGroup = 'erkHoldingCustomization';
    APIPublisher = 'infotek';
    APIVersion = 'v1.0';
    EntityName = 'carCarrierLine';
    EntitySetName = 'carCarrierLines';
    QueryType = API;

    elements
    {
        dataitem(carCarrierLineERK;
        "Car Carrier Line ERK")
        {
            column(departurePort;
            "Departure Port")
            {
            }
            column(departureDateTime;
            "Departure Date-Time")
            {
            }
            column(arrivalPort;
            "Arrival Port")
            {
            }
            column(arrivalDateTime;
            "Arrival Date-Time")
            {
            }
            column(loadedQuantity;
            "Loaded Quantity")
            {
            }
            column(dischargedQuantity;
            "Discharged Quantity")
            {
            }
            column(departurePortDescription;
            "Departure Port Description")
            {
            }
            column(arrivalPortDescription;
            "Arrival Port Description")
            {
            }
            column(shipName;
            "Ship Name")
            {
            }
        }
    }
    trigger OnBeforeOpen()
    begin
    end;
}
