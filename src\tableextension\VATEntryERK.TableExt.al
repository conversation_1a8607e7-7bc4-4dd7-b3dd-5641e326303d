tableextension 60024 "VAT Entry ERK" extends "VAT Entry"
{
    fields
    {
        field(60000; "E-Doc. Exemption Type Code ERK"; Code[20])
        {
            Caption = 'E-Document Exemption Type Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Line"."E-Doc. Exemption Type Code INF" where("Document No." = field("Document No.")));
            ToolTip = 'Specifies the value of the E-Document Exemption Type Code field.';
        }
        field(60001; "E-Document Exemption Code ERK"; Code[10])
        {
            Caption = 'E-Document Exemption Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Line"."E-Document Exemption Code INF" where("Document No." = field("Document No.")));
            ToolTip = 'Specifies the value of the E-Document Exemption Code field.';
        }
    }
}
