page 60083 "Car Carrier Order Line Dtl ERK"
{
    ApplicationArea = All;
    Caption = 'Load Details';
    PageType = List;
    SourceTable = "Car Carrier Order Line Dtl ERK";
    UsageCategory = Lists;
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Discharge Port Code"; Rec."Discharge Port Code")
                {
                }
                field("Your Reference"; Rec."Your Reference")
                {
                }
                field("Transhipment Allowed"; Rec."Transhipment Allowed")
                {
                }
                field("Planned Car Carrier No."; Rec."Planned Car Carrier No.")
                {
                    Editable = false;
                }
                field("Pre-Load Quantity"; Rec."Pre-Load Quantity")
                {
                }
                field("Loaded Quantity"; Rec."Loaded Quantity")
                {
                }
                field("Discharged Quantity"; Rec."Discharged Quantity")
                {
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Shipper No."; Rec."Shipper No.")
                {
                }
                field("Shipper Name"; Rec."Shipper Name")
                {
                }
                field("Consignee No."; Rec."Consignee No.")
                {
                }
                field("Consignee Name"; Rec."Consignee Name")
                {
                }
                field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                {
                }
                field("Bill-to Customer Name"; Rec."Bill-to Customer Name")
                {
                }
                field("Vessel Name"; Rec."Vessel Name")
                {
                }
                field("Discharge Date-Time"; Rec."Discharge Date-Time")
                {
                }
                field("Discharge Port Shipment Method"; Rec."Discharge Port Shipment Method")
                {
                }
                field("Loading Date-Time"; Rec."Loading Date-Time")
                {
                }
                field("Loading Port Shipment Method"; Rec."Loading Port Shipment Method")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(SetTranshipmentAllowedFilter)

            {
                Caption = 'Set Transhipment Allowed Filter';
                Image = FilterLines;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Set Transhipment Allowed Filter action.';

                trigger OnAction()
                begin
                    Rec.SetRange("Planned Car Carrier No.");
                    Rec.SetRange("Transhipment Allowed", true);
                    Rec.SetFilter(Status, '<>%1', Rec.Status::Completed);
                end;
            }
        }
    }
    procedure SetPlannedCarCarrierNo(ParamPlannedCarCarrierNo: Code[20])
    begin
        PlannedCarCarrierNo := ParamPlannedCarCarrierNo;
    end;

    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        Rec.Validate("Planned Car Carrier No.", PlannedCarCarrierNo);
    end;

    trigger OnClosePage()
    var
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
        CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
    begin
        CarCarrierOrderLineDtl.SetAutoCalcFields("Loaded Quantity", "Discharged Quantity");
        CarCarrierOrderLine.SetRange("Document No.", Rec."Document No.");
        CarCarrierOrderLine.SetRange("Planned Car Carrier No.", Rec."Planned Car Carrier No.");
        CarCarrierOrderLineDtl.SetRange("Document No.", Rec."Document No.");
        CarCarrierOrderLineDtl.SetRange("Planned Car Carrier No.", Rec."Planned Car Carrier No.");
        if CarCarrierOrderLineDtl.IsEmpty() then
            exit;
        CarCarrierOrderLineDtl.SetRange("Loaded Quantity", 0);
        if not CarCarrierOrderLineDtl.IsEmpty() then begin
            CarCarrierOrderLine.ModifyAll(Status, CarCarrierOrderLine.Status::Planned, true);
            exit;
        end;
        CarCarrierOrderLineDtl.SetRange("Loaded Quantity");
        CarCarrierOrderLineDtl.SetRange("Discharged Quantity", 0);
        if not CarCarrierOrderLineDtl.IsEmpty() then begin
            CarCarrierOrderLine.ModifyAll(Status, CarCarrierOrderLine.Status::Loaded, true);
            exit;
        end;
        CarCarrierOrderLine.ModifyAll(Status, CarCarrierOrderLine.Status::Discharged, true);
        // if CarCarrierOrderLineDtl.FindSet(false) then
        //     repeat
        //         if CarCarrierOrderLineDtl."Loaded Quantity" = 0 then begin
        //             CarCarrierOrderLine.SetRange("Document No.", CarCarrierOrderLineDtl."Document No.");
        //             CarCarrierOrderLine.SetRange("Planned Car Carrier No.", CarCarrierOrderLineDtl."Planned Car Carrier No.");
        //             CarCarrierOrderLine.ModifyAll("Status", CarCarrierOrderLine.Status::Planned, true);
        //             //exit;
        //         end
        //         else
        //             if CarCarrierOrderLineDtl."Discharged Quantity" = 0 then begin
        //                 CarCarrierOrderLine.SetRange("Document No.", CarCarrierOrderLineDtl."Document No.");
        //                 CarCarrierOrderLine.SetRange("Planned Car Carrier No.", CarCarrierOrderLineDtl."Planned Car Carrier No.");
        //                 CarCarrierOrderLine.ModifyAll("Status", CarCarrierOrderLine.Status::Loaded, true);
        //                 //exit;
        //             end;
        //     until CarCarrierOrderLineDtl.Next() = 0;
        // //CarCarrierOrderLine.ModifyAll("Status", CarCarrierOrderLine.Status::Discharged, true);
    end;

    var
        PlannedCarCarrierNo: Code[20];
    // actions
    // {
    //     area(Processing)
    //     {
    //         action(CarCarrierOrderVehicles)
    //         {
    //             ApplicationArea = All;
    //             Caption = 'Car Carrier Order Vehicles';
    //             Image = LinesFromJob;
    //             RunObject = Page "Car Carrier Order Vehicles ERK";
    //             RunPageLink = "Document No." = field("Document No."), "Document Line No." = field("Line No."), "Document Line Detail No." = field("Line No.");
    //             ToolTip = 'Executes the Car Carrier Order Vehicles action.';
    //             Promoted = true;
    //             PromotedCategory = Process;
    //             PromotedIsBig = true;
    //             PromotedOnly = true;
    //         }
    //         action(ImportPreLoadVehicles)
    //         {
    //             ApplicationArea = All;
    //             Caption = 'Import Pre-Load Vehicles';
    //             Image = Import;
    //             //RunObject = Codeunit "Import Pre-Load Vehicles";
    //             ToolTip = 'Executes the Import Pre-Load Vehicles action.';
    //             Promoted = true;
    //             PromotedCategory = Process;
    //             PromotedIsBig = true;
    //             PromotedOnly = true;
    //             trigger OnAction()
    //             var
    //                 ImportPreLoadVehicles: Page "Import Pre-Load Vehicles ERK";
    //             begin
    //                 ImportPreLoadVehicles.SetCarCarrierOrderLineDetailRecord(Rec);
    //                 ImportPreLoadVehicles.RunModal();
    //             end;
    //         }
    //     }
    // }
    // var
    //     CarCarrierOrderMngt: Codeunit "Car Carrier Order Mngt. ERK";
}
