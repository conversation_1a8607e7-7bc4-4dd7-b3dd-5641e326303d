# Değişiklik Günlüğü - Erk Holding Özelleştirmeleri

## Versiyon 25.5.0.11 (Haziran 2025)

### 🚢 Araç <PERSON>ıyıcı Yönetimi

#### Yeni Özellikler
- **KDV Ürün Deftere Nakil Grubu Desteği**: Car Carrier Revenue/Expense kayıtlarına KDV ürün deftere nakil grubu alanı eklendi
- **Otomatik KDV Grubu Atama**: Stok kaleminden otomatik olarak KDV ürün deftere nakil grubu kopyalanır
- **İnvoice Oluşturma İyileştirmeleri**: Satış ve satın alma faturaları oluşturulurken KDV ürün deftere nakil grubu otomatik olarak aktarılır

#### Geliştirmeler
- **Road Transport Hariç Filtreleme**: Balast yolculuk oluşturma işleminde "Road Transport" türü hariç tutuldu
- **Metin <PERSON>lu<PERSON>tmesi**: "At Sea" etiketi için metin uzunluğu sınırlaması eklendi (10 karakter)
- **Car Carrier'dan Oluşturulmuş İşaret**: Satış ve satın alma faturalarına "Car Carrier'dan Oluşturuldu" alanı eklendi

### 💼 Fatura Yönetimi

#### Kullanıcı Arayüzü İyileştirmeleri
- **Fatura Görünürlüğü**: Satış ve satın alma faturalarında "Car Carrier'dan Oluşturuldu" alanı görünür hale getirildi
- **Araç Dağıtım Çalışma Sayfası**: Car Carrier'dan oluşturulan faturalar için araç dağıtım çalışma sayfası düğmesi devre dışı bırakıldı
- **KDV Ürün Deftere Nakil Grubu**: Revenue/Expenses sayfasında KDV ürün deftere nakil grubu alanı eklendi

#### Fonksiyonel İyileştirmeler
- **Stok Kalemi Temizleme**: Stok kalem numarası silindiğinde açıklama, ölçü birimi ve KDV grubu alanları otomatik temizlenir
- **Fatura Satır Kontrolü**: Car Carrier'dan oluşturulan faturalarda kullanıcı müdahalesini engelleyen kontroller eklendi

### 🔧 Teknik İyileştirmeler

#### Kod Kalitesi
- **Type Safety**: Metin uzunluğu kontrollerinde CopyStr fonksiyonu kullanımı
- **Otomatik Alan Doldurma**: Stok kalemi seçildiğinde ilişkili alanların otomatik doldurulması
- **Veri Tutarlılığı**: Kayıt silindiğinde ilişkili alanların temizlenmesi

#### Performans İyileştirmeleri
- **Filtreleme Optimizasyonu**: Car Carrier sorgularında gereksiz kayıtların filtrelenmesi
- **Otomatik Hesaplamalar**: KDV ve fiyat hesaplamalarının optimize edilmesi

### 📋 Veri Modeli Güncellemeleri

#### Yeni Alanlar
- `"VAT Prod. Posting Group"` - Car Carr. Revenue/Expense ERK tablosunda
- `"Created From Car Carrier ERK"` - Sales Header ve Purchase Header tablolarında

#### Mevcut Alanların İyileştirilmesi
- Stok kalemi validasyonlarının güçlendirilmesi
- Otomatik alan doldurma mantığının geliştirilmesi

### ✅ Hata Düzeltmeleri

- **Metin Uzunluğu Hatası**: "At Sea" durumu için metin uzunluğu sınırlaması sorunu çözüldü
- **Fatura Satır Müdahalesi**: Car Carrier'dan oluşturulan faturalarda istenmeyen kullanıcı müdahalesinin engellenmesi
- **Veri Tutarlılığı**: Stok kalemi değişikliklerinde ilişkili alanların tutarlı şekilde güncellenmesi

---
