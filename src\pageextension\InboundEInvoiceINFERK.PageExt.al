pageextension 60047 "Inbound E-Invoice INF ERK" extends "Inbound E-Invoice INF"
{
    layout
    {
        addafter("Source No.")
        {
            field("Source Name ERK"; ErkHoldingBasicFunctions.GetVendorNameFromVendorNo(Rec."Source No."))
            {
                ApplicationArea = All;
                Caption = 'Source Name';
                ToolTip = 'Specifies the value of the Source Name field.';
            }
        }

        addlast(General)
        {
            field("EBA Status ERK"; Rec."EBA Status ERK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the EBA Status field.';



                trigger OnValidate()
                var
                    ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
                    ErrLbl: Label 'You do not have permission to change EBA Status.';
                begin
                    if not ErkHoldingBasicFunctions.HasUpdateStatusTypePermissionINF() then
                        Error(ErrLbl);

                end;

            }
            field("PDFExistERK ERK"; Rec.GetPDFFileData())
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the PDF Exist field.';
                Caption = 'PDF File';
                Editable = false;


                trigger OnAssistEdit()
                begin
                    Message(Rec.GetPDFFileData());
                end;
            }
        }
    }



    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
