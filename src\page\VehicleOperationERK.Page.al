page 60090 "Vehicle Operation ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle Operation';
    PageType = StandardDialog;
    SourceTable = "Vehicle Operation ERK";
    UsageCategory = Tasks;
    SourceTableTemporary = true;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("Document No."; Rec."Document No.")
                {
                }
                field("Operation Type"; Rec."Operation Type")
                {
                    ValuesAllowed = 0, 4, 5, 8, 9, 14;
                }
                field("To Location Code"; Rec."To Location Code")
                {
                    QuickEntry = false;
                }
                field("Shippping Agent Code"; Rec."Shippping Agent Code")
                {
                    QuickEntry = false;
                    ShowMandatory = true;

                    trigger OnValidate()
                    var
                        ShippingAgent: Record "Shipping Agent";
                    begin
                        ShippingAgent.Get(Rec."Shippping Agent Code");
                        TempTrafficDocumentVisible := ShippingAgent."Req.Temp. Traffic Doc. ERK";
                        CurrPage.Update();
                    end;

                    trigger OnAssistEdit()
                    var
                        ShippingAgent: Record "Shipping Agent";
                        ShippingAgents: Page "Shipping Agents Lite ERK";
                    begin
                        ShippingAgents.LookupMode(true);
                        if ShippingAgents.RunModal() = Action::LookupOK then begin
                            ShippingAgents.GetRecord(ShippingAgent);
                            Rec.Validate("Shippping Agent Code", ShippingAgent.Code);
                            TempTrafficDocumentVisible := ShippingAgent."Req.Temp. Traffic Doc. ERK";
                            CurrPage.Update();
                        end;
                    end;
                }
                field("License Plate"; Rec."License Plate")
                {
                    Editable = TempTrafficDocumentVisible;
                }
                field("Driver Full Name"; Rec."Driver Full Name")
                {
                    Editable = TempTrafficDocumentVisible;
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                    QuickEntry = false;
                    Editable = Rec."Operation Type" = Rec."Operation Type"::"Dealer Dispatch";
                }
                field("Serial No."; Rec."Serial No.")
                {
                    trigger OnValidate()
                    begin
                        LastSuccesfullSerialNo := Rec."Serial No.";
                        Rec."Serial No." := '';
                    end;
                }
                field(LastSuccesfullSerialNo; LastSuccesfullSerialNo)
                {
                    Caption = 'Last Succesfull Serial No.';
                    ToolTip = 'Specifies the value of the Last Succesfull Serial No. field.';
                    Editable = false;
                }
            }
        }
    }
    var
        TempTrafficDocumentVisible: Boolean;
        LastSuccesfullSerialNo: Code[50];

    trigger OnOpenPage()
    begin
        Rec.Init();
        Rec.Insert(false);
    end;
}
