pageextension 60027 "Purchase Order List ERK" extends "Purchase Order List"
{
    layout
    {
        addafter("Buy-from Vendor Name")
        {
            field("Vendor Invoice No. ERK"; Rec."Vendor Invoice No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the document number of the original document.';
                //ToolTip = 'Specifies the document number of the original document you received from the vendor. You can require the document number for posting, or let it be optional. By default, it''s required, so that this document references the original. Making document numbers optional removes a step from the posting process. For example, if you attach the original invoice as a PDF, you might not need to enter the document number. To specify whether document numbers are required, in the Purchases & Payables Setup window, select or clear the Ext. Doc. No. Mandatory field.';
            }
        }
    }
    actions
    {
        addlast("F&unctions")
        {
            action("SetPurchaseEInvoiceType ERK")
            {
                ApplicationArea = All;
                Caption = 'Set Purchase E-Invoice Type ERK';
                ToolTip = 'Set the E-Invoice Type for Purchase Orders based on the VAT Registration No. and Posting Date.';
                Image = ElectronicDoc;

                trigger OnAction()
                var
                    PurchaseHeader: Record "Purchase Header";
                    EBAIntegrationMngt: Codeunit "EBA Integration Mngt. ERK";
                begin
                    PurchaseHeader.Reset();
                    CurrPage.SetSelectionFilter(PurchaseHeader);
                    if PurchaseHeader.FindSet() then
                        repeat
                            PurchaseHeader."Invoice Type INF ERK" := EBAIntegrationMngt.SetPurchaseEInvoiceType(PurchaseHeader."VAT Registration No.", PurchaseHeader."Posting Date");
                            PurchaseHeader.Modify(false);
                        until PurchaseHeader.Next() = 0;
                end;

            }
        }
    }
}
