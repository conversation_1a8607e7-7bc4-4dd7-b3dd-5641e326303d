pageextension 60029 "Serial No. Information List" extends "Serial No. Information List"
{
    layout
    {
        modify(Description)
        {
            Visible = false;
        }
        modify(Blocked)
        {
            Visible = false;
        }
        modify(Control16)
        {
            Visible = false;
        }
        addlast(Control1)
        {
            field("Current Location Code ERK"; Rec."Current Location Code ERK")
            {
                ApplicationArea = All;
            }
            field("Current Bin Code ERK"; Rec."Current Bin Code ERK")
            {
                ApplicationArea = All;
            }
            field("Civil Area ERK"; Rec."Civil Area ERK")
            {
                ApplicationArea = All;
            }
            field("Manufacturer Code ERK"; Rec."Brand Code ERK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Manufacturer Code field.';
            }
            field("SystemCreatedAt ERK"; Rec.SystemCreatedAt)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the SystemCreatedAt field.';
            }
            // field("Model Name ERK"; Rec."Model Name ERK")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Model Name field.';
            // }
            field("Model Version ERK"; Rec."Model Version ERK")
            {
                ApplicationArea = All;
            }
            field("Model Group ERK"; Rec."Model Code ERK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Model Group field.';
            }
            field("Colour Name ERK"; Rec."Colour Name ERK")
            {
                ApplicationArea = All;
            }
            field("Fuel Type ERK"; Rec."Fuel Type ERK")
            {
                ApplicationArea = All;
            }
            field("Engine ID ERK"; Rec."Engine ID ERK")
            {
                ApplicationArea = All;
            }
            field("Gross Weight (KG) ERK"; Rec."Gross Weight (KG) ERK")
            {
                ApplicationArea = All;
            }
            field("Footprint (m2) ERK"; Rec."Footprint (m2) ERK")
            {
                ApplicationArea = All;
            }
            field("Volume (m3) ERK"; Rec."Volume (m3) ERK")
            {
                ApplicationArea = All;
            }
            field("TSE ERK"; Rec."TSE ERK")
            {
                ApplicationArea = All;
            }
            field("Nav. Process Required ERK"; Rec."Nav. Process Required ERK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Nav. Process Required field.';
            }
            field("Commercial Blockage ERK"; Rec."Commercial Blockage ERK")
            {
                ApplicationArea = All;
            }
            // field("Charge Cable Should Exist ERK"; Rec."Charge Cable Should Exist ERK")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Charge Cable Should Exist field.';
            // }
            field("Customs Declaration No. ERK"; Rec."Customs Declaration No. ERK")
            {
                ApplicationArea = All;
            }
            field("Customs Dec. Line No. ERK"; Rec."Customs Dec. Line No. ERK")
            {
                ApplicationArea = All;
            }
            field("Customs Registration Date ERK"; Rec."Customs Registration Date ERK")
            {
                ApplicationArea = All;
            }
            field("Truck Plate ERK"; Rec."Truck Plate ERK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addfirst(processing)
        {
            action("Print Temporary Traffic Document ERK")
            {
                ApplicationArea = All;
                Caption = 'Print Temporary Traffic Document';
                Image = Print;
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Print Temporary Traffic Document action.';
                trigger OnAction()
                begin
                    Rec.SetRecFilter();
                    Report.Run(Report::"Temporary Traffic Document ERK", true, true, Rec);
                end;
            }
        }
        addlast("&Serial No.")
        {
            action("UpdateSizeAndWeightInformation ERK")
            {
                ApplicationArea = All;
                Caption = 'Update Size and Weight Information';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = UpdateDescription;
                ToolTip = 'Executes the Update Size and Weight Information action.';

                trigger OnAction()
                var
                    SerialNoInformation: Record "Serial No. Information";
                    ProgressDialog: Codeunit "Progress Dialog";
                begin
                    SerialNoInformation.SetFilter("Model Version ERK", '<>%1', '');
                    SerialNoInformation.SetRange("Footprint (m2) ERK", 0);
                    if SerialNoInformation.FindSet(true) then begin
                        ProgressDialog.OpenCopyCountMax('Processing...', SerialNoInformation.Count());
                        repeat
                            SerialNoInformation.Validate("Model Version ERK");
                            SerialNoInformation.Modify(true);
                            ProgressDialog.UpdateCopyCount();
                        until SerialNoInformation.Next() = 0;
                    end;
                end;
            }
        }
    }
}
