tableextension 60019 "Dimension Value ERK" extends "Dimension Value"
{
    fields
    {
        field(60000; "Manager No. ERK"; Code[20])
        {
            Caption = 'Manager No.';
            TableRelation = Employee."No.";
            ToolTip = 'Specifies the value of the Manager No. field.';
            trigger OnValidate()
            var
                Employee: Record Employee;
            begin
                Employee.Get("Manager No. ERK");
                Re<PERSON><PERSON>("Manager Full Name ERK", Employee.FullName());
            end;
        }
        field(60001; "Manager Full Name ERK"; Text[100])
        {
            Caption = 'Manager Full Name';
            ToolTip = 'Specifies the value of the Manager Full Name field.';
        }
    }
}
