#pragma warning disable LC0062
page 60106 "Post Codes API ERK"
#pragma warning restore LC0062
{
    PageType = API;
    Caption = 'Post Codes API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'postCodesTB';
    EntitySetName = 'postCodesTB';
    SourceTable = "Post Code";
    DelayedInsert = true;
    ODataKeyFields = SystemId;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(id; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(code; Rec.Code)
                {
                    Caption = 'Code';
                }
                field(city; Rec.City)
                {
                    Caption = 'City';
                }
                field(countryRegionCode; Rec."Country/Region Code")
                {
                    Caption = 'Country/Region Code';
                }
                field(county; Rec.County)
                {
                    Caption = 'County';
                }
            }
        }
    }
}