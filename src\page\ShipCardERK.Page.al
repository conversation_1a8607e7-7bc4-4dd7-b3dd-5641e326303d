page 60021 "Ship Card ERK"
{
    ApplicationArea = All;
    Caption = 'Ship Card';
    PageType = Card;
    SourceTable = "Ship ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                    ShowMandatory = true;
                }
                field(Name; Rec.Name)
                {
                }
                field(Flag; Rec.Flag)
                {
                }
                field("Lenght (m)"; Rec."Lenght (m)")
                {
                }
                field("Width (m)"; Rec."Width (m)")
                {
                }
                field("Draft (m)"; Rec."Draft (m)")
                {
                }
                field("GRT (ton)"; Rec."GRT (ton)")
                {
                }
                field("DWT (ton)"; Rec."DWT (ton)")
                {
                }
                field("Fuel Quantity"; Rec."Fuel Quantity")
                {
                }
                field("Ship Type"; Rec."Ship Type")
                {
                }
                field("Average Speed (Knot)"; Rec."Average Speed (Knot)")
                {
                }
                field("Call Sign"; Rec."Call Sign")
                {
                }
                field("IFO/HSFO Consump. (Ton/Hour)"; Rec."Eco IFO/HSFO Cons. (Ton/Day)")
                {
                }
                field("Max IFO/HSFO Cons. (Ton/Day)"; Rec."Max IFO/HSFO Cons. (Ton/Day)")
                {
                }
                field("MGO Consumption (Ton/Day)"; Rec."MGO Consumption (Ton/Day)")
                {
                }
                field("Capacity (m2)"; Rec."Capacity (m2)")
                {
                }
                field("Capacity (m3)"; Rec."Capacity (m3)")
                {
                }
                field("Eco Speed (kts)"; Rec."Eco Speed (kts)")
                {
                }
                field("Max Speed (kts)"; Rec."Max Speed (kts)")
                {
                }
            }
            group(ContractInformation)
            {
                Caption = 'Contract Information';
                Editable = false;
                field("Contract Start Date"; Rec."Contract Start Date")
                {
                }
                field("Contract End Date"; Rec."Contract End Date")
                {
                }
                field("Contract Currency Code"; Rec."Contract Currency Code")
                {
                }
                field("Contract Unit Cost"; Rec."Contract Unit Cost")
                {
                }
            }
            part(Contracts; "Contract Subpage ERK")
            {
                Caption = 'Contracts';
                SubPageLink = "Ship No." = field("No.");
                Editable = false;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(VerifyIMONo)
            {
                Caption = 'Verify IMO No.';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = CheckList;
                ToolTip = 'Executes the Verify IMO No. action.';

                trigger OnAction()
                begin
                    Message(Format(ErkHoldingBasicFunctions.VerifyIMONumber(CopyStr(Rec."No.", 1, 7))));
                end;
            }
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
