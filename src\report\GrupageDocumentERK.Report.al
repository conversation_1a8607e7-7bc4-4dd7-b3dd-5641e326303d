report 60009 "Grupage Document ERK"
{
    Caption = 'Grupage Document';
    UsageCategory = None;

    dataset
    {
        dataitem(VehicleTransferHeaderERK; "Vehicle Transfer Header ERK")
        {
            column(No; "No.")
            {
            }
            column(TotalQuantity; "Total Quantity")
            {
            }
            column(ShippingAgentCode; "Shipping Agent Code")
            {
            }
            column(DocumentNoBarCode; DocumentNoBarCode)
            {
            }
            column(DocumentNoQRCode; DocumentNoQRCode)
            {
            }
            dataitem("Vehicle Transfer Line ERK"; "Vehicle Transfer Line ERK")
            {
                DataItemLink = "Document No." = field("No.");

                column(SerialNo_VehicleTransferLineERK; "Serial No.")
                {
                }
                column(FromLocationCode_VehicleTransferLineERK; "From Location Code")
                {
                }
                column(FromBinCode_VehicleTransferLineERK; "From Bin Code")
                {
                }
                column(Line_No_; "Line No.")
                {
                }
                dataitem(SerialNoInformation; "Serial No. Information")
                {
                    DataItemLink = "Serial No." = field("Serial No.");

                    column(Grupage_Bin_Code_ERK; "Grupage Bin Code ERK")
                    {
                    }
                    column(Grupage_Ship_to_Name_ERK; "Grupage Ship-to Name ERK")
                    {
                    }
                    column(Grupage_Ship_to_City_ERK; "Grupage Ship-to City ERK")
                    {
                    }
                    column(Grupage_Location_Code_ERK; "Grupage Location Code ERK")
                    {
                    }
                    column(Grupage_Date_ERK; "Grupage Date ERK")
                    {
                    }
                    column(Grupage_No_ERK; "Grupage No. ERK")
                    {
                    }
                    column(Grupage_Ship_to_Address_ERK; "Grupage Ship-to Address ERK")
                    {
                    }
                    column(Model_Code_ERK; "Model Code ERK")
                    {
                    }
                    column(Colour_Name_ERK; "Colour Name ERK")
                    {
                    }
                }
            }
            trigger OnAfterGetRecord()
            var
                BarcodeString: Text;
                BarcodeFontProvider: Interface "Barcode Font Provider";
                BarcodeFontProvider2D: Interface "Barcode Font Provider 2D";
            begin
                // Declare the barcode provider using the barcode provider interface and enum
                BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                BarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;
                // Set data string source
                if VehicleTransferHeaderERK."No." <> '' then begin
                    BarcodeString := VehicleTransferHeaderERK."No.";
                    // Validate the input
                    BarcodeFontProvider.ValidateInput(BarcodeString, BarcodeSymbology);
                    // Encode the data string to the barcode font
                    DocumentNoBarCode := BarcodeFontProvider.EncodeFont(BarcodeString, BarcodeSymbology);
                    DocumentNoQRCode := BarcodeFontProvider2D.EncodeFont(BarcodeString, BarcodeSymbology2D);
                end
            end;
        }
    }
    var
        BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeSymbology2D: Enum "Barcode Symbology 2D";
        DocumentNoBarCode: Text;
        DocumentNoQRCode: Text;

    trigger OnInitReport()
    begin
        BarcodeSymbology := Enum::"Barcode Symbology"::Code39;
        BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
    end;
}
