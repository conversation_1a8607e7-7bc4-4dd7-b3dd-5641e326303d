codeunit 60005 "Car Carrier Management ERK"
{
    procedure CalculateCarCarrierLineDetailQuantity(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK"): Integer
    var
    //VehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    begin
        // VehicleLedgerEntry.SetRange("Document No.", CarCarrierLineDetail."Document No.");
        // VehicleLedgerEntry.SetRange("Customer No.", CarCarrierLineDetail."Customer No.");
        // case CarCarrierLineDetail.Type of
        //     CarCarrierLineDetail.Type::Loading:
        //         VehicleLedgerEntry.SetRange("Loading Port", CarCarrierLineDetail."Loading Port");
        //     CarCarrierLineDetail.Type::Discharge:
        //         VehicleLedgerEntry.SetRange("Discharge Port", CarCarrierLineDetail."Loading Port");
        //     CarCarrierLineDetail.Type::" ":
        //         exit(0);
        // end;
        // exit(VehicleLedgerEntry.Count);
    end;

    procedure RegisterLoadDischarge(var TempLoadingDischargingWorksheet: Record "Loading/Discharging Worksheet" temporary)
    var
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        SuccesfullMsg: Label '%1 Vehicles succesfully Loaded.', Comment = '%1="Loading/Discharging Worksheet".Count';
    begin
        TempLoadingDischargingWorksheet.FindSet(false);
        repeat
            InserVehicleLedgerEntryForLoading(TempLoadingDischargingWorksheet);
        until TempLoadingDischargingWorksheet.Next() = 0;
        CarCarrierLineDetail.Get(TempLoadingDischargingWorksheet."Document No.", TempLoadingDischargingWorksheet."Document Line No.", TempLoadingDischargingWorksheet."Document Line Detail No.");
        PopulateCarCarrierTripStartingDateFromCarCarrierLineDetail(CarCarrierLineDetail);
        CarCarrierOrderMngt.UpdateCarCarrierOrderLineStatusOnLoading(CarCarrierLineDetail);
        Message(SuccesfullMsg, TempLoadingDischargingWorksheet.Count());
    end;

    local procedure InserVehicleLedgerEntryForLoading(LoadingDischargingWorksheet: Record "Loading/Discharging Worksheet")
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        CarCarrierHeader: Record "Car Carrier Header ERK";
        EntryExitPoint: Record "Entry/Exit Point";
        VoyageAccount: Record "Voyage Account ERK";
        TwiceErr: Label 'You can not load Serial No.: %1 twice in this document.', Comment = '%1="Loading/Discharging Worksheet"."Serial No."';
    begin
        CreateSerialNoOnformationIfNeccesary(LoadingDischargingWorksheet."Serial No.", LoadingDischargingWorksheet."Document No.", LoadingDischargingWorksheet."Model Version Code", LoadingDischargingWorksheet."Operation Date-Time".Date(), LoadingDischargingWorksheet."Truck Plate ERK");
        CarCarrierLedgerEntry.SetRange("Document No.", LoadingDischargingWorksheet."Document No.");
        CarCarrierLedgerEntry.SetRange("Serial No.", LoadingDischargingWorksheet."Serial No.");
        if not CarCarrierLedgerEntry.IsEmpty() then
            Error(TwiceErr, LoadingDischargingWorksheet."Serial No.");
        VoyageAccount.Get(LoadingDischargingWorksheet."Customer No.");
        CarCarrierHeader.Get(LoadingDischargingWorksheet."Document No.");
        CarCarrierLineDetail.Get(LoadingDischargingWorksheet."Document No.", LoadingDischargingWorksheet."Document Line No.", LoadingDischargingWorksheet."Document Line Detail No.");
        CarCarrierLedgerEntry.Init();
        CarCarrierLedgerEntry."Document No." := LoadingDischargingWorksheet."Document No.";
        CarCarrierLedgerEntry."Customer No." := LoadingDischargingWorksheet."Customer No.";
        CarCarrierLedgerEntry."Loading Port" := LoadingDischargingWorksheet."Loading Port";
        EntryExitPoint.Get(LoadingDischargingWorksheet."Loading Port");
        CarCarrierLedgerEntry."Loading Port Description" := EntryExitPoint.Description;
        CarCarrierLedgerEntry."Serial No." := LoadingDischargingWorksheet."Serial No.";
        CarCarrierLedgerEntry."Loading DateTime" := LoadingDischargingWorksheet."Operation Date-Time";
        CarCarrierLedgerEntry."Customer Name" := VoyageAccount.Name;
        CarCarrierLedgerEntry."Ship No." := CarCarrierHeader."Ship No.";
        CarCarrierLedgerEntry."Ship Name" := CarCarrierHeader."Ship Name";
        CarCarrierLedgerEntry."Document Line No." := LoadingDischargingWorksheet."Document Line No.";
        CarCarrierLedgerEntry."Document Line Detail No." := LoadingDischargingWorksheet."Document Line Detail No.";
        CarCarrierLedgerEntry."Car Carrier Order No." := CarCarrierLineDetail."Order No.";
        CarCarrierLedgerEntry."CC Order Load Detail Line No." := CarCarrierLineDetail."Order Load Detail Line No.";
        CarCarrierLedgerEntry."Truck Plate ERK" := LoadingDischargingWorksheet."Truck Plate ERK";
        CarCarrierLedgerEntry.Insert(true);
        OnAfterInsertVehicleLedgerEntryForLoading(CarCarrierLedgerEntry);
        //CreateUpdateVehicleTransferOrderForDischarge(LoadingDischargingWorksheet);
    end;

    procedure CreateSerialNoOnformationIfNeccesary(SerialNo: Code[50]; CarCarrierNo: Code[20]; ModelVersionCode: Code[100]; LoadingDate: Date; TruckPlate: Code[10])
    var
        SerialNoInformation: Record "Serial No. Information";
        CarCarrierHeader: Record "Car Carrier Header ERK";
        ModelVersion: Record "Model Version ERK";
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Item No. for Vehicles");
        CarCarrierHeader.Get(CarCarrierNo);
        ModelVersion.SetRange(Code, ModelVersionCode);
        if not ModelVersion.FindFirst() then begin
            ModelVersion.Init();
            ModelVersion.Code := ModelVersionCode;
            ModelVersion.Insert(false);
        end;
        if not SerialNoInformation.Get(ErkHoldingSetup."Item No. for Vehicles", '', SerialNo) then begin
            SerialNoInformation.Init();
            SerialNoInformation."Item No." := ErkHoldingSetup."Item No. for Vehicles";
            SerialNoInformation."Serial No." := SerialNo;
            SerialNoInformation.Validate("Current Location Code ERK", CarCarrierHeader."Ship No.");
            SerialNoInformation.Validate("Current Bin Code ERK", CarCarrierHeader."Ship No.");
            SerialNoInformation.Insert(true);
            SerialNoInformation.Validate("Model Version ERK", ModelVersionCode);
            SerialNoInformation.Validate("Truck Plate ERK", TruckPlate);
            SerialNoInformation.Modify(true);
        end
        else begin
            if (GetMostRecentOperationDateFromVehicleTransferLedgerEntry(SerialNo) < LoadingDate) then begin
                SerialNoInformation.Validate("Current Location Code ERK", CarCarrierHeader."Ship No.");
                SerialNoInformation.Validate("Current Bin Code ERK", CarCarrierHeader."Ship No.");
            end;
            if ModelVersionCode <> '' then
                SerialNoInformation.Validate("Model Version ERK", ModelVersionCode);
            if CopyStr(CarCarrierNo, 2, 2) <> '23' then
                SerialNoInformation.Modify(true);
        end;
    end;

    local procedure GetMostRecentOperationDateFromVehicleTransferLedgerEntry(SerialNo: Code[50]): Date
    var
        VehicleTransferLedgerEntry: Record "Vehicle Transfer Ledger Entry";
    begin
        VehicleTransferLedgerEntry.SetRange("Serial No.", SerialNo);
        if VehicleTransferLedgerEntry.FindLast() then
            exit(VehicleTransferLedgerEntry."Operation Date-Time".Date());
        exit(0D);
    end;
    // local procedure CreateUpdateVehicleTransferOrderForDischarge(var LoadingDischargingWorksheet: Record "Loading/Discharging Worksheet")
    // var
    //     VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
    //     VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    //     EntryExitPoint: Record "Entry/Exit Point";
    //     CarCarrierHeader: Record "Car Carrier Header ERK";
    // //DischargeOrderQst: Label 'Do you want to create Vehicle Transfer Order for Discharge?';
    // begin
    //     EntryExitPoint.Get(LoadingDischargingWorksheet."Discharge Port");
    //     if EntryExitPoint."Location Code ERK" = '' then
    //         exit;
    //     if LoadingDischargingWorksheet."Do Not Create Discharge Order" then
    //         exit;
    //     CarCarrierHeader.Get(LoadingDischargingWorksheet."Document No.");
    //     VehicleTransferHeader.SetRange("Car Carrier No.", LoadingDischargingWorksheet."Document No.");
    //     if not VehicleTransferHeader.FindLast() then begin
    //         VehicleTransferHeader.Init();
    //         VehicleTransferHeader.Insert(true);
    //         VehicleTransferHeader.Validate("Car Carrier No.", LoadingDischargingWorksheet."Document No.");
    //         VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::Discharge);
    //         VehicleTransferHeader.Validate("Ship No.", CarCarrierHeader."Ship No.");
    //         VehicleTransferHeader.Validate("Ship Name", CarCarrierHeader."Ship Name");
    //         VehicleTransferHeader.Modify(false);
    //     end;
    //     VehicleTransferLine.Init();
    //     VehicleTransferLine."Document No." := VehicleTransferHeader."No.";
    //     VehicleTransferLine.Insert(true);
    //     VehicleTransferLine.Validate("Serial No.", LoadingDischargingWorksheet."Serial No.");
    //     VehicleTransferLine.Modify(true);
    // end;
    local procedure CreateBallastTrip(var CarCarrierHeader: Record "Car Carrier Header ERK"; var PreviousCarCarrierHeader: Record "Car Carrier Header ERK"; var BalastTripCount: Integer)
    var
        BalastCarCarrierHeader: Record "Car Carrier Header ERK";
        CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        CarCarrierHeader.CalcFields("Ship Type");
        if (RoundDateTime(CarCarrierHeader."Starting Date-Time", 3600000, '<') - RoundDateTime(PreviousCarCarrierHeader."Ending Date-Time", 3600000, '<')) < 1 then
            exit;
        Clear(BalastCarCarrierHeader);
        Clear(NoSeries);
        BalastCarCarrierHeader.Init();
        if CarCarrierHeader."Ship Type" in [CarCarrierHeader."Ship Type"::"Ro-Ro", CarCarrierHeader."Ship Type"::PCC, CarCarrierHeader."Ship Type"::PCTC] then //NoSeriesManagement.InitSeries(ErkHoldingSetup."Ballast No. Series", BalastCarCarrierHeader."No. Series", 0D, BalastCarCarrierHeader."No.", BalastCarCarrierHeader."No. Series")
            BalastCarCarrierHeader.Validate("Voyage Type", BalastCarCarrierHeader."Voyage Type"::"Ballast - Ro-Ro")
        else
            if CarCarrierHeader."Ship Type" = CarCarrierHeader."Ship Type"::Ferry then //NoSeriesManagement.InitSeries(ErkHoldingSetup."Ferry Ballast No. Series", BalastCarCarrierHeader."No. Series", 0D, BalastCarCarrierHeader."No.", BalastCarCarrierHeader."No. Series");
                BalastCarCarrierHeader.Validate("Voyage Type", BalastCarCarrierHeader."Voyage Type"::"Ballast - Ferry");
        BalastCarCarrierHeader.Insert(true);
        BalastCarCarrierHeader.Status := BalastCarCarrierHeader.Status::Completed;
        BalastCarCarrierHeader.Validate("Ship No.", PreviousCarCarrierHeader."Ship No.");
        BalastCarCarrierHeader.Validate("Ship No.", PreviousCarCarrierHeader."Ship No.");
        BalastCarCarrierHeader.Validate("Starting Port", PreviousCarCarrierHeader."Ending Port");
        BalastCarCarrierHeader.Validate("Starting Date-Time", PreviousCarCarrierHeader."Ending Date-Time");
        BalastCarCarrierHeader.Validate("Ending Port", CarCarrierHeader."Starting Port");
        BalastCarCarrierHeader.Validate("Ending Date-Time", CarCarrierHeader."Starting Date-Time");
        BalastCarCarrierHeader.Validate("Previous Car Carrier No.", PreviousCarCarrierHeader."No.");
        BalastCarCarrierHeader.Validate("Next Car Carrier No.", CarCarrierHeader."No.");
        BalastCarCarrierHeader.Modify(true);
        CarCarrierLine.Init();
        CarCarrierLine."Document No." := BalastCarCarrierHeader."No.";
        CarCarrierLine."Line No." := 10000;
        CarCarrierLine.Insert(true);
        CarCarrierLine.Validate("Departure Port", BalastCarCarrierHeader."Starting Port");
        CarCarrierLine.Validate("Departure Date-Time", BalastCarCarrierHeader."Starting Date-Time");
        CarCarrierLine.Validate("Arrival Port", BalastCarCarrierHeader."Ending Port");
        CarCarrierLine.Validate("Arrival Date-Time", BalastCarCarrierHeader."Ending Date-Time");
        CarCarrierLine.Modify(true);
        BalastTripCount += 1;
    end;
    // procedure UpdateVehicleLedgerEntryForDischarge(LoadingDischargingWorksheet: Record "Loading/Discharging Worksheet")
    // var
    //     VehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    // begin
    //     VehicleLedgerEntry.SetRange("Document No.", LoadingDischargingWorksheet."Document No.");
    //     VehicleLedgerEntry.SetRange("Serial No.", LoadingDischargingWorksheet."Serial No.");
    //     VehicleLedgerEntry.SetRange("Customer No.", LoadingDischargingWorksheet."Customer No.");
    //     VehicleLedgerEntry.FindFirst();
    //     VehicleLedgerEntry."Discharge Port" := LoadingDischargingWorksheet."Loading Port";
    //     VehicleLedgerEntry."Discharge DateTime" := LoadingDischargingWorksheet."Operation Date-Time";
    //     VehicleLedgerEntry.Modify(false);
    //     //VehicleTransferManagement.CreateTransferLedgerEntryFromVehicleLedgerEntry(VehicleLedgerEntry);
    // end;
    procedure DischargeSelectedCarCarrierLineDetails(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        EntryExitPoint: Record "Entry/Exit Point";
        DischargeMsg: Label '%1 Vehicles discharged succesfully.', Comment = '%1="Car Carrier Ledger Entry ERK".Count';
    begin
        CarCarrierLineDetail.TestField("Discharge Start Date-Time");
        CarCarrierLineDetail.TestField("Discharge End Date-Time");
        CarCarrierLineDetail.TestField("Discharge Port");
        CarCarrierLineDetail.TestField("Discharge Port Line No.");

        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLineDetail."Document No.");
        CarCarrierLedgerEntry.SetRange("Document Line No.", CarCarrierLineDetail."Document Line No.");
        CarCarrierLedgerEntry.SetRange("Document Line Detail No.", CarCarrierLineDetail."Line No.");
        CarCarrierLedgerEntry.SetRange("Customer No.", CarCarrierLineDetail."Customer No.");
        CarCarrierLedgerEntry.SetRange("Loading Port", CarCarrierLineDetail."Loading Port");
        EntryExitPoint.Get(CarCarrierLineDetail."Discharge Port");
        CarCarrierLineDetail.CalcFields("Loading Port Description", "Discharge Port Description");
        CarCarrierLedgerEntry.ModifyAll("Discharge Port", CarCarrierLineDetail."Discharge Port", false);
        CarCarrierLedgerEntry.ModifyAll("Discharge Port Line No.", CarCarrierLineDetail."Discharge Port Line No.", false);
        CarCarrierLedgerEntry.ModifyAll("Discharge DateTime", CarCarrierLineDetail."Discharge End Date-Time", false);
        CarCarrierLedgerEntry.ModifyAll("Discharge Port Description", EntryExitPoint.Description, false);
        CarCarrierLedgerEntry.ModifyAll("Loading to Discharge Desc.", CarCarrierLineDetail."Loading Port Description" + ' - ' + CarCarrierLineDetail."Discharge Port Description", false);
        CarCarrierLedgerEntry.ModifyAll("Loading-Discharge Date-Time", Format(CarCarrierLineDetail."Loading End Date-Time") + '-' + Format(CarCarrierLineDetail."Discharge End Date-Time"), false);
        CarCarrierOrderMngt.UpdateCarCarrierOrderLineStatusOnDischarge(CarCarrierLineDetail);
        Message(DischargeMsg, CarCarrierLedgerEntry.Count());
        OnAfterDischargeSelectedCarCarrierLineDetails(CarCarrierLedgerEntry);
    end;

    procedure PopulateCurrentLocationOfShipFromCarCarrierHeader(CarCarrierHeader: Record "Car Carrier Header ERK"): Code[10]
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
        AtSeaLbl: Label 'At Sea';
    begin
        CarCarrierLine.SetRange("Document No.", CarCarrierHeader."No.");
        if not CarCarrierLine.FindLast() then
            exit('');
        if CarCarrierLine."Arrival Date-Time" = 0DT then
            exit(CopyStr(AtSeaLbl, 1, 10));
        exit(CarCarrierLine."Arrival Port");
    end;

    procedure CalcualteRemainingQuantityFromLoadedAndDischargedQuantity(LoadedQty: Integer; DischargedQty: Integer): Integer
    begin
        exit(LoadedQty - DischargedQty);
    end;

    procedure PopulateCarCarrierTripStartingDateFromCarCarrierLineDetail(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
    begin
        CarCarrierHeader.Get(CarCarrierLineDetail."Document No.");
        if CarCarrierHeader."Starting Date-Time" = 0DT then begin
            CarCarrierHeader.Validate("Starting Date-Time", CarCarrierLineDetail."Loading Start Date-Time");
            CarCarrierHeader.Modify(true);
        end
        else
            if (TypeHelper.CompareDateTime(CarCarrierHeader."Starting Date-Time", CarCarrierLineDetail."Loading Start Date-Time") > 0) then begin
                BallastExistCheckFromCarCarrierLineDetail(CarCarrierLineDetail);
                CarCarrierHeader.Validate("Starting Date-Time", CarCarrierLineDetail."Loading Start Date-Time");
                CarCarrierHeader.Modify(true);
            end;
    end;

    procedure GetArrivalDateFromCarCarrierLineDetailForLoading(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK"): DateTime
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        CarCarrierLine.SetRange("Document No.", CarCarrierLineDetail."Document No.");
        CarCarrierLine.SetRange("Arrival Port", CarCarrierLineDetail."Loading Port");
        CarCarrierLine.SetRange("Line No.", CarCarrierLineDetail."Document Line No.");
        if CarCarrierLine.FindFirst() then
            exit(CarCarrierLine."Arrival Date-Time");
    end;

    procedure GetArrivalDateFromCarCarrierLineDetailForDischarge(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK"): DateTime
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        CarCarrierLine.SetRange("Document No.", CarCarrierLineDetail."Document No.");
        CarCarrierLine.SetRange("Line No.", CarCarrierLineDetail."Discharge Port Line No.");
        // CarCarrierLine.SetRange("Arrival Port", CarCarrierLineDetail."Discharge Port");
        // CarCarrierLine.SetRange("Line No.", CarCarrierLineDetail."Document Line No.");
        if CarCarrierLine.FindFirst() then
            exit(CarCarrierLine."Arrival Date-Time");
    end;

    procedure CreateBalastTrips()
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
        PreviousCarCarrierHeader: Record "Car Carrier Header ERK";
        BalastTripCount: Integer;
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Ballast No. Series");
        CarCarrierHeader.SetCurrentKey("Ship No.", "Starting Date-Time");
        CarCarrierHeader.SetFilter("No.", '?24-0*|?25-0*');
        //CarCarrierHeader.SetFilter("Starting Date-Time",'');
        //CarCarrierHeader.SetRange("Ship Type", CarCarrierHeader."Ship Type"::"Ro-Ro");
        CarCarrierHeader.SetFilter("Voyage Type", '<>%1', CarCarrierHeader."Voyage Type"::"Road Transport");
        CarCarrierHeader.SetFilter(Status, '%1|%2', CarCarrierHeader.Status::Active, CarCarrierHeader.Status::Completed);
        CarCarrierHeader.SetFilter("Starting Date-Time", '<>%1', 0DT);
        if not CarCarrierHeader.FindSet() then
            exit;
        PreviousCarCarrierHeader := CarCarrierHeader;
        repeat
            if not BallastAlreadyCreated(PreviousCarCarrierHeader, CarCarrierHeader) then
                CreateBalastLoop(CarCarrierHeader, PreviousCarCarrierHeader, BalastTripCount);
            PreviousCarCarrierHeader.Get(CarCarrierHeader."No.");
        until CarCarrierHeader.Next() = 0;
        if GuiAllowed() then
            Message('Balast Trips Created: %1', BalastTripCount);
    end;

    local procedure BallastAlreadyCreated(PreviousCarCarrierHeader: Record "Car Carrier Header ERK"; NextCarCarrierHeader: Record "Car Carrier Header ERK"): Boolean
    var
        BalastCarCarrierHeader: Record "Car Carrier Header ERK";
    begin
        BalastCarCarrierHeader.SetRange("Previous Car Carrier No.", PreviousCarCarrierHeader."No.");
        BalastCarCarrierHeader.SetRange("Next Car Carrier No.", NextCarCarrierHeader."No.");
        if BalastCarCarrierHeader.IsEmpty() then
            exit(false);
        exit(true);
    end;

    local procedure CreateBalastLoop(var CarCarrierHeader: Record "Car Carrier Header ERK"; var PreviousCarCarrierHeader: Record "Car Carrier Header ERK"; var BalastTripCount: Integer)
    begin
        if PreviousCarCarrierHeader."No." <> CarCarrierHeader."No." then
            if PreviousCarCarrierHeader."Ship No." = CarCarrierHeader."Ship No." then
                if (TypeHelper.CompareDateTime(PreviousCarCarrierHeader."Ending Date-Time", CarCarrierHeader."Starting Date-Time") <> 0) or (PreviousCarCarrierHeader."Ending Port" <> CarCarrierHeader."Starting Port") then
                    CreateBallastTrip(CarCarrierHeader, PreviousCarCarrierHeader, BalastTripCount);
    end;

    local procedure InsertSerialNoRevenueExpense(var CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK"; Rec: Record "Car Carr. Revenue/Expense ERK")
    var
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
    begin
        SerialNoRevenueExpense.Init();
        SerialNoRevenueExpense."Serial No." := CarCarrierLedgerEntry."Serial No.";
        SerialNoRevenueExpense.Insert(true);
        SerialNoRevenueExpense."Document No." := Rec."Document No.";
        SerialNoRevenueExpense.Type := Rec.Type;
        SerialNoRevenueExpense."No." := Rec."No.";
        SerialNoRevenueExpense."Variant Code" := Rec."Variant Code";
        SerialNoRevenueExpense.Description := Rec.Description;
        SerialNoRevenueExpense."Source No." := Rec."Source No.";
        SerialNoRevenueExpense."Source Name" := Rec."Source Name";
        SerialNoRevenueExpense."Amount (ACY)" := Rec."Line Amount (ACY)" / CarCarrierLedgerEntry.Count();
        SerialNoRevenueExpense.Amount := Rec."Line Amount" / CarCarrierLedgerEntry.Count();
        SerialNoRevenueExpense."Currency Code" := Rec."Currency Code";
        SerialNoRevenueExpense."Car Carrier Rev/Exp Line No." := Rec."Line No.";
        SerialNoRevenueExpense.Modify(true);
    end;

    local procedure ClearUnpostedInvoiceNoFieldOnCarCarrierRevenueExpenseLines(var SalesLine: Record "Sales Line")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        if SalesLine."Shortcut Dimension 2 Code" = '' then
            exit;
        CarCarrRevenueExpense.SetRange("Unposted Invoice No.", SalesLine."Document No.");
        if CarCarrRevenueExpense.IsEmpty() then
            exit;

        Message('%1 Car Carrier Revenue/Expense Lines Unposted Invoice Nos has been cleared.', CarCarrRevenueExpense.Count());

        CarCarrRevenueExpense.ModifyAll("Unposted Invoice No.", '', false);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting(var SalesHeader: Record "Sales Header"; var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; var SkipDelete: Boolean; CommitIsSuppressed: Boolean; EverythingInvoiced: Boolean; var TempSalesLineGlobal: Record "Sales Line" temporary)
    var
        SalesLine: Record "Sales Line";
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if SalesLine.IsEmpty() then
            exit;
        CarCarrRevenueExpense.SetRange("Unposted Invoice No.", SalesHeader."No.");
        CarCarrRevenueExpense.ModifyAll("Posted Invoice No.", SalesInvoiceHeader."No.", false);
        CarCarrRevenueExpense.ModifyAll("External Document No.", SalesInvoiceHeader."External Document No.", false);
    end;

    procedure OpenVehicleDistributionList(CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        VehicleDistributionList: Page "Vehicle Distribution List ERK";
        ConfirmQst: Label '%1 %2 will be distributed to %3 vehicles. Do you want to continue?', Comment = '%1=Amount; %2=Currency; %3=Vehicle Count';
        SuccesMsg: Label 'Distrubution completed.';
        ConfirmTxt: Text;
    begin
        CarCarrRevenueExpense.CalcFields("Distrubuted Quantity");
        CarCarrRevenueExpense.TestField("Distrubuted Quantity", 0);
        CarCarrRevenueExpense.TestField("Line Amount (ACY)");
        case CarCarrRevenueExpense.Type of
            CarCarrRevenueExpense.Type::Consumption:
                CarCarrRevenueExpense.TestField("Consumption Amount Calculated", true);
        end;
        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrRevenueExpense."Document No.");
        VehicleDistributionList.SetTableView(CarCarrierLedgerEntry);
        VehicleDistributionList.LookupMode(true);
        if VehicleDistributionList.RunModal() = Action::LookupOK then begin
            VehicleDistributionList.SetSelectionFilter(CarCarrierLedgerEntry);
            CarCarrierLedgerEntry.FindSet(false);
            ConfirmTxt := StrSubstNo(ConfirmQst, CarCarrRevenueExpense."Line Amount", CarCarrRevenueExpense."Currency Code", CarCarrierLedgerEntry.Count());
            if not ConfirmManagement.GetResponseOrDefault(ConfirmTxt, true) then
                exit;
            repeat
                InsertSerialNoRevenueExpense(CarCarrierLedgerEntry, CarCarrRevenueExpense);
            until CarCarrierLedgerEntry.Next() = 0;
            Message(SuccesMsg);
        end;
    end;

    procedure CancelDistribution(CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    var
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        ConfirmLbl: Label '%1 Vehicles distribution will cancelled. Are you sure?', Comment = '%1= Vehicle Count';
        SuccesMsg: Label 'Distribution cancelled.';
        ConfirmTxt: Text;
    begin
        SerialNoRevenueExpense.SetRange("Document No.", CarCarrRevenueExpense."Document No.");
        SerialNoRevenueExpense.SetRange("Car Carrier Rev/Exp Line No.", CarCarrRevenueExpense."Line No.");
        ConfirmTxt := StrSubstNo(ConfirmLbl, SerialNoRevenueExpense.Count());
        if not ConfirmManagement.GetResponseOrDefault(ConfirmTxt, true) then
            exit;
        SerialNoRevenueExpense.DeleteAll(true);
        Message(SuccesMsg);
    end;

    procedure CreatePurchaseInvoiceFromCarCarrierRevenueExpense(var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        CarCarrierHeader: Record "Car Carrier Header ERK";
        xCarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        SuccesMsg: Label 'Purchase Invoice Created: %1', Comment = '%1="Purchase Header"."No."';
        TypeErr: Label 'Type must be Expense or Realized Expense - No Proforma';
        PurchaseLineNo: Integer;
    begin
        CarCarrRevenueExpense.FindSet(true);
        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Invoice;
        PurchaseHeader.Insert(true);
        PurchaseHeader.Validate("Buy-from Vendor No.", CarCarrRevenueExpense."Source No.");
        PurchaseHeader.Validate("Vendor Invoice No.", CarCarrRevenueExpense."External Document No.");
        PurchaseHeader.Validate("Posting Date", CarCarrRevenueExpense."Posting Date");
        PurchaseHeader.Validate("Currency Code", CarCarrRevenueExpense."Currency Code");
        PurchaseHeader.Validate("Your Reference", CarCarrRevenueExpense."Your Reference");
        PurchaseHeader."Created From Car Carrier ERK" := true;
        PurchaseHeader.Modify(true);
        xCarCarrRevenueExpense := CarCarrRevenueExpense;
        repeat
            CarCarrRevenueExpense.TestField("External Document No.");
            CarCarrRevenueExpense.TestField("Unposted Invoice No.", '');
            CarCarrRevenueExpense.TestField("Posted Invoice No.", '');
            //CarCarrRevenueExpense.TestField(Type, CarCarrRevenueExpense.Type::Expense);
            if not (CarCarrRevenueExpense.Type in [CarCarrRevenueExpense.Type::Expense, CarCarrRevenueExpense.Type::"Realized Expense - No Proforma"]) then
                Error(TypeErr);
            PortCodeMandatoryCheck(CarCarrRevenueExpense."No.", CarCarrRevenueExpense."Port Code");
            Item.Get(CarCarrRevenueExpense."No.");
            if Item.IsVariantMandatory() then
                CarCarrRevenueExpense.TestField("Variant Code");
            CarCarrierHeader.Get(CarCarrRevenueExpense."Document No.");
            VoyageMangement.CreateDimensionValueCode(CarCarrierHeader."No.");
            CarCarrRevenueExpense.TestField("Currency Code", xCarCarrRevenueExpense."Currency Code");
            CarCarrRevenueExpense.TestField("Source No.", xCarCarrRevenueExpense."Source No.");
            CarCarrRevenueExpense.TestField("External Document No.", xCarCarrRevenueExpense."External Document No.");
            CarCarrRevenueExpense.TestField("Posting Date", xCarCarrRevenueExpense."Posting Date");
            PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
            PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
            if PurchaseLine.FindLast() then
                PurchaseLineNo := PurchaseLine."Line No." + 10000
            else
                PurchaseLineNo := 10000;
            Clear(PurchaseLine);
            PurchaseLine.Init();
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Line No." := PurchaseLineNo;
            PurchaseLine.Insert(true);
            PurchaseLine.Validate("Type", PurchaseLine."Type"::Item);
            PurchaseLine.Validate("No.", CarCarrRevenueExpense."No.");
            PurchaseLine.Validate("Variant Code", CarCarrRevenueExpense."Variant Code");
            PurchaseLine.Validate(Quantity, CarCarrRevenueExpense.Quantity);
            PurchaseLine.Validate("Unit of Measure Code", CarCarrRevenueExpense."UoM Code");
            PurchaseLine.Validate("Direct Unit Cost", CarCarrRevenueExpense."Unit Price/Cost");
            if CarCarrRevenueExpense."VAT Prod. Posting Group" <> '' then
                PurchaseLine.Validate("VAT Prod. Posting Group", CarCarrRevenueExpense."VAT Prod. Posting Group");
            PurchaseLine.Validate("Shortcut Dimension 1 Code", CarCarrierHeader."Shortcut Dimension 1 Code");
            PurchaseLine.Validate("Shortcut Dimension 2 Code", CarCarrierHeader."No.");
            PurchaseLine.Modify(true);
            CarCarrRevenueExpense."Unposted Invoice No." := PurchaseHeader."No.";
            CarCarrRevenueExpense.Modify(false);
            xCarCarrRevenueExpense := CarCarrRevenueExpense;
        until CarCarrRevenueExpense.Next() = 0;
        Message(SuccesMsg, PurchaseHeader."No.");
    end;

    procedure CreateSalesInvoiceFromCarCarrierRevenueExpense(var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        CarCarrierHeader: Record "Car Carrier Header ERK";
        xCarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        SuccesMsg: Label 'Sales Invoice Created: %1', Comment = '%1="Sales Header"."No."';
        TypeErr: Label 'Type must be %1 or %2', Comment = '%1=Type; %2=Type';
        SalesLineNo: Integer;
    begin
        CarCarrRevenueExpense.FindSet(true);
        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", CarCarrRevenueExpense."Source No.");
        SalesHeader.Validate("External Document No.", CarCarrRevenueExpense."External Document No.");
        SalesHeader.Validate("Posting Date", CarCarrRevenueExpense."Posting Date");
        SalesHeader.Validate("Currency Code", CarCarrRevenueExpense."Currency Code");
        SalesHeader.Validate("Your Reference", CarCarrRevenueExpense."Your Reference");
        SalesHeader."Created From Car Carrier ERK" := true;
        SalesHeader.Modify(true);
        xCarCarrRevenueExpense := CarCarrRevenueExpense;
        repeat
            CarCarrRevenueExpense.TestField("External Document No.", '');
            CarCarrRevenueExpense.TestField("Unposted Invoice No.", '');
            CarCarrRevenueExpense.TestField("Posted Invoice No.", '');
            //CarCarrRevenueExpense.TestField(Type, CarCarrRevenueExpense.Type::Revenue);
            if not (CarCarrRevenueExpense.Type in [CarCarrRevenueExpense.Type::Revenue, CarCarrRevenueExpense.Type::"Unexpected Revenue"]) then
                Error(TypeErr, CarCarrRevenueExpense.Type::Revenue, CarCarrRevenueExpense.Type::"Unexpected Revenue");
            PortCodeMandatoryCheck(CarCarrRevenueExpense."No.", CarCarrRevenueExpense."Port Code");
            Item.Get(CarCarrRevenueExpense."No.");
            if Item.IsVariantMandatory() then
                CarCarrRevenueExpense.TestField("Variant Code");
            CarCarrierHeader.Get(CarCarrRevenueExpense."Document No.");
            VoyageMangement.CreateDimensionValueCode(CarCarrierHeader."No.");
            CarCarrRevenueExpense.TestField("Currency Code", xCarCarrRevenueExpense."Currency Code");
            CarCarrRevenueExpense.TestField("Source No.", xCarCarrRevenueExpense."Source No.");
            CarCarrRevenueExpense.TestField("External Document No.", xCarCarrRevenueExpense."External Document No.");
            CarCarrRevenueExpense.TestField("Posting Date", xCarCarrRevenueExpense."Posting Date");
            SalesLine.SetRange("Document Type", SalesHeader."Document Type");
            SalesLine.SetRange("Document No.", SalesHeader."No.");
            if SalesLine.FindLast() then
                SalesLineNo := SalesLine."Line No." + 10000
            else
                SalesLineNo := 10000;
            Clear(SalesLine);
            SalesLine.Init();
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Line No." := SalesLineNo;
            SalesLine.Insert(true);
            SalesLine.Validate("Type", SalesLine."Type"::Item);
            SalesLine.Validate("No.", CarCarrRevenueExpense."No.");
            SalesLine.Validate("Variant Code", CarCarrRevenueExpense."Variant Code");
            SalesLine.Validate(Quantity, CarCarrRevenueExpense.Quantity);
            SalesLine.Validate("Unit of Measure Code", CarCarrRevenueExpense."UoM Code");
            if CarCarrRevenueExpense."VAT Prod. Posting Group" <> '' then
                SalesLine.Validate("VAT Prod. Posting Group", CarCarrRevenueExpense."VAT Prod. Posting Group");
            SalesLine.Validate("Shortcut Dimension 1 Code", CarCarrierHeader."Shortcut Dimension 1 Code");
            SalesLine.Validate("Shortcut Dimension 2 Code", CarCarrierHeader."No.");
            SalesLine.Validate("Invoice Line Combine Type INF", SalesHeader."DefInvoiceLineCombineType INF");
            SalesLine.Validate("Unit Price", CarCarrRevenueExpense."Unit Price/Cost");
            SalesLine.Modify(true);
            CarCarrRevenueExpense."Unposted Invoice No." := SalesHeader."No.";
            CarCarrRevenueExpense.Modify(false);
            xCarCarrRevenueExpense := CarCarrRevenueExpense;
        until CarCarrRevenueExpense.Next() = 0;
        Message(SuccesMsg, SalesHeader."No.");
    end;

    procedure CreateConsumptionInvoiceFromCarCarrierRevenueExpense(var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        CarCarrierHeader: Record "Car Carrier Header ERK";
        xCarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        SuccesMsg: Label 'Sales Invoice Created: %1', Comment = '%1="Sales Header"."No."';
        SalesLineNo: Integer;
    begin
        ErkHoldingSetup.GetRecordOnce();
        CarCarrRevenueExpense.FindSet(true);
        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", CarCarrRevenueExpense."Source No.");
        SalesHeader.Validate("External Document No.", SalesHeader."No.");
        SalesHeader.Validate("Posting Date", CarCarrRevenueExpense."Posting Date");
        SalesHeader.Validate("Currency Code", CarCarrRevenueExpense."Currency Code");
        SalesHeader."Created From Car Carrier ERK" := true;
        SalesHeader.Modify(true);
        xCarCarrRevenueExpense := CarCarrRevenueExpense;
        repeat
            CarCarrRevenueExpense.TestField("External Document No.", '');
            CarCarrRevenueExpense.TestField("Unposted Invoice No.", '');
            CarCarrRevenueExpense.TestField("Posted Invoice No.", '');
            CarCarrRevenueExpense.TestField(Type, CarCarrRevenueExpense.Type::Consumption);
            PortCodeMandatoryCheck(CarCarrRevenueExpense."No.", CarCarrRevenueExpense."Port Code");
            Item.Get(CarCarrRevenueExpense."No.");
            if Item.IsVariantMandatory() then
                CarCarrRevenueExpense.TestField("Variant Code");
            CarCarrierHeader.Get(CarCarrRevenueExpense."Document No.");
            VoyageMangement.CreateDimensionValueCode(CarCarrierHeader."No.");
            CarCarrRevenueExpense.TestField("Currency Code", xCarCarrRevenueExpense."Currency Code");
            CarCarrRevenueExpense.TestField("Source No.", xCarCarrRevenueExpense."Source No.");
            CarCarrRevenueExpense.TestField("External Document No.", xCarCarrRevenueExpense."External Document No.");
            CarCarrRevenueExpense.TestField("Posting Date", xCarCarrRevenueExpense."Posting Date");
            SalesLine.SetRange("Document Type", SalesHeader."Document Type");
            SalesLine.SetRange("Document No.", SalesHeader."No.");
            if SalesLine.FindLast() then
                SalesLineNo := SalesLine."Line No." + 10000
            else
                SalesLineNo := 10000;
            Clear(SalesLine);
            SalesLine.Init();
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Line No." := SalesLineNo;
            SalesLine.Insert(true);
            SalesLine.Validate("Type", SalesLine."Type"::Item);
            SalesLine.Validate("No.", CarCarrRevenueExpense."No.");
            SalesLine.Validate("Variant Code", CarCarrRevenueExpense."Variant Code");
            SalesLine.Validate("Location Code", CarCarrierHeader."Ship No.");
            SalesLine.Validate("Unit of Measure Code", CarCarrRevenueExpense."UoM Code");
            SalesLine.Validate(Quantity, CarCarrRevenueExpense.Quantity);
            SalesLine.Validate("Bin Code", CarCarrierHeader."Ship No.");
            if CarCarrRevenueExpense."VAT Prod. Posting Group" <> '' then
                SalesLine.Validate("VAT Prod. Posting Group", CarCarrRevenueExpense."VAT Prod. Posting Group");
            SalesLine.Validate("Shortcut Dimension 1 Code", CarCarrierHeader."Shortcut Dimension 1 Code");
            SalesLine.Validate("Shortcut Dimension 2 Code", CarCarrierHeader."No.");
            SalesLine.Validate("Invoice Line Combine Type INF", SalesHeader."DefInvoiceLineCombineType INF");
            SalesLine.Validate("Unit Price", 0);
            SalesLine.Modify(true);
            CarCarrRevenueExpense."Unposted Invoice No." := SalesHeader."No.";
            CarCarrRevenueExpense.Modify(false);
            xCarCarrRevenueExpense := CarCarrRevenueExpense;
        until CarCarrRevenueExpense.Next() = 0;
        Message(SuccesMsg, SalesHeader."No.");
    end;

    procedure CreateIFOConsumptionLineFromCarCarrierHeader(CarCarrierHeader: Record "Car Carrier Header ERK")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        LineCreatedMsg: Label 'IFO Consumption Line created.';
        NoLinesCreatedErr: Label 'No Fuel Consumption Line Created.';
        IFOAlreadyExistErr: Label 'IFO Consumption Line already created. Please delete the existing line and try again.';
        ConsumptionLineCreated: Boolean;
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("IFO Item No.");
        ErkHoldingSetup.TestField("Consumption Customer No.");
        CarCarrRevenueExpense.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrRevenueExpense.SetRange("Type", CarCarrRevenueExpense.Type::Consumption);
        CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."IFO Item No.");
        if not CarCarrRevenueExpense.IsEmpty() then
            Error(IFOAlreadyExistErr);
        if CalculateTotalIFOConsumption(CarCarrierHeader) > 0 then begin
            CarCarrRevenueExpense.Init();
            CarCarrRevenueExpense."Document No." := CarCarrierHeader."No.";
            CarCarrRevenueExpense.Insert(true);
            CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::Consumption);
            CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."IFO Item No.");
            CarCarrRevenueExpense.Validate(Quantity, CalculateTotalIFOConsumption(CarCarrierHeader));
            CarCarrRevenueExpense.Validate("Source No.", ErkHoldingSetup."Consumption Customer No.");
            CarCarrRevenueExpense.Modify(true);
            ConsumptionLineCreated := true;
        end;
        if ConsumptionLineCreated then
            Message(LineCreatedMsg)
        else
            Error(NoLinesCreatedErr);
    end;

    procedure CreateMGOConsumptionLineFromCarCarrierHeader(CarCarrierHeader: Record "Car Carrier Header ERK")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        LineCreatedMsg: Label 'MGO Consumption Lines Created.';
        NoLinesCreatedErr: Label 'No Fuel Consumption Line Created.';
        MGOAlreadyExistErr: Label 'MGO Consumption Line already created. Please delete the existing line and try again.';
        ConsumptionLineCreated: Boolean;
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("MGO Item No.");
        ErkHoldingSetup.TestField("Ferry Fuel Item No.");
        CarCarrierHeader.CalcFields("Ship Type");
        CarCarrRevenueExpense.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrRevenueExpense.SetRange("Type", CarCarrRevenueExpense.Type::Consumption);
        if CarCarrierHeader."Ship Type" = CarCarrierHeader."Ship Type"::Ferry then
            CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."Ferry Fuel Item No.")
        else
            CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."MGO Item No.");
        if not CarCarrRevenueExpense.IsEmpty() then
            Error(MGOAlreadyExistErr);
        if CalculateTotalMGOConsumption(CarCarrierHeader) > 0 then begin
            CarCarrRevenueExpense.Init();
            CarCarrRevenueExpense."Document No." := CarCarrierHeader."No.";
            CarCarrRevenueExpense.Insert(true);
            CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::Consumption);
            if CarCarrierHeader."Ship Type" = CarCarrierHeader."Ship Type"::Ferry then
                CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."Ferry Fuel Item No.")
            else
                CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."MGO Item No.");
            CarCarrRevenueExpense.Validate(Quantity, CalculateTotalMGOConsumption(CarCarrierHeader));
            CarCarrRevenueExpense.Validate("Source No.", ErkHoldingSetup."Consumption Customer No.");
            CarCarrRevenueExpense.Modify(true);
            ConsumptionLineCreated := true;
        end;
        if ConsumptionLineCreated then
            Message(LineCreatedMsg)
        else
            Error(NoLinesCreatedErr);
    end;

    procedure CreateHSFOConsumptionLineFromCarCarrierHeader(CarCarrierHeader: Record "Car Carrier Header ERK")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        LineCreatedMsg: Label 'HSFO Consumption Lines Created.';
        NoLinesCreatedErr: Label 'No Fuel Consumption Lines Created.';
        HSFOAlreadyExistErr: Label 'HSFO Consumption Line already created. Please delete the existing line and try again.';
        ConsumptionLineCreated: Boolean;
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("IFO Item No.");
        ErkHoldingSetup.TestField("MGO Item No.");
        ErkHoldingSetup.TestField("Ferry Fuel Item No.");
        ErkHoldingSetup.TestField("Consumption Customer No.");
        ErkHoldingSetup.TestField("HSFO Item No.");
        CarCarrRevenueExpense.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrRevenueExpense.SetRange("Type", CarCarrRevenueExpense.Type::Consumption);
        CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."HSFO Item No.");
        if not CarCarrRevenueExpense.IsEmpty() then
            Error(HSFOAlreadyExistErr);
        if CalculateTotalHSFOConsumption(CarCarrierHeader) > 0 then begin
            CarCarrRevenueExpense.Init();
            CarCarrRevenueExpense."Document No." := CarCarrierHeader."No.";
            CarCarrRevenueExpense.Insert(true);
            CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::Consumption);
            CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."HSFO Item No.");
            CarCarrRevenueExpense.Validate(Quantity, CalculateTotalHSFOConsumption(CarCarrierHeader));
            CarCarrRevenueExpense.Validate("Source No.", ErkHoldingSetup."Consumption Customer No.");
            CarCarrRevenueExpense.Modify(true);
            ConsumptionLineCreated := true;
        end;
        if ConsumptionLineCreated then
            Message(LineCreatedMsg)
        else
            Error(NoLinesCreatedErr);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure OnDeleteOnBeforeTestStatusOpen(var SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    begin
        if SalesLine.IsTemporary() then
            exit;
        ClearUnpostedInvoiceNoFieldOnCarCarrierRevenueExpenseLines(SalesLine);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure OnDeleteOnBeforeTestStatusOpen_PurchaseLine(var PurchaseLine: Record "Purchase Line"; var IsHandled: Boolean)
    begin
        if PurchaseLine.IsTemporary() then
            exit;

        ClearUnpostedInvoiceNoFieldOnCarCarrierRevenueExpenseLinesForPurchase(PurchaseLine);
    end;

    local procedure ClearUnpostedInvoiceNoFieldOnCarCarrierRevenueExpenseLinesForPurchase(var PurchaseLine: Record "Purchase Line")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        if PurchaseLine."Shortcut Dimension 2 Code" = '' then
            exit;
        CarCarrRevenueExpense.SetRange("Unposted Invoice No.", PurchaseLine."Document No.");
        if CarCarrRevenueExpense.IsEmpty() then
            exit;
        CarCarrRevenueExpense.ModifyAll("Unposted Invoice No.", '', false);
    end;

    local procedure ClearCarCarrierRevenueExpenseFieldsOnSalesCancellation(var SalesHeader: Record "Sales Header")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        if SalesHeader."Document Type" <> SalesHeader."Document Type"::"Credit Memo" then
            exit;
        if not SalesHeader."Cancel INF" then
            exit;
        CarCarrRevenueExpense.SetRange("Posted Invoice No.", SalesHeader."Applies-to Doc. No.");
        CarCarrRevenueExpense.ModifyAll("Unposted Invoice No.", '', false);
        CarCarrRevenueExpense.ModifyAll("External Document No.", '', false);
        CarCarrRevenueExpense.ModifyAll("Posted Invoice No.", '', false);
    end;

    procedure BallastExistCheckFromCarCarrierLineDetail(var CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    var
        BallastCarCarrierHeader: Record "Car Carrier Header ERK";
        BallastVoyageExistErr: Label 'This voyage because has a Ballast Trip: %1. You have to delete it to change Starting Date-Time.', Comment = '%1="Car Carrier Header ERK"."No."';
    begin
        BallastCarCarrierHeader.SetRange("Next Car Carrier No.", CarCarrierLineDetail."Document No.");
        if BallastCarCarrierHeader.FindFirst() then
            Error(BallastVoyageExistErr, BallastCarCarrierHeader."No.");
    end;

    procedure CreateShipHireExpenseLinesFromCarCarrierHeader(CarCarrierHeader: Record "Car Carrier Header ERK")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        Ship: Record "Ship ERK";
        ShipHireExpenseAlreadyExistErr: Label 'Ship Hire Expense Line already created. Please delete the existing line and try again.';
        ShipHireExpenseCreatedMsg: Label 'Ship Hire Expense Line Created.';
        DurationHour: Decimal;
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Hire Item No.");
        CarCarrRevenueExpense.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrRevenueExpense.SetRange("Type", CarCarrRevenueExpense.Type::Consumption);
        CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."Hire Item No.");
        if not CarCarrRevenueExpense.IsEmpty() then
            Error(ShipHireExpenseAlreadyExistErr);
        CarCarrierHeader.TestField(Status, CarCarrierHeader.Status::Completed);
        CarCarrierHeader.TestField("Starting Date-Time");
        CarCarrierHeader.TestField("Ending Date-Time");
        DurationHour := (RoundDateTime(CarCarrierHeader."Ending Date-Time", 3600000, '<') - RoundDateTime(CarCarrierHeader."Starting Date-Time", 3600000, '<')) / 3600000;
        Ship.Get(CarCarrierHeader."Ship No.");
        Ship.TestField("Contract Unit Cost");
        CarCarrRevenueExpense.Init();
        CarCarrRevenueExpense."Document No." := CarCarrierHeader."No.";
        CarCarrRevenueExpense.Insert(true);
        CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::Consumption);
        CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."Hire Item No.");
        CarCarrRevenueExpense.Validate(Quantity, DurationHour);
        CarCarrRevenueExpense.Validate("Unit Price/Cost", Ship."Contract Unit Cost" / 24);
        CarCarrRevenueExpense.Validate("Currency Code", Ship."Contract Currency Code");
        CarCarrRevenueExpense.Validate("Posting Date", CarCarrierHeader."Ending Date-Time".Date());
        CarCarrRevenueExpense.Modify(true);
        Message(ShipHireExpenseCreatedMsg);
    end;

    procedure PortCodeMandatoryCheck(ItemNo: Code[20]; PortCode: Code[10])
    var
        PortCodeMandatoryErr: Label 'Port Code is mandatory for Item: %1', Comment = '%1=ItemNo';
    begin
        Clear(Item);
        Item.Get(ItemNo);
        if Item."Car Carr. Rev/Exp Port Man ERK" and (PortCode = '') then
            Error(PortCodeMandatoryErr, ItemNo);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting_PurchasePost(var PurchaseHeader: Record "Purchase Header"; var PurchInvHeader: Record "Purch. Inv. Header"; var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var SkipDelete: Boolean; CommitIsSupressed: Boolean; var TempPurchLine: Record "Purchase Line" temporary; var TempPurchLineGlobal: Record "Purchase Line" temporary; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    var
        PurchaseLine: Record "Purchase Line";
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetRange(Type, PurchaseLine.Type::Item);
        if PurchaseLine.IsEmpty() then
            exit;
        CarCarrRevenueExpense.SetRange("Unposted Invoice No.", PurchaseHeader."No.");
        CarCarrRevenueExpense.ModifyAll("Posted Invoice No.", PurchInvHeader."No.", false);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterPostSalesDoc, '', false, false)]
    local procedure OnAfterPostSalesDoc(var SalesHeader: Record "Sales Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; SalesShptHdrNo: Code[20]; RetRcpHdrNo: Code[20]; SalesInvHdrNo: Code[20]; SalesCrMemoHdrNo: Code[20]; CommitIsSuppressed: Boolean; InvtPickPutaway: Boolean; var CustLedgerEntry: Record "Cust. Ledger Entry"; WhseShip: Boolean; WhseReceiv: Boolean; PreviewMode: Boolean)
    begin
        ClearCarCarrierRevenueExpenseFieldsOnSalesCancellation(SalesHeader);
    end;

    procedure CheckDatesWithPreviousCarCarrierDocumentFromCarCarrierLineDetail(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    var
        PreviousCarCarrierHeader: Record "Car Carrier Header ERK";
        CarCarrierHeader: Record "Car Carrier Header ERK";
        DateHelperErr: Label 'Previous Car Carriers Ending Time can not be greater than Current Car Carriers Starting Time.';
    begin
        CarCarrierHeader.Get(CarCarrierLineDetail."Document No.");
        CarCarrierHeader.TestField(Status, CarCarrierHeader.Status::Active);
        PreviousCarCarrierHeader.SetCurrentKey("Starting Date-Time");
        PreviousCarCarrierHeader.SetFilter("No.", '<>%1', CarCarrierHeader."No.");
        PreviousCarCarrierHeader.SetRange("Ship No.", CarCarrierHeader."Ship No.");
        PreviousCarCarrierHeader.SetFilter(Status, '<>%1|%2', PreviousCarCarrierHeader.Status::Planned, PreviousCarCarrierHeader.Status::" ");
        PreviousCarCarrierHeader.SetFilter("Starting Date-Time", '<%1', CarCarrierLineDetail."Loading Start Date-Time");
        if not PreviousCarCarrierHeader.FindLast() then
            exit;
        if TypeHelper.CompareDateTime(PreviousCarCarrierHeader."Ending Date-Time", CarCarrierLineDetail."Loading Start Date-Time") > 0 then
            Error(DateHelperErr);
    end;

    procedure CalculateConsumptionValuesFromCarCarrierLine(var CarCarrierLine: Record "Car Carrier Line ERK"; OriginalCarCarrierLine: Record "Car Carrier Line ERK")
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
        PreviousCarCarrierLine: Record "Car Carrier Line ERK";
        NegativeConsumptionErr: Label 'Consumption can not be less than 0.\ Car Carrier No.: %1 - Car Carrier Line No.: %2', Comment = '%1=CarCarrierLine."Document No."; %2=CarCarrierLine."Line No."';
    begin
        CarCarrierHeader.Get(CarCarrierLine."Document No.");
        CarCarrierHeader.Validate("Fuel Consumption Calculated", true);
        CarCarrierHeader.Modify(false);
        CarCarrierLine.Validate("Sea Passage Consumption (IFO)", CarCarrierLine."COSP ROB (IFO)" - CarCarrierLine."EOSP ROB (IFO)");
        CarCarrierLine.Validate("Sea Passage Consumption (MGO)", CarCarrierLine."COSP ROB (MGO)" - CarCarrierLine."EOSP ROB (MGO)");
        CarCarrierLine.Validate("Sea Passage Consum. (HSFO)", CarCarrierLine."COSP ROB (HSFO)" - CarCarrierLine."EOSP ROB (HSFO)");
        CarCarrierLine.Validate("Sea Passage Consum. (LNG)", CarCarrierLine."COSP ROB (LNG)" - CarCarrierLine."EOSP ROB (LNG)");
        CarCarrierLine.Validate("EOSP Consumption (IFO)", CarCarrierLine."EOSP ROB (IFO)" - CarCarrierLine."All Fast ROB (IFO)");
        CarCarrierLine.Validate("EOSP Consumption (MGO)", CarCarrierLine."EOSP ROB (MGO)" - CarCarrierLine."All Fast ROB (MGO)");
        CarCarrierLine.Validate("EOSP Consumption (HSFO)", CarCarrierLine."EOSP ROB (HSFO)" - CarCarrierLine."All Fast ROB (HSFO)");
        CarCarrierLine.Validate("EOSP Consumption (LNG)", CarCarrierLine."EOSP ROB (LNG)" - CarCarrierLine."All Fast ROB (LNG)");
        PreviousCarCarrierLine.SetRange("Document No.", CarCarrierLine."Document No.");
        PreviousCarCarrierLine.SetFilter("Line No.", '<%1', CarCarrierLine."Line No.");
        if PreviousCarCarrierLine.FindLast() then begin
            if PreviousCarCarrierLine."Line No." = OriginalCarCarrierLine."Line No." then begin
                CarCarrierLine.Validate("COSP Consumption (IFO)", OriginalCarCarrierLine."Fueling (IFO)" + OriginalCarCarrierLine."All Fast ROB (IFO)" - CarCarrierLine."COSP ROB (IFO)");
                CarCarrierLine.Validate("COSP Consumption (MGO)", OriginalCarCarrierLine."Fueling (MGO)" + OriginalCarCarrierLine."All Fast ROB (MGO)" - CarCarrierLine."COSP ROB (MGO)");
                CarCarrierLine.Validate("COSP Consumption (HSFO)", OriginalCarCarrierLine."Fueling (HSFO)" + OriginalCarCarrierLine."All Fast ROB (HSFO)" - CarCarrierLine."COSP ROB (HSFO)");
                CarCarrierLine.Validate("COSP Consumption (LNG)", OriginalCarCarrierLine."Fueling (LNG)" + OriginalCarCarrierLine."All Fast ROB (LNG)" - CarCarrierLine."COSP ROB (LNG)");
            end
            else begin
                CarCarrierLine.Validate("COSP Consumption (IFO)", PreviousCarCarrierLine."Fueling (IFO)" + PreviousCarCarrierLine."All Fast ROB (IFO)" - CarCarrierLine."COSP ROB (IFO)");
                CarCarrierLine.Validate("COSP Consumption (MGO)", PreviousCarCarrierLine."Fueling (MGO)" + PreviousCarCarrierLine."All Fast ROB (MGO)" - CarCarrierLine."COSP ROB (MGO)");
                CarCarrierLine.Validate("COSP Consumption (HSFO)", PreviousCarCarrierLine."Fueling (HSFO)" + PreviousCarCarrierLine."All Fast ROB (HSFO)" - CarCarrierLine."COSP ROB (HSFO)");
                CarCarrierLine.Validate("COSP Consumption (LNG)", PreviousCarCarrierLine."Fueling (LNG)" + PreviousCarCarrierLine."All Fast ROB (LNG)" - CarCarrierLine."COSP ROB (LNG)");
            end;
        end
        else begin
            CarCarrierLine.Validate("COSP Consumption (IFO)", CarCarrierHeader."Starting Fuel (IFO)" - CarCarrierLine."COSP ROB (IFO)");
            CarCarrierLine.Validate("COSP Consumption (MGO)", CarCarrierHeader."Starting Fuel (MGO)" - CarCarrierLine."COSP ROB (MGO)");
            CarCarrierLine.Validate("COSP Consumption (HSFO)", CarCarrierHeader."Starting Fuel (HSFO)" - CarCarrierLine."COSP ROB (HSFO)");
            CarCarrierLine.Validate("COSP Consumption (LNG)", CarCarrierHeader."Starting Fuel (LNG)" - CarCarrierLine."COSP ROB (LNG)");
        end;

        if (CarCarrierLine."COSP Consumption (IFO)" < 0) or
            (CarCarrierLine."COSP Consumption (MGO)" < 0) or
            (CarCarrierLine."COSP Consumption (HSFO)" < 0) or
            (CarCarrierLine."COSP Consumption (LNG)" < 0) or
            (CarCarrierLine."EOSP Consumption (IFO)" < 0) or
            (CarCarrierLine."EOSP Consumption (MGO)" < 0) or
            (CarCarrierLine."EOSP Consumption (HSFO)" < 0) or
            (CarCarrierLine."EOSP Consumption (LNG)" < 0) or
            (CarCarrierLine."Sea Passage Consumption (IFO)" < 0) or
            (CarCarrierLine."Sea Passage Consumption (MGO)" < 0) or
            (CarCarrierLine."Sea Passage Consum. (HSFO)" < 0) or
            (CarCarrierLine."Sea Passage Consum. (LNG)" < 0) then
            Error(NegativeConsumptionErr, CarCarrierLine."Document No.", CarCarrierLine."Line No.");
        CarCarrierLine.Modify(true);
    end;

    procedure CalculateConsumptionValuesFromCarCarrierLineLoop(var CarCarrierLine: Record "Car Carrier Line ERK")
    var
        CarCarrierLineLoop: Record "Car Carrier Line ERK";
    begin
        CarCarrierLineLoop.SetRange("Document No.", CarCarrierLine."Document No.");
        if not CarCarrierLineLoop.FindSet(true) then
            exit;
        repeat
            CalculateConsumptionValuesFromCarCarrierLine(CarCarrierLineLoop, CarCarrierLine);
        until CarCarrierLineLoop.Next() = 0;
    end;

    procedure ClearFuelConsumptionCalculatedField(CarCarrierLine: Record "Car Carrier Line ERK")
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
    begin
        CarCarrierHeader.Get(CarCarrierLine."Document No.");
        CarCarrierHeader.Validate("Fuel Consumption Calculated", false);
        CarCarrierHeader.Modify(false);
    end;

    procedure CalculateTotalIFOConsumption(CarCarrierHeader: Record "Car Carrier Header ERK"): Decimal
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        CarCarrierLine.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrierLine.CalcSums("COSP Consumption (IFO)", "EOSP Consumption (IFO)", "Sea Passage Consumption (IFO)");
        exit(CarCarrierLine."COSP Consumption (IFO)" + CarCarrierLine."EOSP Consumption (IFO)" + CarCarrierLine."Sea Passage Consumption (IFO)");
    end;

    procedure CalculateTotalMGOConsumption(CarCarrierHeader: Record "Car Carrier Header ERK"): Decimal
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        CarCarrierLine.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrierLine.CalcSums("COSP Consumption (MGO)", "EOSP Consumption (MGO)", "Sea Passage Consumption (MGO)");
        exit(CarCarrierLine."COSP Consumption (MGO)" + CarCarrierLine."EOSP Consumption (MGO)" + CarCarrierLine."Sea Passage Consumption (MGO)");
    end;

    procedure CalculateTotalHSFOConsumption(CarCarrierHeader: Record "Car Carrier Header ERK"): Decimal
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        CarCarrierLine.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrierLine.CalcSums("COSP Consumption (HSFO)", "EOSP Consumption (HSFO)", "Sea Passage Consum. (HSFO)");
        exit(CarCarrierLine."COSP Consumption (HSFO)" + CarCarrierLine."EOSP Consumption (HSFO)" + CarCarrierLine."Sea Passage Consum. (HSFO)");
    end;

    procedure CalculateTotalLNGConsumption(CarCarrierHeader: Record "Car Carrier Header ERK"): Decimal
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        CarCarrierLine.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrierLine.CalcSums("COSP Consumption (LNG)", "EOSP Consumption (LNG)", "Sea Passage Consum. (LNG)");
        exit(CarCarrierLine."COSP Consumption (LNG)" + CarCarrierLine."EOSP Consumption (LNG)" + CarCarrierLine."Sea Passage Consum. (LNG)");
    end;

    procedure CreateLNGConsumptionLineFromCarCarrierHeader(CarCarrierHeader: Record "Car Carrier Header ERK")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        LineCreatedMsg: Label 'LNG Consumption Lines Created.';
        NoLinesCreatedErr: Label 'No Fuel Consumption Lines Created.';
        LNGAlreadyExistErr: Label 'LNG Consumption Line already created. Please delete the existing line and try again.';
        ConsumptionLineCreated: Boolean;
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("LNG Item No.");
        ErkHoldingSetup.TestField("Consumption Customer No.");
        CarCarrRevenueExpense.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrRevenueExpense.SetRange("Type", CarCarrRevenueExpense.Type::Consumption);
        CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."LNG Item No.");
        if not CarCarrRevenueExpense.IsEmpty() then
            Error(LNGAlreadyExistErr);
        if CalculateTotalLNGConsumption(CarCarrierHeader) > 0 then begin
            CarCarrRevenueExpense.Init();
            CarCarrRevenueExpense."Document No." := CarCarrierHeader."No.";
            CarCarrRevenueExpense.Insert(true);
            CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::Consumption);
            CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."LNG Item No.");
            CarCarrRevenueExpense.Validate(Quantity, CalculateTotalLNGConsumption(CarCarrierHeader));
            CarCarrRevenueExpense.Validate("Source No.", ErkHoldingSetup."Consumption Customer No.");
            CarCarrRevenueExpense.Modify(true);
            ConsumptionLineCreated := true;
        end;
        if ConsumptionLineCreated then
            Message(LineCreatedMsg)
        else
            Error(NoLinesCreatedErr);
    end;

    // procedure CalculateCarCarrierOrderSequenceNo()
    // var
    //     CarCarrierHeader: Record "Car Carrier Header ERK";
    //     xCarCarrierHeader: Record "Car Carrier Header ERK";
    // //VoyageTypeText: Text[30];
    // begin
    //     CarCarrierHeader.ModifyAll("Sequence Order No.", 0, false);
    //     CarCarrierHeader.SetCurrentKey("Voyage Type", "Ship No.", "Starting Date-Time");
    //     CarCarrierHeader.SetFilter("Starting Date-Time", '<>%1', 0DT);
    //     CarCarrierHeader.SetFilter(Status, '%1|%2', CarCarrierHeader.Status::Active, CarCarrierHeader.Status::Completed);
    //     if not CarCarrierHeader.FindSet(true) then
    //         exit;
    //     xCarCarrierHeader := CarCarrierHeader;
    //     CarCarrierHeader."Sequence Order No." := 1;
    //     repeat
    //         if (CarCarrierHeader."No." <> xCarCarrierHeader."No.") and (CarCarrierHeader."Ship No." = xCarCarrierHeader."Ship No.") and (Date2DMY(DT2Date(CarCarrierHeader."Starting Date-Time"), 3) = Date2DMY(DT2Date(xCarCarrierHeader."Starting Date-Time"), 3)) and (CarCarrierHeader."Voyage Type" = xCarCarrierHeader."Voyage Type") then
    //             CarCarrierHeader."Sequence Order No." := xCarCarrierHeader."Sequence Order No." + 1
    //         else
    //             CarCarrierHeader."Sequence Order No." := 1;
    //         CarCarrierHeader."Sequence Order Text" := CopyStr(CarCarrierHeader."Ship Name" + ' ' + Format(Date2DMY(DT2Date(CarCarrierHeader."Starting Date-Time"), 3)) + ' ' + Format(CarCarrierHeader."Sequence Order No.") + '. ' + Format(CarCarrierHeader."Voyage Type"), 1, MaxStrLen(CarCarrierHeader."Sequence Order Text"));
    //         CarCarrierHeader.Modify(true);
    //         xCarCarrierHeader := CarCarrierHeader;
    //     until CarCarrierHeader.Next() = 0;
    //     Message('Ordering process succesfull.');
    // end;

    procedure CancelDischargeSelectedCarCarrierLineDetails(var CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
        CancelDischargeMsg: Label 'All discharge process cancelled succesfully.';
    begin
        CarCarrierLineDetail.FindSet(true);
        repeat
            CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLineDetail."Document No.");
            CarCarrierLedgerEntry.SetRange("Document Line No.", CarCarrierLineDetail."Document Line No.");
            CarCarrierLedgerEntry.SetRange("Document Line Detail No.", CarCarrierLineDetail."Line No.");
            CarCarrierLedgerEntry.ModifyAll("Discharge Port Line No.", 0, false);
            CarCarrierLedgerEntry.ModifyAll("Discharge Port", '', false);
            CarCarrierLedgerEntry.ModifyAll("Discharge Port Description", '', false);
            CarCarrierLedgerEntry.ModifyAll("Discharge DateTime", 0DT, false);
            CarCarrierLedgerEntry.ModifyAll("Loading to Discharge Desc.", '', false);
            CarCarrierLineDetail."Discharge Port Line No." := 0;
            CarCarrierLineDetail."Discharge Port" := '';
            CarCarrierLineDetail."Discharge Start Date-Time" := 0DT;
            CarCarrierLineDetail."Discharge End Date-Time" := 0DT;
            CarCarrierLineDetail.Modify(false);
        until CarCarrierLineDetail.Next() = 0;
        OnAfterInsertCancelDischargeSelectedCarCarrierLineDetails(CarCarrierLineDetail);
        CarCarrierOrderLine.SetRange("Document No.", CarCarrierLineDetail."Order No.");
        CarCarrierOrderLine.SetRange("Planned Car Carrier No.", CarCarrierLineDetail."Document No.");
        if CarCarrierOrderLine.FindFirst() then
            if CarCarrierOrderLine.Status <> CarCarrierOrderLine.Status::Planned then
                CarCarrierOrderLine.ModifyAll(Status, CarCarrierOrderLine.Status::Loaded, true);
        Message(CancelDischargeMsg);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPostPurchaseDoc, '', false, false)]
    local procedure "Purch.-Post_OnAfterPostPurchaseDoc"(var PurchaseHeader: Record "Purchase Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; PurchRcpHdrNo: Code[20]; RetShptHdrNo: Code[20]; PurchInvHdrNo: Code[20]; PurchCrMemoHdrNo: Code[20]; CommitIsSupressed: Boolean)
    begin
        ClearCarCarrierRevenueExpenseFieldsOnPurchaseCancellation(PurchaseHeader);
    end;

    local procedure ClearCarCarrierRevenueExpenseFieldsOnPurchaseCancellation(var PurchaseHeader: Record "Purchase Header")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        if PurchaseHeader."Document Type" <> PurchaseHeader."Document Type"::"Credit Memo" then
            exit;
        if not PurchaseHeader."Cancel INF" then
            exit;
        CarCarrRevenueExpense.SetRange("Posted Invoice No.", PurchaseHeader."Applies-to Doc. No.");
        CarCarrRevenueExpense.ModifyAll("External Document No.", '', false);
        CarCarrRevenueExpense.ModifyAll("Unposted Invoice No.", '', false);
        CarCarrRevenueExpense.ModifyAll("Posted Invoice No.", '', false);
    end;

    procedure CreateRealizedLinesFromExpectedCarCarrierRevenueExpenseLines(var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    var
        RealizedCarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        expenseRevenueRealizationErr: Label 'Only Expected Expense and Expected Revenue lines can be realized.';
        realizedLinesCreatedCountMsg: Label '%1 Realized lines created.', Comment = '%1=Count';
    begin
        CarCarrRevenueExpense.FindSet(false);
        repeat
            case CarCarrRevenueExpense.Type of
                CarCarrRevenueExpense.Type::"Expected Expense", CarCarrRevenueExpense.Type::"Expected Revenue":
                    begin
                        RealizedCarCarrRevenueExpense.Init();
                        RealizedCarCarrRevenueExpense.TransferFields(CarCarrRevenueExpense);
                        RealizedCarCarrRevenueExpense."Line No." := 0;
                        if CarCarrRevenueExpense.Type = CarCarrRevenueExpense.Type::"Expected Revenue" then
                            RealizedCarCarrRevenueExpense.Type := RealizedCarCarrRevenueExpense.Type::Revenue
                        else
                            if CarCarrRevenueExpense.Type = CarCarrRevenueExpense.Type::"Expected Expense" then
                                RealizedCarCarrRevenueExpense.Type := RealizedCarCarrRevenueExpense.Type::Expense;
                        RealizedCarCarrRevenueExpense."Source Proforma Line No." := CarCarrRevenueExpense."Line No.";
                        RealizedCarCarrRevenueExpense.Insert(true);
                        CarCarrRevenueExpense."Source Realized Line No." := RealizedCarCarrRevenueExpense."Line No.";
                    end;
                else
                    Error(expenseRevenueRealizationErr);
            end;
        until CarCarrRevenueExpense.Next() = 0;
        Message(realizedLinesCreatedCountMsg, CarCarrRevenueExpense.Count());
    end;

    procedure CreateEstimatedandExpectedHireLine(CarCarrierHeader: Record "Car Carrier Header ERK")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        Contract: Record "Contract ERK";
        CarCarrRevenueExpenseType: Enum "Voyage Line Detail Type ERK";
        hireLineCreationErr: Label 'Expected Hire Lines already created. Please delete the existing line and try again.';
        notFoundContractErr: Label 'Contract not found for Ship No.: %1', Comment = '%1="Car Carrier Header ERK"."Ship No."';
        hireLinesCreatedMsg: Label 'Expected Hire Line created.';
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Hire Item No.");
        CarCarrierHeader.TestField("Planned Starting Date");
        CarCarrierHeader.TestField("Planned Ending Date");
        CarCarrRevenueExpense.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrRevenueExpense.SetFilter(Type, '%1|%2', CarCarrRevenueExpense.Type::"Estimated Expense", CarCarrRevenueExpense.Type::"Expected Expense");
        CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."Hire Item No.");
        if not CarCarrRevenueExpense.IsEmpty() then
            Error(hireLineCreationErr);
        Contract.SetRange("Ship No.", CarCarrierHeader."Ship No.");
        Contract.SetRange(Active, true);
        if not Contract.FindFirst() then
            Error(notFoundContractErr, CarCarrierHeader."Ship No.");
        Contract.TestField("Daily Hire");
        // CarCarrRevenueExpenseType := CarCarrRevenueExpenseType::"Estimated Expense";
        // InsertCarCarrierRevenueExpenseLineForHire(CarCarrierHeader, CarCarrRevenueExpense, Contract, CarCarrRevenueExpenseType);
        CarCarrRevenueExpenseType := CarCarrRevenueExpenseType::"Expected Expense";
        InsertCarCarrierRevenueExpenseLineForHire(CarCarrierHeader, CarCarrRevenueExpense, Contract, CarCarrRevenueExpenseType);
        Message(hireLinesCreatedMsg);
    end;

    local procedure InsertCarCarrierRevenueExpenseLineForHire(var CarCarrierHeader: Record "Car Carrier Header ERK"; var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK"; var Contract: Record "Contract ERK"; var CarCarrRevenueExpenseType: Enum "Voyage Line Detail Type ERK")
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Consumption Customer No.");

        CarCarrRevenueExpense.Init();
        CarCarrRevenueExpense."Document No." := CarCarrierHeader."No.";
        CarCarrRevenueExpense.Insert(true);
        CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpenseType);
        CarCarrRevenueExpense.Validate("Source No.", ErkHoldingSetup."Consumption Customer No.");
        CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."Hire Item No.");
        CarCarrRevenueExpense.Validate(Quantity, (CarCarrierHeader."Planned Ending Date" - CarCarrierHeader."Planned Starting Date") * 24);
        CarCarrRevenueExpense.Validate("Unit Price/Cost", Contract."Daily Hire" / 24);
        CarCarrRevenueExpense.Validate("Currency Code", Contract."Currency Code");
        CarCarrRevenueExpense.Validate("Posting Date", WorkDate());
        CarCarrRevenueExpense.Modify(true);
    end;

    #region Vehicle Transfer Between Car Carrier Documents
    procedure TransferRemaningVehicles(CarCarrierHeader: Record "Car Carrier Header ERK")
    var
        TargetCarCarrierHeader: Record "Car Carrier Header ERK";
        NewCarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        TargetCarCarrierLine: Record "Car Carrier Line ERK";
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        NewCarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        noActiveCarCarrierDocumentErr: Label 'There is no active Car Carrier Document for Ship Name: %1', Comment = '%1="Car Carrier Header ERK"."Ship Name"';
        noVehicleErr: Label 'There is no vehicle to transfer.';
        ConfirmLbl: Label '%1 Vehicles will be transferred to Car Carrier No.: %2. Do you confirm?', Comment = '%1=; %2=';
        ConfirmTxt: Text;
    begin
        CarCarrierHeader.TestField(Status, CarCarrierHeader.Status::Completed);
        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierHeader."No.");
        CarCarrierLedgerEntry.SetRange("Discharge DateTime", 0DT);
        if not CarCarrierLedgerEntry.FindSet(true) then
            Error(noVehicleErr);

        TargetCarCarrierHeader.SetRange("Ship No.", CarCarrierHeader."Ship No.");
        TargetCarCarrierHeader.SetRange(Status, TargetCarCarrierHeader.Status::Active);
        if not TargetCarCarrierHeader.FindFirst() then
            Error(noActiveCarCarrierDocumentErr, CarCarrierHeader."Ship Name");

        TargetCarCarrierLine.SetRange("Document No.", TargetCarCarrierHeader."No.");
        TargetCarCarrierLine.FindFirst();
        TargetCarCarrierLine.TestField("Departure Port");

        ConfirmTxt := StrSubstNo(ConfirmLbl, CarCarrierLedgerEntry.Count(), TargetCarCarrierHeader."No.");
        if not ConfirmManagement.GetResponseOrDefault(ConfirmTxt) then
            exit;

        repeat
            CarCarrierLineDetail.Get(CarCarrierLedgerEntry."Document No.", CarCarrierLedgerEntry."Document Line No.", CarCarrierLedgerEntry."Document Line Detail No.");
            NewCarCarrierLineDetail.Reset();
            NewCarCarrierLineDetail.SetRange("Document No.", TargetCarCarrierHeader."No.");
            NewCarCarrierLineDetail.SetRange("Document Line No.", TargetCarCarrierLine."Line No.");
            NewCarCarrierLineDetail.SetRange("Order No.", CarCarrierLineDetail."Order No.");
            NewCarCarrierLineDetail.SetRange("Order Load Detail Line No.", CarCarrierLineDetail."Order Load Detail Line No.");
            if not NewCarCarrierLineDetail.FindFirst() then begin
                NewCarCarrierLineDetail.Init();
                NewCarCarrierLineDetail.TransferFields(CarCarrierLineDetail);
                NewCarCarrierLineDetail."Document No." := TargetCarCarrierHeader."No.";
                NewCarCarrierLineDetail."Document Line No." := TargetCarCarrierLine."Line No.";
                NewCarCarrierLineDetail."Discharge Port Line No." := 0;
                NewCarCarrierLineDetail.Insert(true);
                NewCarCarrierLineDetail.Validate("Loading Port", CarCarrierLineDetail."Loading Port");
                NewCarCarrierLineDetail.Validate("Previous Car Carrier No.", CarCarrierLineDetail."Document No.");
                NewCarCarrierLineDetail.Modify(true);
            end;

            NewCarCarrierLedgerEntry.Init();
            NewCarCarrierLedgerEntry.TransferFields(CarCarrierLedgerEntry);
            NewCarCarrierLedgerEntry."Entry No." := 0;

            NewCarCarrierLedgerEntry."Document No." := TargetCarCarrierHeader."No.";
            NewCarCarrierLedgerEntry."From Document No." := CarCarrierHeader."No.";
            NewCarCarrierLedgerEntry."Document Line No." := NewCarCarrierLineDetail."Document Line No.";
            NewCarCarrierLedgerEntry."Document Line Detail No." := NewCarCarrierLineDetail."Line No.";

            NewCarCarrierLedgerEntry.Insert(true);

            CarCarrierLedgerEntry."To Document No." := TargetCarCarrierHeader."No.";
            CarCarrierLedgerEntry.Modify(true);
        until CarCarrierLedgerEntry.Next() = 0;
    end;

    #endregion Vehicle Transfer Between Car Carrier Documents

    [EventSubscriber(ObjectType::Table, Database::"Car Carr. Revenue/Expense ERK", OnBeforeValidateEvent, "Posting Date", false, false)]
    local procedure OnBeforeValidate_PostingDate_CarCarrRevenueExpense(var Rec: Record "Car Carr. Revenue/Expense ERK")
    var
        PostingDateFutureErr: Label 'Posting Date cannot be greater than today.';
    begin
        if Rec."Posting Date" > Today() then
            Error(PostingDateFutureErr);
    end;

    [BusinessEvent(true)]
    local procedure OnAfterDischargeSelectedCarCarrierLineDetails(var CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK")
    begin
    end;

    [BusinessEvent(true)]
    local procedure OnAfterInsertVehicleLedgerEntryForLoading(var CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK")
    begin
    end;

    [BusinessEvent(true)]
    local procedure OnAfterInsertCancelDischargeSelectedCarCarrierLineDetails(var CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    begin
    end;



    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        Item: Record Item;
        ConfirmManagement: Codeunit "Confirm Management";
        NoSeries: Codeunit "No. Series";
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        TypeHelper: Codeunit "Type Helper";
        CarCarrierOrderMngt: Codeunit "Car Carrier Order Mngt. ERK";
}
