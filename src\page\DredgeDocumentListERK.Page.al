page 60152 "Dredge Document List ERK"
{
    ApplicationArea = All;
    Caption = 'Dredge Documents';
    CardPageId = "Dredge Document ERK";
    Editable = false;
    PageType = List;
    SourceTable = "Dredge Header ERK";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Operation Starting Date"; Rec."Operation Starting Date")
                {
                }
                field("Operation Ending Date"; Rec."Operation Ending Date")
                {
                }
                field("Department Code"; Rec."Department Code")
                {
                }
                field("Department Name"; Rec."Department Name")
                {
                }
                field("Port Code"; Rec."Port Code")
                {
                }
                field("Port Name"; Rec."Port Name")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Expected Revenue (ACY)"; Rec."Expected Revenue (ACY)")
                {
                }
                field("Actual Revenue (ACY)"; Rec."Actual Revenue (ACY)")
                {
                }
                field("Expected Expense (ACY)"; Rec."Expected Expense (ACY)")
                {
                }
                field("Actual Expense (ACY)"; Rec."Actual Expense (ACY)")
                {
                }
                field(ExpectedProfitACY; Rec."Expected Revenue (ACY)" - Rec."Expected Expense (ACY)")
                {
                    Caption = 'Expected Profit (ACY)';
                    ToolTip = 'Specifies the expected profit in Additional Currency.';
                }
                field(ActualProfitACY; Rec."Actual Revenue (ACY)" - Rec."Actual Expense (ACY)")
                {
                    Caption = 'Actual Profit (ACY)';
                    ToolTip = 'Specifies the actual profit in Additional Currency.';
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                Caption = 'Attachments';
                SubPageLink = "Table ID" = const(Database::"Dredge Header ERK"), "No." = field("No.");
            }
        }
    }

    actions
    {
        area(Navigation)
        {
            action(DredgeLines)
            {
                Caption = 'Dredge Lines';
                Image = AllLines;
                RunObject = page "Dredge Lines ERK";
                RunPageLink = "Document No." = field("No.");
                ToolTip = 'View the dredge lines for this header.';
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
            }
        }
        area(Processing)
        {
            action(ChangeStatus)
            {
                Caption = 'Change Status';
                Image = Status;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Change the status of the dredge header.';

                trigger OnAction()
                var
                    DredgeStatus: Enum "Dredge Status ERK";
                    Selection: Integer;
                    StatusTxt: Text[30];
                    StatusOptions: Text[100];
                    StatusChangeConfirmQst: Label 'Are you sure you want to change the status to %1?', Comment = '%1 = Status Text';
                begin
                    StatusOptions := Format(DredgeStatus::Planned) + ',' + Format(DredgeStatus::Active) + ',' + Format(DredgeStatus::Completed);
                    Selection := StrMenu(StatusOptions, 1, 'Select Status');

                    if Selection = 0 then
                        exit;

                    case Selection of
                        1:
                            DredgeStatus := DredgeStatus::Planned;
                        2:
                            DredgeStatus := DredgeStatus::Active;
                        3:
                            DredgeStatus := DredgeStatus::Completed;
                    end;

                    StatusTxt := Format(DredgeStatus);
                    if Confirm(StatusChangeConfirmQst, false, StatusTxt) then begin
                        Rec.Validate(Status, DredgeStatus);
                        Rec.Modify(true);
                    end;
                end;
            }
        }
    }
}