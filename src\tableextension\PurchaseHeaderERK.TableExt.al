tableextension 60007 "Purchase Header ERK" extends "Purchase Header"
{
    fields
    {
        field(60000; "Completed ERK"; Boolean)
        {
            Caption = 'Completed';
            ToolTip = 'Specifies the value of the Completed field.';
        }
        field(60001; "Incoming E-Invoice No. ERK"; Code[35])
        {
            Caption = 'Incoming E-Invoice No.';
            ToolTip = 'Specifies the value of the Incoming E-Invoice No. field.';
            trigger OnLookup()
            var
                EInvoiceHeader: Record "E-Invoice Header INF";
                PurchaseLine: Record "Purchase Line";
                DifferentTaxAmountErr: Label 'Tax amounts must be same with selected Incoming E-Invoice.';
                AmountErr: Label 'Total amount must %1. But it is %2', Comment = '%1="E-Invoice Header INF"."Payable Amount"; %2=TotalAmountIncWithholding';
                TotalAmountIncWithholding: Decimal;
            begin
                Rec.CalcFields("Amount Including VAT");
                EInvoiceSetup.Get();
                EInvoiceSetup.TestField("Inv. Type for Exp. Registered");
                EInvoiceHeader.SetRange("Source Type", EInvoiceHeader."Source Type"::Vendor);
                EInvoiceHeader.SetRange("Source No.", Rec."Buy-from Vendor No.");
                EInvoiceHeader.SetRange("Created Inbound Document", false);
                EInvoiceHeader.SetRange("EBA Status ERK", EInvoiceHeader."EBA Status ERK"::Approved);
                if Rec."Vendor Invoice No." <> '' then
                    EInvoiceHeader.SetRange("E-Invoice No.", Rec."Vendor Invoice No.");
                if not (Page.RunModal(Page::"Inbound E-Invoices INF", EInvoiceHeader) = Action::LookupOK) then
                    exit;
                if not "Ignore Inc.E-Inv. Controls ERK" then begin
                    Rec.TestField("Currency Code", EInvoiceHeader."Document Currency Code");
                    if Rec."Posting Date" <> 0D then
                        Rec.TestField("Posting Date", EInvoiceHeader."Posting Date");
                    if not (EInvoiceHeader."Invoice Type Code" = EInvoiceSetup."Inv. Type for Exp. Registered") then begin
                        PurchaseLine.SetRange("Document Type", Rec."Document Type");
                        PurchaseLine.SetRange("Document No.", Rec."No.");
                        PurchaseLine.CalcSums("VAT Base Amount");
                        PurchaseLine.CalcSums("Amount Including VAT");
                        PurchaseLine.CalcSums("VAT Withholding Amount INF");
                        if (PurchaseLine."Amount Including VAT" - PurchaseLine."VAT Base Amount") <> EInvoiceHeader."Tax Total Amount" then
                            Error(DifferentTaxAmountErr);
                        Rec.CalcFields("Amount Including VAT");
                        TotalAmountIncWithholding := Rec."Amount Including VAT" - PurchaseLine."VAT Withholding Amount INF";
                        if TotalAmountIncWithholding <> EInvoiceHeader."Payable Amount" then
                            Error(AmountErr, EInvoiceHeader."Payable Amount", TotalAmountIncWithholding);
                    end;
                end;
                Rec.Validate("Incoming E-Invoice No. ERK", EInvoiceHeader."E-Invoice No.");
                Rec.Validate("Vendor Invoice No.", EInvoiceHeader."E-Invoice No.");
                Rec.Validate("EBA Status ERK", EInvoiceHeader."EBA Status ERK");
                EInvoiceHeader.Validate("Created Inbound Document", true);
                EInvoiceHeader.Modify(true);
            end;

            trigger OnValidate()
            var
                EInvoiceHeader: Record "E-Invoice Header INF";
            begin
                if xRec."Incoming E-Invoice No. ERK" <> Rec."Incoming E-Invoice No. ERK" then begin
                    EInvoiceHeader.SetRange("Source No.", Rec."Buy-from Vendor No.");
                    EInvoiceHeader.SetRange("E-Invoice No.", xRec."Incoming E-Invoice No. ERK");
                    if EInvoiceHeader.FindFirst() then begin
                        EInvoiceHeader.Validate("Created Inbound Document", false);
                        EInvoiceHeader.Validate("EBA Status ERK", EInvoiceHeader."EBA Status ERK"::"Waiting for Approval");
                        EInvoiceHeader.Modify(true);
                    end;
                end;

                // if (xRec."Incoming E-Invoice No. ERK" <> '') and (Rec."Incoming E-Invoice No. ERK" = '') then begin
                //     EInvoiceHeader.SetRange("Source No.", Rec."Buy-from Vendor No.");
                //     EInvoiceHeader.SetRange("E-Invoice No.", xRec."Incoming E-Invoice No. ERK");
                //     if EInvoiceHeader.FindFirst() then begin
                //         EInvoiceHeader.Validate("Created Inbound Document", false);
                //         EInvoiceHeader.Modify(true);
                //     end;
                // end;
            end;
        }
        field(60002; "Ignore Inc.E-Inv. Controls ERK"; Boolean)
        {
            Caption = 'Ignore Incoming E-Invoice Controls';
            ToolTip = 'Specifies the value of the Ignore Incoming E-Invoice Controls field.';
        }
        field(60003; "Requested Receipt Text ERK"; Text[250])
        {
            Caption = 'Requested Receipt Date Text';
            ToolTip = 'Specifies the value of the Requested Receipt Date Text field.';
        }
        field(60004; "EBA Status ERK"; Enum "EBA Status ERK")
        {
            Caption = 'EBA Status';
            ToolTip = 'Specifies EBA Status field.';
            InitValue = 1;
            //Editable = false;

            trigger OnValidate()
            begin
                Rec.TestField("Invoice Type INF ERK");
                if ("EBA Status ERK" = "EBA Status ERK"::"Ready for EBA") and ("Invoice Type INF ERK" = "Invoice Type INF ERK"::"E-Archive") then begin
                    Rec.TestField("Vendor Invoice No.");
                    Rec.TestField("Invoice View PDF File ERK");

                end;
            end;
        }
        field(60005; "Invoice View PDF File ERK"; Blob)
        {
            Caption = 'Invoice View PDF File';
        }
        field(60006; "Invoice Type INF ERK"; Enum "E-Document Inv CrMemo Type INF")
        {
            Caption = 'Invoice Type';
            ToolTip = 'Specifies the value of the Invoice Type field.';

            trigger OnValidate()
            begin
                if ("EBA Status ERK" = "EBA Status ERK"::"Ready for EBA") and ("Invoice Type INF ERK" = "Invoice Type INF ERK"::"E-Archive") then begin
                    Rec.TestField("Vendor Invoice No.");
                    Rec.TestField("Invoice View PDF File ERK");

                end;
            end;
        }
        field(60007; "EBA Your Reference ERK"; Text[35])
        {
            Caption = 'Your Reference';
            AllowInCustomizations = Never;

            trigger OnValidate()
            begin
                Rec."Your Reference" := Rec."EBA Your Reference ERK";
            end;

        }
        field(60008; "Created From Car Carrier ERK"; Boolean)
        {
            Caption = 'Created From Car Carrier Rev./Exp.';
            ToolTip = 'Specifies if this invoice was created from Car Carrier Revenue/Expense data.';
        }
    }

    procedure GetPDFFileData() PDFFileData: Text
    var
        TypeHelper: Codeunit "Type Helper";
        InStr: InStream;

    begin
        CalcFields("Invoice View PDF File ERK");
        "Invoice View PDF File ERK".CreateInStream(InStr, TextEncoding::UTF8);
        if not TypeHelper.TryReadAsTextWithSeparator(InStr, TypeHelper.LFSeparator(), PDFFileData) then
            PDFFileData := 'Error reading PDF file data.';
    end;


    var
        EInvoiceSetup: Record "E-Invoice Setup INF";
}
