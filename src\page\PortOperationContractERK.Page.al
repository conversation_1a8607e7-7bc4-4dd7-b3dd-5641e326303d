page 60123 "Port Operation Contract ERK"
{
    ApplicationArea = All;
    Caption = 'Port Operation Contract';
    PageType = Document;
    SourceTable = "Port Operation Contract Header";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Starting Date"; Rec."Starting Date")
                {
                }
                field("Ending Date"; Rec."Ending Date")
                {
                }
                field(Active; Rec.Active)
                {
                }
            }
            part(Lines; "Port Operation Contract Subpag")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }
}