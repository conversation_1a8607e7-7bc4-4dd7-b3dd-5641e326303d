table 60016 "Voyage Expense ERK"
{
    Caption = 'Voyage Expense';
    DataClassification = CustomerContent;
    DrillDownPageId = "Voyage Expense List ERK";
    LookupPageId = "Voyage Expense List ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "No."; Code[20])
        {
            Caption = 'No.';
            TableRelation = Item;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get(Rec."No.") then begin
                    Rec.Validate(Description, Item.Description);
                    Rec.Validate("Unit of Measure Code", Item."Base Unit of Measure");
                    Rec.Validate("Unit Cost", Item."Last Direct Cost");
                end
                else begin
                    Rec.Validate(Description, '');
                    Rec.Validate("Unit of Measure Code", '');
                    Rec.Validate("Unit Cost", 0);
                end;
            end;
        }
        field(4; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(5; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DecimalPlaces = 0 : 3;
            ToolTip = 'Specifies the value of the Quantity field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Cost", 0));
            end;
        }
        field(6; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            TableRelation = "Unit of Measure";
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(7; "Unit Cost"; Decimal)
        {
            Caption = 'Unit Cost';
            ToolTip = 'Specifies the value of the Unit Price field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Cost", 0));
            end;
        }
        field(8; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency;
            ToolTip = 'Specifies the value of the Currency Code field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Cost", 0));
            end;
        }
        field(9; "Vendor No."; Code[20])
        {
            Caption = 'Vendor No.';
            TableRelation = Vendor;
            ToolTip = 'Specifies the value of the Vendor No. field.';
            trigger OnValidate()
            var
                Vendor: Record Vendor;
            begin
                if Vendor.Get("Vendor No.") then
                    "Currency Code" := Vendor."Currency Code"
                else
                    "Currency Code" := '';
            end;
        }
        field(10; "Vendor Name"; Text[100])
        {
            Caption = 'Vendor Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Vendor No.")));
            ToolTip = 'Specifies the value of the Vendor Name field.';
        }
        field(11; "Line Amount"; Decimal)
        {
            Caption = 'Line Amount';
            Editable = false;
            ToolTip = 'Specifies the value of the Line Amount field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount (ACY)", ExportManagement.ConvertAmountToACY(Rec."Posting Date", Rec."Currency Code", Rec."Line Amount"));
            end;
        }
        field(12; "Purchase Invoice No."; Code[20])
        {
            Caption = 'Purchase Invoice No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Purchase Invoice No. field.';
        }
        field(13; "Purchase Invoice Line No."; Integer)
        {
            Caption = 'Purchase Invoice Line No.';
            Editable = false;
            BlankZero = false;
            ToolTip = 'Specifies the value of the Purchase Invoice Line No. field.';
        }
        field(14; "Vendor Invoice No."; Code[35])
        {
            Caption = 'Vendor Invoice No.';
            ToolTip = 'Specifies the value of the Vendor Invoice No. field.';
            //Editable = false;
        }
        field(15; "Variant Code"; Code[10])
        {
            Caption = 'Variant No.';
            TableRelation = "Item Variant".Code where("Item No." = field("No."));
            ToolTip = 'Specifies the value of the Variant No. field.';
            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                Rec.TestField("Purchase Invoice No.", '');
                if ItemVariant.Get("No.", "Variant Code") then
                    Description := ItemVariant.Description
                else
                    Description := '';
            end;
        }
        // field(16; "Department Code"; Code[20])
        // {
        //     Caption = 'Department Code';
        //     TableRelation = "Dimension Value".Code where("Dimension Code" = const('Departman'));
        // }
        field(17; "Consumption Line"; Boolean)
        {
            Caption = 'Consumption Line';
            ToolTip = 'Specifies the value of the Consumption Line field.';
        }
        field(18; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Cost", 0));
            end;
        }
        field(19; "Is Cancelled"; Boolean)
        {
            Caption = 'Is Cancelled';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = exist("Cancelled Document" where("Cancelled Doc. No." = field("Purchase Invoice No.")));
            ToolTip = 'Specifies the value of the Is Cancelled field.';
        }
        field(20; "Line Amount (ACY)"; Decimal)
        {
            Caption = 'Line Amount (ACY)';
            Editable = false;
            ToolTip = 'Specifies the value of the Line Amount (ACY) field.';
        }
        field(16; Posted; Boolean)
        {
            Caption = 'Posted';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = exist("Value Entry" where("Document No." = field("Purchase Invoice No.")));
            ToolTip = 'Specifies the value of the Posted field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        VoyageExpense: Record "Voyage Expense ERK";
    begin
        VoyageExpense.SetRange("Document No.", Rec."Document No.");
        if VoyageExpense.FindLast() then
            Rec."Line No." := VoyageExpense."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnModify()
    begin
        Rec.TestField("Purchase Invoice No.", '');
    end;

    trigger OnDelete()
    begin
        Rec.TestField("Purchase Invoice No.", '');
    end;

    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        ExportManagement: Codeunit "Export Management ERK";
}
