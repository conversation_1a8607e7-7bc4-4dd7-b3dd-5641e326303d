codeunit 60002 "Company - Extension Map. Mngt."
{
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Application Area Mgmt. Facade", OnSetExperienceTier, '', false, false)]
    local procedure "Application Area Mgmt._OnSetExperienceTier"(ExperienceTierSetup: Record "Experience Tier Setup"; var TempApplicationAreaSetup: Record "Application Area Setup" temporary; var ApplicationAreasSet: Boolean)
    begin
        case true of
            ExperienceTierSetup."RDM ERK":
                GetRDMExperienceAppAreas(TempApplicationAreaSetup);
            ExperienceTierSetup."ErkDeniz ERK":
                GetErkDenizExperienceAppAreas(TempApplicationAreaSetup);
            ExperienceTierSetup."ErkPort ERK":
                GetErkPortExperienceAppAreas(TempApplicationAreaSetup);
        end;
        ApplicationAreasSet := true;
    end;

    local procedure GetBasicAndEssentialExperienceAppAreas(var TempApplicationAreaSetup: Record "Application Area Setup" temporary)
    begin
        //GetBasicExperienceAppAreas(TempApplicationAreaSetup);
        TempApplicationAreaSetup.Basic := true;
        TempApplicationAreaSetup.VAT := true;
        TempApplicationAreaSetup."Basic EU" := true;
        TempApplicationAreaSetup."Relationship Mgmt" := true;
        TempApplicationAreaSetup."Record Links" := true;
        TempApplicationAreaSetup.Notes := true;
        TempApplicationAreaSetup.Suite := true;
        TempApplicationAreaSetup.Jobs := true;
        TempApplicationAreaSetup."Fixed Assets" := true;
        TempApplicationAreaSetup.Location := true;
        TempApplicationAreaSetup.BasicHR := true;
        TempApplicationAreaSetup.Assembly := true;
        TempApplicationAreaSetup."Item Charges" := true;
        TempApplicationAreaSetup."Item References" := true;
        TempApplicationAreaSetup.Intercompany := true;
        TempApplicationAreaSetup."Sales Return Order" := true;
        TempApplicationAreaSetup."Purch Return Order" := true;
        TempApplicationAreaSetup.Prepayments := true;
        TempApplicationAreaSetup."Cost Accounting" := true;
        TempApplicationAreaSetup."Sales Budget" := true;
        TempApplicationAreaSetup."Purchase Budget" := true;
        TempApplicationAreaSetup."Item Budget" := true;
        TempApplicationAreaSetup."Sales Analysis" := true;
        TempApplicationAreaSetup."Purchase Analysis" := true;
        TempApplicationAreaSetup."Inventory Analysis" := true;
        TempApplicationAreaSetup."Item Tracking" := true;
        TempApplicationAreaSetup.Warehouse := true;
        TempApplicationAreaSetup."Order Promising" := true;
        TempApplicationAreaSetup.Reservation := true;
        TempApplicationAreaSetup.Dimensions := true;
        TempApplicationAreaSetup.ADCS := true;
        TempApplicationAreaSetup.Planning := true;
        TempApplicationAreaSetup.Comments := true;
    end;
    #region RDM
    procedure IsExportManagementAreaEnabled(): Boolean
    var
        ApplicationAreaSetup: Record "Application Area Setup";
        ApplicationAreaMgmtFacade: Codeunit "Application Area Mgmt. Facade";
    begin
        if ApplicationAreaMgmtFacade.GetApplicationAreaSetupRecFromCompany(ApplicationAreaSetup, CompanyName()) then
            exit(ApplicationAreaSetup."ExportManagement ERK");
    end;

    procedure GetRDMExperienceAppAreas(var TempApplicationAreaSetup: Record "Application Area Setup" temporary)
    begin
        GetBasicAndEssentialExperienceAppAreas(TempApplicationAreaSetup);
        TempApplicationAreaSetup."ExportManagement ERK" := true;
    end;
    #endregion RDM
    #region Erk Deniz
    procedure GetErkDenizExperienceAppAreas(var TempApplicationAreaSetup: Record "Application Area Setup" temporary)
    begin
        GetBasicAndEssentialExperienceAppAreas(TempApplicationAreaSetup);
        TempApplicationAreaSetup."CustomsOperation ERK" := true;
    end;
    #endregion Erk Deniz
    #region Erk Port
    procedure GetErkPortExperienceAppAreas(var TempApplicationAreaSetup: Record "Application Area Setup" temporary)
    begin
        GetBasicAndEssentialExperienceAppAreas(TempApplicationAreaSetup);
        TempApplicationAreaSetup."ErkPort ERK" := true;
    end;
    #endregion Erk Port
}
