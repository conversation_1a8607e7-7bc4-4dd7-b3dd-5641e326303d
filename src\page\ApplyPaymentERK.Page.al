page 60013 "Apply Payment ERK"
{
    ApplicationArea = ExportManagementERK;
    Caption = 'Apply Payment';
    PageType = Worksheet;
    SourceTable = "Export Ledger Entry ERK";
    UsageCategory = Tasks;

    layout
    {
        area(Content)
        {
            group(ExportInformatiom)
            {
                Caption = 'Export Information';
                Editable = false;

                field(ExportNo; ExportHeader."No.")
                {
                    Caption = 'Export No.';
                }
                field(ExportAmount; ExportHeader."Total Sales Amount")
                {
                    Caption = 'Export Amount';
                }
                field(CurrencyCode; ExportHeader."Sales Currency Code")
                {
                    Caption = 'Currency Code';
                }
                field("Customer Name"; ExportHeader."Customer Name")
                {
                    Caption = 'Customer Name';
                }
                field(RemainingAmount; ExportManagement.CalculateExportRemainingAmount(ExportHeader."No."))
                {
                    Caption = 'Remaning Amount';
                    ToolTip = 'Specifies the value of the Remaning Amount field.';
                }
            }
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Type"; Rec."Document Type")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field(Amount; Rec.Amount)
                {
                }
                field("Remaining Amout"; Rec."Remaining Amout")
                {
                }
                field("Amount to Assign"; Rec."Amount to Assign")
                {
                }
                field("Assignable Amount"; Rec."Assignable Amount")
                {
                }
                field("Assigned Amount"; Rec."Assigned Amount")
                {
                }
                field("Blanket Sales Order No."; Rec."Blanket Sales Order No.")
                {
                }
                field("Source Cust. Ledger Entry No."; Rec."Source Cust. Ledger Entry No.")
                {
                }
                field("Source Export Ledg. Entry No."; Rec."Source Export Ledg. Entry No.")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(PostApplication)
            {
                ApplicationArea = All;
                Caption = 'Post Application';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostApplication;
                PromotedOnly = true;
                ToolTip = 'Executes the Post Application action.';

                trigger OnAction()
                begin
                    ExportManagement.PostApplicationExportCard(Rec, ExportHeader."No.");
                end;
            }
            action(Unapply)
            {
                ApplicationArea = All;
                Caption = 'Unapply';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = UnApply;
                PromotedOnly = true;
                ToolTip = 'Executes the Unapply action.';

                trigger OnAction()
                begin
                    ExportManagement.UnApplyExportCard(Rec);
                end;
            }
        }
    }
    procedure GetExportRecord(ParamExportHeader: Record "Export Header ERK")
    begin
        ExportHeader := ParamExportHeader;
    end;

    var
        ExportHeader: Record "Export Header ERK";
        ExportManagement: Codeunit "Export Management ERK";
}
