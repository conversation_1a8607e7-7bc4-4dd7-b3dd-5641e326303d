table 60037 "PDI Check List ERK"
{
    Caption = 'PDI Check List';
    DataClassification = CustomerContent;
    DrillDownPageId = "PDI Check List ERK";
    LookupPageId = "PDI Check List ERK";

    fields
    {
        field(1; Code; Code[10])
        {
            Caption = 'Code';
            NotBlank = true;
            ToolTip = 'Specifies the value of the Code field.';
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(3; "Parent Code"; Boolean)
        {
            Caption = 'Parent Code';
            ToolTip = 'Specifies the value of the Parent Code field.';
        }
        field(4; "Parent Code Value"; Code[10])
        {
            Caption = 'Parent Code Value';
            ToolTip = 'Specifies the value of the Parent Code Value field.';
        }
        field(5; "Default Result"; Code[20])
        {
            Caption = 'Default Result';
            ToolTip = 'Specifies the value of the Default Result field.';
            TableRelation = "PDI Line Result ERK".Code;
        }
    }
    keys
    {
        key(PK; Code)
        {
            Clustered = true;
        }
    }
}
