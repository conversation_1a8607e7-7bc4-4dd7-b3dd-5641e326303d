table 60029 "Vehicle Transfer Line ERK"
{
    Caption = 'Vehicle Transfer Line';
    DataClassification = CustomerContent;
    LookupPageId = "Vehicle Transfer Lines - Edit";
    DrillDownPageId = "Vehicle Transfer Lines - Edit";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
            trigger OnValidate()
            begin
                VehicleTransferManagement.OnAfterValideSerialNoOnVehicleTransferLine(Rec);
            end;
        }
        field(4; "From Location Code"; Code[10])
        {
            Caption = 'From Location Code';
            ToolTip = 'Specifies the value of the From Location Code field.';
            //Editable = false;
        }
        field(5; "From Bin Code"; Code[20])
        {
            Caption = 'From Bin Code';
            ToolTip = 'Specifies the value of the From Bin Code field.';
            //Editable = false;
        }
        field(6; "To Location Code"; Code[10])
        {
            Caption = 'To Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the To Location Code field.';
            //Editable = false;
        }
        field(7; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("To Location Code"));
            ToolTip = 'Specifies the value of the To Bin Code field.';
            //Editable = false;
        }
        field(8; Processed; Boolean)
        {
            Caption = 'Processed';
            Editable = false;
            ToolTip = 'Specifies the value of the Processed field.';
            trigger OnValidate()
            var
                VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                VehicleTransferLine: Record "Vehicle Transfer Line ERK";
                WarehouseEmployee: Record "Warehouse Employee";
                FromBin: Record Bin;
                SerialNoInformation: Record "Serial No. Information";
                NavigationErr: Label 'Vehicle must go NAVIGASYON process. ';
                Navigation2Err: Label 'Vehicle must go PARK or HASAR.';
                CanNotReadTwiceErr: Label 'Serial No.: %1 is already read by %2 at %3.', Comment = '%1="Vehicle Transfer Line ERK"."Serial No."; %2="Vehicle Transfer Line ERK"."In-Transit By"; %3="Vehicle Transfer Line ERK"."In-Transit At"';
                ConfirmQst: Label 'Vehicle must go NAVIGASYON process but you are sending %1. Do you want to continue?', Comment = '%1 = To-Bin Code';
                ConfirmLTxt: Text;
            begin
                WarehouseEmployee.SetRange("User ID", UserId());
                WarehouseEmployee.FindFirst();
                if Rec."In-Transit" and (not WarehouseEmployee."Multiple Reading Allowed ERK") and (WarehouseEmployee."User ID" = Rec."In-Transit By") then
                    Error(CanNotReadTwiceErr, Rec."Serial No.", Rec."In-Transit By", Rec."In-Transit At");

                "Processed By" := CopyStr(UserId(), 1, MaxStrLen("Processed By"));
                "Processed At" := CurrentDateTime();
                VehicleTransferLine.TestField(Processed, false);
                VehicleTransferHeader.Get(Rec."Document No.");
                case VehicleTransferHeader."Operation Type" of
                    VehicleTransferHeader."Operation Type"::Discharge,
                    VehicleTransferHeader."Operation Type"::Loading,
                    VehicleTransferHeader."Operation Type"::Transfer,
                    VehicleTransferHeader."Operation Type"::Wash,
                    VehicleTransferHeader."Operation Type"::"PDI Entry",
                    VehicleTransferHeader."Operation Type"::"Dispatch Preparation":
                        begin
                            if Rec."To Location Code" = '' then
                                Rec.Validate("To Location Code", VehicleTransferHeader."To Location Code");
                            if Rec."To Bin Code" = '' then
                                Rec.Validate("To Bin Code", VehicleTransferHeader."To Bin Code");
                        end;
                    VehicleTransferHeader."Operation Type"::"PDI Exit":
                        begin
                            SerialNoInformation.SetRange("Serial No.", Rec."Serial No.");
                            if SerialNoInformation.FindFirst() then begin
                                ConfirmLTxt := StrSubstNo(ConfirmQst, Rec."To Bin Code");
                                if ("To Bin Code" = 'NAVIGASYON') and SerialNoInformation."Nav Upload Succesful ERK" then
                                    Error(Navigation2Err);
                                if not SerialNoInformation."Nav. Process Required ERK" and ("To Bin Code" = 'NAVIGASYON') then
                                    Error(Navigation2Err);
                                if SerialNoInformation."Nav. Process Required ERK" and ("To Bin Code" <> 'NAVIGASYON') and not SerialNoInformation."Nav Upload Succesful ERK" then
                                    if not ConfirmManagement.GetResponseOrDefault(ConfirmLTxt, false) then
                                        Error(NavigationErr);


                                // if (SerialNoInformation."Nav. Process Required ERK") and (("To Bin Code" <> 'NAVIGASYON') and not SerialNoInformation."Nav Upload Succesful ERK") and (SerialNoInformation."Current Bin Code ERK" = 'PDI') then
                                //     if not ConfirmManagement.GetResponseOrDefault(ConfirmLTxt, false) then
                                //         Error(NavigationErr);
                                // if (not SerialNoInformation."Nav. Process Required ERK") and (("To Bin Code" = 'NAVIGASYON') and SerialNoInformation."Nav Upload Succesful ERK") and (SerialNoInformation."Current Bin Code ERK" = 'PDI') then
                                //     Error(Navigation2Err);
                            end;
                            FromBin.Get(Rec."From Location Code", Rec."From Bin Code");
                            if (not FromBin."PDI Bin ERK") and FromBin."In-Transit Bin ERK" then begin
                                VehicleTransferLine.SetRange("Serial No.", Rec."Serial No.");
                                VehicleTransferLine.SetRange("Operation Type", VehicleTransferLine."Operation Type"::"Customs Exit");
                                VehicleTransferLine.SetRange("In-Transit", true);
                                VehicleTransferLine.SetRange(Processed, false);
                                VehicleTransferLine.FindLast();
                                VehicleTransferLine.Validate("Serial No. Reading", Rec."Serial No.");
                            end;
                        end;
                    VehicleTransferHeader."Operation Type"::"Dealer Dispatch",
                    VehicleTransferHeader."Operation Type"::"Grupage Dealer Dispatch":
                        begin
                            Rec.Validate("To Location Code", VehicleTransferHeader."To Location Code");
                            Rec.Validate("To Bin Code", VehicleTransferHeader."To Bin Code");
                            FromBin.Get(Rec."From Location Code", Rec."From Bin Code");
                            FromBin.TestField("Man Frm Bin Dealer Dispch. ERK", true);
                        end;
                end;
                VehicleTransferManagement.CheckUserVehicleLocation(Rec);
                VehicleTransferManagement.CreateVehicleTransferLedgerEntryFromVehicleTransferLine(Rec, Rec."To Bin Code", false);
                PDIManagement.CreatePDIDocument(Rec);
                VehicleTransferManagement.VehicleTransferMessageCheck(Rec);
            end;
        }
        field(9; "Processed By"; Code[50])
        {
            Caption = 'Processed By';
            Editable = false;
            ToolTip = 'Specifies the value of the Processed By field.';
        }
        field(10; "Processed At"; DateTime)
        {
            Caption = 'Processed At';
            Editable = false;
            ToolTip = 'Specifies the value of the Processed At field.';
        }
        field(11; "Vehicle Trnsfr Ledg. Entry No."; Integer)
        {
            Caption = 'Vehicle Transfer Ledger Entry No.';
            Editable = false;
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Vehicle Transfer Ledger Entry No. field.';
        }
        field(12; "In-Transit"; Boolean)
        {
            Caption = 'In-Transit';
            Editable = false;
            ToolTip = 'Specifies the value of the In-Transit field.';
            trigger OnValidate()
            begin
                VehicleTransferManagement.OnAfterValidaInTransitOnVehicleTransferLine(Rec);
            end;
        }
        field(13; "Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Operation Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Vehicle Transfer Header ERK"."Operation Type" where("No." = field("Document No.")));
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Operation Type field.';
        }
        field(14; TSE; Boolean)
        {
            Caption = 'TSE';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Serial No. Information"."TSE ERK" where("Serial No." = field("Serial No.")));
            ToolTip = 'Specifies the value of the TSE field.';
        }
        field(15; Comment; Text[500])
        {
            Caption = 'Comment';
            ToolTip = 'Specifies the value of the Comment field.';
        }
        field(16; "Header To Location Code"; Code[10])
        {
            Caption = 'Header To Location Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Vehicle Transfer Header ERK"."To Location Code" where("No." = field("Document No.")));
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Header To Location Code field.';
        }
        field(17; "Header To Bin Code"; Code[20])
        {
            Caption = 'Header To Bin Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Vehicle Transfer Header ERK"."To Bin Code" where("No." = field("Document No.")));
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Header To Bin Code field.';
        }
        field(18; "Serial No. Reading"; Code[50])
        {
            Caption = 'Serial No. Reading';
            ToolTip = 'Specifies the value of the Serial No. Reading field.';
            trigger OnValidate()
            var
                SerialNoInformation: Record "Serial No. Information";
                VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                SerialNoFilter: Text;
            begin
                if Rec."Serial No. Reading" = '' then
                    exit;
                SerialNoFilter := '*' + Rec."Serial No. Reading";
                SerialNoInformation.SetFilter("Serial No.", SerialNoFilter);
                SerialNoInformation.FindFirst();
                Rec.TestField("Serial No.", SerialNoInformation."Serial No.");
                VehicleTransferHeader.Get(Rec."Document No.");
                VehicleTransferHeader.Validate("Serial No.", SerialNoInformation."Serial No.");
                Rec."Serial No. Reading" := '';
            end;
        }
        field(19; "In-Transit By"; Code[50])
        {
            Caption = 'In-Transit By';
            ToolTip = 'Specifies the value of the In-Transit By field.';
        }
        field(20; "In-Transit At"; DateTime)
        {
            Caption = 'In-Transit At';
            ToolTip = 'Specifies the value of the In-Transit At field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(SK; "Serial No.", Processed)
        {
        }
    }
    trigger OnInsert()
    var
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    begin
        VehicleTransferLine.SetRange("Document No.", Rec."Document No.");
        if VehicleTransferLine.FindLast() then
            Rec."Line No." := VehicleTransferLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
        VehicleTransferHeader.Get(Rec."Document No.");
        VehicleTransferHeader.TestField("Lines Locked", false);
        // VehicleTransferHeader.TestField("To Location Code");
        // VehicleTransferHeader.TestField("To Bin Code");
    end;
    // trigger OnModify()
    // var
    //     VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    //     SerialNoAlreadyExistErr: Label 'Serial No. already exists in the document.';
    // begin
    //     VehicleTransferLine.SetRange("Document No.", Rec."Document No.");
    //     VehicleTransferLine.SetRange("Serial No.", Rec."Serial No.");
    //     if not VehicleTransferLine.IsEmpty then
    //         Error(SerialNoAlreadyExistErr);
    // end;
    trigger OnDelete()
    var
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
    begin
        TestField("In-Transit", false);
        TestField(Processed, false);
        VehicleTransferHeader.Get(Rec."Document No.");
        VehicleTransferHeader.TestField("Lines Locked", false);
    end;

    var
        ConfirmManagement: Codeunit "Confirm Management";
        PDIManagement: Codeunit "PDI Management ERK";
        VehicleTransferManagement: Codeunit "Vehicle Transfer Management";
}
