#pragma warning disable LC0062
page 60109 "VATBusPostingGrAPI ERK"
#pragma warning restore LC0062
{
    PageType = API;
    Caption = 'VAT Business Posting Group API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'vatBusinessPostingGr';
    EntitySetName = 'vatBusinessPostingGr';
    SourceTable = "VAT Business Posting Group";
    DelayedInsert = true;
    ODataKeyFields = SystemId;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(id; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(code; Rec.Code)
                {
                    Caption = 'Code';
                }
                field(description; Rec.Description)
                {
                    Caption = 'Description';
                }
                field(lastModifiedDateTime; Rec."Last Modified Date Time")
                {
                    Caption = 'Last Modified Date Time';
                }
            }
        }
    }
}
