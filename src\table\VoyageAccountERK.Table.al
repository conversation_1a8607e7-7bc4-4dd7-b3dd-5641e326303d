table 60017 "Voyage Account ERK"
{
    Caption = 'Voyage Account';
    DataClassification = CustomerContent;
    LookupPageId = "Voyage Account List ERK";
    DrillDownPageId = "Voyage Account List ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            NotBlank = false;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                //NoSeries: Codeunit NoSeriesManagement;
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Voyage Account No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; Name; Text[100])
        {
            Caption = 'Name';
            ToolTip = 'Specifies the value of the Name field.';
        }
        field(3; "Name 2"; Text[50])
        {
            Caption = 'Name 2';
            ToolTip = 'Specifies the value of the Name 2 field.';
        }
        field(4; Address; Text[100])
        {
            Caption = 'Address';
            ToolTip = 'Specifies the value of the Address field.';
        }
        field(5; "Address 2"; Text[50])
        {
            Caption = 'Address 2';
            ToolTip = 'Specifies the value of the Address 2 field.';
        }
        field(6; "Post Code"; Code[20])
        {
            Caption = 'Post Code';
            TableRelation = "Post Code".Code;
            ToolTip = 'Specifies the value of the Post Code field.';
        }
        field(7; City; Text[30])
        {
            Caption = 'City';
            TableRelation = "Post Code".City;
            ToolTip = 'Specifies the value of the City field.';
        }
        field(8; "Country/Region Code"; Code[10])
        {
            Caption = 'Country/Region Code';
            TableRelation = "Country/Region".Code;
            ToolTip = 'Specifies the value of the Country/Region Code field.';
        }
        field(9; "Phone No."; Text[30])
        {
            Caption = 'Phone No.';
            ToolTip = 'Specifies the value of the Phone No. field.';
        }
        field(10; "Car Carrier Related"; Boolean)
        {
            Caption = 'Car Carrier Related';
            ToolTip = 'Specifies the value of the Car Carrier Related field.';
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        //NoSeriesManagement: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Voyage Account No. Series");
            //NoSeriesManagement.InitSeries(ErkHoldingSetup."Voyage Account No. Series", xRec."No. Series", 0D, "No.", "No. Series");
            "No. Series" := ErkHoldingSetup."Voyage Account No. Series";
            if NoSeries.AreRelated(ErkHoldingSetup."Voyage Account No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
}
