reportextension 60001 "Order ERK" extends "Order"
{
    dataset
    {
        add("Purchase Header")
        {
            column(RemittoNameERK; ErkHoldingBasicFunctions.GetRemittoNameFromRemittoCode("Purchase Header"."Buy-from Vendor No.", CopyStr("Purchase Header"."Remit-to Code", 1, 10)))
            {
            }
            column(RequestedReceiptDateText_PurchaseHeaderERK; "Requested Receipt Text ERK")
            {
            }
        }
        add(RoundLoop)
        {
            column(BrandCodeERK_PurchaseLineERK; ExportManagement.GetBrandFromItemAndVariantCode("Purchase Line"."No.", "Purchase Line"."Variant Code"))
            {
            }
            column(SpecialFeatureCodeERK_PurchaseLineERK; ExportManagement.GetSpecificationFromItemAndVariantCode("Purchase Line"."No.", "Purchase Line"."Variant Code"))
            {
            }
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        ExportManagement: Codeunit "Export Management ERK";
}
