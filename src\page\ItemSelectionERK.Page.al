page 60014 "Item Selection ERK"
{
    ApplicationArea = All;
    Caption = 'Item Selection';
    PageType = List;
    SourceTable = "Export Line ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Load Quantity"; Rec."Load Quantity")
                {
                }
                field(Brand; Rec.<PERSON>)
                {
                }
            }
        }
    }
}
