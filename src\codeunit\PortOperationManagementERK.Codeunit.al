codeunit 60004 "Port Operation Management ERK"
{
    Access = Internal;

    procedure CreateNewSalesInvoiceFromSelectedLines(var PortOperationLineDetail: Record "Port Operation Line Detail ERK")
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        SalesCommentLine: Record "Sales Comment Line";
        AlreadyInInvoiceErr: Label 'Line No.: %1 is already in Sales Invoice No.: %2', Comment = '%1="Port Operation Line Detail ERK"."Line No."; %2="Port Operation Line Detail ERK"."Invoice No."';
        DifferentCurrencyErr: Label 'Currency codes must be same for selected all lines.';
        //UnitPriceErr: Label 'Unit price can not be zero.';
        SalesLineLineNo: Integer;
        SalesCommentLineLineNo: Integer;
    begin
        PortOperationLineDetail.FindSet(false);
        repeat
            if PortOperationLineDetail."Invoice No." <> '' then
                Error(AlreadyInInvoiceErr, PortOperationLineDetail."Line No.", PortOperationLineDetail."Invoice No.");
        until PortOperationLineDetail.Next() = 0;
        PortOperationLineDetail.SetFilter("Currency Code", '<>%1', PortOperationLineDetail."Currency Code");
        if not PortOperationLineDetail.IsEmpty() then
            Error(DifferentCurrencyErr);
        PortOperationLineDetail.SetRange("Currency Code");
        PortOperationLineDetail.FindSet(false);
        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", PortOperationLineDetail."Source No.");
        SalesHeader.Validate("Posting Date", PortOperationLineDetail."Posting Date");
        SalesHeader.Validate("Currency Code", PortOperationLineDetail."Currency Code");
        SalesHeader.Modify(true);
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        if SalesLine.FindLast() then
            SalesLineLineNo := SalesLine."Line No." + 10000
        else
            SalesLineLineNo := 10000;
        SalesCommentLineLineNo := 10000;
        repeat
            PortOperationLineDetail.TestField(Type, PortOperationLineDetail.Type::Revenue);
            if PortOperationLineDetail."Unit Price" = 0 then
                if PortOperationLineDetail."Invoice Comment" <> '' then begin
                    SalesCommentLine.Init();
                    SalesCommentLine."Document Type" := SalesHeader."Document Type";
                    SalesCommentLine."No." := SalesHeader."No.";
                    SalesCommentLine."Line No." := SalesCommentLineLineNo;
                    SalesCommentLine.Comment := PortOperationLineDetail."Invoice Comment";
                    SalesCommentLine."Transfer to E-Invoice INF" := true;
                    SalesCommentLine.Insert(true);
                    SalesCommentLineLineNo += 10000;
                end;
            Item.Get(PortOperationLineDetail."No.");
            if Item.IsVariantMandatory() then
                PortOperationLineDetail.TestField("Variant Code");
            SalesLine.Init();
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Line No." := SalesLineLineNo;
            SalesLine.Insert(true);
            SalesLine.Validate(Type, SalesLine.Type::Item);
            SalesLine.Validate("No.", PortOperationLineDetail."No.");
            SalesLine.Validate("Variant Code", PortOperationLineDetail."Variant Code");
            SalesLine.Validate(Quantity, PortOperationLineDetail.Quantity);
            SalesLine.Validate("VAT Prod. Posting Group", PortOperationLineDetail."VAT Prod. Posting Group");
            SalesLine.Validate("VAT Wthld Prod Posting Grp INF", PortOperationLineDetail."VAT With. Prod. Posting Group");
            SalesLine.Validate("Unit of Measure Code", PortOperationLineDetail."Unit of Measure Code");
            SalesLine.Validate("Unit Price", PortOperationLineDetail."Unit Price");
            if PortOperationLineDetail."Duration (Days)" > 0 then
                SalesLine.Validate("Unit Price", SalesLine."Unit Price" * PortOperationLineDetail."Duration (Days)");
            SalesLine.Validate("Shortcut Dimension 1 Code", PortOperationLineDetail."Shortcut Dimension 1 Code");
            VoyageMangement.CreateDimensionValueCode(PortOperationLineDetail."Document No.");
            SalesLine.Validate("Shortcut Dimension 2 Code", PortOperationLineDetail."Document No.");
            SalesLine.Validate("Invoice Line Combine Type INF", SalesHeader."DefInvoiceLineCombineType INF");
            if PortOperationLineDetail."No." = 'HZMT0017' then begin
                SalesLine.Validate("Load Type ERK", PortOperationLineDetail."Parent Load Type");
                SalesLine.Validate("Vessel Load/Unload Type ERK", PortOperationLineDetail."Variant Code");
            end;
            SalesLine.Modify(true);
            PortOperationLineDetail."Invoice No." := SalesHeader."No.";
            PortOperationLineDetail.Modify(false);
            SalesLineLineNo += 10000;
        until PortOperationLineDetail.Next() = 0;
        PageManagement.PageRun(SalesHeader);
    end;

    local procedure ClearSalesInvoiceFieldsOnPortOperationLineDetail(SalesLine: Record "Sales Line")
    var
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
    begin
        PortOperationLineDetail.SetRange("Invoice No.", SalesLine."Document No.");
        if not PortOperationLineDetail.FindFirst() then
            exit;
        PortOperationLineDetail."Invoice No." := '';
        PortOperationLineDetail.Modify(false);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure OnDeleteOnBeforeTestStatusOpen(var SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    begin
        ClearSalesInvoiceFieldsOnPortOperationLineDetail(SalesLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting(var SalesHeader: Record "Sales Header"; var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; var SkipDelete: Boolean; CommitIsSuppressed: Boolean; EverythingInvoiced: Boolean; var TempSalesLineGlobal: Record "Sales Line" temporary)
    var
        SalesLine: Record "Sales Line";
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if not SalesLine.FindSet(false) then
            exit;
        repeat
            PortOperationLineDetail.SetRange("Invoice No.", SalesLine."Document No.");
            if PortOperationLineDetail.FindFirst() then begin
                PortOperationLineDetail."Invoice No." := SalesInvoiceHeader."No.";
                PortOperationLineDetail.Modify(false);
            end;
        until SalesLine.Next() = 0;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting_PurchasePost(var PurchaseHeader: Record "Purchase Header"; var PurchInvHeader: Record "Purch. Inv. Header"; var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var SkipDelete: Boolean; CommitIsSupressed: Boolean; var TempPurchLine: Record "Purchase Line" temporary; var TempPurchLineGlobal: Record "Purchase Line" temporary; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    var
        PurchaseLine: Record "Purchase Line";
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetRange(Type, PurchaseLine.Type::Item);
        if not PurchaseLine.FindSet(false) then
            exit;
        repeat
            PortOperationLineDetail.SetRange("Invoice No.", PurchaseLine."Document No.");
            //PortOperationLineDetail.SetRange("Purchase Invoice Line No.", PurchaseLine."Line No.");
            if PortOperationLineDetail.FindFirst() then begin
                PortOperationLineDetail."Invoice No." := PurchInvHeader."No.";
                PortOperationLineDetail."External Document No." := PurchInvHeader."Vendor Invoice No.";
                PortOperationLineDetail.Modify(false);
            end;
        until PurchaseLine.Next() = 0;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure OnDeleteOnBeforeTestStatusOpen_PurchaseLine(var PurchaseLine: Record "Purchase Line"; var IsHandled: Boolean)
    begin
        ClearPurchaseInvoiceFieldsOnPortOperationLineDetail(PurchaseLine)
    end;

    local procedure ClearPurchaseInvoiceFieldsOnPortOperationLineDetail(PurchaseLine: Record "Purchase Line")
    var
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
    begin
        PortOperationLineDetail.SetRange("Invoice No.", PurchaseLine."Document No.");
        //PortOperationLineDetail.SetRange("Purchase Invoice Line No.", PurchaseLine."Line No.");
        if not PortOperationLineDetail.FindFirst() then
            exit;
        PortOperationLineDetail."Invoice No." := '';
        //PortOperationLineDetail."Purchase Invoice Line No." := 0;
        PortOperationLineDetail.Modify(false);
    end;

    procedure CreatePurchaseInvoiceFromPortOperationLineDetails(var PortOperationLineDetail: Record "Port Operation Line Detail ERK")
    var
        DifferentCurrencyErr: Label 'There is different currency selected that Currency Code: %1. All selected lines must be in same currency.', Comment = '%1="Port Operation Line Detail ERK"."Currency Code"';
        DifferentVendorErr: Label 'There is different vendor selected than Vendor No: %1. All selected lines must be from same vendor.', Comment = '%1="Port Operation Line Detail ERK"."Source No."';
    begin
        PortOperationLineDetail.FindFirst();
        PortOperationLineDetail.SetFilter("Source No.", '<>%1', PortOperationLineDetail."Source No.");
        if not PortOperationLineDetail.IsEmpty() then
            Error(DifferentVendorErr, PortOperationLineDetail."Source No.");
        PortOperationLineDetail.SetRange("Source No.");
        PortOperationLineDetail.FindFirst();
        PortOperationLineDetail.SetFilter("Currency Code", '<>%1', PortOperationLineDetail."Currency Code");
        if not PortOperationLineDetail.IsEmpty() then
            Error(DifferentCurrencyErr, PortOperationLineDetail."Currency Code");
        PortOperationLineDetail.SetRange("Currency Code");
        CreatePurchaseHeaderAndLines(PortOperationLineDetail);
    end;

    local procedure CreatePurchaseHeaderAndLines(var PortOperationLineDetail: Record "Port Operation Line Detail ERK")
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        PurchaseLineLineNo: Integer;
    begin
        PortOperationLineDetail.FindSet(true);
        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Invoice;
        PurchaseHeader.Insert(true);
        PurchaseHeader.Validate("Buy-from Vendor No.", PortOperationLineDetail."Source No.");
        PurchaseHeader.Validate("Posting Date", PortOperationLineDetail."Posting Date");
        PurchaseHeader.Validate("Currency Code", PortOperationLineDetail."Currency Code");
        PurchaseHeader.Validate("Vendor Invoice No.", PortOperationLineDetail."External Document No.");
        PurchaseHeader.Modify(true);
        PurchaseLineLineNo := 10000;
        repeat
            PortOperationLineDetail.TestField("Invoice No.", '');
            PortOperationLineDetail.TestField(Type, PortOperationLineDetail.Type::Expense);
            PortOperationLineDetail.TestField("External Document No.");
            Item.Get(PortOperationLineDetail."No.");
            if Item.IsVariantMandatory() then
                PortOperationLineDetail.TestField("Variant Code");
            PurchaseLine.Init();
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Line No." := PurchaseLineLineNo;
            PurchaseLine.Insert(true);
            PurchaseLine.Validate(Type, PurchaseLine.Type::Item);
            PurchaseLine.Validate("No.", PortOperationLineDetail."No.");
            PurchaseLine.Validate("Variant Code", PortOperationLineDetail."Variant Code");
            PurchaseLine.Validate(Description, PortOperationLineDetail.Description);
            PurchaseLine.Validate(Quantity, PortOperationLineDetail.Quantity);
            PurchaseLine.Validate("VAT Prod. Posting Group", PortOperationLineDetail."VAT Prod. Posting Group");
            PurchaseLine.Validate("VAT Wthld Prod Posting Grp INF", PortOperationLineDetail."VAT With. Prod. Posting Group");
            PurchaseLine.Validate("Unit of Measure Code", PortOperationLineDetail."Unit of Measure Code");
            // PurchaseLine.Validate("Location Code", PortOperationLineDetail."Location Code");
            // PurchaseLine.Validate("Bin Code", PortOperationLineDetail."Bin Code");
            PurchaseLine.Validate("Shortcut Dimension 1 Code", PortOperationLineDetail."Shortcut Dimension 1 Code");
            VoyageMangement.CreateDimensionValueCode(PortOperationLineDetail."Document No.");
            PurchaseLine.Validate("Shortcut Dimension 2 Code", PortOperationLineDetail."Document No.");
            PurchaseLine.Validate("Direct Unit Cost", PortOperationLineDetail."Unit Price");
            PurchaseLine.Modify(true);
            PurchaseLineLineNo += 10000;
            PortOperationLineDetail."Invoice No." := PurchaseLine."Document No.";
            PortOperationLineDetail.Modify(false);
        until PortOperationLineDetail.Next() = 0;
        PageManagement.PageRun(PurchaseHeader);
    end;

    procedure CreatePortOperationLineDetailForLoadTransfer(var TempLoadTranser: Record "Load Transer ERK" temporary)
    var
        ToPortOperationLineDetail: Record "Port Operation Line Detail ERK";
        FromPortOperationLineDetail: Record "Port Operation Line Detail ERK";
        FromPortOperationLine: Record "Port Operation Line ERK";
        ToPortOperationLine: Record "Port Operation Line ERK";
        ChangeCustomerErr: Label 'You need to change at least one customer.';
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Load Item No.");
        FromPortOperationLine.Get(TempLoadTranser."Operation No.", TempLoadTranser."From Operation Line No.");
        FromPortOperationLineDetail.Get(TempLoadTranser."Operation No.", TempLoadTranser."From Operation Line No.", TempLoadTranser."From Prt Opr Line Dtl Line No.");
        FromPortOperationLine.CalcFields("Bill-to Customer Name");
        if (TempLoadTranser."From Load Owner No." = TempLoadTranser."To Load Owner No.") and (TempLoadTranser."From Bill-to Customer No." = TempLoadTranser."To Bill-to Customer No.") then
            Error(ChangeCustomerErr)
        else begin
            //create new port operation line
            ToPortOperationLine.SetRange("Bill-to Customer No.", TempLoadTranser."To Bill-to Customer No.");
            ToPortOperationLine.SetRange("Load Owner No.", TempLoadTranser."To Load Owner No.");
            ToPortOperationLine.SetRange("Variant Code", FromPortOperationLine."Variant Code");
            if not ToPortOperationLine.FindFirst() then begin
                ToPortOperationLine.Init();
                ToPortOperationLine."Document No." := TempLoadTranser."Operation No.";
                ToPortOperationLine.Insert(true);
                ToPortOperationLine.Validate("Bill-to Customer No.", TempLoadTranser."To Bill-to Customer No.");
                ToPortOperationLine.Validate("Parent Load Type", FromPortOperationLine."Parent Load Type");
                ToPortOperationLine.Validate("Sub Load Type", FromPortOperationLine."Sub Load Type");
                ToPortOperationLine.Validate("Variant Code", FromPortOperationLine."Variant Code");
                ToPortOperationLine.Validate("Load Owner No.", TempLoadTranser."To Load Owner No.");
                ToPortOperationLine.Validate("Shortcut Dimension 1 Code", FromPortOperationLine."Shortcut Dimension 1 Code");
                ToPortOperationLine.Validate("Contract No.", TempLoadTranser."To Contract No.");
                ToPortOperationLine.Modify(true);
            end;
            ToPortOperationLineDetail.Init();
            ToPortOperationLineDetail."Document No." := TempLoadTranser."Operation No.";
            ToPortOperationLineDetail."Document Line No." := ToPortOperationLine."Line No.";
            ToPortOperationLineDetail.Insert(true);
            ToPortOperationLineDetail.Validate(Type, ToPortOperationLineDetail.Type::"Load Transfer");
            ToPortOperationLineDetail.Validate("No.", ErkHoldingSetup."Load Item No.");
            ToPortOperationLineDetail.Validate("Variant Code", TempLoadTranser."Variant Code");
            ToPortOperationLineDetail.Validate("Shortcut Dimension 1 Code", FromPortOperationLineDetail."Shortcut Dimension 1 Code");
            ToPortOperationLineDetail.Validate("Location Code", TempLoadTranser."To Location Code");
            ToPortOperationLineDetail.Validate("Bin Code", TempLoadTranser."To Bin Code");
            ToPortOperationLineDetail.Validate(Quantity, TempLoadTranser.Quantity);
            ToPortOperationLineDetail.Validate("Unit of Measure Code", FromPortOperationLineDetail."Unit of Measure Code");
            ToPortOperationLineDetail.Validate("Source No.", TempLoadTranser."To Bill-to Customer No.");
            ToPortOperationLineDetail.Validate("Transfer Customer Name", FromPortOperationLine."Bill-to Customer Name");
            ToPortOperationLineDetail.Validate("Operation Date", FromPortOperationLineDetail."Operation Date");
            ToPortOperationLineDetail.Modify(true);
        end;
        TempLoadTranser.CalcFields("To Load Owner Name");
        FromPortOperationLineDetail.Validate("Transfer Customer Name", TempLoadTranser."To Load Owner Name");
        FromPortOperationLineDetail.Modify(true);
    end;

    procedure PopulateLoadTransferPopUpFromPortOperationLineDetail(PortOperationLineDetail: Record "Port Operation Line Detail ERK")
    var
        TempLoadTranser: Record "Load Transer ERK" temporary;
        FromPortOperationLine: Record "Port Operation Line ERK";
        //LotNoInformation: Record "Lot No. Information";
        QtyMustBeNegativeErr: Label 'Quantity must be negative.';
    begin
        //LotNoInformation.Get(PortOperationLineDetail."No.", PortOperationLineDetail."Variant Code", FindOrCreateLotNoInformationForPortOperationLineDetail(PortOperationLineDetail));
        if PortOperationLineDetail.Quantity >= 0 then
            Error(QtyMustBeNegativeErr);
        //PortOperationLineDetail.TestField("Posting Date");
        PortOperationLineDetail.TestField("No.");
        PortOperationLineDetail.TestField("Variant Code");
        PortOperationLineDetail.TestField("Shortcut Dimension 1 Code");
        PortOperationLineDetail.TestField("Location Code");
        PortOperationLineDetail.TestField("Unit of Measure Code");
        FromPortOperationLine.Get(PortOperationLineDetail."Document No.", PortOperationLineDetail."Document Line No.");
        TempLoadTranser.Init();
        TempLoadTranser."Operation No." := PortOperationLineDetail."Document No.";
        TempLoadTranser."From Operation Line No." := PortOperationLineDetail."Document Line No.";
        TempLoadTranser."From Location Code" := PortOperationLineDetail."Location Code";
        TempLoadTranser."From Bin Code" := PortOperationLineDetail."Bin Code";
        //TempLoadTranser."From Bill-to Customer No." := PortOperationLineDetail."Bill-to Customer No.";
        TempLoadTranser."From Load Owner No." := FromPortOperationLine."Bill-to Customer No.";
        TempLoadTranser.Quantity := -PortOperationLineDetail.Quantity;
        TempLoadTranser."Variant Code" := PortOperationLineDetail."Variant Code";
        TempLoadTranser."From Prt Opr Line Dtl Line No." := PortOperationLineDetail."Line No.";
        TempLoadTranser."Posting Date" := PortOperationLineDetail."Posting Date";
        TempLoadTranser."To Location Code" := TempLoadTranser."From Location Code";
        TempLoadTranser."To Bin Code" := TempLoadTranser."From Bin Code";
        //TempLoadTranser."To Load Owner No." := TempLoadTranser."From Load Owner No.";
        TempLoadTranser."To Contract No." := FromPortOperationLine."Contract No.";
        TempLoadTranser.Insert(true);
        Page.Run(Page::"Load Transfer ERK", TempLoadTranser);
    end;

    procedure UpdateCommonOperationField(PortOperationLineDetail: Record "Port Operation Line Detail ERK")
    var
        PortOperationHeader: Record "Port Operation Header ERK";
        PortOperationLineDetail2: Record "Port Operation Line Detail ERK";
        GeneralLedgerSetup: Record "General Ledger Setup";
        DimensionValue: Record "Dimension Value";
    begin
        PortOperationHeader.Get(PortOperationLineDetail."Document No.");
        GeneralLedgerSetup.GetRecordOnce();
        PortOperationLineDetail2.SetRange("Document No.", PortOperationLineDetail."Document No.");
        PortOperationLineDetail2.SetFilter("Line No.", '<>%1', PortOperationLineDetail."Line No.");
        PortOperationLineDetail2.SetFilter("Shortcut Dimension 1 Code", '<>%1', PortOperationLineDetail."Shortcut Dimension 1 Code");
        if PortOperationLineDetail2.IsEmpty() then begin
            PortOperationHeader."Common Operation" := false;
            DimensionValue.Get(GeneralLedgerSetup."Global Dimension 1 Code", PortOperationLineDetail."Shortcut Dimension 1 Code");
            PortOperationHeader."Deparment Name" := DimensionValue.Name;
        end
        else begin
            PortOperationHeader."Common Operation" := true;
            PortOperationHeader."Deparment Name" := 'ORTAK';
        end;
        PortOperationHeader.Modify(true);
    end;

    procedure CreateAndPostConsumptionInvoice(var PortOperationLineDetail: Record "Port Operation Line Detail ERK")
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        SalesLineLineNo: Integer;
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Consumption Customer No.");
        PortOperationLineDetail.FindSet(true);
        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", ErkHoldingSetup."Consumption Customer No.");
        SalesHeader.Validate("Posting Date", PortOperationLineDetail."Posting Date");
        SalesHeader.Validate("External Document No.", SalesHeader."No.");
        SalesHeader.Modify(true);
        SalesLineLineNo := 10000;
        repeat
            PortOperationLineDetail.TestField("Invoice No.", '');
            PortOperationLineDetail.TestField(Type, PortOperationLineDetail.Type::Consumption);
            PortOperationLineDetail.TestField("Unit Price", 0);
            // PortOperationLineDetail.TestField("Consumption Location Code");
            // PortOperationLineDetail.TestField("Consumption Bin Code");
            Item.Get(PortOperationLineDetail."No.");
            if Item.IsVariantMandatory() then
                PortOperationLineDetail.TestField("Variant Code");
            SalesLine.Init();
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Line No." := SalesLineLineNo;
            SalesLine.Insert(true);
            SalesLine.Validate(Type, SalesLine.Type::Item);
            SalesLine.Validate("No.", PortOperationLineDetail."No.");
            SalesLine.Validate("Variant Code", PortOperationLineDetail."Variant Code");
            if PortOperationLineDetail."Consumption Location Code" = '' then begin
                SalesLine.Validate("Location Code", PortOperationLineDetail."Location Code");
                SalesLine.Validate("Bin Code", PortOperationLineDetail."Bin Code");
            end
            else begin
                SalesLine.Validate("Location Code", PortOperationLineDetail."Consumption Location Code");
                SalesLine.Validate("Bin Code", PortOperationLineDetail."Consumption Bin Code");
            end;
            SalesLine.Validate(Quantity, PortOperationLineDetail.Quantity);
            SalesLine.Validate("VAT Prod. Posting Group", PortOperationLineDetail."VAT Prod. Posting Group");
            SalesLine.Validate("Unit of Measure Code", PortOperationLineDetail."Unit of Measure Code");
            SalesLine.Validate("Shortcut Dimension 1 Code", PortOperationLineDetail."Shortcut Dimension 1 Code");
            VoyageMangement.CreateDimensionValueCode(PortOperationLineDetail."Document No.");
            SalesLine.Validate("Shortcut Dimension 2 Code", PortOperationLineDetail."Document No.");
            SalesLine.Validate("Invoice Line Combine Type INF", SalesHeader."DefInvoiceLineCombineType INF");
            SalesLine.Modify(true);
            PortOperationLineDetail."Invoice No." := SalesHeader."No.";
            PortOperationLineDetail.Modify(false);
            SalesLineLineNo += 10000;
        until PortOperationLineDetail.Next() = 0;
        //Page.Run(Page::"Sales Invoice", SalesHeader);
        PageManagement.PageRun(SalesHeader);
    end;

    procedure CreateNewRevenueLineFromPortOperationLineDetail(FromPortOperationLineDetail: Record "Port Operation Line Detail ERK")
    var
        PortOperationLine: Record "Port Operation Line ERK";
        ToPortOperationLineDetail: Record "Port Operation Line Detail ERK";
    begin
        FromPortOperationLineDetail.TestField("Next Invoice Date");
        FromPortOperationLineDetail.TestField(Type, FromPortOperationLineDetail.Type::Revenue);

        PortOperationLine.Get(FromPortOperationLineDetail."Document No.", FromPortOperationLineDetail."Document Line No.");
        PortOperationLine.CalcFields("Remaining Quantity");
        if PortOperationLine."Remaining Quantity" = 0 then
            exit;

        ToPortOperationLineDetail.Init();
        ToPortOperationLineDetail.TransferFields(FromPortOperationLineDetail);
        ToPortOperationLineDetail."Line No." := 0;
        ToPortOperationLineDetail.Insert(true);
        ToPortOperationLineDetail.Validate(Quantity, PortOperationLine."Remaining Quantity");
        ToPortOperationLineDetail.Validate("Posting Date", FromPortOperationLineDetail."Next Invoice Date");
        ToPortOperationLineDetail.Validate("Next Invoice Period");
        ToPortOperationLineDetail.Modify(true);
    end;

    procedure SetDefaultConsuptionInformationForLogistics(var PortOperationLineDetail: Record "Port Operation Line Detail ERK")
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Logistics Dept. Value Code");
        ErkHoldingSetup.TestField("Def. Cons. Loc. for Logistics");
        ErkHoldingSetup.TestField("Def. Cons. Bin for Logistics");
        // if PortOperationLineDetail."Shortcut Dimension 1 Code" <> ErkHoldingSetup."Logistics Dept. Value Code" then
        //     exit;
        if (PortOperationLineDetail.Type <> PortOperationLineDetail.Type::Consumption) or (PortOperationLineDetail."Shortcut Dimension 1 Code" <> ErkHoldingSetup."Logistics Dept. Value Code") then begin
            PortOperationLineDetail.Validate("Consumption Location Code", '');
            PortOperationLineDetail.Validate("Consumption Bin Code", '');
        end
        else begin
            PortOperationLineDetail.Validate("Consumption Location Code", ErkHoldingSetup."Def. Cons. Loc. for Logistics");
            PortOperationLineDetail.Validate("Consumption Bin Code", ErkHoldingSetup."Def. Cons. Bin for Logistics");
        end;
    end;

    procedure CombineInvoiceLines(SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        SalesLine2: Record "Sales Line";
        RepresentativeSalesLine: Record "Sales Line";
        ItemVariant: Record "Item Variant";
        NoItemLinesToCombineErr: Label 'There is no item line to combine.';
    //MissingRepresantativeLineErr: Label 'There is no representative line to combine for Load Type: %1 and Vessel Load/Unload Type: %2.', Comment = '%1="Sales Line"."Load Type ERK"; %2="Sales Line"."Vessel Load/Unload Type ERK"';
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if not SalesLine.FindSet(true) then
            Error(NoItemLinesToCombineErr);
        repeat
            RepresentativeSalesLine.SetRange("Document Type", SalesHeader."Document Type");
            RepresentativeSalesLine.SetRange("Document No.", SalesHeader."No.");
            RepresentativeSalesLine.SetRange(Type, RepresentativeSalesLine.Type::"Representative COI INF");
            //RepresentativeSalesLine.SetRange("Load Type ERK", SalesLine."Load Type ERK");
            RepresentativeSalesLine.SetRange("Vessel Load/Unload Type ERK", SalesLine."Vessel Load/Unload Type ERK");
            if not RepresentativeSalesLine.FindFirst() then begin
                RepresentativeSalesLine.Init();
                RepresentativeSalesLine."Document Type" := SalesHeader."Document Type";
                RepresentativeSalesLine."Document No." := SalesHeader."No.";
                SalesLine2.SetRange("Document Type", SalesHeader."Document Type");
                SalesLine2.SetRange("Document No.", SalesHeader."No.");
                if SalesLine2.FindLast() then
                    RepresentativeSalesLine."Line No." := SalesLine2."Line No." + 10000
                else
                    RepresentativeSalesLine."Line No." := 10000;
                RepresentativeSalesLine.Insert(true);
                ItemVariant.Get(SalesLine."No.", SalesLine."Variant Code");
                RepresentativeSalesLine.Validate(Type, RepresentativeSalesLine.Type::"Representative COI INF");
                RepresentativeSalesLine.Validate("Load Type ERK", SalesLine."Load Type ERK");
                RepresentativeSalesLine.Validate("Vessel Load/Unload Type ERK", SalesLine."Vessel Load/Unload Type ERK");
                RepresentativeSalesLine.Validate(Description, ItemVariant.Description);
            end;
            //Error(MissingRepresantativeLineErr, SalesLine."Load Type ERK", SalesLine."Vessel Load/Unload Type ERK");
            if RepresentativeSalesLine."Unit of Measure Code" = '' then
                RepresentativeSalesLine.Validate("Unit of Measure Code", SalesLine."Unit of Measure Code");
            if RepresentativeSalesLine."Unit Price" = 0 then
                RepresentativeSalesLine.Validate("Unit Price", SalesLine."Unit Price");
            if RepresentativeSalesLine."Gen. Prod. Posting Group" = '' then
                RepresentativeSalesLine.Validate("Gen. Prod. Posting Group", SalesLine."Gen. Prod. Posting Group");
            RepresentativeSalesLine.Modify(true);
            SalesLine.Validate("Invoice Line Combine Type INF", SalesLine."Invoice Line Combine Type INF"::"Add To Representative Line (Add Quantity and Amount)");
            SalesLine.Validate("Add This Line To Line INF", RepresentativeSalesLine."Line No.");
            SalesLine.Modify(true);
        until SalesLine.Next() = 0;
    end;
    //     procedure CalculateRevenueACYFromPortOperationLine(PortOperationLine: Record "Port Operation Line ERK"): Decimal
    //     var
    //         PortOperationLineDetail: Record "Port Operation Line Detail ERK";
    //         RevenueACY: Decimal;
    //     begin
    //         PortOperationLineDetail.SetRange("Document No.", PortOperationLine."Document No.");
    //         PortOperationLineDetail.SetRange("Document Line No.", PortOperationLine."Line No.");
    //         PortOperationLineDetail.SetRange(Type, PortOperationLineDetail.Type::Revenue);
    //         if not PortOperationLineDetail.FindSet(false) then
    //             exit;
    //         repeat
    // RevenueACY +=
    //         until PortOperationLineDetail.Next() = 0;
    //     end;
    var
        Item: Record Item;
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        PageManagement: Codeunit "Page Management";
}
