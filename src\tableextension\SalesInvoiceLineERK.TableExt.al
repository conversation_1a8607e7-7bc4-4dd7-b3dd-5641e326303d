tableextension 60023 "Sales Invoice Line ERK" extends "Sales Invoice Line"
{
    fields
    {
        field(60000; "Sell-to Customer Name ERK"; Text[100])
        {
            Caption = 'Sell-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Sell-to Customer Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Sell-to Customer Name field.';
        }
        field(60002; "Distributed Quantity ERK"; Integer)
        {
            Caption = 'Distributed Quantity';
            ToolTip = 'Specifies the quantity of distributed vehicles.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Serial No. Revenue/Expense ERK" where("Posted Invoice No." = field("Document No."), "Invoice Line No." = field("Line No.")));
        }
    }
}
