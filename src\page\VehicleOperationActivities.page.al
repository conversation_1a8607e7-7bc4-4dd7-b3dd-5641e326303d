page 60051 "Vehicle Operation Activities"
{
    Caption = 'Vehicle Operation Activities';
    PageType = CardPart;
    RefreshOnActivate = true;
    SourceTable = "Vehicle Operation Activ. Cue";
    ShowFilter = false;
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            cuegroup("Vehicle Query Activity")
            {
                Caption = 'Vehicle Query';

                actions
                {
                    action(VehicleQueryAction)
                    {
                        RunObject = page "Vehicle Query ERK";
                        Image = TileHelp;
                        Caption = 'Vehicle Query';
                        ToolTip = 'Executes the Vehicle Query Action action.';
                        AccessByPermission = tabledata "Vehicle Transfer Line ERK" = r;
                        // Gesture = RightSwipe;
                        // Ellipsis = true;
                    }
                    action(DeclarationQueryAction)
                    {
                        //RunObject = page "Vehicle Query ERK";
                        Image = TileHelp;
                        Caption = 'Declaration Query';
                        ToolTip = 'Executes the Declaration Query Action action.';

                        trigger OnAction()
                        var
                            VehicleQuery: Page "Vehicle Query ERK";
                        begin
                            VehicleQuery.SetDeclarationQuery(true);
                            VehicleQuery.Run();
                        end;
                        // Gesture = RightSwipe;
                        // Ellipsis = true;
                    }
                    action(FuelOperationAction)
                    {
                        RunObject = page "Vehicle Add. Operation ERK";
                        Image = TileBrickNearBy;
                        Caption = 'Vehicle Additional Operation';
                        ToolTip = 'Executes the Fuel Operation Action action.';
                    }
                    action(CreateAddressingDocumentAction)
                    {
                        //RunObject = page "Vehicle Addressing Document ERK";
                        Image = TileNew;
                        Caption = 'Create Addressing Document';
                        ToolTip = 'Executes the Create Addressing Document Action action.';

                        trigger OnAction()
                        var
                            VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                        begin
                            VehicleTransferHeader.Init();
                            VehicleTransferHeader.Insert(true);
                            VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::Addressing);
                            VehicleTransferHeader.Modify(true);
                            PageManagement.PageRun(VehicleTransferHeader);
                        end;
                    }
                    action(CustomsExitAction)
                    {
                        RunObject = page "Customs Exit ERK";
                        Image = TileNew;
                        Caption = 'Field Operation';
                        ToolTip = 'Executes the Customs Exit action.';
                    }
                    action(PDIEntry)
                    {
                        Image = TileNew;
                        Caption = 'PDI Entry';
                        ToolTip = 'Executes the PDI Entry Action action.';
                        Visible = false;

                        trigger OnAction()
                        var
                            VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                        begin
                            VehicleTransferHeader.Init();
                            VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::"PDI Entry");
                            VehicleTransferHeader.Insert(true);
                            VehicleTransferHeader.Modify(true);
                            PageManagement.PageRun(VehicleTransferHeader);
                        end;
                    }
                    action(CreatePDIExitDocumentAction)
                    {
                        //RunObject = page "Vehicle Addressing Document ERK";
                        Image = TileNew;
                        Caption = 'Create PDI Exit Document';
                        ToolTip = 'Executes the Create PDI Exit Document Action action.';

                        trigger OnAction()
                        var
                            VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                        begin
                            VehicleTransferHeader.Init();
                            VehicleTransferHeader.Insert(true);
                            VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::"PDI Exit");
                            VehicleTransferHeader.Modify(true);
                            PageManagement.PageRun(VehicleTransferHeader);
                        end;
                    }
                    action(CreateNavExitDocumentAction)
                    {
                        Image = TileNew;
                        Caption = 'Create Nav Exit Document';
                        ToolTip = 'Executes the Create Nav Exit Document Action action.';

                        trigger OnAction()
                        var
                            VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                        begin
                            VehicleTransferHeader.Init();
                            VehicleTransferHeader.Insert(true);
                            VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::"Nav Exit");
                            VehicleTransferHeader.Validate("To Bin Code", 'XX');
                            VehicleTransferHeader.Modify(true);
                            PageManagement.PageRun(VehicleTransferHeader);
                        end;
                    }
                    action(DamageExit)
                    {
                        Image = TileNew;
                        Caption = 'Damage Exit';
                        ToolTip = 'Executes the Damage Exit Action action.';

                        trigger OnAction()
                        var
                            VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                        begin
                            VehicleTransferHeader.Init();
                            VehicleTransferHeader.Insert(true);
                            VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::"Damage Exit");
                            VehicleTransferHeader.Modify(true);
                            PageManagement.PageRun(VehicleTransferHeader);
                        end;
                    }
                    action(VehicleOperationAction)
                    {
                        RunObject = page "Vehicle Operation ERK";
                        Image = TileNew;
                        Caption = 'Vehicle Operation';
                        ToolTip = 'Executes the Vehicle Operation action.';
                        // trigger OnAction()
                        // var
                        //     VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                        // begin
                        //     VehicleTransferHeader.Init();
                        //     VehicleTransferHeader.Insert(true);
                        //     VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::"Dealer Dispatch");
                        //     VehicleTransferHeader.Modify(true);
                        //     PageManagement.PageRun(VehicleTransferHeader);
                        // end;
                    }
                    // action(CreateVehicleEntryDocumentAction)
                    // {
                    //     Image = TileNew;
                    //     Caption = 'Create Vehicle Entry Document';
                    //     ApplicationArea = All;
                    //     ToolTip = 'Executes the Create Vehicle Entry Document Action action.';
                    //     trigger OnAction()
                    //     var
                    //         VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                    //     begin
                    //         VehicleTransferHeader.Init();
                    //         VehicleTransferHeader.Insert(true);
                    //         VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::"Vehicle Entry");
                    //         VehicleTransferHeader.Modify(true);
                    //         PageManagement.PageRun(VehicleTransferHeader);
                    //     end;
                    // }
                    action(CreateStockTakingDocumentAction)
                    {
                        Image = TileNew;
                        Caption = 'Create Stock-Taking Document';
                        ToolTip = 'Executes the Create Stock-Taking Document Action action.';

                        trigger OnAction()
                        var
                            VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                        begin
                            VehicleTransferHeader.Init();
                            VehicleTransferHeader.Insert(true);
                            VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::"Stock-Taking");
                            VehicleTransferHeader.Validate("To Location Code", '');
                            VehicleTransferHeader.Modify(true);
                            PageManagement.PageRun(VehicleTransferHeader);
                        end;
                    }
                    action(TakePictureAction)
                    {
                        Image = TileNew;
                        Caption = 'Take Picture';
                        ToolTip = 'Executes the Take Picture Action action.';

                        trigger OnAction()
                        var
                            AttachmentHandlerERK: Codeunit "Attachment Handler ERK";
                        begin
                            AttachmentHandlerERK.ShowSerialNoAttachmentDialog();
                        end;
                    }
                }
            }
            cuegroup(VehicleTransferActivites)
            {
                Caption = 'Vehicle Transfer Documents';

                field("Discharge Documents - Open"; Rec."Discharge Documents - Open")
                {
                }
                // field("Addressing Documents - Open"; Rec."Addressing Documents - Open")
                // {
                //     ApplicationArea = All;
                //     ToolTip = 'Specifies the value of the Transfer Documents - Open field.';
                // }
                field("Customs Exit Documents - Open"; Rec."Customs Exit Docs. - Released")
                {
                }
                field("Nav Entry - Released"; Rec."Nav Entry - Released")
                {
                }

                field("Loading Documents - Open"; Rec."Loading Documents - Open")
                {
                }
                field("Transfer Documents - Open"; Rec."Transfer Documents - Open")
                {
                }
                field("Wash Documents - Open"; Rec."Wash Documents - Open")
                {
                }
                field("PDI Entry Documents - Open"; Rec."PDI Entry Documents - Released")
                {
                    Visible = false;
                }
                field("Dispatch Prep. - Released"; Rec."Dispatch Prep. - Released")
                {
                }
                field("PDI Documents - Open"; Rec."PDI Documents - Open")
                {
                    Visible = false;
                }
                field("Vehicles in Perron Bins"; Rec."Vehicles in Perron Bins")
                {
                    ToolTip = 'Specifies the number of vehicles in perron bins.';
                    DrillDownPageId = Bins;

                    trigger OnDrillDown()
                    var
                        BinRec: Record Bin;
                    begin
                        BinRec.SetRange("Is Perron ERK", true);
                        BinRec.SetFilter("Vehicle Quantity ERK", '>0');
                        Page.Run(Page::"Perron Bins ERK", BinRec);
                    end;
                }
            }
        }
    }
    trigger OnOpenPage()
    var
        WarehouseEmployee: Record "Warehouse Employee";
        LocationFilter: Text[250];
        IsFirst: Boolean;
    begin
        Rec.Reset();
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert(true);
        end;

        // Build location filter based on warehouse employee setup
        IsFirst := true;
        LocationFilter := '';
        WarehouseEmployee.SetRange("User ID", UserId());
        if WarehouseEmployee.FindSet() then
            repeat
                if IsFirst then begin
                    LocationFilter := '*' + WarehouseEmployee."Location Code" + '*';
                    IsFirst := false;
                end else
                    if StrLen(LocationFilter) + StrLen(WarehouseEmployee."Location Code") + 3 <= 250 then
                        LocationFilter := CopyStr(LocationFilter + '|*' + WarehouseEmployee."Location Code" + '*', 1, MaxStrLen(LocationFilter));
            until WarehouseEmployee.Next() = 0;

        // Apply the location filter
        if LocationFilter <> '' then
            Rec.SetFilter("Location Filter", LocationFilter);
    end;

    var
        PageManagement: Codeunit "Page Management";
}
