tableextension 60001 "Purch. Inv. Line ERK" extends "Purch. Inv. Line"
{
    fields
    {
        field(60000; "Export No. ERK"; Code[20])
        {
            Caption = 'Export No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Export No. field.';
        }
        field(60001; "Export Line No. ERK"; Integer)
        {
            Caption = 'Export Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Export Line No. field.';
        }
        field(60002; "Currency Code ERK"; Code[10])
        {
            Caption = 'Currency Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purch. Inv. Header"."Currency Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the code of the currency that the record is stated in.';
        }
        field(60003; "Vendor Invoice No. ERK"; Code[35])
        {
            Caption = 'Vendor Invoice No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purch. Inv. Header"."Vendor Invoice No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the number of the vendor invoice.';
        }
        field(60004; "Distributed Quantity ERK"; Integer)
        {
            Caption = 'Distributed Quantity';
            ToolTip = 'Specifies the quantity of distributed vehicles.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Serial No. Revenue/Expense ERK" where("Posted Invoice No." = field("Document No."), "Invoice Line No." = field("Line No.")));
        }
    }
}
