pageextension 60024 "Lot No. Information List ERK" extends "Lot No. Information List"
{
    layout
    {
        addlast(Control1)
        {
            field("Bill-to Customer No. ERK"; Rec."Bill-to Customer No. ERK")
            {
                ApplicationArea = All;
            }
            field("Bill-to Customer Name ERK"; Rec."Bill-to Customer Name ERK")
            {
                ApplicationArea = All;
            }
            field("Load Owner No. ERK"; Rec."Load Owner No. ERK")
            {
                ApplicationArea = All;
            }
            field("Load Owner Name ERK"; Rec."Load Owner Name ERK")
            {
                ApplicationArea = All;
            }
            // field("Location Code ERK"; Rec."Location Code ERK")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Location Code field.';
            // }
            field("Package Type ERK"; Rec."Package Type ERK")
            {
                ApplicationArea = All;
            }
            field("Package Count ERK"; Rec."Package Count ERK")
            {
                ApplicationArea = All;
            }
            field("SystemCreatedAt ERK"; Rec.SystemCreatedAt)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the SystemCreatedAt field.';
            }
        }
    }
}
