table 60006 "Container Header ERK"
{
    Caption = 'Container';
    DataClassification = CustomerContent;
    LookupPageId = "Container List ERK";
    DrillDownPageId = "Container List ERK";

    fields
    {
        field(1; "Export No."; Code[20])
        {
            Caption = 'Export No.';
            ToolTip = 'Specifies the value of the Export No. field.';
        }
        field(2; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the value of the No. field.';
        }
        field(3; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(4; "Total Box Quantity"; Integer)
        {
            Caption = 'Total Box Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Container Line ERK"."Box Quantity" where("Export No." = field("Export No."), "Container No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Box Quantity field.';
        }
        field(5; "Total Net Weight (KG)"; Decimal)
        {
            Caption = 'Total Net Weight (KG)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Container Line ERK"."Net Weight (KG)" where("Export No." = field("Export No."), "Container No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Net Weight (KG) field.';
        }
        field(6; "Total Gross Weight (KG)"; Decimal)
        {
            Caption = 'Total Gross Weight (KG)';
            ToolTip = 'Specifies the value of the Total Gross Weight (KG) field.';
        }
        field(8; "Total Palette Quantity"; Integer)
        {
            Caption = 'Total Palette Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Container Line ERK"."Palette Quantity" where("Export No." = field("Export No."), "Container No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Palette Quantity field.';
        }
        field(7; Completed; Boolean)
        {
            Caption = 'Completed';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Export Header ERK".Completed where("No." = field("Export No.")));
            ToolTip = 'Specifies the value of the Completed field.';
        }
        field(9; "E-Export No."; Code[35])
        {
            Caption = 'E-Export No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Export Header ERK"."E-Export No." where("No." = field("Export No.")));
            ToolTip = 'Specifies the value of the E-Export No. field.';
        }
    }
    keys
    {
        key(PK; "Export No.", "No.")
        {
            Clustered = true;
        }
    }
    trigger OnDelete()
    var
        ContainerLine: Record "Container Line ERK";
    begin
        ContainerLine.SetRange("Export No.", "Export No.");
        ContainerLine.SetRange("Container No.", "No.");
        ContainerLine.DeleteAll(true);
    end;

    trigger OnInsert()
    var
        ExportHeader: Record "Export Header ERK";
    begin
        if ExportHeader.Get("Export No.") then begin
            ExportHeader.CalcFields("Customer Name");
            Rec."Customer Name" := ExportHeader."Customer Name";
        end;
    end;
}
