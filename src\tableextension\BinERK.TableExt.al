tableextension 60013 "Bin ERK" extends Bin
{
    fields
    {
        field(60000; "Default Discharge ERK"; Boolean)
        {
            Caption = 'Default Discharge';
            ToolTip = 'Specifies the value of the Default Loading/Discharge field.';
        }
        field(60001; "Vehicle Quantity ERK"; Integer)
        {
            Caption = 'Vehicle Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Serial No. Information" where("Current Location Code ERK" = field("Location Code"), "Current Bin Code ERK" = field(Code)));
            ToolTip = 'Specifies the value of the Vehicle Quantity field.';
        }
        field(60002; "Vehicle Capacity ERK"; Integer)
        {
            Caption = 'Vehicle Capacity';
            ToolTip = 'Specifies the value of the Vehicle Capacity field.';
        }
        field(60003; "PDI Bin ERK"; Boolean)
        {
            Caption = 'PDI Bin';
            ToolTip = 'Specifies the value of the PDI Bin field.';
        }
        field(60004; "In-Transit Bin ERK"; Boolean)
        {
            Caption = 'In-Transit Bin';
            ToolTip = 'Specifies the value of the In-Transit Bin field.';
        }
        field(60005; "Civil Area ERK"; Boolean)
        {
            Caption = 'Civil Area';
            ToolTip = 'Specifies the value of the Civil Area field.';
        }
        field(60006; "Default Loading ERK"; Boolean)
        {
            Caption = 'Default Loading';
            ToolTip = 'Specifies the value of the Default Loading field.';
        }
        field(60007; "Customs Exit ERK"; Boolean)
        {
            Caption = 'Customs Exit';
            ToolTip = 'Specifies the value of the Customs Exit field.';
        }
        field(60008; "PDI Area ERK"; Boolean)
        {
            Caption = 'PDI Exit';
            ToolTip = 'Specifies the value of the PDI Area field.';
        }
        field(60009; "Nav Area ERK"; Boolean)
        {
            Caption = 'Nav Exit';
            ToolTip = 'Specifies the value of the Nav Area field.';
        }
        field(60010; "Transfer ERK"; Boolean)
        {
            Caption = 'Transfer';
            ToolTip = 'Specifies the value of the Transfer field.';
        }
        field(60011; "Vis. for Dealer Dispatch ERK"; Boolean)
        {
            Caption = 'Visible for Dealer Dispatch';
            ToolTip = 'Specifies the value of the Dealer field.';
        }
        field(60012; "Hide in Addressing ERK"; Boolean)
        {
            Caption = 'Hide in Addressing';
            ToolTip = 'Specifies the value of the Hide in Addressing field.';
        }
        field(60013; "Visible for PDI Entry ERK"; Boolean)
        {
            Caption = 'Visible for PDI Entry';
            ToolTip = 'Specifies the value of the PDI Entry field.';
        }
        field(60014; "Visible for Damage Exit ERK"; Boolean)
        {
            Caption = 'Visible for Damage Exit';
            ToolTip = 'Specifies the value of the Damage Exit field.';
        }
        field(60015; "Vehicle Entry ERK"; Boolean)
        {
            Caption = 'Visible for Vehicle Entry';
            ToolTip = 'Specifies the value of the Vehicle Entry field.';
        }
        field(60017; "Mandatory From Bin For PDI ERK"; Boolean)
        {
            Caption = 'Mandatory From Bin For PDI';
            ToolTip = 'Specifies the value of the Mandatory From Bin For PDI field.';
        }
        field(60018; "Man. From Bin For Nav Exit ERK"; Boolean)
        {
            Caption = 'Mandatory From Bin For Nav Exit';
            ToolTip = 'Specifies the value of the Mandatory To Bin For Nav Exit field.';
        }
        field(60019; "Man. From Bin Dmg. Exit ERK"; Boolean)
        {
            Caption = 'Mandatory From Bin For Damage Exit';
            ToolTip = 'Specifies the value of the Mandatory From Bin For Damage Exit field.';
        }
        field(60020; "Man Frm Bin Dealer Dispch. ERK"; Boolean)
        {
            Caption = 'Mandatory From Bin For Dealer Dispatch';
            ToolTip = 'Specifies the value of the Mandatory From Bin For Dealer Dispatch field.';
        }
        field(60016; "Visible for Wash ERK"; Boolean)
        {
            Caption = 'Visible for Wash';
            ToolTip = 'Specifies the value of the Visible for Wash field.';
        }
        field(60021; "Visible for Dispatch Prep. ERK"; Boolean)
        {
            Caption = 'Visible for Dispatch Preparation';
            ToolTip = 'Specifies the value of the Visible for Dispatch Preparation field.';
        }
        field(60022; "Visible for Nav Entry ERK"; Boolean)
        {
            Caption = 'Visible for Nav Entry';
            ToolTip = 'Specifies the value of the Visible for Nav Entry field.';
        }
        field(60023; "Is Perron ERK"; Boolean)
        {
            Caption = 'Is Perron';
            ToolTip = 'Specifies whether this bin is a perron location.';
        }
    }
    keys
    {
        key(SK; "Default Discharge ERK", "In-Transit Bin ERK")
        {
        }
    }
}
