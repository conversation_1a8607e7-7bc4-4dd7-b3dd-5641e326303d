pageextension 60002 "Purch. Invoice Subform ERK" extends "Purch. Invoice Subform"
{
    layout
    {
        addafter(Quantity)
        {
            field("Distributed Quantity ERK"; Rec."Distributed Quantity ERK")
            {
                ApplicationArea = ErkPortERK;
            }
            field("Export No. ERK"; Rec."Export No. ERK")
            {
                ApplicationArea = All;
                Visible = false;
            }
            field("Export Line No. ERK"; Rec."Export Line No. ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
        }
        modify("Deferral Code")
        {
            Visible = true;
        }
    }
    actions
    {
        addfirst("&Line")
        {
            action("OpenVehicleDistributionWorksheet ERK")
            {
                ApplicationArea = All;
                Caption = 'Open Vehicle Distribution Worksheet';
                Image = AccountingPeriods;
                ToolTip = 'Executes the Open Vehicle Distribution Worksheet action.';
                Enabled = not PurchaseHeaderCreatedFromCarCarrier;

                trigger OnAction()
                var
                    PurchaseHeader: Record "Purchase Header";
                begin
                    PurchaseHeader.Get(Rec."Document Type", Rec."Document No.");

                    VehicleRevExpManagement.CreateVehicleRevenueExpenseWorksheet(Rec."Document No.", Rec."Line No.", Enum::"Voyage Line Detail Type ERK"::Expense, Rec."No.", Rec."Variant Code", '', PurchaseHeader."Buy-from Vendor No.", PurchaseHeader."Your Reference", PurchaseHeader."Posting Date", PurchaseHeader."Currency Code", PurchaseHeader."Vendor Invoice No.", Rec."Line Amount", true, PurchaseHeader."Shortcut Dimension 1 Code");
                end;
            }
        }
    }

    trigger OnAfterGetCurrRecord()
    var
        PurchaseHeader: Record "Purchase Header";
    begin
        if PurchaseHeader.Get(Rec."Document Type", Rec."Document No.") then
            PurchaseHeaderCreatedFromCarCarrier := PurchaseHeader."Created From Car Carrier ERK"
        else
            PurchaseHeaderCreatedFromCarCarrier := false;
    end;

    var
        VehicleRevExpManagement: Codeunit "Vehicle Rev./Exp. Management";
        PurchaseHeaderCreatedFromCarCarrier: Boolean;
}
