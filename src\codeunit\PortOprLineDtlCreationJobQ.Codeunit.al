codeunit 60027 "PortOprLineDtl.Creation Job Q."
{

    trigger OnRun()
    var
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
    begin
        PortOperationLineDetail.SetRange(Type, PortOperationLineDetail.Type::Revenue);
        PortOperationLineDetail.SetRange("Next Invoice Date", WorkDate());
        if not PortOperationLineDetail.FindSet(false) then
            exit;

        repeat
            PortOperationManagement.CreateNewRevenueLineFromPortOperationLineDetail(PortOperationLineDetail);
        until PortOperationLineDetail.Next() = 0;
    end;

    var
        PortOperationManagement: Codeunit "Port Operation Management ERK";
}
