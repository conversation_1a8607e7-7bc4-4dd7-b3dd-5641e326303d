tableextension 60015 "Vendor ERK" extends Vendor
{
    fields
    {
        field(60002; "Tax Area Description ERK"; Text[100])
        {
            Caption = 'Tax Area Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Tax Area".Description where(Code = field("Tax Area Code")));
            AllowInCustomizations = Always;
        }
        field(60000; "Purchaser Name ERK"; Text[50])
        {
            Caption = 'Purchaser Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Salesperson/Purchaser".Name where(Code = field("Purchaser Code")));
            ToolTip = 'Specifies the value of the Purchaser Name field.';
        }
        field(60004; "Erk Holding Intercompany ERK"; Boolean)
        {
            Caption = 'Erk Holding Intercompany';
            ToolTip = 'Specifies the value of the Erk Holding Intercompany field.';
        }
    }
}
