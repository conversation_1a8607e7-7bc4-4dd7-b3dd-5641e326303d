page 60035 "Port Operation Card ERK"
{
    ApplicationArea = All;
    Caption = 'Port Operation Card';
    PageType = Card;
    SourceTable = "Port Operation Header ERK";
    UsageCategory = None;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Third Party"; Rec."Third Party")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Deparment Name"; Rec."Deparment Name")
                {
                }
                field("Contract No."; Rec."Contract No.")
                {
                }
                field("Dock No."; Rec."Dock No.")
                {
                }
                field("Declared Quantity (KG)"; Rec."Declared Quantity (Ton)")
                {
                }
                field("Port Code"; Rec."Port Code")
                {
                }
                field("Port Name"; Rec."Port Name")
                {
                }
                field("Actual Docking Time"; Rec."Actual Docking Time")
                {
                }
                field("Operation Starting Time"; Rec."Operation Starting Time")
                {
                }
                field("Operation Ending Time"; Rec."Operation Ending Time")
                {
                }
                field("Actual Departure Time"; Rec."Actual Departure Time")
                {
                }
                field("Actual Tonnage"; Rec."Actual Tonnage")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
            part(Lines; "Port Operation Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(PortOperationLineDetails)
            {
                Caption = 'Port Operation Line Details';
                Image = GetLines;
                RunObject = page "Port Operation Line DetailsERK";
                RunPageLink = "Document No." = field("No.");
                ToolTip = 'Executes the Port Operation Line Details action.';
                Promoted = true;
                PromotedOnly = true;
            }
        }
    }
    trigger OnClosePage()
    var
        PortOperationLine: Record "Port Operation Line ERK";
    begin
        PortOperationLine.SetRange("Document No.", Rec."No.");
        if not PortOperationLine.IsEmpty() then begin
            PortOperationLine.SetRange(Completed, false);
            if PortOperationLine.IsEmpty() then begin
                Rec.Completed := true;
                Rec.Modify(true);
            end;
        end;
    end;
}
