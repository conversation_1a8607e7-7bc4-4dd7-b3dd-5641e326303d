query 60003 "Sales Invoice Line ERK"
{
    Caption = 'Sales Invoice Line';
    QueryType = Normal;

    elements
    {
        dataitem(SalesInvoiceLine;
        "Sales Invoice Line")
        {
            column(SelltoCustomerNo;
            "Sell-to Customer No.")
            {
            }
            column(DocumentNo;
            "Document No.")
            {
            }
            column(LineNo;
            "Line No.")
            {
            }
            column("Type";
            "Type")
            {
            }
            column(No;
            "No.")
            {
            }
            column(LocationCode;
            "Location Code")
            {
            }
            column(PostingGroup;
            "Posting Group")
            {
            }
            column(ShipmentDate;
            "Shipment Date")
            {
            }
            column(Description;
            Description)
            {
            }
            column(Description2;
            "Description 2")
            {
            }
            column(UnitofMeasure;
            "Unit of Measure")
            {
            }
            column(Quantity;
            Quantity)
            {
            }
            column(UnitPrice;
            "Unit Price")
            {
            }
            column(UnitCostLCY;
            "Unit Cost (LCY)")
            {
            }
            column(VAT;
            "VAT %")
            {
            }
            column(LineDiscount;
            "Line Discount %")
            {
            }
            column(LineDiscountAmount;
            "Line Discount Amount")
            {
            }
            column(Amount;
            Amount)
            {
            }
            column(AmountIncludingVAT;
            "Amount Including VAT")
            {
            }
            column(AllowInvoiceDisc;
            "Allow Invoice Disc.")
            {
            }
            column(GrossWeight;
            "Gross Weight")
            {
            }
            column(NetWeight;
            "Net Weight")
            {
            }
            column(UnitsperParcel;
            "Units per Parcel")
            {
            }
            column(UnitVolume;
            "Unit Volume")
            {
            }
            column(AppltoItemEntry;
            "Appl.-to Item Entry")
            {
            }
            column(ShortcutDimension1Code;
            "Shortcut Dimension 1 Code")
            {
            }
            column(ShortcutDimension2Code;
            "Shortcut Dimension 2 Code")
            {
            }
            column(CustomerPriceGroup;
            "Customer Price Group")
            {
            }
            column(JobNo;
            "Job No.")
            {
            }
            column(WorkTypeCode;
            "Work Type Code")
            {
            }
            column(ShipmentNo;
            "Shipment No.")
            {
            }
            column(ShipmentLineNo;
            "Shipment Line No.")
            {
            }
            column(OrderNo;
            "Order No.")
            {
            }
            column(OrderLineNo;
            "Order Line No.")
            {
            }
            column(BilltoCustomerNo;
            "Bill-to Customer No.")
            {
            }
            column(InvDiscountAmount;
            "Inv. Discount Amount")
            {
            }
            column(DropShipment;
            "Drop Shipment")
            {
            }
            column(GenBusPostingGroup;
            "Gen. Bus. Posting Group")
            {
            }
            column(GenProdPostingGroup;
            "Gen. Prod. Posting Group")
            {
            }
            column(VATCalculationType;
            "VAT Calculation Type")
            {
            }
            column(TransactionType;
            "Transaction Type")
            {
            }
            column(TransportMethod;
            "Transport Method")
            {
            }
            column(AttachedtoLineNo;
            "Attached to Line No.")
            {
            }
            column(ExitPoint;
            "Exit Point")
            {
            }
            column("Area";
            "Area")
            {
            }
            column(TransactionSpecification;
            "Transaction Specification")
            {
            }
            column(TaxCategory;
            "Tax Category")
            {
            }
            column(TaxAreaCode;
            "Tax Area Code")
            {
            }
            column(TaxLiable;
            "Tax Liable")
            {
            }
            column(TaxGroupCode;
            "Tax Group Code")
            {
            }
            column(VATClauseCode;
            "VAT Clause Code")
            {
            }
            column(VATBusPostingGroup;
            "VAT Bus. Posting Group")
            {
            }
            column(VATProdPostingGroup;
            "VAT Prod. Posting Group")
            {
            }
            column(BlanketOrderNo;
            "Blanket Order No.")
            {
            }
            column(BlanketOrderLineNo;
            "Blanket Order Line No.")
            {
            }
            column(VATBaseAmount;
            "VAT Base Amount")
            {
            }
            column(UnitCost;
            "Unit Cost")
            {
            }
            column(SystemCreatedEntry;
            "System-Created Entry")
            {
            }
            column(LineAmount;
            "Line Amount")
            {
            }
            column(VATDifference;
            "VAT Difference")
            {
            }
            column(VATIdentifier;
            "VAT Identifier")
            {
            }
            column(ICPartnerRefType;
            "IC Partner Ref. Type")
            {
            }
            column(ICPartnerReference;
            "IC Partner Reference")
            {
            }
            column(PrepaymentLine;
            "Prepayment Line")
            {
            }
            column(ICPartnerCode;
            "IC Partner Code")
            {
            }
            column(PostingDate;
            "Posting Date")
            {
            }
            column(ICItemReferenceNo;
            "IC Item Reference No.")
            {
            }
            column(PmtDiscountAmount;
            "Pmt. Discount Amount")
            {
            }
            column(LineDiscountCalculation;
            "Line Discount Calculation")
            {
            }
            column(DimensionSetID;
            "Dimension Set ID")
            {
            }
            column(JobTaskNo;
            "Job Task No.")
            {
            }
            column(JobContractEntryNo;
            "Job Contract Entry No.")
            {
            }
            column(DeferralCode;
            "Deferral Code")
            {
            }
            column(AllocationAccountNo;
            "Allocation Account No.")
            {
            }
            column(VariantCode;
            "Variant Code")
            {
            }
            column(BinCode;
            "Bin Code")
            {
            }
            column(QtyperUnitofMeasure;
            "Qty. per Unit of Measure")
            {
            }
            column(UnitofMeasureCode;
            "Unit of Measure Code")
            {
            }
            column(QuantityBase;
            "Quantity (Base)")
            {
            }
            column(FAPostingDate;
            "FA Posting Date")
            {
            }
            column(DepreciationBookCode;
            "Depreciation Book Code")
            {
            }
            column(DepruntilFAPostingDate;
            "Depr. until FA Posting Date")
            {
            }
            column(DuplicateinDepreciationBook;
            "Duplicate in Depreciation Book")
            {
            }
            column(UseDuplicationList;
            "Use Duplication List")
            {
            }
            column(ResponsibilityCenter;
            "Responsibility Center")
            {
            }
            column(ItemCategoryCode;
            "Item Category Code")
            {
            }
            column(Nonstock;
            Nonstock)
            {
            }
            column(PurchasingCode;
            "Purchasing Code")
            {
            }
            column(ItemReferenceNo;
            "Item Reference No.")
            {
            }
            column(ItemReferenceUnitofMeasure;
            "Item Reference Unit of Measure")
            {
            }
            column(ItemReferenceType;
            "Item Reference Type")
            {
            }
            column(ItemReferenceTypeNo;
            "Item Reference Type No.")
            {
            }
            column(ApplfromItemEntry;
            "Appl.-from Item Entry")
            {
            }
            column(ReturnReasonCode;
            "Return Reason Code")
            {
            }
            column(PriceCalculationMethod;
            "Price Calculation Method")
            {
            }
            column(AllowLineDisc;
            "Allow Line Disc.")
            {
            }
            column(CustomerDiscGroup;
            "Customer Disc. Group")
            {
            }
            column(Pricedescription;
            "Price description")
            {
            }
            column(SystemCreatedAt;
            SystemCreatedAt)
            {
            }
            column(SystemCreatedBy;
            SystemCreatedBy)
            {
            }
            column(SystemId;
            SystemId)
            {
            }
            column(SystemModifiedAt;
            SystemModifiedAt)
            {
            }
            column(SystemModifiedBy;
            SystemModifiedBy)
            {
            }
        }
    }
    trigger OnBeforeOpen()
    begin
    end;
}
