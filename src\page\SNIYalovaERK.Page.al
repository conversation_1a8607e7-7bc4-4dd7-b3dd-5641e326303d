page 60128 "SNI - <PERSON><PERSON>a ERK"
{
    ApplicationArea = All;
    Caption = 'SNI - Yalova';
    PageType = List;
    SourceTable = "Serial No. Information";
    UsageCategory = Lists;
    Editable = false;
    SourceTableView = where("Current Location Code ERK" = filter('YALOVA 1|YALOVA 2'));
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Serial No."; Rec."Serial No.")
                {
                    ToolTip = 'Specifies the value of the Serial No. field.';
                }
                field("Brand Code ERK"; Rec."Brand Code ERK")
                {
                }
                // field("Commercial Blockage ERK"; Rec."Commercial Blockage ERK")
                // {
                // }
                field("Model Version ERK"; Rec."Model Version ERK")
                {
                }
                field("Model Code ERK"; Rec."Model Code ERK")
                {
                }
                field("Colour Name ERK"; Rec."Colour Name ERK")
                {
                }
                field("Fuel Type ERK"; Rec."Fuel Type ERK")
                {
                }
                field("Engine ID ERK"; Rec."Engine ID ERK")
                {
                }
                // field("Gross Weight (KG) ERK"; Rec."Gross Weight (KG) ERK")
                // {
                // }
                // field("Footprint (m2) ERK"; Rec."Footprint (m2) ERK")
                // {
                // }
                // field("TSE ERK"; Rec."TSE ERK")
                // {
                // }
                field("Customs Declaration No. ERK"; Rec."Customs Declaration No. ERK")
                {
                }
                field("Customs Dec. Line No. ERK"; Rec."Customs Dec. Line No. ERK")
                {
                }
                field("Current Location Code ERK"; Rec."Current Location Code ERK")
                {
                }
                field("Current Bin Code ERK"; Rec."Current Bin Code ERK")
                {
                }
                // field("Civil Area ERK"; Rec."Civil Area ERK")
                // {
                // }
                field("Customs Registration Date ERK"; Rec."Customs Registration Date ERK")
                {
                }
                // field("Car Carrier Ledger Entries ERK"; Rec."Car Carrier Ledger Entries ERK")
                // {
                // }
                // field("Vehicle Trns Ledg. Entries ERK"; Rec."Vehicle Trns Ledg. Entries ERK")
                // {
                // }
                // field("Nav. Process Required ERK"; Rec."Nav. Process Required ERK")
                // {
                // }
                // field("Volume (m3) ERK"; Rec."Volume (m3) ERK")
                // {
                // }
                // field("Grupage No. ERK"; Rec."Grupage No. ERK")
                // {
                // }
                // field("Grupage Date ERK"; Rec."Grupage Date ERK")
                // {
                // }
                // field("Grupage Ship-to Name ERK"; Rec."Grupage Ship-to Name ERK")
                // {
                // }
                // field("Grupage Ship-to City ERK"; Rec."Grupage Ship-to City ERK")
                // {
                // }
                // field("Grupage Bin Code ERK"; Rec."Grupage Bin Code ERK")
                // {
                // }
                // field("Grupage Ship-to Address ERK"; Rec."Grupage Ship-to Address ERK")
                // {
                // }
                // field("Grupage Location Code ERK"; Rec."Grupage Location Code ERK")
                // {
                // }
                // field("Print Grupage Label ERK"; Rec."Print Grupage Label ERK")
                // {
                // }
                // field("Temp. Traffic Document No. ERK"; Rec."Temp. Traffic Document No. ERK")
                // {
                //     ToolTip = 'Specifies the value of the Temp. Traffic Document No. field.';
                // }
                // field("Temp. License Plate No. ERK"; Rec."Temp. License Plate No. ERK")
                // {
                //     ToolTip = 'Specifies the value of the Temp. License Plate No. field.';
                // }
                // field("Temp. Driver Full Name ERK"; Rec."Temp. Driver Full Name ERK")
                // {
                //     ToolTip = 'Specifies the value of the Temp. Driver Full Name field.';
                // }
                field("Summary Declaration No. ERK"; Rec."Summary Declaration No. ERK")
                {
                    ToolTip = 'Specifies the value of the Summary Declaration No. field.';
                }
            }
        }
    }
}