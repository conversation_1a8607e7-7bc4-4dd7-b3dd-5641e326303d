table 60058 "Car Carrier Order Line Dtl ERK"
{
    Caption = 'Car Carrier Order Line Detail';
    DataClassification = CustomerContent;
    LookupPageId = "Car Carrier Order Line Dtl ERK";
    DrillDownPageId = "Car Carrier Order Line Dtl ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
            AllowInCustomizations = Always;
        }
        field(2; "Planned Car Carrier No."; Code[20])
        {
            Caption = 'Planned Car Carrier No.';
            TableRelation = "Car Carrier Header ERK"."No." where(Status = filter('<>Completed'));
            ToolTip = 'Specifies the value of the Planned Car Carrier No. field.';
        }
        // field(2; "Document Line No."; Integer)
        // {
        //     Caption = 'Document Line No.';
        //     AllowInCustomizations = Always;
        // }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(4; "Shipper No."; Code[20])
        {
            Caption = 'Shipper No.';
            TableRelation = "Voyage Account ERK"."No." where("Car Carrier Related" = const(true));
            ToolTip = 'Specifies the value of the Shipper No. field.';
            trigger OnValidate()
            var
                VoyageAccountERK: Record "Voyage Account ERK";
            begin
                CarCarrierOrderMngt.CheckCarCarrierOrderLineDetailSelectedInCarCarrierLineDetail(Rec);
                if VoyageAccountERK.Get("Shipper No.") then
                    Rec.Validate("Shipper Name", VoyageAccountERK.Name)
                else
                    Rec.Validate("Shipper Name", '');
            end;
        }
        field(5; "Shipper Name"; Text[100])
        {
            Caption = 'Shipper Name';
            ToolTip = 'Specifies the value of the Shipper Name field.';
        }
        field(6; "Consignee No."; Code[20])
        {
            Caption = 'Consignee No.';
            TableRelation = "Voyage Account ERK"."No." where("Car Carrier Related" = const(true));
            ToolTip = 'Specifies the value of the Consignee No. field.';
            trigger OnValidate()
            var
                VoyageAccountERK: Record "Voyage Account ERK";
            begin
                CarCarrierOrderMngt.CheckCarCarrierOrderLineDetailSelectedInCarCarrierLineDetail(Rec);
                if VoyageAccountERK.Get("Consignee No.") then
                    Rec.Validate("Consignee Name", VoyageAccountERK.Name)
                else
                    Rec.Validate("Consignee Name", '');
            end;
        }
        field(7; "Consignee Name"; Text[100])
        {
            Caption = 'Consignee Name';
            ToolTip = 'Specifies the value of the Consignee Name field.';
        }
        field(8; "Bill-to Customer No."; Code[20])
        {
            Caption = 'Bill-to Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Bill-to Customer No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
            begin
                CarCarrierOrderMngt.CheckCarCarrierOrderLineDetailSelectedInCarCarrierLineDetail(Rec);
                if Customer.Get("Bill-to Customer No.") then
                    Rec.Validate("Bill-to Customer Name", Customer.Name)
                else
                    Rec.Validate("Bill-to Customer Name", '');
            end;
        }
        field(9; "Bill-to Customer Name"; Text[100])
        {
            Caption = 'Bill-to Customer Name';
            ToolTip = 'Specifies the value of the Bill-to Customer Name field.';
        }
        field(10; "Loaded Quantity"; Integer)
        {
            Caption = 'Loaded Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Car Carrier Order No." = field("Document No."), "CC Order Load Detail Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Loaded Quantity field.';
        }
        field(11; "Discharged Quantity"; Integer)
        {
            Caption = 'Discharged Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Car Carrier Order No." = field("Document No."), "CC Order Load Detail Line No." = field("Line No."), "Discharge DateTime" = filter(<> '')));
            ToolTip = 'Specifies the value of the Discharged Quantity field.';
        }
        // field(10; "Imported Quantity"; Integer)
        // {
        //     Caption = 'Imported Quantity';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = count("Car Carrier Order Vehicle ERK" where("Document No." = field("Document No."), "Document Line No." = field("Document Line No."), "Document Line Detail No." = field("Line No.")));
        // }
        // field(11; "Load Type"; Code[50])
        // {
        //     Caption = 'Load Type';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Car Carrier Order Line ERK"."Load Type" where("Document No." = field("Document No."), "Line No." = field("Document Line No.")));
        // }
        field(12; "Pre-Load Quantity"; Decimal)
        {
            Caption = 'Pre-Load Quantity';
            ToolTip = 'Specifies the value of the Pre-Load Quantity field.';
            // Editable = false;
            // FieldClass = FlowField;
            // CalcFormula = lookup("Car Carrier Order Line ERK"."Pre-Load Quantity" where("Document No." = field("Document No."), "Line No." = field("Document Line No.")));
        }
        // field(13; "Brand Code"; Code[30])
        // {
        //     Caption = 'Brand Code';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Car Carrier Order Line ERK"."Brand Code" where("Document No." = field("Document No."), "Line No." = field("Document Line No.")));
        // }
        // field(14; "Model Code"; Code[40])
        // {
        //     Caption = 'Model Code';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Car Carrier Order Line ERK"."Model Code" where("Document No." = field("Document No."), "Line No." = field("Document Line No.")));
        // }
        field(15; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Customer No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Customer No. field.';
        }
        field(16; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Customer Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(13; "Booking No."; Code[20])
        {
            Caption = 'Booking No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Booking No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Booking No. field.';
        }
        field(14; Status; Enum "Car Carrier Order Status ERK")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK".Status where("No." = field("Document No.")));
        }
        field(17; "Discharge Port Code"; Code[10])
        {
            Caption = 'Discharge Port Code';
            ToolTip = 'Specifies the value of the Discharge Port Code field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Discharge Port Code" where("No." = field("Document No.")));
        }
        field(18; "Your Reference"; Text[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Your Reference" where("No." = field("Document No.")));
        }
        field(19; "Transhipment Allowed"; Boolean)
        {
            Caption = 'Transhipment Allowed';
            ToolTip = 'Specifies the value of the Transhipment Allowed field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Trans Shipment Allowed" where("No." = field("Document No.")));
        }
        field(20; "Loading Port Shipment Method"; Code[10])
        {
            Caption = 'Loading Port Shipment Method';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Loading Port Shipment Method" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Loading Port Shipment Method field.';
        }
        field(21; "Discharge Port Shipment Method"; Code[10])
        {
            Caption = 'Discharge Port Shipment Method';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Discharge Port Shipment Method" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Discharge Port Shipment Method field.';
        }
        field(22; "Vessel Name"; Text[100])
        {
            Caption = 'Vessel Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ship Name" where("No." = field("Planned Car Carrier No.")));
            ToolTip = 'Specifies the value of the Vessel Name field.';
        }
        field(23; "Loading Date-Time"; DateTime)
        {
            Caption = 'Loading Date-Time';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Line Detail ERK"."Loading Start Date-Time" where("Document No." = field("Planned Car Carrier No."), "Order No." = field("Document No."), "Order Load Detail Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Loading Date-Time field.';
        }
        field(24; "Discharge Date-Time"; DateTime)
        {
            Caption = 'Discharge Date-Time';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Line Detail ERK"."Discharge End Date-Time" where("Document No." = field("Planned Car Carrier No."), "Order No." = field("Document No."), "Order Load Detail Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Discharge Date-Time field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(SK; "Document No.", "Planned Car Carrier No.", "Shipper No.", "Consignee No.", "Bill-to Customer No.")
        {
            Unique = true;
        }
    }
    trigger OnInsert()
    var
        CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
    begin
        CarCarrierOrderLineDtl.SetRange("Document No.", Rec."Document No.");
        //CarCarrierOrderLineDtl.SetRange("Document Line No.", Rec."Document Line No.");
        if CarCarrierOrderLineDtl.FindLast() then
            Rec."Line No." := CarCarrierOrderLineDtl."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnDelete()
    var
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        AlreadySelectedInCarCarrierErr: Label 'You can not deleted this line because it is already selected in Car Carrier No.: %1.', Comment = '%1="Car Carrier Line Detail ERK"."Document No."';
    begin
        CarCarrierLineDetail.SetRange("Order No.", Rec."Document No.");
        CarCarrierLineDetail.SetRange("Order Load Detail Line No.", Rec."Line No.");
        if CarCarrierLineDetail.FindFirst() then
            Error(AlreadySelectedInCarCarrierErr, CarCarrierLineDetail."Document No.");
    end;

    var
        CarCarrierOrderMngt: Codeunit "Car Carrier Order Mngt. ERK";
}
