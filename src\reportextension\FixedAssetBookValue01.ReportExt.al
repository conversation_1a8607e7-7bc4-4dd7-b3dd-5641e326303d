reportextension 60003 "Fixed Asset - Book Value 01" extends "Fixed Asset - Book Value 01"
{
    RDLCLayout = 'src/reportLayouts/FixAssetBookValue01.rdlc';
    dataset
    {
        modify("Fixed Asset")
        {
            CalcFields = "Department Name ERK";
        }
        add("Fixed Asset")
        {
            column(CurrentYearDepreciationERK; CalculateFinancialYearDepreciation("Fixed Asset", StartingDateERK, EndingDateERK))
            {
            }
            column(AccumulatedDepreciationERK; CalcualteAccumulatedDepreciation("Fixed Asset", StartingDateERK))
            {
            }
            column(DepreciationERK; CalcualteDepreciation("Fixed Asset", EndingDateERK))
            {
            }
            column(WriteDownERK; CalcualteWriteDown("Fixed Asset", EndingDateERK))
            {
            }
            column(LocationCode_FixedAssetERK; "Location Code")
            {
            }
            column(FALocationCode_FixedAssetERK; "FA Location Code")
            {
            }
            column(GlobalDimension1Code_FixedAssetERK; "Global Dimension 1 Code")
            {
            }
            column(DepartmentNameERK_FixedAssetERK; "Department Name ERK")
            {
            }
            column(Description_FixedAssetERK; Description)
            {
            }
        }
        addlast("Fixed Asset")
        {
            dataitem("FA Depreciation Book"; "FA Depreciation Book")
            {
                DataItemLink = "FA No." = field("No.");
                CalcFields = "Acquisition Cost", Appreciation, Depreciation;

                column(DepreciationBookCode_FADepreciationBookERK; "Depreciation Book Code")
                {
                }
                column(DepreciationMethod_FADepreciationBookERK; "Depreciation Method")
                {
                }
                column(DepreciationStartingDate_FADepreciationBookERK; "Depreciation Starting Date")
                {
                }
                column(StraightLine_FADepreciationBookERK; "Straight-Line %")
                {
                }
                column(NoofDepreciationYears_FADepreciationBookERK; "No. of Depreciation Years")
                {
                }
                column(NoofDepreciationMonths_FADepreciationBookERK; "No. of Depreciation Months")
                {
                }
                column(FAPostingGroup_FADepreciationBookERK; "FA Posting Group")
                {
                }
                column(DepreciationEndingDate_FADepreciationBookERK; "Depreciation Ending Date")
                {
                }
                column(AcquisitionCost_FADepreciationBookERK; "Acquisition Cost")
                {
                }
                column(Appreciation_FADepreciationBookERK; Appreciation)
                {
                }
                column(Depreciation_FADepreciationBookERK; Depreciation)
                {
                }
                column(BookValue_FADepreciationBookERK; "Book Value")
                {
                }
                column(GainLoss_FADepreciationBookERK; "Gain/Loss")
                {
                }
                column(BookValueonDisposal_FADepreciationBookERK; "Book Value on Disposal")
                {
                }
                column(DisposalDate_FADepreciationBookERK; "Disposal Date")
                {
                }
                column(AcquisitionDate_FADepreciationBookERK; "Acquisition Date")
                {
                }
            }
        }
    }
    requestpage
    {
        layout
        {
            modify(StartingDate)
            {
                Visible = false;
            }
            modify(EndingDate)
            {
                Visible = false;
            }
            addafter(StartingDate)
            {
                field(StartingDateERK; StartingDateERK)
                {
                    Caption = 'Starting Date';
                    ToolTip = 'Specifies the value of the Starting Date field.';
                    ApplicationArea = All;

                    trigger OnValidate()
                    begin
                        SetMandatoryFields(DeprBookCode, StartingDateERK, EndingDateERK);
                    end;
                }
                field(EndingDateERK; EndingDateERK)
                {
                    Caption = 'Ending Date';
                    ToolTip = 'Specifies the value of the Ending Date field.';
                    ApplicationArea = All;

                    trigger OnValidate()
                    begin
                        SetMandatoryFields(DeprBookCode, StartingDateERK, EndingDateERK);
                    end;
                }
            }
        }
    }
    local procedure CalculateFinancialYearDepreciation(FixedAsset: Record "Fixed Asset"; StartingDate: Date; EndingDate: Date): Decimal
    var
        FALedgerEntry: Record "FA Ledger Entry";
    begin
        FALedgerEntry.SetRange("FA No.", FixedAsset."No.");
        FALedgerEntry.SetRange("FA Posting Type", FALedgerEntry."FA Posting Type"::Depreciation);
        FALedgerEntry.SetRange("Posting Date", StartingDate, EndingDate);
        FALedgerEntry.CalcSums(Amount);
        exit(FALedgerEntry.Amount);
    end;

    local procedure CalcualteAccumulatedDepreciation(FixedAsset: Record "Fixed Asset"; StartingDate: Date): Decimal
    var
        FALedgerEntry: Record "FA Ledger Entry";
    begin
        FALedgerEntry.SetRange("FA No.", FixedAsset."No.");
        FALedgerEntry.SetRange("FA Posting Type", FALedgerEntry."FA Posting Type"::Depreciation);
        FALedgerEntry.SetRange("Posting Date", 0D, StartingDate - 1);
        FALedgerEntry.CalcSums(Amount);
        exit(FALedgerEntry.Amount);
    end;

    local procedure CalcualteDepreciation(FixedAsset: Record "Fixed Asset"; EndingDate: Date): Decimal
    var
        FALedgerEntry: Record "FA Ledger Entry";
    begin
        FALedgerEntry.SetRange("FA No.", FixedAsset."No.");
        FALedgerEntry.SetRange("FA Posting Type", FALedgerEntry."FA Posting Type"::Depreciation);
        FALedgerEntry.SetRange("Posting Date", 0D, EndingDate);
        FALedgerEntry.CalcSums(Amount);
        exit(FALedgerEntry.Amount);
    end;

    local procedure CalcualteWriteDown(FixedAsset: Record "Fixed Asset"; EndingDate: Date): Decimal
    var
        FALedgerEntry: Record "FA Ledger Entry";
    begin
        FALedgerEntry.SetRange("FA No.", FixedAsset."No.");
        FALedgerEntry.SetRange("FA Posting Type", FALedgerEntry."FA Posting Type"::"Write-Down");
        FALedgerEntry.SetRange("Posting Date", 0D, EndingDate);
        FALedgerEntry.CalcSums(Amount);
        exit(FALedgerEntry.Amount);
    end;

    var
        // ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        StartingDateERK: Date;
        EndingDateERK: Date;
}
