table 60069 "Restricted Location-Oper. Type"
{
    Caption = 'Restricted Location-Oper. Type';
    DataClassification = CustomerContent;
    DrillDownPageId = "Restricted Location-Oper. Type";
    LookupPageId = "Restricted Location-Oper. Type";

    fields
    {
        field(1; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the location code where the operation type is restricted.';
            TableRelation = Location.Code;
        }
        field(2; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            ToolTip = 'Specifies the bin code where the operation type is restricted.';
            TableRelation = Bin.Code where("Location Code" = field("Location Code"));
        }
        field(3; "Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Operation Type';
            ToolTip = 'Specifies the operation type that is restricted for the location and bin code.';
        }
    }
    keys
    {
        key(PK; "Location Code", "Bin Code", "Operation Type")
        {
            Clustered = true;
        }
    }
}