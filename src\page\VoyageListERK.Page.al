page 60017 "Voyage List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Voyage List';
    PageType = List;
    SourceTable = "Voyage Header ERK";
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Voyage Card ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Loading Port Code"; Rec."Loading Port Code")
                {
                }
                field("Loading Port Name"; Rec."Loading Port Name")
                {
                }
                field("Loading Port Arrival Date"; Rec."Loading Port Arrival Date")
                {
                }
                field("Loading Port Departure Date"; Rec."Loading Port Departure Date")
                {
                }
                field("Discharge Port Code"; Rec."Discharge Port Code")
                {
                }
                field("Discharge Port Name"; Rec."Discharge Port Name")
                {
                }
                field("Discharge Port Arrival Date"; Rec."Discharge Port Arrival Date")
                {
                }
                field("Discharge Port Departure Date"; Rec."Discharge Port Departure Date")
                {
                }
                field("Ending Date"; Rec."Ending Date")
                {
                }
                field("Consumption IFO Quantity"; Rec."Consumption IFO Quantity")
                {
                }
                field("Consumption MGO Quantity"; Rec."Consumption MGO Quantity")
                {
                }
                field("Empty Trip"; Rec."Empty Trip")
                {
                }
                field("Total Unposted Revenue (ACY)"; Rec."Total Unposted Revenue (ACY)")
                {
                }
                field("Total Unposted Voyage Exp(ACY)"; Rec."Total Unposted Voyage Exp(ACY)")
                {
                }
                field("Total Unposted Load Exp. (ACY)"; Rec."Total Unposted Load Exp. (ACY)")
                {
                }
                field("Total Voyage Revenue (ACY)"; Rec."Total Voyage Revenue (ACY)")
                {
                }
                field("Total Voyage Expense (ACY)"; Rec."Total Voyage Expense (ACY)")
                {
                }
                field("Total Load Expense (ACY)"; Rec."Total Load Expense (ACY)")
                {
                }
                field("Total Fuel Cost (ACY)"; Rec."Total Fuel Cost (ACY)")
                {
                }
                field("Total Hire Cost (ACY)"; Rec."Total Hire Cost (ACY)")
                {
                }
                field(ProfitLossACY; Rec."Total Voyage Revenue (ACY)" - Rec."Total Voyage Expense (ACY)" + Rec."Total Fuel Cost (ACY)" + Rec."Total Hire Cost (ACY)" - Rec."Total Load Expense (ACY)")
                {
                    Caption = 'Profit/Loss (ACY)';
                    ToolTip = 'Specifies the value of the Profit/Loss (ACY) field.';
                    StyleExpr = StyleTxt;
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(NewEmptyTrip)
            {
                ApplicationArea = All;
                Caption = 'New Empty Trip';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the New Empty Trip action.';
                Image = "8ball";
                PromotedOnly = true;

                trigger OnAction()
                var
                    VoyageHeader: Record "Voyage Header ERK";
                    //NoSeries: Codeunit NoSeriesManagement;
                    NoSeries: Codeunit "No. Series";
                begin
                    ErkHoldingSetup.GetRecordOnce();
                    VoyageHeader.Init();
                    VoyageHeader."No." := NoSeries.GetNextNo(ErkHoldingSetup."Empty Trip No Series", WorkDate(), true);
                    VoyageHeader."Empty Trip" := true;
                    VoyageHeader.Insert(true);
                    Page.Run(Page::"Voyage Card ERK", VoyageHeader);
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        StyleTxt := Format(SetStyle());
    end;

    procedure SetStyle() Style: PageStyle
    var
        LocalProfitLossACY: Decimal;
    begin
        LocalProfitLossACY := Rec."Total Voyage Revenue (ACY)" - Rec."Total Voyage Expense (ACY)" + Rec."Total Fuel Cost (ACY)" + Rec."Total Hire Cost (ACY)" - Rec."Total Load Expense (ACY)";
        if LocalProfitLossACY <= 0 then
            exit(PageStyle::Unfavorable)
        else
            exit(PageStyle::Favorable);
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        StyleTxt: Text;
}
