page 60081 "Car Carrier Order Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Car Carrier Order Subpage';
    PageType = ListPart;
    SourceTable = "Car Carrier Order Line ERK";
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Load Type"; Rec."Load Type")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Pre-Load Quantity"; Rec."Pre-Load Quantity")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field(Description; Rec.Description)
                {
                }
                field("Planned Car Carrier No."; Rec."Planned Car Carrier No.")
                {
                    trigger OnLookup(var Text: Text): Boolean
                    var
                        CarCarrierHeader: Record "Car Carrier Header ERK";
                    begin
                        CarCarrierHeader.SetFilter(Status, '<>Completed');
                        if Page.RunModal(Page::"Car Carrier List ERK", CarCarrierHeader) = Action::LookupOK then
                            Rec.Validate("Planned Car Carrier No.", CarCarrierHeader."No.");
                    end;
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field(Status; Rec.Status)
                {
                    Editable = false;
                }
                field("Serial No."; Rec."Serial No.")
                {
                    Editable = (Rec."Pre-Load Quantity" = 1) and SerialNoEditable;
                    //ShowMandatory = Rec."Pre-Load Quantity" = 1;
                }
                field("Carline Code"; Rec."Model Code")
                {
                    Visible = ModelBrandVisible;
                    Editable = ModelEditable;
                }
                field("Brand Code"; Rec."Brand Code")
                {
                    Editable = false;
                    Visible = ModelBrandVisible;
                }
                field("Width (cm)"; Rec."Width (cm)")
                {
                    Editable = UoMEditable;
                    // Visible = OrderTypeSpotVisible;
                }
                field("Length (cm)"; Rec."Length (cm)")
                {
                    Editable = UoMEditable;
                    // Visible = OrderTypeSpotVisible;
                }
                field("Height (cm)"; Rec."Height (cm)")
                {
                    Editable = UoMEditable;
                    // Visible = OrderTypeSpotVisible;
                }
                field("Unit Area (m2)"; Rec."Unit Area (m2)")
                {
                    Editable = false;
                    // Visible = OrderTypeSpotVisible;
                }
                field("Line Area (m2)"; Rec."Line Area (m2)")
                {
                    Editable = false;
                    // Visible = OrderTypeSpotVisible;
                }
                field("Unit Volume (m3)"; Rec."Unit Volume (m3)")
                {
                    Editable = false;
                    // Visible = OrderTypeSpotVisible;
                }
                field("Line Volume (m3)"; Rec."Line Volume (m3)")
                {
                    Editable = false;
                    // Visible = OrderTypeSpotVisible;
                }
                field("Unit Gross Weight (kg)"; Rec."Unit Gross Weight (kg)")
                {
                    Editable = GrossWeightEditable;
                    // Visible = OrderTypeSpotVisible;
                }
                field("Line Gross Weight (kg)"; Rec."Line Gross Weight (kg)")
                {
                    Editable = false;
                    // Visible = OrderTypeSpotVisible;
                }
                // field("Est. Revenue Amount"; Rec."Est. Revenue Amount")
                // {
                //     ToolTip = 'Specifies the value of the Est. Revenue Amount field.';
                // }
                // field("Est. Revenue Currency Code"; Rec."Est. Revenue Currency Code")
                // {
                //     ToolTip = 'Specifies the value of the Est. Revenue Currency Code field.';
                // }
                // field("Est. Fuel Cost Amount"; Rec."Est. Fuel Cost Amount")
                // {
                //     ToolTip = 'Specifies the value of the Est. Fuel Cost Amount field.';
                // }
                // field("Est. Fuel Cost Currency Code"; Rec."Est. Fuel Cost Currency Code")
                // {
                //     ToolTip = 'Specifies the value of the Est. Fuel Cost Currency Code field.';
                // }
                // field("Est. Hire Cost Amount"; Rec."Est. Hire Cost Amount")
                // {
                //     ToolTip = 'Specifies the value of the Est. Hire Cost Amount field.';
                // }
                // field("Est. Hire Cost Currency Code"; Rec."Est. Hire Cost Currency Code")
                // {
                //     ToolTip = 'Specifies the value of the Est. Hire Cost Currency Code field.';
                // }
                // field("Est. Other Cost Amount"; Rec."Est. Other Cost Amount")
                // {
                //     ToolTip = 'Specifies the value of the Est. Other Cost Amount field.';
                // }
                // field("Est. Other Cost Currency Code"; Rec."Est. Other Cost Currency Code")
                // {
                //     ToolTip = 'Specifies the value of the Est. Other Cost Currency Code field.';
                // }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(LoadDetails)
            {
                Caption = 'Load Details';
                Image = LinesFromJob;
                ToolTip = 'Executes the Load Details action.';

                trigger OnAction()
                var
                    CarCarrierOrderLineDtlRec: Record "Car Carrier Order Line Dtl ERK";
                    CarCarrierOrderLineDtl: Page "Car Carrier Order Line Dtl ERK";
                begin
                    Rec.TestField("Planned Car Carrier No.");
                    CarCarrierOrderLineDtlRec.SetRange("Document No.", Rec."Document No.");
                    CarCarrierOrderLineDtlRec.SetRange("Planned Car Carrier No.", Rec."Planned Car Carrier No.");
                    CarCarrierOrderLineDtl.SetPlannedCarCarrierNo(Rec."Planned Car Carrier No.");
                    CarCarrierOrderLineDtl.SetTableView(CarCarrierOrderLineDtlRec);
                    CarCarrierOrderLineDtl.RunModal();
                end;
            }
            action(DocAttach)
            {
                ApplicationArea = Basic, Suite;
                Caption = 'Attachments';
                Image = Attach;
                ToolTip = 'Add a file as an attachment. You can attach images as well as documents.';

                trigger OnAction()
                var
                    DocumentAttachmentDetails: Page "Document Attachment Details";
                    RecRef: RecordRef;
                begin
                    RecRef.GetTable(Rec);
                    DocumentAttachmentDetails.OpenForRecRef(RecRef);
                    DocumentAttachmentDetails.RunModal();
                end;
            }
            group("Page")
            {
                Caption = 'Page';

                action(EditInExcel)
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Edit in Excel';
                    Image = Excel;
                    //Visible = IsSaaSExcelAddinEnabled;
                    ToolTip = 'Send the data in the sub page to an Excel file for analysis or editing.';
                    //AccessByPermission = system "Allow Action Export To Excel" = X;

                    trigger OnAction()
                    var
                        EditinExcel: Codeunit "Edit in Excel";
                        EditinExcelFilters: Codeunit "Edit in Excel Filters";
                        ExcelFileNameTxt: Label 'Car Carrier Order %1 - Lines', Comment = '%1 = document number, ex. 10000';
                    begin
                        EditinExcelFilters.AddFieldV2('Document_No',
                                                    Enum::"Edit in Excel Filter Type"::Equal,
                                                    Rec."Document No.",
                                                    Enum::"Edit in Excel Edm Type"::"Edm.String");

                        EditinExcel.EditPageInExcel(
                            'Car_Carrier_Order_Line',
                            Page::"Car Carrier Order Subpage ERK",
                            EditinExcelFilters,
                            StrSubstNo(ExcelFileNameTxt, Rec."Document No."));
                    end;
                }
            }
        }
    }
    trigger OnInit()
    begin
        //CarCarrierOrderHeader.Get(Rec."Document No.");
        //OrderTypeSpotVisible := true;
    end;

    trigger OnAfterGetRecord()
    begin
        Rec.CalcFields(Type, Vehicle);
        if (Rec.Type = Rec.Type::Spot) and (Rec.Vehicle) then begin
            ModelBrandVisible := true;
            UoMEditable := false;
            GrossWeightEditable := false;
            ModelEditable := true;
            SerialNoEditable := true;
        end
        else
            if (Rec.Type = Rec.Type::Spot) and (not Rec.Vehicle) then begin
                ModelBrandVisible := true;
                UoMEditable := true;
                GrossWeightEditable := true;
                ModelEditable := false;
                SerialNoEditable := false;
            end
            else
                if (Rec.Type = Rec.Type::Contract) and Rec.Vehicle then begin
                    ModelBrandVisible := false;
                    UoMEditable := false;
                    GrossWeightEditable := false;
                    ModelEditable := true;
                    SerialNoEditable := true;
                end
                else
                    if (Rec.Type = Rec.Type::Contract) and not Rec.Vehicle then begin
                        ModelBrandVisible := false;
                        UoMEditable := true;
                        GrossWeightEditable := true;
                        ModelEditable := true;
                        SerialNoEditable := false;
                    end;
    end;

    var
        ModelBrandVisible: Boolean;
        UoMEditable: Boolean;
        GrossWeightEditable: Boolean;
        ModelEditable: Boolean;
        SerialNoEditable: Boolean;
}
