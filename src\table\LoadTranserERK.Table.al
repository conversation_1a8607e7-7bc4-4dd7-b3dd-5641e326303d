table 60025 "Load Transer ERK"
{
    Caption = 'Load Transer';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "From Location Code"; Code[10])
        {
            Caption = 'From Location Code';
            ToolTip = 'Specifies the value of the From Location Code field.';
        }
        field(3; "From Bin Code"; Code[20])
        {
            Caption = 'From Bin Code';
            ToolTip = 'Specifies the value of the From Bin Code field.';
        }
        field(4; "From Bill-to Customer No."; Code[20])
        {
            Caption = 'From Bill-to Customer No.';
            ToolTip = 'Specifies the value of the From Bill-to Customer No. field.';
        }
        field(5; "From Load Owner No."; Code[20])
        {
            Caption = 'From Load Owner No.';
            ToolTip = 'Specifies the value of the From Load Owner No. field.';
        }
        field(6; "From Operation Line No."; Integer)
        {
            Caption = 'From Operation Line No.';
            ToolTip = 'Specifies the value of the From Operation Line No. field.';
        }
        field(7; "To Location Code"; Code[10])
        {
            Caption = 'To Location Code';
            TableRelation = Location;
            ToolTip = 'Specifies the value of the To Location Code field.';
        }
        field(8; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("To Location Code"));
            ToolTip = 'Specifies the value of the To Bin Code field.';
        }
        field(9; "To Bill-to Customer No."; Code[20])
        {
            Caption = 'To Bill-to Customer No.';
            TableRelation = Customer;
            ToolTip = 'Specifies the value of the To Bill-to Customer No. field.';
        }
        field(10; "To Load Owner No."; Code[20])
        {
            Caption = 'To Load Owner No.';
            TableRelation = Customer;
            ToolTip = 'Specifies the value of the To Load Owner No. field.';
            // trigger OnValidate()
            // var
            //     PortOperationLine: Record "Port Operation Line ERK";
            // //PortOperationLineDetail: Record "Port Operation Line Detail ERK";
            // begin
            //     PortOperationLine.SetRange("Document No.", Rec."Operation No.");
            //     PortOperationLine.SetRange("Customer No.", Rec."To Load Owner No.");
            //     PortOperationLine.SetRange("Variant Code", Rec."Variant Code");
            //     PortOperationLine.FindFirst();
            //     //Rec."To Operation Line No." := PortOperationLine."Line No.";
            // end;
        }
        // field(11; "To Operation Line No."; Integer)
        // {
        //     Caption = 'To Operation Line No.';
        // }
        field(12; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(13; "Operation No."; Code[20])
        {
            Caption = 'Operation No.';
            AllowInCustomizations = Never;
        }
        field(14; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            AllowInCustomizations = Never;
        }
        field(15; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            AllowInCustomizations = Never;
        }
        field(16; "From Prt Opr Line Dtl Line No."; Integer)
        {
            Caption = 'From Port Operation Line Detail Line No.';
            AllowInCustomizations = Never;
        }
        field(17; "To Load Owner Name"; Text[100])
        {
            Caption = 'To Load Owner Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("To Load Owner No.")));
            AllowInCustomizations = Never;
        }
        field(18; "To Bill-to Customer Name"; Text[100])
        {
            Caption = 'To Bill-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("To Bill-to Customer No.")));
            AllowInCustomizations = Never;
        }
        field(11; "To Contract No."; Code[20])
        {
            Caption = 'To Contract No.';
            ToolTip = 'Specifies the value of the To Contract No. field.';
            TableRelation = "Port Operation Contract Header";
        }

    }
    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }
}
