//gerekirse açılacak //Yıldız Özdemir

// page 60144 "EBA Purchase Orders API ERK"
// {
//     APIVersion = 'v1.0';
//     EntityCaption = 'Purchase Order';
//     EntitySetCaption = 'Purchase Orders';
//     ChangeTrackingAllowed = true;
//     DelayedInsert = true;
//     EntityName = 'EBApurchaseOrderERK';
//     EntitySetName = 'EBApurchaseOrdersERK';
//     ODataKeyFields = SystemId;
//     PageType = API;
//     SourceTable = "Purchase Header";
//     SourceTableView = where("Document Type" = const(Order));
//     Extensible = false;
//     APIPublisher = 'infotek';
//     APIGroup = 'eh';

//     layout
//     {
//         area(Content)
//         {
//             repeater(Group)
//             {
//                 field(id; Rec.SystemId)
//                 {
//                     Caption = 'Id';
//                     Editable = false;


//                 }
//                 field(number; Rec."No.")
//                 {
//                     Caption = 'No.';
//                     Editable = false;
//                 }
//                 field(orderDate; Rec."Document Date")
//                 {
//                     Caption = 'Order Date';


//                 }
//                 field(postingDate; Rec."Posting Date")
//                 {
//                     Caption = 'Posting Date';


//                 }

//                 field(vendorNumber; Rec."Buy-from Vendor No.")
//                 {
//                     Caption = 'Vendor No.';


//                 }
//                 field(vendorName; Rec."Buy-from Vendor Name")
//                 {
//                     Caption = 'Vendor Name';
//                     Editable = false;
//                 }
//                 field(payToName; Rec."Pay-to Name")
//                 {
//                     Caption = 'Pay-to Name';
//                     Editable = false;
//                 }

//                 field(payToVendorNumber; Rec."Pay-to Vendor No.")
//                 {
//                     Caption = 'Pay-to Vendor No.';

//                 }
//                 field(shipToName; Rec."Ship-to Name")
//                 {
//                     Caption = 'Ship-to Name';
//                 }
//                 field(shipToContact; Rec."Ship-to Contact")
//                 {
//                     Caption = 'Ship-to Contact';
//                 }
//                 field(buyFromAddressLine1; Rec."Buy-from Address")
//                 {
//                     Caption = 'Buy-from Address Line 1';


//                 }
//                 field(buyFromAddressLine2; Rec."Buy-from Address 2")
//                 {
//                     Caption = 'Buy-from Address Line 2';


//                 }
//                 field(buyFromCity; Rec."Buy-from City")
//                 {
//                     Caption = 'Buy-from City';


//                 }
//                 field(buyFromCountry; Rec."Buy-from Country/Region Code")
//                 {
//                     Caption = 'Buy-from Country/Region Code';


//                 }
//                 field(buyFromState; Rec."Buy-from County")
//                 {
//                     Caption = 'Buy-from State';


//                 }
//                 field(buyFromPostCode; Rec."Buy-from Post Code")
//                 {
//                     Caption = 'Buy-from Post Code';


//                 }
//                 field(payToAddressLine1; Rec."Pay-to Address")
//                 {
//                     Caption = 'Pay-to Address Line 1';
//                     Editable = false;
//                 }
//                 field(payToAddressLine2; Rec."Pay-to Address 2")
//                 {
//                     Caption = 'Pay-to Address Line 2';
//                     Editable = false;
//                 }
//                 field(payToCity; Rec."Pay-to City")
//                 {
//                     Caption = 'Pay-to City';
//                     Editable = false;
//                 }
//                 field(payToCountry; Rec."Pay-to Country/Region Code")
//                 {
//                     Caption = 'Pay-to Country/Region Code';
//                     Editable = false;
//                 }
//                 field(payToState; Rec."Pay-to County")
//                 {
//                     Caption = 'Pay-to State';
//                     Editable = false;
//                 }
//                 field(payToPostCode; Rec."Pay-to Post Code")
//                 {
//                     Caption = 'Pay-to Post Code';
//                     Editable = false;
//                 }
//                 field(shipToAddressLine1; Rec."Ship-to Address")
//                 {
//                     Caption = 'Ship-to Address Line 1';


//                 }
//                 field(shipToAddressLine2; Rec."Ship-to Address 2")
//                 {
//                     Caption = 'Ship-to Address Line 2';


//                 }
//                 field(shipToCity; Rec."Ship-to City")
//                 {
//                     Caption = 'Ship-to City';


//                 }
//                 field(shipToCountry; Rec."Ship-to Country/Region Code")
//                 {
//                     Caption = 'Ship-to Country/Region Code';


//                 }
//                 field(shipToState; Rec."Ship-to County")
//                 {
//                     Caption = 'Ship-to State';

//                 }
//                 field(shipToPostCode; Rec."Ship-to Post Code")
//                 {
//                     Caption = 'Ship-to Post Code';


//                 }
//                 field(shortcutDimension1Code; Rec."Shortcut Dimension 1 Code")
//                 {
//                     Caption = 'Shortcut Dimension 1 Code';


//                 }
//                 field(shortcutDimension2Code; Rec."Shortcut Dimension 2 Code")
//                 {
//                     Caption = 'Shortcut Dimension 2 Code';


//                 }
//                 field(currencyCode; Rec."Currency Code")
//                 {
//                     Caption = 'Currency Code';
//                 }


//                 field(pricesIncludeTax; Rec."Prices Including VAT")
//                 {
//                     Caption = 'Prices Include Tax';


//                 }

//                 field(purchaser; Rec."Purchaser Code")
//                 {
//                     Caption = 'Purchaser';


//                 }
//                 field(requestedReceiptDate; Rec."Requested Receipt Date")
//                 {
//                     Caption = 'Requested Receipt Date';


//                 }
//                 part(purchaseOrderLines; "EBA Purch Order Lines API ERK")
//                 {
//                     Caption = 'Lines';
//                     EntityName = 'EBApurchaseOrderLineERK';
//                     EntitySetName = 'EBApurchaseOrderLinesERK';
//                     SubPageLink = "Document Type" = field("Document Type"), "Document No." = FIELD("No.");
//                 }
//                 field(discountAmount; Rec."Invoice Discount Amount")
//                 {
//                     Caption = 'Discount Amount';

//                 }
//                 field(yourReference; Rec."Your Reference")
//                 {
//                     Caption = 'Your Reference';
//                 }
//                 field(totalAmountExcludingTax; Rec.Amount)
//                 {
//                     Caption = 'Total Amount Excluding Tax';
//                     Editable = false;
//                 }

//                 field(totalAmountIncludingTax; Rec."Amount Including VAT")
//                 {
//                     Caption = 'Total Amount Including Tax';
//                     Editable = false;

//                 }
//                 field(fullyReceived; Rec."Completely Received")
//                 {
//                     Caption = 'Fully Received';

//                 }
//                 field(status; Rec.Status)
//                 {
//                     Caption = 'Status';
//                     Editable = false;
//                 }
//                 field(lastModifiedDateTime; Rec.SystemModifiedAt)
//                 {
//                     Caption = 'Last Modified Date';
//                     Editable = false;
//                 }


//             }
//         }
//     }

//     actions
//     {
//     }

//     trigger OnNewRecord(BelowxRec: Boolean)
//     begin
//         Rec."Document Type" := Rec."Document Type"::Order;

//     end;

// }