page 60036 "Port Operation List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Port Operations';
    PageType = List;
    SourceTable = "Port Operation Header ERK";
    UsageCategory = Lists;
    CardPageId = "Port Operation Card ERK";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Third Party"; Rec."Third Party")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Port Code"; Rec."Port Code")
                {
                }
                // field("Starting Date"; Rec."Starting Date")
                // {
                //     ToolTip = 'Specifies the value of the Starting Date field.';
                // }
                field("Port Name"; Rec."Port Name")
                {
                }
                field("Actual Departure Time"; Rec."Actual Departure Time")
                {
                }
                field("Actual Docking Time"; Rec."Actual Docking Time")
                {
                }
                field("Declared Quantity (KG)"; Rec."Declared Quantity (Ton)")
                {
                }
                field("Operation Ending Time"; Rec."Operation Ending Time")
                {
                }
                field("Operation Starting Time"; Rec."Operation Starting Time")
                {
                }
                field("Deparment Name"; Rec."Deparment Name")
                {
                }
                field("Common Operation"; Rec."Common Operation")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }
}
