table 60027 "Loading/Discharging Worksheet"
{
    Caption = 'Loading/Discharging Worksheet';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Never;
            TableRelation = "Car Carrier Header ERK"."No.";
        }
        field(2; "Line No."; Integer)
        {
            AllowInCustomizations = Never;
            Caption = 'Line No.';
        }
        field(3; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(4; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            AllowInCustomizations = Never;
        }
        field(5; "Loading Port"; Code[10])
        {
            Caption = 'Loading Port';
            AllowInCustomizations = Never;
        }
        field(7; "Operation Date-Time"; DateTime)
        {
            Caption = 'Operation Date-Time';
            AllowInCustomizations = Never;
        }
        field(8; "Model Version Code"; Code[100])
        {
            Caption = 'Model Version Code';
            ToolTip = 'Specifies the value of the Carline Code field.';
            //TableRelation = "Model ERK".Code;
        }
        field(9; "Discharge Port"; Code[10])
        {
            Caption = 'Discharge Port';
            AllowInCustomizations = Never;
        }
        field(10; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            AllowInCustomizations = Never;
        }
        field(11; "Document Line Detail No."; Integer)
        {
            Caption = 'Document Line Detail No.';
            AllowInCustomizations = Never;
        }
        field(12; "Do Not Create Discharge Order"; Boolean)
        {
            Caption = 'Do Not Create Discharge Order';
            AllowInCustomizations = Never;
        }
        field(13; "Truck Plate ERK"; Code[10])
        {
            Caption = 'Truck Plate';
            ToolTip = 'Specifies the truck plate used for loading this vehicle.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
}
