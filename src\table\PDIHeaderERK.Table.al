table 60035 "PDI Header ERK"
{
    DataClassification = CustomerContent;
    Caption = 'PDI Header';
    DrillDownPageId = "PDI Documents ERK";
    LookupPageId = "PDI Documents ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                //NoSeries: Codeunit NoSeriesManagement;
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."PDI No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            TableRelation = "Serial No. Information"."Serial No.";
            Editable = false;
            ToolTip = 'Specifies the value of the Serial No. field.';
            trigger OnValidate()
            var
                SerialNoInformation: Record "Serial No. Information";
            begin
                SerialNoInformation.SetRange("Serial No.", Rec."Serial No.");
                if SerialNoInformation.FindFirst() then
                    // Rec."Model Code" := SerialNoInformation."Model Code ERK";
                    // Rec."Brand Code" := SerialNoInformation."Brand Code ERK";
                    // //Rec."Fuel Type" := SerialNoInformation."Fuel Type ERK";
                    // Rec."Color Name" := SerialNoInformation."Colour Name ERK";
                    Rec."Location Code" := SerialNoInformation."Current Location Code ERK";
            end;
        }
        field(3; "Operation Starting Date-Time"; DateTime)
        {
            Caption = 'Operation Starting Date-Time';
            ToolTip = 'Specifies the value of the Operation Starting Date-Time field.';
        }
        field(4; "Operation Ending Date-Time"; DateTime)
        {
            Caption = 'Operation Ending Date-Time';
            ToolTip = 'Specifies the value of the Operation Ending Date-Time field.';
        }
        field(5; "Responsible Name"; Code[50])
        {
            Caption = 'Responsible Name';
            ToolTip = 'Specifies the value of the Assigned User ID field.';
            TableRelation = "Port User ERK"."No.";
        }
        field(6; "Brand Code"; Code[10])
        {
            Caption = 'Brand Code';
            ToolTip = 'Specifies the value of the Manufacturer Code field.';
            TableRelation = Manufacturer.Code;
        }
        field(7; "Model Code"; Code[40])
        {
            Caption = 'Model Code';
            TableRelation = "Model ERK".Code where("Brand Code" = field("Brand Code"));
            ToolTip = 'Specifies the value of the Carline Code field.';
        }
        field(8; "Color Name"; Code[100])
        {
            Caption = 'Color Name';
            ToolTip = 'Specifies the value of the Color Name field.';
        }
        field(9; "Fuel Type"; Enum "Fuel Type ERK")
        {
            Caption = 'Fuel Type';
            ToolTip = 'Specifies the value of the Fuel Type field.';
        }
        field(10; Damaged; Boolean)
        {
            Caption = 'Damaged';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = exist("Document Attachment" where("No." = field("Serial No."), "Reason Code ERK" = filter(<> '')));
            ToolTip = 'Specifies the value of the Damaged field.';
        }
        field(11; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the value of the Line No. field.';
            TableRelation = Integer where(Number = filter(1 .. 10));
        }
        field(12; Saved; Boolean)
        {
            Caption = 'Saved';
            ToolTip = 'Specifies the value of the Saved field.';
            Editable = false;
            trigger OnValidate()
            begin
                if Saved then begin
                    Rec."Saved Atleast Once" := true; // Set when document is saved
                    Rec.Validate("Operation Ending Date-Time", CurrentDateTime());
                    PDIManagement.CreateAndSendPDIExitVehicleTransferDocument(Rec);
                end;
            end;
        }
        field(13; Notes; Text[250])
        {
            Caption = 'Notes';
            ToolTip = 'Specifies the value of the Notes field.';
        }
        field(14; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Location Code field.';
            Editable = false;
        }
        field(15; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Location Code"), "PDI Area ERK" = const(true));
            ToolTip = 'Specifies the value of the To Bin Code field.';
        }
        field(16; "Saved Atleast Once"; Boolean)
        {
            Caption = 'Saved Atleast Once';
            ToolTip = 'Specifies if the document has been saved at least once.';
            Editable = false;
            AllowInCustomizations = Always;
            DataClassification = SystemMetadata;
        }

        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("PDI No. Series");
            "No. Series" := ErkHoldingSetup."PDI No. Series";
            if NoSeries.AreRelated(ErkHoldingSetup."PDI No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;

        Rec."Operation Starting Date-Time" := CurrentDateTime();
    end;

    trigger OnDelete()
    var
        PDILine: Record "PDI Line ERK";
    begin
        Rec.TestField(Saved, false);

        PDILine.SetRange("Document No.", Rec."No.");
        PDILine.DeleteAll(true);
    end;

    var
        PDIManagement: Codeunit "PDI Management ERK";
}
