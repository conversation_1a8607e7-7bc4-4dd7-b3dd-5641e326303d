query 60010 "Vehicle Transfer Ledger Entry"
{
    Caption = 'Vehicle Transfer Ledger Entry';
    QueryType = Normal;

    elements
    {
        dataitem(VehicleTransferLedgerEntry; "Vehicle Transfer Ledger Entry")
        {
            column(EntryNo; "Entry No.")
            {
            }
            column(FromLocationCode; "From Location Code")
            {
            }
            column(FromBinCode; "From Bin Code")
            {
            }
            column(ToLocationCode; "To Location Code")
            {
            }
            column(ToBinCode; "To Bin Code")
            {
            }
            column(OperationDateTime; "Operation Date-Time")
            {
            }
            column(OperationType; "Operation Type")
            {
            }
            column(SerialNo; "Serial No.")
            {
            }
            column(DocumentNo; "Document No.")
            {
            }
            column(DocumentLineNo; "Document Line No.")
            {
            }
            column(FromBinDescription; "From Bin Description")
            {
            }
            column(ToBinDescription; "To Bin Description")
            {
            }
            column(ShippingAgentCode; "Shipping Agent Code")
            {
            }
            column(T1; T1)
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}