reportextension 60004 "SN Label ERK" extends "SN Label"
{
    dataset
    {
        add("Serial No. Information")
        {
            column(Grupage_No__ERK; "Grupage No. ERK")
            {
            }
            column(Serial_No_ERK; "Serial No.")
            {
            }
            column(Grupage_Ship_to_Name_ERK; "Grupage Ship-to Name ERK")
            {
            }
            column(Grupage_Ship_to_City_ERK; "Grupage Ship-to City ERK")
            {
            }
            column(Model_Code_ERK; "Model Code ERK")
            {
            }
            column(Brand_Code_ERK; "Brand Code ERK")
            {
            }
            column(Current_Location_Code_ERK; "Current Location Code ERK")
            {
            }
            column(Current_Bin_Code_ERK; "Current Bin Code ERK")
            {
            }
            column(Grupage_Bin_Code_ERK; "Grupage Bin Code ERK")
            {
            }
        }
    }
}
