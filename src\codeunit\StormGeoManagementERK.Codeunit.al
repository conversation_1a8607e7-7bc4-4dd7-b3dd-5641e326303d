codeunit 60020 "StormGeo Management ERK"
{
    procedure UpdateROBQuantitiesFromCarCarrierHeader(CarCarrierHeader: Record "Car Carrier Header ERK")
    var
        CarCarrierLine: Record "Car Carrier Line ERK";
        VesselReportSIN: Record "Vessel Report SIN INF";
        PreviousEOSPVesselReportSIN: Record "Vessel Report SIN INF";
    begin
        VesselReportSIN.SetCurrentKey("Event Date ERK");
        VesselReportSIN.SetRange("Vessel Imo", CarCarrierHeader."Ship No.");
        if not VesselReportSIN.FindSet(false) then
            exit;

        CarCarrierLine.SetRange("Document No.", CarCarrierHeader."No.");

        repeat
            case VesselReportSIN."Report Type" of
                'BOSP':
                    begin
                        CarCarrierLine.SetRange("Arrival Date-Time");
                        CarCarrierLine.SetRange("Departure Date-Time", VesselReportSIN."Event Date ERK");
                        if CarCarrierLine.FindFirst() then begin
                            CarCarrierLine.Validate("COSP ROB (HSFO)", VesselReportSIN.TOTAL_BROB_HSFO / 1000);
                            CarCarrierLine.Validate("COSP ROB (IFO)", VesselReportSIN.TOTAL_BROB_IFO / 1000);
                            CarCarrierLine.Validate("COSP ROB (MGO)", VesselReportSIN.TOTAL_BROB_MGO / 1000);
                            CarCarrierLine.Modify(true);
                        end;
                    end;
                'EOSP':
                    begin
                        CarCarrierLine.SetRange("Departure Date-Time");
                        CarCarrierLine.SetRange("Arrival Date-Time", VesselReportSIN."Event Date ERK");
                        if CarCarrierLine.FindFirst() then begin
                            CarCarrierLine.Validate("All Fast ROB (HSFO)", VesselReportSIN.TOTAL_BROB_HSFO / 1000);
                            CarCarrierLine.Validate("All Fast ROB (IFO)", VesselReportSIN.TOTAL_BROB_IFO / 1000);
                            CarCarrierLine.Validate("All Fast ROB (MGO)", VesselReportSIN.TOTAL_BROB_MGO / 1000);

                            CarCarrierLine."Overwrite All Fast ROBs" := true;

                            CarCarrierLine.Validate("EOSP ROB (HSFO)", VesselReportSIN.TOTAL_BROB_HSFO / 1000);
                            CarCarrierLine.Validate("EOSP ROB (IFO)", VesselReportSIN.TOTAL_BROB_IFO / 1000);
                            CarCarrierLine.Validate("EOSP ROB (MGO)", VesselReportSIN.TOTAL_BROB_MGO / 1000);
                            CarCarrierLine.Modify(true);
                        end;
                    end;
                'BUNKERING':
                    begin
                        PreviousEOSPVesselReportSIN.SetCurrentKey("Event Date ERK");
                        PreviousEOSPVesselReportSIN.SetRange("Vessel Imo", CarCarrierHeader."Ship No.");
                        PreviousEOSPVesselReportSIN.SetRange("Report Type", 'EOSP');
                        PreviousEOSPVesselReportSIN.SetFilter("Event Date ERK", '<%1', VesselReportSIN."Event Date ERK");
                        if PreviousEOSPVesselReportSIN.FindLast() then begin
                            CarCarrierLine.SetRange("Departure Date-Time");
                            CarCarrierLine.SetRange("Arrival Date-Time", PreviousEOSPVesselReportSIN."Event Date ERK");

                            if CarCarrierLine.FindFirst() then begin
                                if CarCarrierLine."Overwrite All Fast ROBs" then begin
                                    CarCarrierLine.Validate("All Fast ROB (HSFO)", (VesselReportSIN.TOTAL_BROB_HSFO / 1000) - (VesselReportSIN.TOTAL_LIFT_HSFO / 1000));
                                    CarCarrierLine.Validate("All Fast ROB (IFO)", (VesselReportSIN.TOTAL_BROB_IFO / 1000) - (VesselReportSIN.TOTAL_LIFT_IFO / 1000));
                                    CarCarrierLine.Validate("All Fast ROB (MGO)", (VesselReportSIN.TOTAL_BROB_MGO / 1000) - (VesselReportSIN.TOTAL_LIFT_MGO / 1000));

                                    CarCarrierLine."Overwrite All Fast ROBs" := false;
                                end;

                                CarCarrierLine.Validate("Fueling (HSFO)", VesselReportSIN.TOTAL_LIFT_HSFO / 1000);
                                CarCarrierLine.Validate("Fueling (IFO)", VesselReportSIN.TOTAL_LIFT_IFO / 1000);
                                CarCarrierLine.Validate("Fueling (MGO)", VesselReportSIN.TOTAL_LIFT_MGO / 1000);
                                CarCarrierLine.Modify(true);
                            end;
                        end;
                    end;
                'ARRIVAL':
                    begin
                        PreviousEOSPVesselReportSIN.SetCurrentKey("Event Date ERK");
                        PreviousEOSPVesselReportSIN.SetRange("Vessel Imo", CarCarrierHeader."Ship No.");
                        PreviousEOSPVesselReportSIN.SetRange("Report Type", 'EOSP');
                        PreviousEOSPVesselReportSIN.SetFilter("Event Date ERK", '<%1', VesselReportSIN."Event Date ERK");
                        if PreviousEOSPVesselReportSIN.FindLast() then begin
                            CarCarrierLine.SetRange("Departure Date-Time");
                            CarCarrierLine.SetRange("Arrival Date-Time", PreviousEOSPVesselReportSIN."Event Date ERK");

                            if CarCarrierLine.FindFirst() and CarCarrierLine."Overwrite All Fast ROBs" then begin
                                CarCarrierLine.Validate("All Fast ROB (HSFO)", VesselReportSIN.TOTAL_BROB_HSFO / 1000);
                                CarCarrierLine.Validate("All Fast ROB (IFO)", VesselReportSIN.TOTAL_BROB_IFO / 1000);
                                CarCarrierLine.Validate("All Fast ROB (MGO)", VesselReportSIN.TOTAL_BROB_MGO / 1000);

                                CarCarrierLine."Overwrite All Fast ROBs" := false;
                                CarCarrierLine.Modify(true);
                            end;
                        end;
                    end;
            end;
        until VesselReportSIN.Next() = 0;

        Message('Fuel quantities updated successfully.');
    end;
}