table 60055 "Car Carrier Order Header ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Car Carrier Order Header';
    DrillDownPageId = "Car Carrier Orders ERK";
    LookupPageId = "Car Carrier Orders ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.GetRecordOnce();
                    NoSeries.TestManual(ErkHoldingSetup."Car Carrier Order Nos");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = "Voyage Account ERK"."No." where("Car Carrier Related" = const(true));
            ToolTip = 'Specifies the value of the Customer No. field.';
            trigger OnValidate()
            var
                VoyageAccount: Record "Voyage Account ERK";
                CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
            begin
                if Rec."Customer No." <> xRec."Customer No." then begin
                    CarCarrierOrderLine.SetRange("Document No.", Rec."No.");
                    if not CarCarrierOrderLine.IsEmpty() then
                        Error(CanNotChangeErr, Rec.FieldCaption("Customer No."));
                end;
                if VoyageAccount.Get("Customer No.") then
                    Rec.Validate("Customer Name", VoyageAccount.Name)
                else
                    Rec.Validate("Customer Name", '');
            end;
        }
        field(3; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(4; "Order Date"; Date)
        {
            Caption = 'Order Date';
            ToolTip = 'Specifies the value of the Order Date field.';
            trigger OnValidate()
            begin
                if Rec.Status = Rec.Status::"In Progress" then
                    Rec.TestField("Order Date");
            end;
        }
        field(5; Type; Enum "Car Carrier Order Type ERK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
            trigger OnValidate()
            var
                CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
                TypeMustBeSpecifiedErr: Label 'Type must be specified.';
            begin
                if Rec.Status = Rec.Status::"In Progress" then
                    if Rec.Type = Rec.Type::" " then
                        Error(TypeMustBeSpecifiedErr);
                CarCarrierOrderLine.SetRange("Document No.", Rec."No.");
                if not CarCarrierOrderLine.IsEmpty() then
                    Error(CanNotChangeErr, Rec.FieldCaption(Type));
            end;
        }
        field(6; "Contract No."; Code[20])
        {
            Caption = 'Contract No.';
            ToolTip = 'Specifies the value of the Contract No. field.';
        }
        field(7; "Your Reference"; Code[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
        }
        field(8; "Loading Port Code"; Code[10])
        {
            Caption = 'Loading Port Code';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Loading Port Code field.';
            trigger OnValidate()
            begin
                if Rec.Status = Rec.Status::"In Progress" then
                    Rec.TestField("Loading Port Code");
                Rec.CalcFields("Loading Port Description");
            end;
        }
        field(9; "Discharge Port Code"; Code[10])
        {
            Caption = 'Discharge Port Code';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Discharge Port Code field.';
            trigger OnValidate()
            begin
                if Rec.Status = Rec.Status::"In Progress" then
                    Rec.TestField("Discharge Port Code");
                Rec.CalcFields("Discharge Port Description");
            end;
        }
        field(10; "Loading Port Description"; Text[100])
        {
            Caption = 'Loading Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Loading Port Code")));
            ToolTip = 'Specifies the value of the Loading Port Description field.';
        }
        field(11; "Discharge Port Description"; Text[100])
        {
            Caption = 'Discharge Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Discharge Port Code")));
            ToolTip = 'Specifies the value of the Discharge Port Description field.';
        }
        field(12; "Trans Shipment Allowed"; Boolean)
        {
            Caption = 'Transshipment Allowed';
            ToolTip = 'Specifies the value of the Trans Shipment Allowed field.';
            trigger OnValidate()
            begin
                if not Rec."Trans Shipment Allowed" then
                    Rec."Transshipment Port" := '';
            end;
        }
        field(19; "Transshipment Port"; Code[10])
        {
            Caption = 'Transshipment Port';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Transshipment Port field.';
            trigger OnValidate()
            begin
                Rec.TestField("Trans Shipment Allowed", true);
            end;
        }
        field(13; "Ready-to Load Date"; Date)
        {
            Caption = 'Ready-to Load Date';
            ToolTip = 'Specifies the value of the Ready-to Load Date field.';
            trigger OnValidate()
            begin
                if Rec.Status = Rec.Status::"In Progress" then
                    Rec.TestField("Ready-to Load Date");
            end;
        }
        field(14; "Shipment Method Code"; Code[10])
        {
            Caption = 'Shipment Method Code';
            TableRelation = "Shipment Method".Code;
            Editable = false;
            ToolTip = 'Specifies the value of the Shipment Method Code field.';
        }
        field(15; "BoL Type"; Code[10])
        {
            Caption = 'BoL Type';
            ToolTip = 'Specifies the value of the BoL Type field.';
        }
        field(16; Status; Enum "Car Carrier Order Status ERK")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
            trigger OnValidate()
            begin
                case Rec.Status of
                    Rec.Status::" ":
                        ;//Nothing to do here yet.
                    Rec.Status::"In Progress":
                        PendingVoyagePlanningChecks(Rec);
                    Rec.Status::Completed:
                        ;//Nothing to do here yet.
                end;
            end;
        }
        field(17; "Loading Port Shipment Method"; Code[10])
        {
            Caption = 'Loading Port Shipment Method';
            TableRelation = "Shipment Method".Code;
            ToolTip = 'Specifies the value of the Loading Port Shipment Method field.';
        }
        field(18; "Discharge Port Shipment Method"; Code[10])
        {
            Caption = 'Discharge Port Shipment Method';
            TableRelation = "Shipment Method".Code;
            ToolTip = 'Specifies the value of the Discharge Port Shipment Method field.';
        }
        field(20; "Booking No."; Code[20])
        {
            Caption = 'Booking No.';
            ToolTip = 'Specifies the value of the Booking No. field.';
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Car Carrier Order Nos");
            "No. Series" := ErkHoldingSetup."Car Carrier Order Nos";
            if NoSeries.AreRelated(ErkHoldingSetup."Car Carrier Order Nos", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;
        "Order Date" := WorkDate();
    end;

    trigger OnDelete()
    var
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
        CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
    begin
        CarCarrierOrderLine.SetRange("Document No.", Rec."No.");
        CarCarrierOrderLine.DeleteAll(true);
        CarCarrierOrderLineDtl.SetRange("Document No.", Rec."No.");
        CarCarrierOrderLineDtl.DeleteAll(true);
    end;

    var
        CanNotChangeErr: Label 'Cannot change the %1 as lines exist for this order.', Comment = '%1=FieldCaption(Type)';

    procedure PendingVoyagePlanningChecks(CarCarrierOrderHeader: Record "Car Carrier Order Header ERK")
    begin
        CarCarrierOrderHeader.TestField("Customer No.");
        CarCarrierOrderHeader.TestField("Order Date");
        CarCarrierOrderHeader.TestField(Type);
        CarCarrierOrderHeader.TestField("Loading Port Code");
        CarCarrierOrderHeader.TestField("Discharge Port Code");
        CarCarrierOrderHeader.TestField("Ready-to Load Date");
        CarCarrierOrderHeader.TestField("Loading Port Shipment Method");
        CarCarrierOrderHeader.TestField("Discharge Port Shipment Method");

        CarCarrierOrderHeader.TestField("Booking No.");
        CarCarrierOrderHeader.TestField("Your Reference");
    end;
}
