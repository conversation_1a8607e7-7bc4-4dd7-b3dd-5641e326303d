query 60001 "Blanket Sales Header ERK"
{
    Caption = 'Blanket Sales Header';
    QueryType = Normal;

    elements
    {
        dataitem(SalesHeader;
        "Sales Header")
        {
            DataItemTableFilter = "Document Type" = const("Blanket Order");

            column(No;
            "No.")
            {
            }
            column(BilltoName;
            "Bill-to Name")
            {
            }
            column(DocumentDate;
            "Document Date")
            {
            }
            column(Amount;
            Amount)
            {
            }
            column(AmountIncludingVAT;
            "Amount Including VAT")
            {
            }
            column(CurrencyCode;
            "Currency Code")
            {
            }
        }
    }
    trigger OnBeforeOpen()
    begin
    end;
}
