pageextension 60031 "Inbound E-Invoices Ext. ERK" extends "Inbound E-Invoices INF"
{
    layout
    {
        addlast(Group)
        {
            field("EBA Status ERK"; Rec."EBA Status ERK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the EBA Status field.';
                //Editable = false;


                trigger OnValidate()
                var
                    ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
                    ErrLbl: Label 'You do not have permission to change EBA Status.';
                begin
                    if not ErkHoldingBasicFunctions.HasUpdateStatusTypePermissionINF() then
                        Error(ErrLbl);

                end;
            }
        }
    }

    actions
    {
        // Add changes to page actions here
    }
}