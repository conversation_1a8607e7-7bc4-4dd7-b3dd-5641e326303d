page 60027 "Car Carrier Line Details ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Car Carrier Line Details';
    PageType = List;
    SourceTable = "Car Carrier Line Detail ERK";
    UsageCategory = Lists;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Car Carrier Voyage Type"; Rec."Car Carrier Voyage Type")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Order No."; Rec."Order No.")
                {
                    Editable = false;
                }
                field("Load No."; Rec."Order Load Detail Line No.")
                {
                    trigger OnLookup(var Text: Text): Boolean
                    var
                        CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
                        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
                    begin
                        CarCarrierOrderLineDtl.SetRange("Planned Car Carrier No.", Rec."Document No.");
                        if Page.RunModal(Page::"Car Carrier Order Line Dtl ERK", CarCarrierOrderLineDtl) = Action::LookupOK then begin
                            CarCarrierOrderLineDtl.TestField("Customer No.");
                            CarCarrierOrderLineDtl.TestField("Shipper No.");
                            CarCarrierOrderLineDtl.TestField("Consignee No.");
                            CarCarrierOrderLineDtl.TestField("Bill-to Customer No.");

                            Rec.Validate("Order No.", CarCarrierOrderLineDtl."Document No.");
                            Rec.Validate("Order Load Detail Line No.", CarCarrierOrderLineDtl."Line No.");
                            Rec.Validate("Customer No.", CarCarrierOrderLineDtl."Customer No.");
                            Rec.Validate("Shipper No.", CarCarrierOrderLineDtl."Shipper No.");
                            Rec.Validate("Consignee No.", CarCarrierOrderLineDtl."Consignee No.");
                            Rec.Validate("Bill-to Customer No.", CarCarrierOrderLineDtl."Bill-to Customer No.");

                            CarCarrierOrderLine.SetRange("Document No.", Rec."Order No.");
                            CarCarrierOrderLine.ModifyAll(Status, CarCarrierOrderLine.Status::Planned, true);
                        end;
                    end;
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                field("Loading Port Shipment Medhod"; Rec."Loading Port Shipment Method")
                {
                }
                field("Discharge Port Shipment Method"; Rec."Discharge Port Shipment Method")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ShowMandatory = true;
                    Editable = (Rec."Car Carrier Voyage Type" = Rec."Car Carrier Voyage Type"::"Normal - Ferry") or (Rec."Car Carrier Voyage Type" = Rec."Car Carrier Voyage Type"::"Road Transport");

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    Editable = false;
                }
                field("Shipper No."; Rec."Shipper No.")
                {
                    ShowMandatory = true;
                    Editable = (Rec."Car Carrier Voyage Type" = Rec."Car Carrier Voyage Type"::"Normal - Ferry") or (Rec."Car Carrier Voyage Type" = Rec."Car Carrier Voyage Type"::"Road Transport");

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Shipper Name"; Rec."Shipper Name")
                {
                }
                field("Consignee No."; Rec."Consignee No.")
                {
                    ShowMandatory = true;
                    Editable = (Rec."Car Carrier Voyage Type" = Rec."Car Carrier Voyage Type"::"Normal - Ferry") or (Rec."Car Carrier Voyage Type" = Rec."Car Carrier Voyage Type"::"Road Transport");

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Consignee Name"; Rec."Consignee Name")
                {
                }
                field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                {
                    ShowMandatory = true;
                    //Editable = Rec."Car Carrier Voyage Type" = Rec."Car Carrier Voyage Type"::"Normal - Ferry";

                    trigger OnValidate()
                    begin
                        //CurrPage.Update();
                    end;
                }
                field("Bill-to Customer Name"; Rec."Bill-to Customer Name")
                {
                }
                // field(Type; Rec."Type")
                // {
                //     ToolTip = 'Specifies the value of the Type field.';
                //     ShowMandatory = true;
                // }
                field("Loading Port"; Rec."Loading Port")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Loading Port Description"; Rec."Loading Port Description")
                {
                }
                field("Loaded Quantity"; Rec."Loaded Quantity")
                {
                }
                field("Car Carrier Line No."; Rec."Discharge Port Line No.")
                {
                }
                field("Discharge Port"; Rec."Discharge Port")
                {
                    ShowMandatory = true;
                    Editable = false;

                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Discharge Port Description"; Rec."Discharge Port Description")
                {
                }
                field("Discharged Quantity"; Rec."Discharged Quantity")
                {
                }
                // field(Quantity; CarCarrierManagement.CalculateCarCarrierLineDetailQuantity(Rec))
                // {
                //     Caption = 'Quantity';
                //     ToolTip = 'Specifies the value of the Quantity field.';
                //     trigger OnDrillDown()
                //     var
                //     begin
                //         //     VehicleLedgerEntry.SetRange("Document No.", Rec."Document No.");
                //         //     VehicleLedgerEntry.SetRange("Customer No.", Rec."Customer No.");
                //         //     case Rec.Type of
                //         //         Rec.Type::Loading:
                //         //             VehicleLedgerEntry.SetRange("Loading Port", Rec."Loading Port");
                //         //         Rec.Type::Discharge:
                //         //             VehicleLedgerEntry.SetRange("Discharge Port", Rec."Loading Port");
                //         //     end;
                //         //     Page.Run(Page::"Car Carrier Ledger Entries ERK", VehicleLedgerEntry);
                //     end;
                // }
                field("Operation Starting Date-Time"; Rec."Loading Start Date-Time")
                {
                    ShowMandatory = true;
                }
                field("Operation Ending Date-Time"; Rec."Loading End Date-Time")
                {
                    ShowMandatory = true;
                }
                field("Discharge Start Date-Time"; Rec."Discharge Start Date-Time")
                {
                }
                field("Discharge End Date-Time"; Rec."Discharge End Date-Time")
                {
                }
                field("Previous Car Carrier No."; Rec."Previous Car Carrier No.")
                {
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                Caption = 'Attachments';
                Visible = false;
                SubPageLink = "Table ID" = const(Database::"Car Carrier Line Detail ERK"), "No." = field("Document No."), "Line No." = field("Document Line No."), "Line Detail No. ERK" = field("Line No.");
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Load)
            {
                ApplicationArea = All;
                Caption = 'Load Vehicles';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = SalesShipment;
                ToolTip = 'Executes the Load Vehicles action.';

                trigger OnAction()
                var
                    LoadingDischargingWorksheet: Page "Loading/Discharging Worksheet";
                begin
                    Rec.CalcFields("Car Carrier Voyage Type");
                    if Rec."Car Carrier Voyage Type" = Rec."Car Carrier Voyage Type"::"Normal - Ro-Ro" then begin
                        Rec.TestField("Order No.");
                        Rec.TestField("Order Load Detail Line No.");
                    end;
                    Rec.TestField("Loading Start Date-Time");
                    Rec.TestField("Loading End Date-Time");
                    Rec.TestField("Customer No.");
                    Rec.TestField("Consignee No.");
                    Rec.TestField("Shipper No.");
                    Rec.TestField("Bill-to Customer No.");
                    CarCarrierManagement.CheckDatesWithPreviousCarCarrierDocumentFromCarCarrierLineDetail(Rec);
                    //CarCarrierManagement.PopulateCarCarrierTripStartingDateFromCarCarrierLineDetail(Rec);
                    LoadingDischargingWorksheet.SetGlobalVars(Rec."Document No.", Rec."Customer No.", Rec."Loading Port", Rec."Discharge Port", Rec."Loading End Date-Time", Rec."Document Line No.", Rec."Line No.");
                    LoadingDischargingWorksheet.Run();
                end;
            }
            action(Discharge)
            {
                ApplicationArea = All;
                Caption = 'Discharge Vehicles';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = ExportShipment;
                ToolTip = 'Executes the Discharge Vehicles action.';

                trigger OnAction()
                var
                    CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
                begin
                    CurrPage.SetSelectionFilter(CarCarrierLineDetail);
                    CarCarrierLineDetail.FindSet(false);
                    repeat
                        CarCarrierManagement.DischargeSelectedCarCarrierLineDetails(CarCarrierLineDetail);
                    until CarCarrierLineDetail.Next() = 0;
                end;
            }
            action(CancelDischarge)
            {
                ApplicationArea = All;
                Caption = 'Cancel Discharge';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Cancel;
                ToolTip = 'Executes the Cancel Discharge action.';

                trigger OnAction()
                var
                    CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
                begin
                    CurrPage.SetSelectionFilter(CarCarrierLineDetail);
                    CarCarrierManagement.CancelDischargeSelectedCarCarrierLineDetails(CarCarrierLineDetail);
                end;
            }
            action(Attachments)
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                Image = Attach;
                ToolTip = 'Add a file as an attachment. You can attach images as well as documents.';

                trigger OnAction()
                var
                    DocumentAttachmentDetails: Page "Document Attachment Details";
                    RecRef: RecordRef;
                begin
                    RecRef.GetTable(Rec);
                    DocumentAttachmentDetails.OpenForRecRef(RecRef);
                    DocumentAttachmentDetails.RunModal();
                end;
            }
        }
    }
    trigger OnNewRecord(BelowXRec: Boolean)
    begin
        Rec.TestField("Document Line No.");
    end;

    var
        CarCarrierManagement: Codeunit "Car Carrier Management ERK";
}
