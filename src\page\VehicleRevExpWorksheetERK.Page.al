page 60072 "Vehicle Rev/Exp. Worksheet ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle Revenue/Expense Worksheet';
    PageType = Document;
    SourceTable = "Vehicle Rev/Exp. Worksheet Hdr";
    SourceTableTemporary = true;
    UsageCategory = Tasks;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = not Rec."Created From Invoice Line";
                field("Created From Invoice Line"; Rec."Created From Invoice Line")
                {
                }
                field("Invoice No."; Rec."Invoice No.")
                {
                }
                field("Invoice Line No."; Rec."Invoice Line No.")
                {
                }
                field(Type; Rec.Type)
                {
                    ValuesAllowed = 0, 1, 2;
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Port Code"; Rec."Port Code")
                {
                }
                field("Port Description"; Rec."Port Description")
                {
                }
                field("Customer No."; Rec."Source No.")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Customer Name"; Rec."Source Name")
                {
                }
                field("Car Carrier No."; Rec."Car Carrier No.")
                {
                    Visible = false;
                }
                field("Your Reference"; Rec."Your Reference")
                {
                    Editable = Rec.Type = Rec.Type::Revenue;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("External Document No."; Rec."External Document No.")
                {
                    Editable = Rec.Type = Rec.Type::Expense;
                }
                field("Total Amount"; Rec."Total Amount")
                {
                    Editable = Rec.Type = Rec.Type::Expense;
                }
                // field("Total Line Amount"; Rec."Total Line Amount")
                // {
                // }
            }
            part(Lines; "Vehicle Rev/Exp Wksh. Subpage")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }
    actions
    {
        area(Processing)
        {

            action(DistributeTotalAmount)
            {
                Caption = 'Distribute Total Amount';
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Image = AmountByPeriod;
                ToolTip = 'Executes the Distribute Total Amount action.';

                //Visible = Rec.Type = Rec.Type::Expense;
                trigger OnAction()
                var
                    TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line" temporary;
                begin
                    CurrPage.Lines.Page.SetRecord(TempVehicleRevExpWorksheetLine);
                    VehicleRevExpManagement.DistributeTotalAmount(Rec, TempVehicleRevExpWorksheetLine);
                    CurrPage.Lines.Page.UpdateUnitAmount(TempVehicleRevExpWorksheetLine);
                    CurrPage.Update();
                end;
            }
            action(ClearCarCarrierNo)
            {
                Caption = 'Clear Car Carrier No.';
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Image = ClearLog;
                ToolTip = 'Clears the Car Carrier No. field in all worksheet lines.';

                trigger OnAction()
                var
                    TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line" temporary;
                begin
                    CurrPage.Lines.Page.SetRecord(TempVehicleRevExpWorksheetLine);
                    VehicleRevExpManagement.ClearCarCarrierNumbers(TempVehicleRevExpWorksheetLine);
                    CurrPage.Lines.Page.UpdateCarCarrierNo(TempVehicleRevExpWorksheetLine);
                    CurrPage.Update();
                end;
            }
            action(CreateVehicleRevExpenseLines)
            {
                Caption = 'Create Vehicle Rev/Exp Lines';
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Image = LinesFromJob;
                ToolTip = 'Executes the Create Vehicle Rev/Exp Lines action.';

                trigger OnAction()
                var
                    TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line" temporary;
                begin
                    CurrPage.Lines.Page.SetRecord(TempVehicleRevExpWorksheetLine);

                    if Rec."Created From Invoice Line" then
                        VehicleRevExpManagement.CreateVehicleRevenueExpenseLinesFromVehicleRevenueExpenseWorksheet(Rec, TempVehicleRevExpWorksheetLine)
                    else
                        VehicleRevExpManagement.CreateCarCarrierRevenueExpenseLines(Rec, TempVehicleRevExpWorksheetLine);

                    CurrPage.Close();
                end;
            }
        }
    }
    trigger OnOpenPage()
    begin
        if Rec.IsEmpty() then begin
            Rec.Init();
            Rec.Insert(false);
        end;
    end;

    var
        VehicleRevExpManagement: Codeunit "Vehicle Rev./Exp. Management";
}
