codeunit 60001 "Erk Holding Basic Functions"
{
    trigger OnRun()
    begin
    end;

    procedure GetItemVariantDescription(ItemNo: Code[20]; VariantCode: Code[10]): Text[100]
    var
        Item: Record Item;
        ItemVariant: Record "Item Variant";
        Description: Text[100];
    begin
        if Item.Get(ItemNo) then
            Description := Item.Description;

        if VariantCode <> '' then
            if ItemVariant.Get(ItemNo, VariantCode) then
                Description := ItemVariant.Description;

        exit(Description);
    end;

    procedure GetWorkDescription(SalesHeader: Record "Sales Header") WorkDescription: Text
    var
        TypeHelper: Codeunit "Type Helper";
        InStream: InStream;
    begin
        SalesHeader.CalcFields("Work Description");
        SalesHeader."Work Description".CreateInStream(InStream, TextEncoding::UTF8);
        exit(TypeHelper.ReadAsTextWithSeparator(InStream, TypeHelper.LFSeparator()));
    end;

    procedure GetCountryRegionNameInCurrentLanguage(CountryRegionCode: Code[10]): Text[50]
    var
        CountryRegion: Record "Country/Region";
        CountryRegionTranslation: Record "Country/Region Translation";
        Language: Codeunit Language;
    begin
        if not CountryRegion.Get(CountryRegionCode) then
            exit('');
        if CountryRegionTranslation.Get(CountryRegionCode, Language.GetUserLanguageCode()) then
            exit(CountryRegionTranslation.Name);
        exit(CountryRegion.Name);
    end;

    procedure GetCountryOfOriginFromItemNo(ItemNo: Code[20]): Code[10]
    var
        Item: Record Item;
    begin
        if not Item.Get(ItemNo) then
            exit('');
        exit(Item."Country/Region of Origin Code");
    end;

    procedure GetPaymentMethodDescriptionTranslated(PaymentMethodCode: Code[10]; LanguageCode: Code[10]): Text
    var
        PaymentMethod: Record "Payment Method";
        PaymentMethodTranslation: Record "Payment Method Translation";
    begin
        if PaymentMethodTranslation.Get(PaymentMethodCode, LanguageCode) then
            exit(PaymentMethodTranslation.Description);
        if PaymentMethod.Get(PaymentMethodCode) then
            exit(PaymentMethod.Description);
        exit('');
    end;

    procedure GetTranslatedPaymentTermsDescriptionFromCode(PaymentTermsCode: Code[10]; LanguageCode: Code[10]): Text[100]
    var
        PaymentTerms: Record "Payment Terms";
        PaymentTermTranslation: Record "Payment Term Translation";
    begin
        if not PaymentTerms.Get(PaymentTermsCode) then
            exit('');
        if LanguageCode = '' then
            exit(PaymentTerms.Description);
        if not PaymentTermTranslation.Get(PaymentTermsCode, LanguageCode) then
            exit(PaymentTerms.Description);
        exit(PaymentTermTranslation.Description);
    end;

    procedure GetCustomerNameFromCustomerNo(CustomerNo: Code[20]): Text[100]
    var
        Customer: Record Customer;
    begin
        if Customer.Get(CustomerNo) then
            exit(Customer.Name);
        exit('');
    end;

    [EventSubscriber(ObjectType::Table, Database::"VAT Registration No. Format", OnBeforeShowCheckCustMessage, '', false, false)]
    local procedure OnBeforeShowCheckCustMessage(TextString: Text; var IsHandled: Boolean)
    var
        ErrorErr: Label 'This VAT registration number has already been entered for the following customers:\ %1', Comment = ' %1 = Customer No.';
        Error1Err: Label '%1%2', Comment = '%1=ErrorErr; %2=TextString';
    begin
        IsHandled := true;
        Error(Error1Err, ErrorErr, TextString);
    end;

    procedure VerifyIMONumber(IMONumber: Text[7]) Result: Boolean
    var
        Sum: Integer;
        Factor: Integer;
        Digit: Integer;
        CheckDigit: Integer;
        NumberVar: Integer;
    begin
        if (not Evaluate(NumberVar, IMONumber)) or (StrLen(IMONumber) <> 7) then
            exit(false);
        Sum := 0;
        Factor := 7;
        for Digit := 1 to 6 do begin
            Sum += Factor * (IMONumber[Digit] - '0');
            Factor -= 1;
        end;
        CheckDigit := Sum mod 10;
        if CheckDigit = IMONumber[7] - '0' then
            exit(true)
        else
            exit(false);
    end;

    procedure GetTranslatedUnitOfMeasureDescription(UoMCode: Code[10]; LanguageCode: Code[10]): Text[100]
    var
        UnitofMeasure: Record "Unit of Measure";
        UnitofMeasureTranslation: Record "Unit of Measure Translation";
    begin
        if not UnitofMeasure.Get(UoMCode) then
            exit(UoMCode);
        if UnitofMeasureTranslation.Get(UoMCode, LanguageCode) then
            exit(UnitofMeasureTranslation.Description);
        exit(UnitofMeasure.Description);
    end;

    procedure GetEDocumentPackageTypeNameFromCode(EDocumentPackagingTypeCode: Code[10]): Text
    var
        EDocumentPackagingType: Record "E-Document Packaging Type INF";
    begin
        if EDocumentPackagingType.Get(EDocumentPackagingTypeCode) then
            exit(EDocumentPackagingType.Name);
        exit(EDocumentPackagingTypeCode);
    end;

    procedure ShipMandatoryChecks(ShipNo: Code[10]; SkipActiveContractCheck: Boolean)
    var
        Ship: Record "Ship ERK";
        Contract: Record "Contract ERK";
        activeContractNotFoundErr: Label 'Active contract not found for the Ship No.: %1', Comment = '%1=ShipNo';
    begin
        if not Ship.Get(ShipNo) then
            exit;
        Ship.TestField("Ship Type");
        Ship.TestField(Name);

        if not SkipActiveContractCheck then
            if Ship."Ship Type" <> Ship."Ship Type"::Ferry then begin
                Contract.SetRange("Ship No.", ShipNo);
                Contract.SetRange(Active, true);
                if Contract.IsEmpty() then
                    Error(activeContractNotFoundErr, ShipNo);
            end;

        //Ship.TestField("Cargo Type");
    end;

    procedure GetUserNameFromSecurityId(UserSecurityID: Guid): Code[50]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."User Name");
        exit('');
    end;

    procedure GetVendorNameFromVendorNo(VendorNo: Code[20]): Text[100]
    var
        Vendor: Record Vendor;
    begin
        if Vendor.Get(VendorNo) then
            exit(Vendor.Name);
        exit('');
    end;

    procedure UpdateIncomingDocumentCreatedFieldOnEInvoiceHeader(PurchInvHeader: Record "Purch. Inv. Header")
    var
        EInvoiceHeader: Record "E-Invoice Header INF";
        SuccesMsg: Label 'Incoming documents created field updated successfully.';
    begin
        PurchInvHeader.FindSet(false);
        repeat
            EInvoiceHeader.SetRange("E-Invoice No.", PurchInvHeader."Vendor Invoice No.");
            EInvoiceHeader.SetRange("Source No.", PurchInvHeader."Buy-from Vendor No.");
            if EInvoiceHeader.FindFirst() then begin
                EInvoiceHeader.Validate("Created Inbound Document", true);
                EInvoiceHeader.Modify(false);
            end;
        until PurchInvHeader.Next() = 0;
        Message(SuccesMsg);
    end;

    procedure GetBilltoPaytoNameFromVATEntry(VATEntry: Record "VAT Entry"): Text[100]
    var
        Vendor: Record Vendor;
        Customer: Record Customer;
    begin
        case VATEntry.Type of
            VATEntry.Type::Sale:
                if Customer.Get(VATEntry."Bill-to/Pay-to No.") then
                    exit(Customer.Name);
            VATEntry.Type::Purchase:
                if Vendor.Get(VATEntry."Bill-to/Pay-to No.") then
                    exit(Vendor.Name);
        end;
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::NappPyrGLPostingMgt, OnBeforeInsertGenJournalLineOnCreateGenJnlLines, '', false, false)]
    // local procedure NappPyrGLPostingMgt_OnBeforeInsertGenJournalLineOnCreateGenJnlLines(TempGLPostingBuffer: Record NappPyrGLPostingBuffer temporary; var GenJournalLine: Record "Gen. Journal Line")
    // var
    //     SourceCodeELedDocType: Record "Source Code/E-Led Doc Type INF";
    // begin
    //     SourceCodeELedDocType.Get(GenJournalLine."Source Code");
    //     GenJournalLine.Validate("GIB Document Type INF", SourceCodeELedDocType."GIB Document Type INF");
    //     GenJournalLine.Validate("GIB Document Desc. INF", SourceCodeELedDocType."GIB Document Desc. INF");
    //     GenJournalLine.Validate("E-Book Description INF", SourceCodeELedDocType."E-Book Description INF");
    // end;

    procedure GetTariffNoFromItemNo(ItemNo: Code[20]; VariantCode: Code[10]): Code[20]
    var
        Item: Record Item;
        ItemVariant: Record "Item Variant";
    begin
        if (ItemVariant.Get(ItemNo, VariantCode)) and (ItemVariant."Tariff No. ERK" <> '') then
            exit(ItemVariant."Tariff No. ERK");

        if Item.Get(ItemNo) then
            exit(Item."Tariff No.");

        exit('');
    end;

    procedure GetSalesPersonNameFromSalesPersonCode(SalesPersonCode: Code[20]): Text[100]
    var
        SalesPerson: Record "Salesperson/Purchaser";
    begin
        if SalesPerson.Get(SalesPersonCode) then
            exit(SalesPerson.Name);
        exit('');
    end;

    procedure GetRemittoNameFromRemittoCode(VendorNo: Code[20]; RemittoCode: Code[10]): Text[100]
    var
        RemitAddress: Record "Remit Address";
    begin
        if RemitAddress.Get(RemittoCode, VendorNo) then
            exit(RemitAddress.Name);
        exit('');
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Correct Posted Sales Invoice", OnBeforeTestSalesInvoiceHeaderAmount, '', false, false)]
    // local procedure "Correct Posted Sales Invoice_OnBeforeTestSalesInvoiceHeaderAmount"(var SalesInvoiceHeader: Record "Sales Invoice Header"; Cancelling: Boolean; var IsHandled: Boolean)
    // begin
    //     IsHandled := true;
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Customer Templ. Mgt.", OnApplyTemplateOnBeforeCustomerModify, '', false, false)]
    local procedure "Customer Templ. Mgt._OnApplyTemplateOnBeforeCustomerModify"(var Customer: Record Customer; CustomerTempl: Record "Customer Templ."; UpdateExistingValues: Boolean)
    begin
        Customer.Validate("Tax Area Code", CustomerTempl."Tax Area Code ERK");
        Customer.Validate("E-Invoice Profile ID INF", CustomerTempl."E-Invoice Profile ID ERK");
        Customer.Validate("E-Shipment Profile ID INF", CustomerTempl."E-Shipment Profile ID ERK");
        Customer.Validate("VAT Registration No.", CustomerTempl."VAT Registration No. ERK");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Vendor Templ. Mgt.", OnApplyTemplateOnBeforeVendorModify, '', false, false)]
    local procedure "Vendor Templ. Mgt._OnApplyTemplateOnBeforeVendorModify"(var Vendor: Record Vendor; VendorTempl: Record "Vendor Templ."; UpdateExistingValues: Boolean)
    begin
        Vendor.Validate("Tax Area Code", VendorTempl."Tax Area Code ERK");
        Vendor.Validate("E-Invoice Profile ID INF", VendorTempl."E-Invoice Profile ID ERK");
        Vendor.Validate("E-Shipment Profile ID INF", VendorTempl."E-Shipment Profile ID ERK");
        Vendor.Validate("VAT Registration No.", VendorTempl."VAT Registration No. ERK");
    end;

    procedure ConvertDateTimetoYear(MyDateTime: DateTime): Integer
    begin
        exit(MyDateTime.Date().Year());
    end;

    procedure ConvertDateTimeToMonth(MyDateTime: DateTime): Integer
    begin
        exit(MyDateTime.Date().Month());
    end;

    procedure ConvertDateTimeToWeek(MyDateTime: DateTime): Integer
    begin
        exit(MyDateTime.Date().WeekNo());
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Correct Posted Sls.Cr.Memo INF", OnAfterCreateCopyDocument, '', false, false)]
    local procedure "Correct Posted Sls.Cr.Memo INF_OnAfterCreateCopyDocument"(var SalesHeader: Record "Sales Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header")
    begin
        ValidateVATProdPostingGroupFromSalesLine(SalesHeader);
    end;

    local procedure ValidateVATProdPostingGroupFromSalesLine(SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        VATPostingSetup: Record "VAT Posting Setup";
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetFilter("VAT Prod. Posting Group", '<>%1', '');
        if SalesLine.FindSet() then
            repeat
                VATPostingSetup.Get(SalesLine."VAT Bus. Posting Group", SalesLine."VAT Prod. Posting Group");
                SalesLine."Tax Code INF" := VATPostingSetup."Tax Code INF";
                SalesLine.Modify(false);
            until SalesLine.Next() = 0;
    end;


    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnValidatePostingDateOnBeforeAssignDocumentDate, '', false, false)]
    local procedure "Sales Header_OnValidatePostingDateOnBeforeAssignDocumentDate"(var SalesHeader: Record "Sales Header"; var IsHandled: Boolean)
    var
        EArchiveSetup: Record "E-Archive Setup INF";
    begin
        EArchiveSetup.Get();
        if EArchiveSetup."Foreign VATBusPostFilter" = '' then
            exit;

        if SalesHeader."VAT Bus. Posting Group" = EArchiveSetup."Foreign VATBusPostFilter" then
            IsHandled := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Inbound Invoice Management INF", OnAfterSetSourceFieldsInInboundDocument, '', false, false)]
    local procedure "Inbound Invoice Management INF_OnAfterSetSourceFieldsInInboundDocument"(var EInvHeader: Record "E-Invoice Header INF")
    var
        Vendor: Record Vendor;
    begin

        ErkHoldingSetup.Get();
        if ErkHoldingSetup."Inb. E-Inv Vendor Filter" = '' then
            exit;
        EInvoiceSetup.Get();
        EInvoiceSetup.TestField("Invoice Type for Purchase Ret.");
        if not (EInvHeader."Invoice Type Code" = EInvoiceSetup."Invoice Type for Purchase Ret.") then begin


            EInvHeader."Source Type" := EInvHeader."Source Type"::Vendor;

            Vendor.Reset();
            Vendor.SetFilter("No.", ErkHoldingSetup."Inb. E-Inv Vendor Filter");
            Vendor.SetRange("VAT Registration No.", EInvHeader."Supplier ID");
            if not Vendor.FindFirst() then
                exit;

            EInvHeader."Source No." := Vendor."No.";
            //if EInvHeader."VAT Bus.Posting Grp" = '' then
            EInvHeader."VAT Bus.Posting Grp" := Vendor."VAT Bus. Posting Group";
        end;
    end;

    internal procedure HasUpdateStatusTypePermissionINF(): Boolean
    var
        UserSetup: Record "User Setup";
    begin
        if UserSetup.Get(UserId()) then
            exit(UserSetup."Allw. Change EBA Status ERK")
        else
            exit(false);
    end;







    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        EInvoiceSetup: Record "E-Invoice Setup INF";
}
