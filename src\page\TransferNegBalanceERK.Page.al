page 60135 "Transfer Neg. Balance ERK"
{
    Caption = 'Transfer Negative Balance Setup';
    PageType = Card;
    SourceTable = "Transfer Neg. Balance ERK";
    UsageCategory = Administration;
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("Customer Balance Account No."; Rec."Customer Balance Account No.")
                {
                }
                field("Vendor Balance Account No."; Rec."Vendor Balance Account No.")
                {
                }
                field("Journal Template Name"; Rec."Journal Template Name")
                {
                }
                field("Journal Batch Name"; Rec."Journal Batch Name")
                {
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert(true);
        end;
    end;
}