page 60041 "Car Carrier Ledger Entries ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Car Carrier Ledger Entries';
    PageType = List;
    SourceTable = "Car Carrier Ledger Entry ERK";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Document Line Detail No."; Rec."Document Line Detail No.")
                {
                }
                field("Line No."; Rec."Entry No.")
                {
                }
                field("Car Carrier Order No."; Rec."Car Carrier Order No.")
                {
                }
                field("CC Order Load Detail Line No."; Rec."CC Order Load Detail Line No.")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Loading Port"; Rec."Loading Port")
                {
                }
                field("Loading Port Description"; Rec."Loading Port Description")
                {
                }
                field("Loading DateTime"; Rec."Loading DateTime")
                {
                }
                field("Discharge Port Line No."; Rec."Discharge Port Line No.")
                {
                }
                field("Discharge Port"; Rec."Discharge Port")
                {
                }
                field("Discharge Port Description"; Rec."Discharge Port Description")
                {
                }
                field("Discharge DateTime"; Rec."Discharge DateTime")
                {
                }
                field("Loading to Discharge Desc."; Rec."Loading to Discharge Desc.")
                {
                }
                field("From Document No."; Rec."From Document No.")
                {
                }
                field("To Document No."; Rec."To Document No.")
                {
                }
                field("Truck Plate ERK"; Rec."Truck Plate ERK")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(DeleteAll)
            {
                ApplicationArea = All;
                Caption = 'Delete All Entries';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = DeleteQtyToHandle;
                ToolTip = 'Executes the Delete All Entries action.';
                PromotedOnly = true;
                Visible = false;

                trigger OnAction()
                var
                    VehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK";
                begin
                    if Confirm('Do you want to delete all entries?', false) then
                        VehicleLedgerEntry.DeleteAll(true);
                end;
            }
            action(TransferSelectedLines)
            {
                ApplicationArea = All;
                Caption = 'Transfer Selected Lines Another Car Carrier Document';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = TransferOrder;
                ToolTip = 'Executes the Transfer Selected Lines Another Car Carrier Document action.';

                trigger OnAction()
                var
                    VehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK";
                    TransferPopUp: Page "Transfer Pop-Up ERK";
                begin
                    CurrPage.SetSelectionFilter(VehicleLedgerEntry);
                    TransferPopUp.SetVehicleLedgerEntryFilter(VehicleLedgerEntry);
                    TransferPopUp.Run();
                end;
            }
            action(CancelDischarge)
            {
                ApplicationArea = All;
                Caption = 'Cancel Discharge';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = ReopenCancelled;
                ToolTip = 'Executes the Cancel Discharge action.';
                Visible = false;

                trigger OnAction()
                var
                    CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
                begin
                    CurrPage.SetSelectionFilter(CarCarrierLedgerEntry);
                    CarCarrierLedgerEntry.ModifyAll("Discharge DateTime", 0DT, false);
                    Message('%1 Vehicles discharge process has been cancelled.', CarCarrierLedgerEntry.Count());
                end;
            }
        }
    }
}
