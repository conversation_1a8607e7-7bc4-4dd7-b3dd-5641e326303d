table 60015 "Car Carrier Line Detail ERK"
{
    Caption = 'Car Carrier Line Detail';
    DataClassification = CustomerContent;
    DrillDownPageId = "Car Carrier Line Details ERK";
    LookupPageId = "Car Carrier Line Details ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            TableRelation = "Car Carrier Header ERK"."No.";
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document Line No. field.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(4; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = "Voyage Account ERK"."No." where("Car Carrier Related" = const(true));
            ToolTip = 'Specifies the value of the Customer No. field.';
            trigger OnValidate()
            // var
            //     CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
            //     CarCarrierLedgerEntriesUpdatedMsg: Label '%1 Car Carrier Ledger Entries have been updated. New Customer No.: %2 - %3', Comment = '%1="Car Carrier Ledger Entry ERK".Count(); %2="Car Carrier Line Detail ERK"."Customer No."; %3="Car Carrier Line Detail ERK"."Customer Name"';
            begin
                //"Customer Name" := VoyageMangement.GetCustomerNameFromCustomerNo("Customer No.");
                Rec.CalcFields("Loaded Quantity");
                Rec.TestField("Loaded Quantity", 0);
                Rec.CalcFields("Customer Name");
                // Rec.CalcFields("Customer Name");
                // CarCarrierLedgerEntry.SetRange("Document No.", Rec."Document No.");
                // CarCarrierLedgerEntry.SetRange("Discharge Port", Rec."Discharge Port");
                // CarCarrierLedgerEntry.SetRange("Loading Port", Rec."Loading Port");
                // CarCarrierLedgerEntry.SetRange("Customer No.", xRec."Customer No.");
                // Message(CarCarrierLedgerEntriesUpdatedMsg, CarCarrierLedgerEntry.Count(), Rec."Customer No.", Rec."Customer Name");
                // CarCarrierLedgerEntry.ModifyAll("Customer No.", Rec."Customer No.", true);
                // CarCarrierLedgerEntry.ModifyAll("Customer Name", Rec."Customer Name", true);
            end;
        }
        field(5; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Voyage Account ERK".Name where("No." = field("Customer No.")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(6; "Loading Port"; Code[10])
        {
            Caption = 'Loading Port';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Loading Port field.';

            //Editable = false;
            trigger OnValidate()
            begin
                Rec.CalcFields("Loaded Quantity");
                Rec.TestField("Loaded Quantity", 0);
            end;
        }
        field(8; "Loading Start Date-Time"; DateTime)
        {
            Caption = 'Loading Start Date-Time';
            ToolTip = 'Specifies the value of the Operation Starting Date-Time field.';
            trigger OnValidate()
            var
                CarCarrierLine: Record "Car Carrier Line ERK";
            begin
                Rec.CalcFields("Loaded Quantity");
                Rec.TestField("Loaded Quantity", 0);
                if CarCarrierManagement.GetArrivalDateFromCarCarrierLineDetailForLoading(Rec) <> 0DT then
                    if TypeHelper.CompareDateTime("Loading Start Date-Time", CarCarrierManagement.GetArrivalDateFromCarCarrierLineDetailForLoading(Rec)) < 0 then
                        Error(DateErr, Rec.FieldCaption("Loading Start Date-Time"), CarCarrierLine.FieldCaption("Arrival Date-Time"));
                CarCarrierLine.Get(Rec."Document No.", Rec."Document Line No.");
                if TypeHelper.CompareDateTime("Loading Start Date-Time", CarCarrierLine."Departure Date-Time") > 0 then
                    Error(DateErr, CarCarrierLine.FieldCaption("Departure Date-Time"), FieldCaption("Loading Start Date-Time"));
            end;
        }
        field(9; "Loading End Date-Time"; DateTime)
        {
            Caption = 'Loading End Date-Time';
            ToolTip = 'Specifies the value of the Operation Ending Date-Time field.';
            trigger OnValidate()
            var
                TypeHelper: Codeunit "Type Helper";
            begin
                Rec.CalcFields("Loaded Quantity");
                Rec.TestField("Loaded Quantity", 0);
                if TypeHelper.CompareDateTime("Loading End Date-Time", "Loading Start Date-Time") <= 0 then
                    Error(DateErr, FieldCaption("Loading End Date-Time"), FieldCaption("Loading Start Date-Time"));
            end;
        }
        field(10; "Discharge Port"; Code[10])
        {
            Caption = 'Discharge Port';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Discharge Port field.';
        }
        field(11; "Discharge Start Date-Time"; DateTime)
        {
            Caption = 'Discharge Start Date-Time';
            ToolTip = 'Specifies the value of the Discharge Start Date-Time field.';
            trigger OnValidate()
            begin
                if (CarCarrierManagement.GetArrivalDateFromCarCarrierLineDetailForDischarge(Rec) <> 0DT) and (Rec."Discharge Start Date-Time" <> 0DT) then
                    if TypeHelper.CompareDateTime("Discharge Start Date-Time", CarCarrierManagement.GetArrivalDateFromCarCarrierLineDetailForDischarge(Rec)) < 0 then
                        Error(DateErr, Rec.FieldCaption("Discharge Start Date-Time"), CarCarrierLine.FieldCaption("Arrival Date-Time"));

                if TypeHelper.CompareDateTime("Discharge Start Date-Time", "Loading End Date-Time") < 0 then
                    Error(DateErr, FieldCaption("Discharge Start Date-Time"), FieldCaption("Loading End Date-Time"));
            end;
        }
        field(12; "Discharge End Date-Time"; DateTime)
        {
            Caption = 'Discharge End Date-Time';
            ToolTip = 'Specifies the value of the Discharge End Date-Time field.';
            trigger OnValidate()
            var
                TypeHelper: Codeunit "Type Helper";
            begin
                Rec.CalcFields("Discharged Quantity");
                Rec.TestField("Discharged Quantity", 0);
                if TypeHelper.CompareDateTime("Discharge End Date-Time", "Discharge Start Date-Time") <= 0 then
                    Error(DateErr, FieldCaption("Discharge End Date-Time"), FieldCaption("Discharge Start Date-Time"));
            end;
        }
        field(13; "Loaded Quantity"; Integer)
        {
            Caption = 'Loaded Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Document No." = field("Document No."), "Document Line No." = field("Document Line No."), "Document Line Detail No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Loaded Quantity field.';
        }
        field(14; "Discharged Quantity"; Integer)
        {
            Caption = 'Discharged Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Document No." = field("Document No."), "Document Line No." = field("Document Line No."), "Document Line Detail No." = field("Line No."), "Discharge DateTime" = filter(<> '')));
            ToolTip = 'Specifies the value of the Discharged Quantity field.';
        }
        field(7; "Loading Port Description"; Text[100])
        {
            Caption = 'Loading Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Loading Port")));
            ToolTip = 'Specifies the value of the Loading Port Description field.';
        }
        field(15; "Discharge Port Description"; Text[100])
        {
            Caption = 'Discharge Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Discharge Port")));
            ToolTip = 'Specifies the value of the Discharge Port Description field.';
        }
        field(16; Status; Enum "Car Carrier Status ERK")
        {
            Caption = 'Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK".Status where("No." = field("Document No.")));
            AllowInCustomizations = Always;
        }
        field(17; "Bill-to Customer No."; Code[20])
        {
            Caption = 'Bill-to Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Bill-to Customer No. field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Bill-to Customer Name")
            end;
        }
        field(18; "Bill-to Customer Name"; Text[100])
        {
            Caption = 'Bill-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Bill-to Customer No.")));
            ToolTip = 'Specifies the value of the Bill-to Customer Name field.';
        }
        field(19; "Consignee No."; Code[20])
        {
            Caption = 'Consignee No.';
            TableRelation = "Voyage Account ERK"."No." where("Car Carrier Related" = const(true));
            ToolTip = 'Specifies the value of the Consignee No. field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Consignee Name");
            end;
        }
        field(20; "Consignee Name"; Text[100])
        {
            Caption = 'Consignee Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Voyage Account ERK".Name where("No." = field("Consignee No.")));
            ToolTip = 'Specifies the value of the Consignee Name field.';
        }
        field(21; "Shipper No."; Code[20])
        {
            Caption = 'Shipper No.';
            TableRelation = "Voyage Account ERK"."No." where("Car Carrier Related" = const(true));
            ToolTip = 'Specifies the value of the Shipper No. field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Shipper Name");
            end;
        }
        field(22; "Shipper Name"; Text[100])
        {
            Caption = 'Shipper Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Voyage Account ERK".Name where("No." = field("Shipper No.")));
            ToolTip = 'Specifies the value of the Shipper Name field.';
        }
        field(23; "Order No."; Code[20])
        {
            Caption = 'Order No.';
            ToolTip = 'Specifies the value of the Order No. field.';
            trigger OnValidate()
            begin
                CalcFields("Booking No.");
            end;
        }
        field(24; "Order Load Detail Line No."; Integer)
        {
            Caption = 'Order Load Detail Line No.';
            ToolTip = 'Specifies the value of the Load No. field.';
            //TableRelation = "Car Carrier Order Line Dtl ERK"."Line No." where("Planned Car Carrier No." = field("Document No."));

            trigger OnValidate()
            begin
                Rec.CalcFields("Loaded Quantity");
                Rec.TestField("Loaded Quantity", 0);
                if Rec."Order Load Detail Line No." = 0 then begin
                    Rec.Validate("Order No.", '');
                    Rec.Validate("Customer No.", '');
                    Rec.Validate("Shipper No.", '');
                    Rec.Validate("Consignee No.", '');
                    Rec.Validate("Bill-to Customer No.", '');
                end;
                CalcFields("Loading Port Shipment Method", "Discharge Port Shipment Method");
            end;
        }
        field(25; "Discharge Port Line No."; Integer)
        {
            Caption = 'Discharge Port Line No.';
            TableRelation = "Car Carrier Line ERK"."Line No." where("Document No." = field("Document No."));
            ToolTip = 'Specifies the value of the Car Carrier Line No. field.';
            trigger OnValidate()
            var
                CarCarrierLine: Record "Car Carrier Line ERK";
                LineNoErr: Label 'Choosen Discharge Port Car Carrier Line No. must be greater than Car Carrier Line Detail Document Line No.';
            begin
                Rec.CalcFields("Discharged Quantity");
                Rec.TestField("Discharged Quantity", 0);
                if CarCarrierLine.Get(Rec."Document No.", Rec."Discharge Port Line No.") then begin
                    if CarCarrierLine."Line No." < Rec."Document Line No." then
                        Error(LineNoErr);
                    Rec.Validate("Discharge Port", CarCarrierLine."Arrival Port");
                end
                else
                    Rec.Validate("Discharge Port", '');
                Rec.CalcFields("Discharge Port Description");
            end;
        }
        field(26; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ship Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(27; "Voyage Ending Date-Time"; DateTime)
        {
            Caption = 'Voyage Ending Date-Time';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ending Date-Time" where("No." = field("Document No.")));
            AllowInCustomizations = Always;
        }
        field(28; "Total Sales (ACY)"; Decimal)
        {
            Caption = 'Total Sales (ACY)';
            ToolTip = 'Specifies the value of the Total Sales (ACY) field.';
        }
        field(29; "Total Profit (ACY)"; Decimal)
        {
            Caption = 'Total Profit (ACY)';
            ToolTip = 'Specifies the value of the Total Profit (ACY) field.';
        }
        field(30; "Total Expense (ACY)"; Decimal)
        {
            Caption = 'Total Expense (ACY)';
            AllowInCustomizations = Always;
        }
        field(31; Profitability; Decimal)
        {
            Caption = 'Profitability';
            ToolTip = 'Specifies the value of the Profitability field.';
        }
        field(32; Week; Integer)
        {
            Caption = 'Week';
            ToolTip = 'Specifies the value of the Week field.';
        }
        field(33; Month; Integer)
        {
            Caption = 'Month';
            ToolTip = 'Specifies the value of the Month field.';
        }
        field(34; Year; Integer)
        {
            Caption = 'Year';
            ToolTip = 'Specifies the value of the Year field.';
        }
        field(35; "Car Carrier Voyage Type"; Enum "Car Carrier Voyage Type ERK")
        {
            Caption = 'Car Carrier Voyage Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Voyage Type" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Car Carrier Voyage Type field.';
        }
        // field(40; "Ship Type"; Enum "Ship Type ERK")
        // {
        //     Caption = 'Ship Type';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Car Carrier Header ERK"."Ship Type" where("No." = field("Document No.")));
        // }
        field(36; "Loading Port Shipment Method"; Code[10])
        {
            Caption = 'Loading Port Shipment Method';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Loading Port Shipment Method" where("No." = field("Order No.")));
            ToolTip = 'Specifies the value of the Loading Port Shipment Medhod field.';
        }
        field(37; "Discharge Port Shipment Method"; Code[10])
        {
            Caption = 'Discharge Port Shipment Method';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Discharge Port Shipment Method" where("No." = field("Order No.")));
            ToolTip = 'Specifies the value of the Discharge Port Shipment Method field.';
        }
        field(38; "Booking No."; Code[50])
        {
            Caption = 'Booking No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Booking No." where("No." = field("Order No.")));
            ToolTip = 'Specifies the value of the Booking No. field.';
        }
        field(39; "Previous Car Carrier No."; Code[20])
        {
            Caption = 'Previous Car Carrier No.';
            ToolTip = 'Specfies the previous Car Carrier No. for the current Car Carrier No.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
        }
        key(SK; "Document No.", "Document Line No.", "Customer No.", "Loading Port", "Discharge Port", "Consignee No.", "Shipper No.", "Bill-to Customer No.", "Order No.")
        {
            Unique = true;
        }
    }
    trigger OnInsert()
    var
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
    //CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        CarCarrierLineDetail.SetRange("Document No.", Rec."Document No.");
        CarCarrierLineDetail.SetRange("Document Line No.", Rec."Document Line No.");
        if CarCarrierLineDetail.FindLast() then
            Rec."Line No." := CarCarrierLineDetail."Line No." + 10000
        else
            Rec."Line No." := 10000;
        CarCarrierLine.Get(Rec."Document No.", Rec."Document Line No.");
        Rec.Validate("Loading Port", CarCarrierLine."Departure Port");
    end;

    trigger OnModify()
    // var
    //     StatusErr: Label 'You can not modify Car Carrier Line Detail that has been completed.';
    begin
        // Rec.CalcFields(Status);
        // if Rec.Status = Status::Completed then
        //     Error(StatusErr);
    end;

    trigger OnDelete()
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        CarCarrierHeader: Record "Car Carrier Header ERK";
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
    begin
        CarCarrierLedgerEntry.SetRange("Document No.", Rec."Document No.");
        CarCarrierLedgerEntry.SetRange("Document Line No.", Rec."Document Line No.");
        CarCarrierLedgerEntry.SetRange("Document Line Detail No.", Rec."Line No.");
        CarCarrierLedgerEntry.DeleteAll(true);
        CarCarrierHeader.Get(Rec."Document No.");
        CarCarrierLineDetail.SetCurrentKey("Loading Start Date-Time");
        CarCarrierLineDetail.SetRange("Document No.", Rec."Document No.");
        //CarCarrierLineDetail.SetRange("Document Line No.", Rec."Document Line No.");
        CarCarrierLineDetail.SetFilter("Line No.", '<>%1', Rec."Line No.");
        if CarCarrierLineDetail.FindFirst() then
            CarCarrierHeader.Validate("Starting Date-Time", CarCarrierLineDetail."Loading Start Date-Time")
        else
            CarCarrierHeader.Validate("Starting Date-Time", 0DT);
        CarCarrierHeader.Modify(true);
        CarCarrierOrderLine.SetRange("Document No.", Rec."Order No.");
        CarCarrierOrderLine.SetRange("Planned Car Carrier No.", Rec."Document No.");
        CarCarrierOrderLine.ModifyAll(Status, CarCarrierOrderLine.Status::Planned, true);
    end;

    var
        CarCarrierLine: Record "Car Carrier Line ERK";
        TypeHelper: Codeunit "Type Helper";
        CarCarrierManagement: Codeunit "Car Carrier Management ERK";
        DateErr: Label '%1 can not be less than %2', Comment = '%1=FieldCaption("Loading Start Date-Time"); %2=FieldCaption("Arrival Date")';
}
