table 60044 "Vehicle Fuel Ledger Entry ERK"
{
    Caption = 'Vehicle Fuel Ledger Entry';
    DataClassification = CustomerContent;
    DrillDownPageId = "Vehicle Fuel Ledger Entries";
    LookupPageId = "Vehicle Fuel Ledger Entries";

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            ToolTip = 'Specifies the value of the Entry No. field.';
        }
        field(2; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(3; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        // field(4; "Location Code"; Code[10])
        // {
        //     Caption = 'Location Code';
        // }
        // field(5; "Bin Code"; Code[20])
        // {
        //     Caption = 'Bin Code';
        // }
        field(6; "Quantity (LT)"; Decimal)
        {
            Caption = 'Quantity (LT)';
            ToolTip = 'Specifies the value of the Quantity (LT) field.';
        }
        field(4; "Fuel Type"; Enum "Fuel Type ERK")
        {
            Caption = 'Fuel Type';
            ToolTip = 'Specifies the value of the Fuel Type field.';
        }
        field(5; "Card ID"; Code[10])
        {
            Caption = 'Card ID';
            ToolTip = 'Specifies the value of the Card ID field.';
        }
        field(8; "Charge Station ID"; Code[20])
        {
            Caption = 'Charge Station ID';
            ToolTip = 'Specifies the value of the Charge Station ID field.';
        }
        field(7; kWh; Decimal)
        {
            Caption = 'kWh';
            DecimalPlaces = 2 : 2;
            ToolTip = 'Specifies the value of the kWh field.';
        }
        field(9; "Operation Type"; Enum "Vehicle Add. Operation Type")
        {
            Caption = 'Operation Type';
            ToolTip = 'Specifies the value of the Operation Type field.';
        }
        field(10; "Tire Location"; Enum "Tire Location ERK")
        {
            Caption = 'Tire Location';
            ToolTip = 'Specifies the value of the Tire Location field.';
        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        VehicleFuelLedgerEntry: Record "Vehicle Fuel Ledger Entry ERK";
    begin
        if VehicleFuelLedgerEntry.FindLast() then
            Rec."Entry No." := VehicleFuelLedgerEntry."Entry No." + 1
        else
            Rec."Entry No." := 1;
    end;
}
