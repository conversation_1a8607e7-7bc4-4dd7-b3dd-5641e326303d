codeunit 60022 "Internal Voucher Mngt. ERK"
{
    procedure CreateInternalVoucher(SalesHeader: Record "Sales Header")
    var
        //NewSerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        SalesLine: Record "Sales Line";
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        TempVehicleRevExpWorksheetHdr: Record "Vehicle Rev/Exp. Worksheet Hdr" temporary;
        TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line" temporary;
        NoLinesFoundErr: Label 'No lines found in the sales header.';
        PurchaseLineLineNo: Integer;
        VehicleRevExpLineNo: Integer;
    begin
        SalesHeader.TestField("Shortcut Dimension 1 Code");

        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Invoice;
        PurchaseHeader.Insert(true);
        PurchaseHeader.Validate("Buy-from Vendor No.", SalesHeader."Shortcut Dimension 1 Code");
        PurchaseHeader.Validate("Posting Date", SalesHeader."Posting Date");
        PurchaseHeader.Validate("Currency Code", SalesHeader."Currency Code");
        PurchaseHeader.Validate("Shortcut Dimension 1 Code", SalesHeader."Sell-to Customer No.");
        PurchaseHeader.Modify(true);

        PurchaseLineLineNo := 10000;
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        if not SalesLine.FindSet(false) then
            Error(NoLinesFoundErr);

        repeat
            PurchaseLine.Init();
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Line No." := PurchaseLineLineNo;
            PurchaseLine.Insert(true);
            PurchaseLine.Validate(Type, SalesLine.Type);
            PurchaseLine.Validate("No.", SalesLine."No.");
            PurchaseLine.Validate("Variant Code", SalesLine."Variant Code");
            PurchaseLine.Validate(Quantity, SalesLine.Quantity);
            PurchaseLine.Validate("Direct Unit Cost", SalesLine."Unit Price");
            PurchaseLine.Modify(true);

            TempVehicleRevExpWorksheetHdr := VehicleRevExpManagement.CreateVehicleRevenueExpenseWorksheet(PurchaseLine."Document No.", PurchaseLine."Line No.", Enum::"Voyage Line Detail Type ERK"::Expense, PurchaseLine."No.", PurchaseLine."Variant Code", '', PurchaseHeader."Buy-from Vendor No.", PurchaseHeader."Your Reference", PurchaseHeader."Posting Date", PurchaseHeader."Currency Code", PurchaseHeader."Vendor Invoice No.", PurchaseLine."Line Amount", false, PurchaseHeader."Shortcut Dimension 1 Code");

            PurchaseLineLineNo += 10000;

            SalesLine.CalcFields("Distributed Quantity ERK");
            if SalesLine."Distributed Quantity ERK" > 0 then begin
                SerialNoRevenueExpense.SetRange("Unposted Invoice No.", SalesLine."Document No.");
                SerialNoRevenueExpense.SetRange("Invoice Line No.", SalesLine."Line No.");
                SerialNoRevenueExpense.FindSet(true);
                repeat
                    VehicleRevExpLineNo += 10000;
                    TempVehicleRevExpWorksheetLine.Init();
                    TempVehicleRevExpWorksheetLine."Document No." := TempVehicleRevExpWorksheetHdr."No.";
                    TempVehicleRevExpWorksheetLine."Line No." := VehicleRevExpLineNo;
                    TempVehicleRevExpWorksheetLine."Serial No." := SerialNoRevenueExpense."Serial No.";
                    TempVehicleRevExpWorksheetLine."Unit Amount" := SerialNoRevenueExpense.Amount;
                    TempVehicleRevExpWorksheetLine.Insert(true);

                // NewSerialNoRevenueExpense.Init();
                // NewSerialNoRevenueExpense.TransferFields(SerialNoRevenueExpense);
                // NewSerialNoRevenueExpense."Document No." := PurchaseHeader."No.";
                // NewSerialNoRevenueExpense."Car Carrier Rev/Exp Line No." := PurchaseLine."Line No.";
                // NewSerialNoRevenueExpense.Type := NewSerialNoRevenueExpense.Type::Expense;
                // NewSerialNoRevenueExpense."Source No." := PurchaseHeader."Buy-from Vendor No.";
                // NewSerialNoRevenueExpense."Source Name" := PurchaseHeader."Buy-from Vendor Name";
                // NewSerialNoRevenueExpense.Insert(true);
                until SerialNoRevenueExpense.Next() = 0;

                VehicleRevExpManagement.CreateVehicleRevenueExpenseLinesFromVehicleRevenueExpenseWorksheet(TempVehicleRevExpWorksheetHdr, TempVehicleRevExpWorksheetLine);
            end;
        until SalesLine.Next() = 0;

        Message('Internal voucher created. \Purchase Invoice No.: %1', PurchaseHeader."No.");
    end;

    procedure CreateOrUpdateCarCarrierRevenueExpenseFromSerialNoRevenueExpense(SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK")
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        // if SerialNoRevenueExpense."Department Code" <> 'P04.001.0001' then
        //     exit;

        if SerialNoRevenueExpense."Document No." = '' then
            exit;

        CarCarrRevenueExpense.SetRange("Document No.", SerialNoRevenueExpense."Document No.");
        CarCarrRevenueExpense.SetRange(Type, SerialNoRevenueExpense.Type);
        CarCarrRevenueExpense.SetRange("No.", SerialNoRevenueExpense."No.");
        CarCarrRevenueExpense.SetRange("Variant Code", SerialNoRevenueExpense."Variant Code");
        CarCarrRevenueExpense.SetRange("Currency Code", SerialNoRevenueExpense."Currency Code");
        CarCarrRevenueExpense.SetRange("Posting Date", SerialNoRevenueExpense."Posting Date");
        CarCarrRevenueExpense.SetRange("Source No.", SerialNoRevenueExpense."Source No.");
        //CarCarrRevenueExpense.SetRange("Port Code", TempVehicleRevExpWorksheetHdr."Port Code");
        //CarCarrRevenueExpense.SetRange("Your Reference", TempVehicleRevExpWorksheetHdr."Your Reference");
        //CarCarrRevenueExpense.SetRange("External Document No.", SerialNoRevenueExpense."External Document No.");
        CarCarrRevenueExpense.SetRange("Unposted Invoice No.", SerialNoRevenueExpense."Unposted Invoice No.");
        if not CarCarrRevenueExpense.FindFirst() then begin
            CarCarrRevenueExpense.Init();
            CarCarrRevenueExpense."Document No." := SerialNoRevenueExpense."Document No.";
            CarCarrRevenueExpense.Insert(true);
            CarCarrRevenueExpense.Validate(Type, SerialNoRevenueExpense.Type);
            CarCarrRevenueExpense.Validate("No.", SerialNoRevenueExpense."No.");
            CarCarrRevenueExpense.Validate("Variant Code", SerialNoRevenueExpense."Variant Code");
            CarCarrRevenueExpense.Validate("Posting Date", SerialNoRevenueExpense."Posting Date");
            CarCarrRevenueExpense.Validate("Source No.", SerialNoRevenueExpense."Source No.");
            CarCarrRevenueExpense.Validate("Currency Code", SerialNoRevenueExpense."Currency Code");
            CarCarrRevenueExpense.Validate("Unposted Invoice No.", SerialNoRevenueExpense."Unposted Invoice No.");
            CarCarrRevenueExpense.Validate(Quantity, 1);
            CarCarrRevenueExpense.Validate(Status, CarCarrRevenueExpense.Status::Ready);
        end;
        CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrRevenueExpense."Unit Price/Cost" + SerialNoRevenueExpense.Amount);
        CarCarrRevenueExpense.Modify(false);

        SerialNoRevenueExpense.Validate("Car Carrier Rev/Exp Line No.", CarCarrRevenueExpense."Line No.");
        SerialNoRevenueExpense.Modify(true);
    end;

    var
        VehicleRevExpManagement: Codeunit "Vehicle Rev./Exp. Management";
}