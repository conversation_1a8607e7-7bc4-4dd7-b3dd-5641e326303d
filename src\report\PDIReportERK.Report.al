report 60020 "PDI Report ERK"
{
    ApplicationArea = All;
    Caption = 'PDI Report';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(PDIHeaderERK; "PDI Header ERK")
        {
            DataItemTableView = where(Saved = const(true));
            column(No; "No.")
            {
            }
            column(SerialNo; "Serial No.")
            {
            }
            column(OperationStartingDateTime; "Operation Starting Date-Time")
            {
            }
            column(OperationEndingDateTime; "Operation Ending Date-Time")
            {
            }
            column(ResponsibleName; "Responsible Name")
            {
            }
            column(BrandCode; "Brand Code")
            {
            }
            column(ModelCode; "Model Code")
            {
            }
            column(ColorName; "Color Name")
            {
            }
            column(FuelType; "Fuel Type")
            {
            }
            column(Damaged; Damaged)
            {
            }
            column(LineNo; "Line No.")
            {
            }
            column(Closed; Saved)
            {
            }
            column(Notes; Notes)
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(ToBinCode; "To Bin Code")
            {
            }
            column(NoSeries; "No. Series")
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
            dataitem("PDI Line ERK"; "PDI Line ERK")
            {
                DataItemLink = "Document No." = field("No.");

                column(Code_PDILineERK; Code)
                {
                }
                column(Comment_PDILineERK; Comment)
                {
                }
                column(Description_PDILineERK; Description)
                {
                }
                column(DocumentNo_PDILineERK; "Document No.")
                {
                }
                column(Indent_PDILineERK; Indent)
                {
                }
                column(LineNo_PDILineERK; "Line No.")
                {
                }
                column(ParentCode_PDILineERK; "Parent Code")
                {
                }
                column(Result_PDILineERK; Result)
                {
                }
                column(SystemCreatedAt_PDILineERK; SystemCreatedAt)
                {
                }
                column(SystemCreatedBy_PDILineERK; SystemCreatedBy)
                {
                }
                column(SystemId_PDILineERK; SystemId)
                {
                }
                column(SystemModifiedAt_PDILineERK; SystemModifiedAt)
                {
                }
                column(SystemModifiedBy_PDILineERK; SystemModifiedBy)
                {
                }
            }
        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(Processing)
    //         {
    //         }
    //     }
    // }
}