page 60095 "Grupage Import Worksheet ERK"
{
    ApplicationArea = All;
    Caption = 'Grupage Import Worksheet';
    PageType = List;
    SourceTable = "Grupage Import ERK";
    UsageCategory = Tasks;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("To Location Code"; Rec."To Location Code")
                {
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                }
                field("Shipping Agent Code"; Rec."Shipping Agent Code")
                {
                }
                field("Grupage Date"; Rec."Grupage Date")
                {
                }
                field("Grupage Ship-to Name"; Rec."Grupage Ship-to Name")
                {
                }
                field("Grupage Ship-to City"; Rec."Grupage Ship-to City")
                {
                }
                field("Grupage Ship-to Address"; Rec."Grupage Ship-to Address")
                {
                }
                field("Operation Type"; Rec."Operation Type")
                {
                }
            }
        }
    }
    actions
    {
        area(Creation)
        {
            action(CreateVehicleTransferDocuments)
            {
                Caption = 'Create Vehicle Transfer Documents';
                Image = Import;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Vehicle Transfer Documents action.';

                trigger OnAction()
                begin
                    GrupageManagement.CreateVehicleTransferDocuments(Rec);
                    CurrPage.Close();
                end;
            }
        }
    }
    var
        //VehicleTransferManagement: Codeunit "Vehicle Transfer Management";
        GrupageManagement: Codeunit "Grupage Management ERK";
}
