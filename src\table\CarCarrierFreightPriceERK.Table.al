table 60031 "Car Carrier Freight Price ERK"
{
    Caption = 'Car Carrier Freight Price';
    DataClassification = CustomerContent;
    DrillDownPageId = "Car Carrier Freight Price List";
    LookupPageId = "Car Carrier Freight Price List";

    fields
    {
        field(1; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Customer No. field.';
        }
        field(2; "Departure Port"; Code[10])
        {
            Caption = 'Departure Port';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Departure Port field.';
        }
        field(3; "Arrival Port"; Code[10])
        {
            Caption = 'Arrival Port';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Arrival Port field.';
        }
        field(4; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        field(5; "Vehicle Size"; Code[10])
        {
            Caption = 'Vehicle Size';
            TableRelation = "Vehicle Size ERK".Code;
            ToolTip = 'Specifies the value of the Vehicle Size field.';
        }
        field(6; "Start Date"; Date)
        {
            Caption = 'Start Date';
            ToolTip = 'Specifies the value of the Start Date field.';
        }
        field(7; Price; Decimal)
        {
            Caption = 'Price';
            ToolTip = 'Specifies the value of the Price field.';
        }
        field(10; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            TableRelation = "Unit of Measure".Code;
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(8; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Customer No.")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
    }
    keys
    {
        key(PK; "Customer No.", "Departure Port", "Arrival Port", "Currency Code", "Vehicle Size")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        Customer: Record Customer;
    begin
        if (Rec."Currency Code" = '') and (Customer.Get(Rec."Customer No.")) then
            Rec."Currency Code" := Customer."Currency Code";
    end;
}
