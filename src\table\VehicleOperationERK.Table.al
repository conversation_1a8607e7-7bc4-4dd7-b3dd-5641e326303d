table 60040 "Vehicle Operation ERK"
{
    Caption = 'Vehicle Operation';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; PK; Code[10])
        {
            Caption = 'PK';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "To Location Code"; Code[10])
        {
            Caption = 'To Location Code';
            TableRelation = Location.Code where("Car Carrier Related ERK" = const(true));
            ToolTip = 'Specifies the value of the To Location Code field.';
            trigger OnValidate()
            var
                Bin: Record Bin;
                VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
            begin
                if Rec."To Location Code" = '' then
                    exit;

                case "Operation Type" of
                    "Vehicle Transfer Opr. Type ERK"::"Dealer Dispatch":
                        begin
                            Bin.SetRange("Location Code", Rec."To Location Code");
                            Bin.SetRange("Vis. for Dealer Dispatch ERK", true);
                            Bin.FindFirst();
                            Rec.Validate("To Bin Code", Bin.Code);
                        end;
                    "Vehicle Transfer Opr. Type ERK"::"Vehicle Entry":
                        begin
                            Bin.SetRange("Location Code", Rec."To Location Code");
                            Bin.SetRange("Vehicle Entry ERK", true);
                            Bin.FindFirst();
                            Rec.Validate("To Bin Code", Bin.Code);
                        end;
                    "Vehicle Transfer Opr. Type ERK"::"Grupage Dealer Dispatch":
                        begin
                            VehicleTransferHeader.SetRange("Operation Type", Rec."Operation Type");
                            VehicleTransferHeader.SetRange("To Location Code", Rec."To Location Code");
                            VehicleTransferHeader.SetRange(Completed, false);
                            if Page.RunModal(0, VehicleTransferHeader) = Action::LookupOK then begin
                                Rec.Validate("Document No.", VehicleTransferHeader."No.");
                                Rec.Validate("To Bin Code", VehicleTransferHeader."To Bin Code");
                                Rec.Validate("Shippping Agent Code", VehicleTransferHeader."Shipping Agent Code");
                            end;
                        end;
                end;
            end;
        }
        field(4; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            TableRelation = if ("Operation Type" = const(" ")) Bin.Code where("Location Code" = field("To Location Code"))
            else if ("Operation Type" = const(Discharge)) Bin.Code where("Location Code" = field("To Location Code"), "Default Discharge ERK" = const(true))
            else if ("Operation Type" = const(Loading)) Bin.Code where("Location Code" = field("To Location Code"), "Default Loading ERK" = const(true))
            else if ("Operation Type" = const(Transfer)) Bin.Code where("Location Code" = field("To Location Code"), "Transfer ERK" = const(true))
            else if ("Operation Type" = const(Addressing)) Bin.Code where("Location Code" = field("To Location Code"), "Hide in Addressing ERK" = const(false))
            else if ("Operation Type" = const("Customs Exit")) Bin.Code where("Location Code" = field("To Location Code"), "Customs Exit ERK" = const(true))
            else if ("Operation Type" = const("PDI Exit")) Bin.Code where("Location Code" = field("To Location Code"), "PDI Area ERK" = const(true))
            else if ("Operation Type" = const("Damage Exit")) Bin.Code where("Location Code" = field("To Location Code"), "Visible for Damage Exit ERK" = const(true))
            else if ("Operation Type" = filter("Nav Exit" | "Vehicle Entry")) Bin.Code where("Location Code" = field("To Location Code"), "Nav Area ERK" = const(true))
            else if ("Operation Type" = const("PDI Entry")) Bin.Code where("Location Code" = field("To Location Code"), "Visible for PDI Entry ERK" = const(true))
            else if ("Operation Type" = filter("Dealer Dispatch" | "Grupage Dealer Dispatch")) Bin.Code where("Location Code" = field("To Location Code"), "Vis. for Dealer Dispatch ERK" = const(true));
            ToolTip = 'Specifies the value of the To Bin Code field.';
        }
        field(3; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
            trigger OnValidate()
            var
                VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                VehicleTransferLine: Record "Vehicle Transfer Line ERK";
                Location: Record Location;
                SerialNoInformation: Record "Serial No. Information";
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                ShippingAgent: Record "Shipping Agent";
                NoSeries: Codeunit "No. Series";
                MoreThanOneErr: Label 'There are %1 valid Vehicle Line exist. It must be 1.', Comment = '%1= Vehicle Line Count';
                SuccessMsg: Label 'Serial No.: %1 is successfully read for %3 - %2.', Comment = '%1="Vehicle Operation ERK"."Serial No."; %2="Vehicle Transfer Header ERK"."To Bin Code", %3 = "Vehicle Transfer Header ERK"."To Location Code"';
                SerialNoFilter: Text;
            begin
                SerialNoFilter := '*' + Rec."Serial No.";

                if Rec."Operation Type" in [Rec."Operation Type"::Discharge,
                                            Rec."Operation Type"::Loading,
                                            Rec."Operation Type"::"PDI Entry",
                                            Rec."Operation Type"::"Grupage Dealer Dispatch"] then begin
                    VehicleTransferLine.SetRange(Processed, false);
                    VehicleTransferLine.SetFilter("Serial No.", SerialNoFilter);
                    //#pragma warning disable AA0210
                    // VehicleTransferLine.SetFilter("Operation Type", '%1|%2|%3', VehicleTransferLine."Operation Type"::"Customs Exit",
                    //                                                             VehicleTransferLine."Operation Type"::Discharge,
                    //                                                             VehicleTransferLine."Operation Type"::Loading);
                    VehicleTransferLine.SetRange("Operation Type", Rec."Operation Type");
                    if Rec."To Location Code" <> '' then
                        VehicleTransferLine.SetRange("Header To Location Code", Rec."To Location Code");

                    if Rec."To Bin Code" <> '' then
                        VehicleTransferLine.SetRange("Header To Bin Code", Rec."To Bin Code");

                    //#pragma warning restore AA0210
#pragma warning disable LC0082
                    if VehicleTransferLine.Count() <> 1 then
#pragma warning restore LC0082
                        Error(MoreThanOneErr, VehicleTransferLine.Count());

                    VehicleTransferLine.FindFirst();
                    VehicleTransferHeader.Get(VehicleTransferLine."Document No.");
                    if VehicleTransferHeader."Operation Type" = VehicleTransferHeader."Operation Type"::Transfer then begin
                        Rec.TestField("To Location Code");
                        Rec.TestField("To Bin Code");
                    end;
                end
                else
                    case Rec."Operation Type" of
                        "Vehicle Transfer Opr. Type ERK"::"Dealer Dispatch":
                            begin
                                Rec.TestField("To Location Code");
                                Rec.TestField("To Bin Code");
                                Rec.TestField("Shippping Agent Code");
                                if Rec."Document No." <> '' then
                                    VehicleTransferHeader.SetRange("No.", Rec."Document No.");

                                VehicleTransferHeader.SetRange("Operation Type", Rec."Operation Type");
                                VehicleTransferHeader.SetRange("To Location Code", Rec."To Location Code");
                                VehicleTransferHeader.SetRange("To Bin Code", Rec."To Bin Code");
                                VehicleTransferHeader.SetRange(Completed, false);
                                if (not VehicleTransferHeader.FindFirst()) and (Rec."Operation Type" <> Rec."Operation Type"::"Grupage Dealer Dispatch") then begin
                                    VehicleTransferHeader.Init();
                                    VehicleTransferHeader.Insert(true);
                                    VehicleTransferHeader.Validate("Operation Type", Rec."Operation Type");
                                    VehicleTransferHeader.Validate("To Location Code", Rec."To Location Code");
                                    VehicleTransferHeader.Validate("To Bin Code", Rec."To Bin Code");
                                end;
                            end;
                        "Vehicle Transfer Opr. Type ERK"::Transfer,
                        "Vehicle Transfer Opr. Type ERK"::"Customs Exit",
                        "Vehicle Transfer Opr. Type ERK"::"Nav Entry":
                            begin
                                VehicleTransferLine.SetRange(Processed, false);
                                VehicleTransferLine.SetFilter("Serial No.", SerialNoFilter);
                                VehicleTransferLine.SetRange("Operation Type", Rec."Operation Type");
                                VehicleTransferLine.FindFirst();
                                if not VehicleTransferLine."In-Transit" then
                                    Rec.TestField("Shippping Agent Code");

                                VehicleTransferHeader.Get(VehicleTransferLine."Document No.");

                                SerialNoInformation.SetFilter("Serial No.", SerialNoFilter);
                                SerialNoInformation.FindFirst();
                                Location.Get(SerialNoInformation."Current Location Code ERK");

                                if Location."Req.Temp. Traffic Doc. ERK" then
                                    if not VehicleTransferLine."In-Transit" then begin
                                        ShippingAgent.Get(Rec."Shippping Agent Code");
                                        if ShippingAgent."Req.Temp. Traffic Doc. ERK" then begin
                                            Rec.TestField("License Plate");
                                            Rec.TestField("Driver Full Name");

                                            ErkHoldingSetup.GetRecordOnce();
                                            ErkHoldingSetup.TestField("Temp. Traffic Doc. No. Series");

                                            if SerialNoInformation."Temp. Traffic Document No. ERK" = '' then
                                                SerialNoInformation.Validate("Temp. Traffic Document No. ERK", NoSeries.GetNextNo(ErkHoldingSetup."Temp. Traffic Doc. No. Series", WorkDate()));

                                            SerialNoInformation.Validate("Temp. License Plate No. ERK", Rec."License Plate");
                                            SerialNoInformation.Validate("Temp. Driver Full Name ERK", Rec."Driver Full Name");
                                            SerialNoInformation.Modify(true);
                                            Commit();//

                                            Report.Run(Report::"Temporary Traffic Document ERK", true, true, SerialNoInformation);
                                        end;
                                    end;
                            end;
                        "Vehicle Transfer Opr. Type ERK"::"Vehicle Entry":
                            begin
                                Rec.TestField("To Location Code");
                                Rec.TestField("To Bin Code");
                                VehicleTransferHeader.SetRange("Operation Type", Rec."Operation Type");
                                VehicleTransferHeader.SetRange("To Location Code", Rec."To Location Code");
                                VehicleTransferHeader.SetRange("To Bin Code", Rec."To Bin Code");
                                VehicleTransferHeader.SetRange(Completed, false);
                                if not VehicleTransferHeader.FindFirst() then begin
                                    VehicleTransferHeader.Init();
                                    VehicleTransferHeader.Insert(true);
                                    VehicleTransferHeader.Validate("Operation Type", Rec."Operation Type");
                                    VehicleTransferHeader.Validate("To Location Code", Rec."To Location Code");
                                    VehicleTransferHeader.Validate("To Bin Code", Rec."To Bin Code");
                                end;
                            end;
                    end;

                VehicleTransferHeader.Validate("Shipping Agent Code", Rec."Shippping Agent Code");
                VehicleTransferHeader.Modify(true);
                VehicleTransferHeader.Validate("Serial No.", Rec."Serial No.");

                Message(SuccessMsg, Rec."Serial No.", VehicleTransferHeader."To Bin Code", VehicleTransferHeader."To Location Code");

                //Rec."Serial No." := '';

                if Rec."Operation Type" = Rec."Operation Type"::"Grupage Dealer Dispatch" then begin
                    Rec."Document No." := '';
                    Rec."Operation Type" := Rec."Operation Type"::" ";
                    Rec."To Location Code" := '';
                    Rec."To Bin Code" := '';
                    Rec."Shippping Agent Code" := '';
                    Rec."License Plate" := '';
                    Rec."Driver Full Name" := '';
                end;
            end;
        }
        field(5; "Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Operation Type';
            ValuesAllowed = 0, 1, 2, 4, 5, 8, 9, 14;
            ToolTip = 'Specifies the value of the Operation Type field.';
            trigger OnValidate()
            var
                WarehouseEmployee: Record "Warehouse Employee";
            begin
                Rec.Validate("To Location Code", '');
                Rec.Validate("To Bin Code", '');
                Rec.Validate("Shippping Agent Code", '');
                Rec.Validate("Document No.", '');
                case Rec."Operation Type" of
                    "Vehicle Transfer Opr. Type ERK"::"Dealer Dispatch":
                        begin
                            WarehouseEmployee.SetRange("User ID", UserId());
                            WarehouseEmployee.FindFirst();
                            Rec.Validate("To Location Code", WarehouseEmployee."Location Code");
                        end;
                    "Vehicle Transfer Opr. Type ERK"::"Vehicle Entry":
                        begin
                            WarehouseEmployee.SetRange("User ID", UserId());
                            WarehouseEmployee.FindFirst();
                            Rec.Validate("To Location Code", WarehouseEmployee."Location Code");
                        end;
                end;
            end;
        }
        field(6; "Shippping Agent Code"; Code[10])
        {
            Caption = 'Shippping Agent Code';
            ToolTip = 'Specifies the value of the Shippping Agent Code field.';
        }
        field(7; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
            trigger OnValidate()
            var
                VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
            begin
                if Rec."Document No." = '' then
                    exit;
                VehicleTransferHeader.Get(Rec."Document No.");
                Rec."Operation Type" := VehicleTransferHeader."Operation Type";
                //Rec.Validate("To Location Code", VehicleTransferHeader."To Location Code");

                //Rec.Validate("To Bin Code", VehicleTransferHeader."To Bin Code");

                Rec.Validate("Shippping Agent Code", '');
            end;
        }
        field(8; "License Plate"; Code[20])
        {
            Caption = 'License Plate';
            ToolTip = 'Specifies the value of the License Plate field.';
            TableRelation = "Temporary License Plate ERK"."No.";
            // trigger OnValidate()
            // var
            //     TemporaryLicensePlate: Record "Temporary License Plate ERK";
            // begin
            //     TemporaryLicensePlate.Get(Rec."License Plate");
            //     Rec.Validate("Shippping Agent Code", TemporaryLicensePlate."Shipping Agent Code");
            // end;
        }
        field(9; "Driver Full Name"; Text[100])
        {
            Caption = 'Driver Full Name';
            ToolTip = 'Specifies the value of the Driver Full Name field.';
            TableRelation = "Driver Information ERK"."Driver Full Name" where("License Plate No." = field("License Plate"));
        }
    }
    keys
    {
        key(PK; PK)
        {
            Clustered = true;
        }
    }
}
