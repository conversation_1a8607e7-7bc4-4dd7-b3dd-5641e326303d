page 60140 "Logistics Operation Activities"
{
    PageType = CardPart;
    SourceTable = "Logistics Operation Cue ERK";
    Caption = 'Logistics Activities';
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            cuegroup(Invoicing)
            {
                Caption = 'Invoicing';

                field("Port Ops Due Today"; Rec."Port Ops Due Today")
                {
                    // DrillDownPageId = "Port Operation Line DetailsERK";
                    Style = Attention;

                    // trigger OnDrillDown()
                    // var
                    //     PortOpLineDetail: Record "Port Operation Line Detail ERK";
                    // begin
                    //     PortOpLineDetail.Reset();
                    //     PortOpLineDetail.SetRange("Next Invoice Date", WorkDate());
                    //     PortOpLineDetail.SetRange("External Document No.", '');
                    //     PortOpLineDetail.SetRange("Invoice No.", '');
                    //     PortOpLineDetail.SetRange("Is Cancelled", false);
                    //     Page.Run(Page::"Port Operation Line DetailsERK", PortOpLineDetail);
                    // end;
                }

                field("Port Ops Due This Week"; Rec."Port Ops Due This Week")
                {
                    DrillDownPageId = "Port Operation Line DetailsERK";
                    Style = Favorable;
                    trigger OnDrillDown()
                    var
                        PortOpLineDetail: Record "Port Operation Line Detail ERK";
                    begin
                        PortOpLineDetail.Reset();
                        PortOpLineDetail.SetFilter("Next Invoice Date", '%1..%2', WorkDate() + 1, CalcDate('<1W>', WorkDate()));
                        PortOpLineDetail.SetRange("External Document No.", '');
                        PortOpLineDetail.SetRange("Invoice No.", '');
                        PortOpLineDetail.SetRange("Is Cancelled", false);
                        Page.Run(Page::"Port Operation Line DetailsERK", PortOpLineDetail);
                    end;
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert(false);
        end;

        Rec.SetRange("Date Filter Today", WorkDate());
        Rec.SetFilter("Date Filter Week", '%1..%2', WorkDate() + 1, CalcDate('<1W>', WorkDate()));
    end;
}