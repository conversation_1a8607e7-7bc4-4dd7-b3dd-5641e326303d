pageextension 60001 "Blanket Sales Order ERK" extends "Blanket Sales Order"
{
    layout
    {
        modify("Shortcut Dimension 1 Code")
        {
            Visible = false;
        }
        addafter("Exit Point")
        {
            field("Port of Arrival ERK"; Rec."Port of Arrival ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
        }
        addafter("Order Date")
        {
            field("Requested Delivery Date ERK"; Rec."Requested Delivery Date")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the date that the customer has asked for the order to be delivered.';
            }
        }
        addafter(Status)
        {
            field("Estimated Time of Delivery ERK"; Rec."Estimated Time of Delivery ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
            field("WorkDescription ERK"; WorkDescription)
            {
                ApplicationArea = Basic, Suite;
                Caption = 'Work Description';
                MultiLine = true;
                ToolTip = 'Specifies the products or service being offered.';

                trigger OnValidate()
                begin
                    Rec.SetWorkDescription(WorkDescription);
                end;
            }
            field("Freight ERK"; Rec."Freight ERK")
            {
                ApplicationArea = All;
            }
            field("Completed ERK"; Rec."Completed ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
            field("Payment Amount ERK"; Rec."Payment Amount ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
            field("Applied Amount ERK"; Rec."Applied Amount ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
        }
        addafter(BillToOptions)
        {
            group("Notify ERK")
            {
                Caption = 'Notify';

                field("Notify Ship-to Code ERK"; Rec."Notify Ship-to Code ERK")
                {
                    ApplicationArea = ExportManagementERK;
                }
                field("Notify Ship-to Name ERK"; Rec."Notify Ship-to Name ERK")
                {
                    ApplicationArea = ExportManagementERK;
                }
            }
        }
    }
    actions
    {
        addafter(MakeOrder)
        {
            action("RelatedExports ERK")
            {
                ApplicationArea = ExportManagementERK;
                Caption = 'Related Exports';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ExportReceipt;
                RunObject = page "Export List ERK";
                RunPageLink = "Blanket Sales Order No." = field("No.");
                ToolTip = 'Executes the Related Exports action.';
            }
            action("ExportLedgerEntries ERK")
            {
                ApplicationArea = ExportManagementERK;
                Caption = 'Export Ledger Entries';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = LedgerEntries;
                ToolTip = 'Executes the Export Ledger Entries action.';

                trigger OnAction()
                var
                    ExportLedgerEntry: Record "Export Ledger Entry ERK";
                begin
                    ExportLedgerEntry.SetRange("Blanket Sales Order No.", Rec."No.");
                    ExportLedgerEntry.SetRange("Customer No.", Rec."Sell-to Customer No.");
                    Page.Run(Page::"Export Ledger Entries ERK", ExportLedgerEntry);
                end;
            }
        }
    }
    var
        WorkDescription: Text;
}
