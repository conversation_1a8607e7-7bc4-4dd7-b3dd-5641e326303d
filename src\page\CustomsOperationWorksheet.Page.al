page 60056 "Customs Operation Worksheet"
{
    ApplicationArea = CustomsOperationERK;
    Caption = 'Customs Operation Worksheet';
    PageType = Worksheet;
    SourceTable = "Customs Operation Header ERK";
    UsageCategory = Tasks;
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Regstration Date"; Rec."Registration Date")
                {
                }
                field("Stamp Tax Line No."; Rec."Stamp Tax Line No.")
                {
                }
                field(MRN; Rec.MRN)
                {
                }
                field("Truck License Plate"; Rec."Truck License Plate")
                {
                }
                field("Trailer License Plate"; Rec."Trailer License Plate")
                {
                }
                field("Container No."; Rec."Container No.")
                {
                }
                field("Exit Customs"; Rec."Exit Customs")
                {
                    ToolTip = 'Specifies the value of the Exit Customs field.';
                }
                field(Sender; Rec.Sender)
                {
                }
                field(Receiver; Rec.Receiver)
                {
                }
                field("Customer Representative"; Rec."Customer Representative")
                {
                }
                field("Declaration Type"; Rec."Declaration Type")
                {
                }
                field("Bill-To Customer Name"; Rec."Bill-To Customer Name")
                {
                }
                field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                {
                    ShowMandatory = true;
                }
                field("Invoicing Group Code"; Rec."Invoicing Group Code")
                {
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateCustomsOperations)
            {
                ApplicationArea = All;
                Caption = 'Create Customs Operations';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = CreateBinContent;
                ToolTip = 'Executes the Create Customs Operations action.';
                PromotedOnly = true;

                trigger OnAction()
                // var
                //     CustomsOperationHeader: Record "Customs Operation Header ERK";
                begin
                    // Message(Format(Rec.Count()));
                    // CurrPage.SetSelectionFilter(CustomsOperationHeader);
                    // Message(Format(CustomsOperationHeader.Count()));
                    CustomsOperationManagement.CreateCustomsOperationFromWorksheet(Rec);
                end;
            }
        }
    }
    var
        CustomsOperationManagement: Codeunit "Customs Operation Management";
}
