report 60013 "Easy Reconciliation Cst. ERK"
{
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;
    DefaultLayout = RDLC;
    Caption = 'Easy Reconciliation Customer';
    RDLCLayout = 'src/reportLayouts/EasyReconciliationCstERK.rdlc';

    dataset
    {
        dataitem(Customer; Customer)
        {
            RequestFilterFields = "No.";

            #region Labels
            column(CariHesapKoduLbl; CariHesapKoduLblTok)
            {
            }
            column(CariHesapAdiLbl; CariHesapAdiLblTok)
            {
            }
            column(SubeLbl; SubeLblTok)
            {
            }
            column(CariHesapTuruLbl; CariHesapTuruLblTok)
            {
            }
            column(TutarLbl; TutarLblTok)
            {
            }
            column(ParaBirimiLbl; ParaBirimiLblTok)
            {
            }
            column(BorcAlacakLbl; BorcAlacakLblTok)
            {
            }
            column(VergiDairesiLbl; VergiDairesiLblTok)
            {
            }
            column(VergiNoLbl; VergiNoLblTok)
            {
            }
            column(FaksNumarasiLbl; FaksNumarasiLblTok)
            {
            }
            column(İlgiliKisiEpostaAdresiLbl; "İlgiliKisiEpostaAdresiLblTok")
            {
            }
            column(NullLColumnLbl; NullLColumnLblTok)
            {
            }
            column(OzelNotLbl; OzelNotLblTok)
            {
            }
            column(HataBildirimleriLbl; HataBildirimleriLblTok)
            {
            }
            #endregion

            column(CustomerNo; "No.")
            {
            }
            column(CustomerName; Name)
            {
            }
            column(CariHesapTuru; CariHesapTuruTok)
            {
            }
            column(BorcAlacak; BorcAlacak)
            {
            }
            column(Tax_Area_Code; "Tax Area Code")
            {
            }
            column(VAT_Registration_No_; "VAT Registration No.")
            {
            }
            column(Fax_No_; "Fax No.")
            {
            }
            column(E_Mail; "E-Mail")
            {
            }
            column(TaxAreaDescription; TaxAreaDescription)
            {
            }
            dataitem("Cust. Ledger Entry"; "Cust. Ledger Entry")
            {
                DataItemLink = "Customer No." = field("No.");
                RequestFilterFields = "Date Filter";

                column(Original_Amount; "Original Amount")
                {
                }
                column(Currency_Code; "Currency Code")
                {
                }
            }

            trigger OnAfterGetRecord()
            var
                TaxArea: Record "Tax Area";
                CustLedgerEntry: Record "Cust. Ledger Entry";
            begin
                CustLedgerEntry.Reset();
                CustLedgerEntry.SetRange("Customer No.", Customer."No.");
                if CustLedgerEntry.IsEmpty() then
                    CurrReport.Skip();

                if TaxArea.Get(Customer."Tax Area Code") then
                    TaxAreaDescription := TaxArea.Description
                else
                    TaxAreaDescription := '';
            end;
        }
    }

    var
        CariHesapKoduLblTok: Label 'Cari Hesap Kodu', Locked = true;
        CariHesapAdiLblTok: Label 'Cari Hesap Adı', Locked = true;
        SubeLblTok: Label 'Şube', Locked = true;
        CariHesapTuruLblTok: Label 'Cari Hesap Türü', Locked = true;
        TutarLblTok: Label 'Tutar', Locked = true;
        ParaBirimiLblTok: Label 'Para Birimi', Locked = true;
        BorcAlacakLblTok: Label 'Borç/Alacak', Locked = true;
        VergiDairesiLblTok: Label 'Vergi Dairesi', Locked = true;
        VergiNoLblTok: Label 'Vergi No', Locked = true;
        FaksNumarasiLblTok: Label 'Faks Numarası,', Locked = true;
        "İlgiliKisiEpostaAdresiLblTok": Label 'İlgili Kişi e-posta adresi', Locked = true;
        NullLColumnLblTok: Label '', Locked = true;
        OzelNotLblTok: Label 'Özel Not', Locked = true;
        HataBildirimleriLblTok: Label 'Hata Bildirimleri', Locked = true;

        CariHesapTuruTok: Label 'Müşteri', Locked = true;
        BorcAlacak: Text;
        TaxAreaDescription: Text;
}