table 60011 "Ship ERK"
{
    Caption = 'Ship';
    DataClassification = CustomerContent;
    LookupPageId = "Ship List ERK";
    DrillDownPageId = "Ship List ERK";

    fields
    {
        field(1; "No."; Code[10])
        {
            Caption = 'No.';
            NotBlank = true;
            ToolTip = 'Specifies the value of the No. field.';
        }
        field(2; Name; Text[100])
        {
            Caption = 'Name';
            ToolTip = 'Specifies the value of the Name field.';
        }
        field(3; Flag; Code[10])
        {
            Caption = 'Flag';
            TableRelation = "Country/Region".Code;
            ToolTip = 'Specifies the value of the Flag field.';
        }
        field(4; "Lenght (m)"; Decimal)
        {
            Caption = 'Lenght (m)';
            ToolTip = 'Specifies the value of the Lenght (m) field.';
        }
        field(5; "Width (m)"; Decimal)
        {
            Caption = 'Width (m)';
            ToolTip = 'Specifies the value of the Width (m) field.';
        }
        field(6; "Draft (m)"; Decimal)
        {
            Caption = 'Draft (m)';
            ToolTip = 'Specifies the value of the Draft (m) field.';
        }
        field(7; "GRT (ton)"; Decimal)
        {
            Caption = 'GRT (ton)';
            ToolTip = 'Specifies the value of the GRT (ton) field.';
        }
        field(8; "DWT (ton)"; Decimal)
        {
            Caption = 'DWT (ton)';
            ToolTip = 'Specifies the value of the DWT (ton) field.';
        }
        field(9; "Fuel Quantity"; Decimal)
        {
            Caption = 'Fuel Quantity';
            Editable = false;
            FieldClass = FlowField;
            //CalcFormula = sum("Item Ledger Entry".Quantity where("Location Code" = field("No.")));
            CalcFormula = sum("Warehouse Entry".Quantity where("Bin Code" = field("No.")));
            ToolTip = 'Specifies the value of the Fuel Quantity field.';
        }
        field(10; "Contract Start Date"; Date)
        {
            Caption = 'Contract Start Date';
            ToolTip = 'Specifies the value of the Contract Start Date field.';
        }
        field(11; "Contract End Date"; Date)
        {
            Caption = 'Contract End Date';
            ToolTip = 'Specifies the value of the Contract End Date field.';
        }
        field(12; "Contract Currency Code"; Code[10])
        {
            Caption = 'Contract Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Contract Currency Code field.';
        }
        field(13; "Contract Unit Cost"; Decimal)
        {
            Caption = 'Contract Unit Cost';
            ToolTip = 'Specifies the value of the Contract Unit Cost field.';
        }
        // field(14; "Cargo Type"; Code[50])
        // {
        //     Caption = 'Cargo Type';
        //     TableRelation = "Cargo Type ERK";
        // }
        field(15; "Ship Type"; Enum "Ship Type ERK")
        {
            Caption = 'Ship Type';
            ToolTip = 'Specifies the value of the Ship Type field.';
        }
        field(16; "Call Sign"; Code[10])
        {
            Caption = 'Call Sign';
            ToolTip = 'Specifies the value of the Call Sign field.';
        }
        field(17; "Average Speed (Knot)"; Decimal)
        {
            Caption = 'Average Speed (Knot)';
            ToolTip = 'Specifies the value of the Average Speed (Knot) field.';
        }
        field(18; "Eco IFO/HSFO Cons. (Ton/Day)"; Decimal)
        {
            Caption = 'Eco Speed IFO/HSFO Consumption (Ton/Day)';
            ToolTip = 'Specifies the value of the IFO/HSFO Consumption (Ton/Hour) field.';
        }
        field(19; "MGO Consumption (Ton/Day)"; Decimal)
        {
            Caption = 'MGO Consumption (Ton/Day)';
            ToolTip = 'Specifies the value of the MGO Consumption (Ton/Day) field.';
        }
        field(20; "Max IFO/HSFO Cons. (Ton/Day)"; Decimal)
        {
            Caption = 'Max Speed IFO/HSFO Cons. (Ton/Day)';
            ToolTip = 'Specifies the value of the Max Speed IFO/HSFO Cons. (Ton/Day) field.';
        }
        field(21; "Capacity (m2)"; Decimal)
        {
            Caption = 'Capacity (m2)';
            ToolTip = 'Specifies the value of the Capacity (m2) field.';
        }
        field(22; "Capacity (m3)"; Decimal)
        {
            Caption = 'Capacity (m3)';
            ToolTip = 'Specifies the value of the Capacity (m3) field.';
        }
        field(23; "Eco Speed (kts)"; Decimal)
        {
            Caption = 'Eco Speed (kts)';
            ToolTip = 'Specifies the value of the Eco Speed (kts) field.';
        }
        field(24; "Max Speed (kts)"; Decimal)
        {
            Caption = 'Max Speed (kts)';
            ToolTip = 'Specifies the value of the Max Speed (kts) field.';
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
}
