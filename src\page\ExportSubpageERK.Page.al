page 60003 "Export Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Export Lines';
    PageType = ListPart;
    SourceTable = "Export Line ERK";
    DeleteAllowed = false;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                }
                field("Item Description"; Rec."Item Description")
                {
                    Editable = false;
                }
                field(Brand; Rec.Brand)
                {
                    Editable = false;
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                }
                field("Load Quantity"; Rec."Load Quantity")
                {
                    Editable = false;
                }
                field("Quantity Received"; Rec."Quantity Received")
                {
                }
                field(OutstandingQuantity; ExportManagement.CalculateOutstandingQuantityOnExportLine(Rec))
                {
                    Caption = 'Outstanding Quantity';
                    ToolTip = 'Specifies the value of the Outstanding Quantity field.';
                    Editable = false;
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    Editable = false;
                }
                field("Unit Price"; Rec."Unit Price")
                {
                    Editable = false;
                }
                field("Unit Price (LCY)"; Rec."Unit Price (LCY)")
                {
                    Editable = false;
                }
                field("Unit Cost"; Rec."Unit Cost")
                {
                }
                field("Unit Cost (LCY)"; Rec."Unit Cost (LCY)")
                {
                }
                field("Purchase Line Amount"; Rec."Purchase Line Amount")
                {
                }
                field("Sales Line Amount"; Rec."Sales Line Amount")
                {
                    Editable = false;
                }
                field("Country/Region of Origin Code"; Rec."Country/Region of Origin Code")
                {
                }
                field("Vendor No."; Rec."Vendor No.")
                {
                }
                field("Vendor Name"; Rec."Vendor Name")
                {
                }
                field("Purchase Due Date"; Rec."Purchase Due Date")
                {
                }
                field("Purchase Shipment Method Code"; Rec."Purchase Shipment Method Code")
                {
                }
                field("Purchase Currency Code"; Rec."Purchase Currency Code")
                {
                }
                field("Purchase Receipt No."; Rec."Purchase Receipt No.")
                {
                    Visible = false;
                }
            }
        }
    }
    var //ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        ExportManagement: Codeunit "Export Management ERK";
}
