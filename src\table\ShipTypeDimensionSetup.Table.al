table 60046 "Ship Type - Dimension Setup"
{
    Caption = 'Ship Type - Dimension Setup';
    DataClassification = CustomerContent;
    DrillDownPageId = "Ship Type - Dimension Setup";
    LookupPageId = "Ship Type - Dimension Setup";

    fields
    {
        field(1; Type; Enum "Ship Type ERK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(2; "Dimension Value Code"; Code[20])
        {
            Caption = 'Dimension Value Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1), Blocked = const(false));
            ToolTip = 'Specifies the value of the Dimension Value Code field.';
        }
    }
    keys
    {
        key(PK; "Type")
        {
            Clustered = true;
        }
    }
}
