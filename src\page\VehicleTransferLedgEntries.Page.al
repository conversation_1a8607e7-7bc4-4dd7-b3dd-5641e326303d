page 60044 "Vehicle Transfer Ledg. Entries"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Vehicle Transfer Ledg. Entries';
    PageType = List;
    SourceTable = "Vehicle Transfer Ledger Entry";
    UsageCategory = History;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Operation Type"; Rec."Operation Type")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                    trigger OnDrillDown()
                    var
                        SerialNoInformation: Record "Serial No. Information";
                    begin
                        SerialNoInformation.SetRange("Serial No.", Rec."Serial No.");
                        if SerialNoInformation.FindFirst() then
                            PageManagement.PageRun(SerialNoInformation);
                    end;
                }
                field("From Location Code"; Rec."From Location Code")
                {
                }
                field("From Bin Code"; Rec."From Bin Code")
                {
                }
                field("From Bin Description"; Rec."From Bin Description")
                {
                }
                field("To Location Code"; Rec."To Location Code")
                {
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                }
                field("To Bin Description"; Rec."To Bin Description")
                {
                }
                field("Operation Date-Time"; Rec."Operation Date-Time")
                {
                }
                field(UserName; ErkHoldingBasicFunctions.GetUserNameFromSecurityId(Rec.SystemCreatedBy))
                {
                    ApplicationArea = All;
                    Caption = 'User Name';
                    ToolTip = 'Specifies the value of the User Name field.';
                }
                field("Shipping Agent Code"; Rec."Shipping Agent Code")
                {
                }
                field(T1; Rec.T1)
                {
                }
                field("NAV Succesfull"; Rec."NAV Succesfull")
                {
                }
            }
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        PageManagement: Codeunit "Page Management";
}
