codeunit 60023 "Grupage Management ERK"
{
    procedure CreateVehicleTransferDocuments(var TempGrupageImport: Record "Grupage Import ERK" temporary)
    var
        Bin: Record Bin;
        TempGrupageImportByDoc: Record "Grupage Import ERK" temporary;
        SerialNoInformation: Record "Serial No. Information";
        DispatchVehicleHeader: Record "Vehicle Transfer Header ERK";
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        DispatchVehicleLine: Record "Vehicle Transfer Line ERK";
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
        DispatchLineCount: Integer;
        DocumentCount: Integer;
        LineCount: Integer;
        SuccesMsg: Label '%1 vehicle transfer documents with %2 total lines created successfully', Comment = '%1 is the number of documents created, %2 is the total number of lines created';
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Item No. for Vehicles");

        // Copy the temporary table to work with distinct document numbers
        if TempGrupageImport.FindSet() then
            repeat
                TempGrupageImportByDoc.Copy(TempGrupageImport);
                TempGrupageImportByDoc.Insert(false);
            until TempGrupageImport.Next() = 0;

        // Process each distinct document number
        TempGrupageImportByDoc.Reset();
        TempGrupageImportByDoc.SetCurrentKey("Document No.");
        if TempGrupageImportByDoc.FindSet() then
            repeat
                // Create or get the Vehicle Transfer Header for this document
                Bin.SetRange("Location Code", TempGrupageImportByDoc."To Location Code");
                Bin.SetRange("Vis. for Dealer Dispatch ERK", true);
                Bin.FindFirst();

                Clear(VehicleTransferHeader);
                VehicleTransferHeader.Init();
                VehicleTransferHeader.Insert(true);
                VehicleTransferHeader.Validate("Operation Type", TempGrupageImportByDoc."Operation Type");
                VehicleTransferHeader.Validate("To Location Code", TempGrupageImportByDoc."To Location Code");
                VehicleTransferHeader.Validate("To Bin Code", Bin.Code);
                VehicleTransferHeader.Validate("Shipping Agent Code", TempGrupageImportByDoc."Shipping Agent Code");
                VehicleTransferHeader.Modify(true);
                DocumentCount += 1;

                // Process all lines for the current document number
                TempGrupageImport.Reset();
                TempGrupageImport.SetRange("Document No.", TempGrupageImportByDoc."Document No.");
                if TempGrupageImport.FindSet() then
                    repeat
                        // Update Serial No Information
                        SerialNoInformation.Get(ErkHoldingSetup."Item No. for Vehicles", '', TempGrupageImport."Serial No.");

                        // Update Grupage-related fields in the Serial No. Information
                        SerialNoInformation.Validate("Grupage No. ERK", TempGrupageImport."Document No.");
                        SerialNoInformation.Validate("Grupage Date ERK", TempGrupageImport."Grupage Date");
                        SerialNoInformation.Validate("Grupage Ship-to Name ERK", TempGrupageImport."Grupage Ship-to Name");
                        SerialNoInformation.Validate("Grupage Ship-to City ERK", TempGrupageImport."Grupage Ship-to City");
                        SerialNoInformation.Validate("Grupage Ship-to Address ERK", TempGrupageImport."Grupage Ship-to Address");
                        SerialNoInformation.Validate("Grupage Location Code ERK", TempGrupageImport."To Location Code");
                        SerialNoInformation.Validate("Grupage Bin Code ERK", TempGrupageImport."To Bin Code");
                        SerialNoInformation.Validate("Truck Plate ERK", TempGrupageImport."Shipping Agent Code");
                        SerialNoInformation.Modify(true);

                        // Create Vehicle Transfer Line if it doesn't exist
                        VehicleTransferLine.Reset();
                        VehicleTransferLine.SetRange("Document No.", VehicleTransferHeader."No.");
                        VehicleTransferLine.SetRange("Serial No.", TempGrupageImport."Serial No.");
                        if not VehicleTransferLine.FindFirst() then begin
                            VehicleTransferLine.Init();
                            VehicleTransferLine."Document No." := VehicleTransferHeader."No.";
                            VehicleTransferLine.Insert(true);
                            VehicleTransferLine.Validate("Serial No.", TempGrupageImport."Serial No.");
                            VehicleTransferLine.Modify(true);
                            LineCount += 1;
                        end;
                    until TempGrupageImport.Next() = 0;
                VehicleTransferHeader.Validate("Lines Locked", true);
                // VehicleTransferHeader.Validate(Released, true);
                VehicleTransferHeader.Modify(true);

                // Move to next distinct document number
                TempGrupageImportByDoc.SetFilter("Document No.", '>%1', TempGrupageImportByDoc."Document No.");
            until TempGrupageImportByDoc.FindFirst() = false;

        // Create additional vehicle transfer document for Dispatch Preparation
        TempGrupageImport.Reset();
        if TempGrupageImport.FindSet() then begin
            // Create dispatch preparation header
            DispatchVehicleHeader.Init();
            DispatchVehicleHeader.Insert(true);
            DispatchVehicleHeader.Validate("Operation Type", DispatchVehicleHeader."Operation Type"::"Dispatch Preparation");
            DispatchVehicleHeader.Validate("To Location Code", TempGrupageImport."To Location Code");
            DispatchVehicleHeader.Validate("To Bin Code", 'SEVK');
            // To Bin Code remains empty for header
            DispatchVehicleHeader.Modify(true);
            DocumentCount += 1;

            // Create lines for each vehicle in the grupage import
            TempGrupageImport.Reset();
            if TempGrupageImport.FindSet() then
                repeat
                    DispatchVehicleLine.Init();
                    DispatchVehicleLine."Document No." := DispatchVehicleHeader."No.";
                    DispatchVehicleLine.Insert(true);
                    DispatchVehicleLine.Validate("Serial No.", TempGrupageImport."Serial No.");
                    DispatchVehicleLine.Validate("To Location Code", TempGrupageImport."To Location Code");
                    DispatchVehicleLine.Validate("To Bin Code", TempGrupageImport."To Bin Code");
                    DispatchVehicleLine.Modify(true);
                    DispatchLineCount += 1;
                until TempGrupageImport.Next() = 0;
            DispatchVehicleHeader.Validate("Lines Locked", true);
            // DispatchVehicleHeader.Validate(Released, true);
            DispatchVehicleHeader.Modify(true);
        end;

        Message(SuccesMsg, DocumentCount, LineCount + DispatchLineCount);
        TempGrupageImport.Reset();
        TempGrupageImport.DeleteAll(false);
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
}