codeunit 60010 "Vehicle Rev./Exp. Management"
{
    procedure CreateCarCarrierRevenueExpenseLines(var TempVehicleRevExpWorksheetHdr: Record "Vehicle Rev/Exp. Worksheet Hdr"; var TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line")
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        ConfirmLbl: Label 'Do you want to process Serial Nos?';
    begin
        case TempVehicleRevExpWorksheetHdr.Type of
            TempVehicleRevExpWorksheetHdr.Type::Revenue:
                TempVehicleRevExpWorksheetHdr.TestField("Your Reference");
            TempVehicleRevExpWorksheetHdr.Type::Expense:
                TempVehicleRevExpWorksheetHdr.TestField("External Document No.");
        end;

        if not TempVehicleRevExpWorksheetLine.FindSet(false) then
            Error(NoLinesFoundErr);

        if not ConfirmManagement.GetResponseOrDefault(ConfirmLbl, true) then
            exit;

        Item.Get(TempVehicleRevExpWorksheetHdr."Item No.");
        if Item.IsVariantMandatory() then
            TempVehicleRevExpWorksheetHdr.TestField("Variant Code");

        CarCarrierManagement.PortCodeMandatoryCheck(TempVehicleRevExpWorksheetHdr."Item No.", TempVehicleRevExpWorksheetHdr."Port Code");

        repeat
            TempVehicleRevExpWorksheetLine.TestField("Unit Amount");
            CarCarrierLedgerEntry.SetRange("Serial No.", TempVehicleRevExpWorksheetLine."Serial No.");
            //CarCarrierLedgerEntry.SetFilter("Document No.", 'C*');
            if TempVehicleRevExpWorksheetHdr."Car Carrier No." <> '' then
                CarCarrierLedgerEntry.SetRange("Document No.", TempVehicleRevExpWorksheetHdr."Car Carrier No.");
            CarCarrierLedgerEntry.FindLast();

            CarCarrRevenueExpense.SetRange("Document No.", CarCarrierLedgerEntry."Document No.");
            CarCarrRevenueExpense.SetRange(Type, TempVehicleRevExpWorksheetHdr.Type);
            CarCarrRevenueExpense.SetRange("No.", TempVehicleRevExpWorksheetHdr."Item No.");
            CarCarrRevenueExpense.SetRange("Variant Code", TempVehicleRevExpWorksheetHdr."Variant Code");
            CarCarrRevenueExpense.SetRange("Currency Code", TempVehicleRevExpWorksheetHdr."Currency Code");
            CarCarrRevenueExpense.SetRange("Posting Date", TempVehicleRevExpWorksheetHdr."Posting Date");
            CarCarrRevenueExpense.SetRange("Source No.", TempVehicleRevExpWorksheetHdr."Source No.");
            CarCarrRevenueExpense.SetRange("Port Code", TempVehicleRevExpWorksheetHdr."Port Code");
            CarCarrRevenueExpense.SetRange("Your Reference", TempVehicleRevExpWorksheetHdr."Your Reference");
            CarCarrRevenueExpense.SetRange("External Document No.", TempVehicleRevExpWorksheetHdr."External Document No.");
            CarCarrRevenueExpense.SetRange("Unposted Invoice No.", '');

            if not CarCarrRevenueExpense.FindFirst() then
                CreateCarCarrierRevenueExpenseFromVehicleRevenueExpenseHeader(TempVehicleRevExpWorksheetHdr, CarCarrierLedgerEntry."Document No.", CarCarrRevenueExpense);

            CarCarrRevenueExpense.TestField("Unposted Invoice No.", '');
            CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrRevenueExpense."Unit Price/Cost" + TempVehicleRevExpWorksheetLine."Unit Amount");
            CarCarrRevenueExpense.Modify(true);

            SerialNoRevenueExpense.Init();
            SerialNoRevenueExpense."Serial No." := TempVehicleRevExpWorksheetLine."Serial No.";
            SerialNoRevenueExpense.Insert(true);
            SerialNoRevenueExpense.Validate("Document No.", CarCarrRevenueExpense."Document No.");
            SerialNoRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type);
            SerialNoRevenueExpense.Validate("No.", CarCarrRevenueExpense."No.");
            SerialNoRevenueExpense.Validate("Variant Code", CarCarrRevenueExpense."Variant Code");
            SerialNoRevenueExpense.Validate(Description, CarCarrRevenueExpense.Description);
            SerialNoRevenueExpense.Validate("Source No.", CarCarrRevenueExpense."Source No.");
            SerialNoRevenueExpense.Validate("Source Name", CarCarrRevenueExpense."Source Name");
            SerialNoRevenueExpense.Validate("Currency Code", CarCarrRevenueExpense."Currency Code");
            SerialNoRevenueExpense.Validate("Car Carrier Rev/Exp Line No.", CarCarrRevenueExpense."Line No.");
            SerialNoRevenueExpense.Validate("Currency Code", CarCarrRevenueExpense."Currency Code");
            SerialNoRevenueExpense.Validate(Amount, TempVehicleRevExpWorksheetLine."Unit Amount");
            SerialNoRevenueExpense.Modify(true);
        until TempVehicleRevExpWorksheetLine.Next() = 0;
    end;

    procedure DistributeTotalAmount(var TempVehicleRevExpWorksheetHdr: Record "Vehicle Rev/Exp. Worksheet Hdr"; var TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line")
    var
        UnitAmount: Decimal;
    begin
        TempVehicleRevExpWorksheetHdr.TestField("Total Amount");
        if not TempVehicleRevExpWorksheetLine.FindSet(false) then
            Error(NoLinesFoundErr);
        UnitAmount := TempVehicleRevExpWorksheetHdr."Total Amount" / TempVehicleRevExpWorksheetLine.Count();
        TempVehicleRevExpWorksheetLine.ModifyAll("Unit Amount", UnitAmount, false);
        TempVehicleRevExpWorksheetLine.CalcSums("Unit Amount");
        Message('Total Amount: %1\Total Line Amount: %2', TempVehicleRevExpWorksheetHdr."Total Amount", TempVehicleRevExpWorksheetLine."Unit Amount");
    end;

    procedure ClearCarCarrierNumbers(var TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line")
    begin
        if not TempVehicleRevExpWorksheetLine.FindSet(false) then
            Error(NoLinesFoundErr);
        TempVehicleRevExpWorksheetLine.ModifyAll("Car Carrier No.", '', false);
        Message('Car Carrier No. cleared from all worksheet lines.');
    end;

    procedure CalcualteTotalSalesACYFromCarCarrierLineDetail(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK"): Decimal
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        TotalSalesACY: Decimal;
    begin
        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLineDetail."Document No.");
        CarCarrierLedgerEntry.SetRange("Document Line No.", CarCarrierLineDetail."Document Line No.");
        CarCarrierLedgerEntry.SetRange("Document Line Detail No.", CarCarrierLineDetail."Line No.");
        if not CarCarrierLedgerEntry.FindSet() then
            exit(1);
        repeat
            SerialNoRevenueExpense.SetRange("Serial No.", CarCarrierLedgerEntry."Serial No.");
            SerialNoRevenueExpense.SetRange("Document No.", CarCarrierLedgerEntry."Document No.");
            SerialNoRevenueExpense.SetRange(Type, SerialNoRevenueExpense.Type::Revenue);
            SerialNoRevenueExpense.CalcSums("Amount (ACY)");
            TotalSalesACY += SerialNoRevenueExpense."Amount (ACY)";
        until CarCarrierLedgerEntry.Next() = 0;
        if TotalSalesACY = 0 then
            exit(1);
        exit(TotalSalesACY);
    end;

    procedure CalcualteTotalExpenseACYFromCarCarrierLineDetail(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK"): Decimal
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        TotalExpenseACY: Decimal;
    begin
        CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLineDetail."Document No.");
        CarCarrierLedgerEntry.SetRange("Document Line No.", CarCarrierLineDetail."Document Line No.");
        CarCarrierLedgerEntry.SetRange("Document Line Detail No.", CarCarrierLineDetail."Line No.");
        if not CarCarrierLedgerEntry.FindSet() then
            exit(0);
        repeat
            SerialNoRevenueExpense.SetRange("Serial No.", CarCarrierLedgerEntry."Serial No.");
            SerialNoRevenueExpense.SetRange("Document No.", CarCarrierLedgerEntry."Document No.");
            SerialNoRevenueExpense.SetRange(Type, SerialNoRevenueExpense.Type::Expense);
            SerialNoRevenueExpense.CalcSums("Amount (ACY)");
            TotalExpenseACY += SerialNoRevenueExpense."Amount (ACY)";
        until CarCarrierLedgerEntry.Next() = 0;
        exit(TotalExpenseACY);
    end;

    procedure CarCarrierReportJobQueue()
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        CarCarrierLineHandled: Record "Car Carrier Line Handled ERK";
        TypeHelper: Codeunit "Type Helper";
        LastDocumentNo: Code[20];
    begin
        LastDocumentNo := '';
        CarCarrierLineDetail.SetAutoCalcFields("Voyage Ending Date-Time");
        //CarCarrierLineDetail.SetFilter("Document No.", '%1', 'C25*');
        CarCarrierLineDetail.SetFilter("Discharge Start Date-Time", '>%1', CreateDateTime(20241231D, 235900T));
        if not CarCarrierLineDetail.FindSet(true) then
            exit;

        CarCarrierLineHandled.Reset();
        CarCarrierLineHandled.SetRange(Handled, false);
        CarCarrierLineHandled.DeleteAll(false);

        repeat

            if LastDocumentNo <> CarCarrierLineDetail."Document No." then begin
                CarCarrierLineDetail."Total Sales (ACY)" := CalcualteTotalSalesACYFromCarCarrierLineDetailWithCarCarrRevenueExpense(CarCarrierLineDetail);
                CarCarrierLineDetail."Total Expense (ACY)" := CalcualteTotalExpenseACYFromCarCarrierLineDetailWithCarCarrRevenueExpense(CarCarrierLineDetail);
            end else begin
                CarCarrierLineDetail."Total Sales (ACY)" := 0;
                CarCarrierLineDetail."Total Expense (ACY)" := 0;
            end;

            CarCarrierLineDetail."Total Profit (ACY)" := CarCarrierLineDetail."Total Sales (ACY)" - CarCarrierLineDetail."Total Expense (ACY)";

            if (CarCarrierLineDetail."Total Sales (ACY)" = 0) or (CarCarrierLineDetail."Total Expense (ACY)" = 0) then
                CarCarrierLineDetail.Profitability := 0
            else
                CarCarrierLineDetail.Profitability := CarCarrierLineDetail."Total Profit (ACY)" / CarCarrierLineDetail."Total Sales (ACY)";



            if TypeHelper.CompareDateTime(CarCarrierLineDetail."Voyage Ending Date-Time", CreateDateTime(0D, 0T)) in [0, -1] then begin
                if CarCarrierHeader.Get(CarCarrierLineDetail."Document No.") then begin
                    if CarCarrierHeader."Planned Ending Date" = 0D then begin
                        CarCarrierLineDetail.Week := 0;
                        CarCarrierLineDetail.Month := 0;
                        CarCarrierLineDetail.Year := 0;
                    end else begin
                        CarCarrierLineDetail.Week := ErkHoldingBasicFunctions.ConvertDateTimeToWeek(CreateDateTime(CarCarrierHeader."Planned Ending Date", 0T));
                        CarCarrierLineDetail.Month := ErkHoldingBasicFunctions.ConvertDateTimeToMonth(CreateDateTime(CarCarrierHeader."Planned Ending Date", 0T));
                        CarCarrierLineDetail.Year := ErkHoldingBasicFunctions.ConvertDateTimetoYear(CreateDateTime(CarCarrierHeader."Planned Ending Date", 0T));
                    end;
                end else begin
                    CarCarrierLineDetail.Week := 0;
                    CarCarrierLineDetail.Month := 0;
                    CarCarrierLineDetail.Year := 0;
                end;

            end else begin
                CarCarrierLineDetail.Week := ErkHoldingBasicFunctions.ConvertDateTimeToWeek(CarCarrierLineDetail."Voyage Ending Date-Time");
                CarCarrierLineDetail.Month := ErkHoldingBasicFunctions.ConvertDateTimeToMonth(CarCarrierLineDetail."Voyage Ending Date-Time");
                CarCarrierLineDetail.Year := ErkHoldingBasicFunctions.ConvertDateTimetoYear(CarCarrierLineDetail."Voyage Ending Date-Time");
            end;

            CarCarrierLineDetail.Modify(false);
            CarCarrierLineHandled.InsertNewRecord(CarCarrierLineDetail);

            LastDocumentNo := CarCarrierLineDetail."Document No.";
        until CarCarrierLineDetail.Next() = 0;
    end;

    local procedure CalcualteTotalSalesACYFromCarCarrierLineDetailWithCarCarrRevenueExpense(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK"): Decimal
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        CarCarrRevenueExpense.Reset();
        CarCarrRevenueExpense.SetRange("Document No.", CarCarrierLineDetail."Document No.");
        CarCarrRevenueExpense.SetRange(Type, CarCarrRevenueExpense.Type::Revenue);
        CarCarrRevenueExpense.SetRange(Status, CarCarrRevenueExpense.Status::Ready);
        CarCarrRevenueExpense.CalcSums("Line Amount (ACY)");
        exit(CarCarrRevenueExpense."Line Amount (ACY)");
    end;

    local procedure CalcualteTotalExpenseACYFromCarCarrierLineDetailWithCarCarrRevenueExpense(CarCarrierLineDetail: Record "Car Carrier Line Detail ERK"): Decimal
    var
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        CarCarrRevenueExpense.Reset();
        CarCarrRevenueExpense.SetRange("Document No.", CarCarrierLineDetail."Document No.");
        CarCarrRevenueExpense.SetFilter(Type, '%1|%2|%3', CarCarrRevenueExpense.Type::Expense, CarCarrRevenueExpense.Type::Consumption, CarCarrRevenueExpense.Type::"Realized Expense - No Proforma");
        CarCarrRevenueExpense.SetRange(Status, CarCarrRevenueExpense.Status::Ready);
        CarCarrRevenueExpense.CalcSums("Line Amount (ACY)");
        exit(CarCarrRevenueExpense."Line Amount (ACY)");
    end;

    internal procedure CreateCarCarrierDetailForBalastVoyages()
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
        CarCarrierLine: Record "Car Carrier Line ERK";
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        SuccessMsg: Label 'Success';
    begin
        CarCarrierHeader.Reset();
        CarCarrierHeader.SetRange("Voyage Type", CarCarrierHeader."Voyage Type"::"Ballast - Ro-Ro");
        if CarCarrierHeader.FindSet() then
            repeat
                CarCarrierLine.Reset();
                CarCarrierLine.SetRange("Document No.", CarCarrierHeader."No.");
                if not CarCarrierLine.FindFirst() then begin
                    CarCarrierLine.Init();
                    CarCarrierLine."Document No." := CarCarrierHeader."No.";
                    CarCarrierLine.Insert(true);
                end;

                Clear(CarCarrierLineDetail);
                if not CarCarrierLineDetail.Get(CarCarrierLine."Document No.", CarCarrierLine."Line No.", 10000) then begin
                    CarCarrierLineDetail.Init();
                    CarCarrierLineDetail."Document No." := CarCarrierLine."Document No.";
                    CarCarrierLineDetail."Document Line No." := CarCarrierLine."Line No.";
                    CarCarrierLineDetail.Insert(true);
                end;
            until CarCarrierHeader.Next() = 0;

        Message(SuccessMsg);
    end;

    procedure CreateVehicleRevenueExpenseWorksheet(InvoiceNo: Code[20];
                                                    InvoiceLineNo: Integer;
                                                    Type: Enum "Voyage Line Detail Type ERK";
                                                    ItemNo: Code[20];
                                                    VariantCode: Code[10];
                                                    PortCode: Code[10];
                                                    SourceNo: Code[20];
                                                    YourReference: Text[35];
                                                    PostingDate: Date;
                                                    CurrencyCode: Code[10];
                                                    ExternalDocumentNo: Text[35];
                                                    TotalAmount: Decimal;
                                                    RunPage: Boolean;
                                                    DepartmentCode: Code[20]): Record "Vehicle Rev/Exp. Worksheet Hdr" temporary
    var
        TempVehicleRevExpWorksheetHdr: Record "Vehicle Rev/Exp. Worksheet Hdr" temporary;
        departmentCodeMissingErr: Label 'Department Code must be filled.';
    begin
        if DepartmentCode = '' then
            Error(departmentCodeMissingErr);

        TempVehicleRevExpWorksheetHdr.Init();
        TempVehicleRevExpWorksheetHdr."Created From Invoice Line" := true;
        TempVehicleRevExpWorksheetHdr."Invoice No." := InvoiceNo;
        TempVehicleRevExpWorksheetHdr."Invoice Line No." := InvoiceLineNo;
        TempVehicleRevExpWorksheetHdr.Type := Type;
        TempVehicleRevExpWorksheetHdr."Item No." := ItemNo;
        TempVehicleRevExpWorksheetHdr."Variant Code" := VariantCode;
        TempVehicleRevExpWorksheetHdr."Port Code" := PortCode;
        TempVehicleRevExpWorksheetHdr.Validate("Source No.", SourceNo);
        TempVehicleRevExpWorksheetHdr."Your Reference" := YourReference;
        TempVehicleRevExpWorksheetHdr."Posting Date" := PostingDate;
        TempVehicleRevExpWorksheetHdr."Currency Code" := CurrencyCode;
        TempVehicleRevExpWorksheetHdr."External Document No." := ExternalDocumentNo;
        TempVehicleRevExpWorksheetHdr."Total Amount" := TotalAmount;
        TempVehicleRevExpWorksheetHdr."Department Code" := DepartmentCode;
        TempVehicleRevExpWorksheetHdr.Insert(false);

        if RunPage then
            Page.RunModal(Page::"Vehicle Rev/Exp. Worksheet ERK", TempVehicleRevExpWorksheetHdr);

        exit(TempVehicleRevExpWorksheetHdr);
    end;

    procedure CreateVehicleRevenueExpenseLinesFromVehicleRevenueExpenseWorksheet(var TempVehicleRevExpWorksheetHdr: Record "Vehicle Rev/Exp. Worksheet Hdr"; var TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line")
    var
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        AmountsNoEqualErr: Label 'Total Amount and Total Line Amount are not equal.\Total Amount: %1\Total Line Amount: %2', Comment = '%1="Vehicle Rev/Exp. Worksheet Hdr"."Total Amount"; %2=TotalLineAmount';
        TotalLineAmount: Decimal;
    begin
        if not TempVehicleRevExpWorksheetLine.FindSet(false) then
            Error(NoLinesFoundErr);

        repeat
            SerialNoRevenueExpense.Init();
            SerialNoRevenueExpense.Validate("Serial No.", TempVehicleRevExpWorksheetLine."Serial No.");
            SerialNoRevenueExpense.Insert(true);
            SerialNoRevenueExpense."Document No." := TempVehicleRevExpWorksheetLine."Car Carrier No.";
            SerialNoRevenueExpense.Validate("Unposted Invoice No.", TempVehicleRevExpWorksheetHdr."Invoice No.");
            SerialNoRevenueExpense.Validate("Invoice Line No.", TempVehicleRevExpWorksheetHdr."Invoice Line No.");
            SerialNoRevenueExpense.Validate(Type, TempVehicleRevExpWorksheetHdr.Type);
            SerialNoRevenueExpense.Validate("No.", TempVehicleRevExpWorksheetHdr."Item No.");
            SerialNoRevenueExpense.Validate("Variant Code", TempVehicleRevExpWorksheetHdr."Variant Code");
            SerialNoRevenueExpense.Validate(Description, ExportManagement.GetItemTranslation(TempVehicleRevExpWorksheetHdr."Item No.", TempVehicleRevExpWorksheetHdr."Variant Code", ''));
            SerialNoRevenueExpense.Validate("Source No.", TempVehicleRevExpWorksheetHdr."Source No.");
            SerialNoRevenueExpense.Validate("Source Name", TempVehicleRevExpWorksheetHdr."Source Name");
            SerialNoRevenueExpense.Validate("Currency Code", TempVehicleRevExpWorksheetHdr."Currency Code");
            SerialNoRevenueExpense.Validate("Department Code", TempVehicleRevExpWorksheetHdr."Department Code");
            SerialNoRevenueExpense.Validate("Created From Invoice", TempVehicleRevExpWorksheetHdr."Created From Invoice Line");
            SerialNoRevenueExpense.Validate("Posting Date", TempVehicleRevExpWorksheetHdr."Posting Date");

            SerialNoRevenueExpense.Validate(Amount, TempVehicleRevExpWorksheetLine."Unit Amount"); //Must be last line to validate.

            TotalLineAmount += TempVehicleRevExpWorksheetLine."Unit Amount";
            SerialNoRevenueExpense.Modify(true);

            InternalVoucherMngt.CreateOrUpdateCarCarrierRevenueExpenseFromSerialNoRevenueExpense(SerialNoRevenueExpense);
        until TempVehicleRevExpWorksheetLine.Next() = 0;

        if Abs(TempVehicleRevExpWorksheetHdr."Total Amount" - TotalLineAmount) > 1 then
            Error(AmountsNoEqualErr, TempVehicleRevExpWorksheetHdr."Total Amount", TotalLineAmount);
    end;

    #region Sales Documents

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnValidatePostingDateOnAfterCheckNeedUpdateCurrencyFactor, '', false, false)]
    local procedure "Sales Header_OnValidatePostingDateOnAfterCheckNeedUpdateCurrencyFactor"(var SalesHeader: Record "Sales Header"; xSalesHeader: Record "Sales Header"; var NeedUpdateCurrencyFactor: Boolean)
    begin
        UpdateSerialNoRevExpPostingDatesForSalesHeader(SalesHeader);
    end;

    local procedure UpdateSerialNoRevExpPostingDatesForSalesHeader(var SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        SuccesMsg: Label 'Posting Dates are updated for Serial No. Revenue/Expense Lines.';
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetFilter("Distributed Quantity ERK", '<> %1', 0);
        if not SalesLine.FindSet() then
            exit;

        repeat
            // SerialNoRevenueExpense.SetRange("Document No.", SalesHeader."No.");
            // SerialNoRevenueExpense.SetRange("Car Carrier Rev/Exp Line No.", SalesLine."Line No.");
            SerialNoRevenueExpense.SetRange("Unposted Invoice No.", SalesHeader."No.");
            SerialNoRevenueExpense.SetRange("Invoice Line No.", SalesLine."Line No.");
            SerialNoRevenueExpense.FindSet(true);
            repeat
                SerialNoRevenueExpense.Validate("Posting Date", SalesHeader."Posting Date");
                SerialNoRevenueExpense.Modify(true);
            until SerialNoRevenueExpense.Next() = 0;
        until SalesLine.Next() = 0;

        Message(SuccesMsg);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterSalesInvLineInsert, '', false, false)]
    local procedure "Sales-Post_OnAfterSalesInvLineInsert"(var SalesInvLine: Record "Sales Invoice Line"; SalesInvHeader: Record "Sales Invoice Header"; SalesLine: Record "Sales Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSuppressed: Boolean; var SalesHeader: Record "Sales Header"; var TempItemChargeAssgntSales: Record "Item Charge Assignment (Sales)" temporary; var TempWhseShptHeader: Record "Warehouse Shipment Header" temporary; var TempWhseRcptHeader: Record "Warehouse Receipt Header" temporary; PreviewMode: Boolean)
    begin
        UpdateDocumentNoOnAfterSalesInvoicePost(SalesInvHeader, SalesLine, SalesHeader);
    end;

    local procedure UpdateDocumentNoOnAfterSalesInvoicePost(var SalesInvHeader: Record "Sales Invoice Header"; var SalesLine: Record "Sales Line"; var SalesHeader: Record "Sales Header")
    var
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
    begin
        if SalesHeader."Document Type" <> SalesHeader."Document Type"::Invoice then
            exit;

        // SerialNoRevenueExpense.SetRange("Document No.", SalesHeader."No.");
        // SerialNoRevenueExpense.SetRange("Car Carrier Rev/Exp Line No.", SalesLine."Line No.");
        SerialNoRevenueExpense.SetRange("Unposted Invoice No.", SalesHeader."No.");
        SerialNoRevenueExpense.SetRange("Invoice Line No.", SalesLine."Line No.");
        SerialNoRevenueExpense.ModifyAll("Posted Invoice No.", SalesInvHeader."No.", true);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnBeforeUpdateAmounts, '', false, false)]
    local procedure "Sales Line_OnBeforeUpdateAmounts"(var SalesLine: Record "Sales Line"; xSalesLine: Record "Sales Line"; CurrentFieldNo: Integer; var IsHandled: Boolean)
    begin
        CheckDistributedQtyOnSalesLine(SalesLine, CurrentFieldNo);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnBeforeValidateType, '', false, false)]
    local procedure "Sales Line_OnBeforeValidateType"(var SalesLine: Record "Sales Line"; xSalesLine: Record "Sales Line"; CurrentFieldNo: Integer; var IsHandled: Boolean)
    begin
        CheckDistributedQtyOnSalesLine(SalesLine, CurrentFieldNo);
    end;

    local procedure CheckDistributedQtyOnSalesLine(var SalesLine: Record "Sales Line"; CurrentFieldNo: Integer)
    begin
        if CurrentFieldNo = 0 then
            exit;

        SalesLine.CalcFields("Distributed Quantity ERK");
        if SalesLine."Distributed Quantity ERK" = 0 then
            exit;

        Error(DistributedQtyMustBeZeroErr);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure "Sales Line_OnDeleteOnBeforeTestStatusOpen"(var SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    begin
        CheckDistributedQtyOnSalesLine(SalesLine, 1);
    end;

    #endregion Sales Documents

    #region Purchase Documents
    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnValidatePostingDateOnAfterCheckNeedUpdateCurrencyFactor, '', false, false)]
    local procedure "Purchase Header_OnValidatePostingDateOnAfterCheckNeedUpdateCurrencyFactor"(var PurchaseHeader: Record "Purchase Header"; xPurchaseHeader: Record "Purchase Header"; var SkipJobCurrFactorUpdate: Boolean)
    begin
        UpdateSerialNoRevExpPostingDatesForPurchaseHeader(PurchaseHeader);
    end;

    procedure UpdateSerialNoRevExpPostingDatesForPurchaseHeader(var PurchaseHeader: Record "Purchase Header")
    var
        PurchaseLine: Record "Purchase Line";
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        SuccesMsg: Label 'Posting Dates are updated for Serial No. Revenue/Expense Lines.';
    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetFilter("Distributed Quantity ERK", '<> %1', 0);
        if not PurchaseLine.FindSet() then
            exit;

        repeat
            // SerialNoRevenueExpense.SetRange("Document No.", PurchaseHeader."No.");
            // SerialNoRevenueExpense.SetRange("Car Carrier Rev/Exp Line No.", PurchaseLine."Line No.");
            SerialNoRevenueExpense.SetRange("Unposted Invoice No.", PurchaseHeader."No.");
            SerialNoRevenueExpense.SetRange("Invoice Line No.", PurchaseLine."Line No.");
            SerialNoRevenueExpense.FindSet(true);
            repeat
                SerialNoRevenueExpense.Validate("Posting Date", PurchaseHeader."Posting Date");
                SerialNoRevenueExpense.Modify(true);
            until SerialNoRevenueExpense.Next() = 0;
        until PurchaseLine.Next() = 0;

        Message(SuccesMsg);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPurchInvLineInsert, '', false, false)]
    local procedure "Purch.-Post_OnAfterPurchInvLineInsert"(var PurchInvLine: Record "Purch. Inv. Line"; PurchInvHeader: Record "Purch. Inv. Header"; PurchLine: Record "Purchase Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSupressed: Boolean; PurchHeader: Record "Purchase Header"; PurchRcptHeader: Record "Purch. Rcpt. Header"; TempWhseRcptHeader: Record "Warehouse Receipt Header"; var ItemJnlPostLine: Codeunit "Item Jnl.-Post Line")
    begin
        UpdateDocumentNoOnAfterPurchaseInvoicePost(PurchInvHeader, PurchLine, PurchHeader);
    end;

    local procedure UpdateDocumentNoOnAfterPurchaseInvoicePost(var PurchInvHeader: Record "Purch. Inv. Header"; var PurchLine: Record "Purchase Line"; var PurchHeader: Record "Purchase Header")
    var
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
    begin
        if PurchHeader."Document Type" <> PurchHeader."Document Type"::Invoice then
            exit;

        // SerialNoRevenueExpense.SetRange("Document No.", PurchHeader."No.");
        // SerialNoRevenueExpense.SetRange("Car Carrier Rev/Exp Line No.", PurchLine."Line No.");
        SerialNoRevenueExpense.SetRange("Unposted Invoice No.", PurchHeader."No.");
        SerialNoRevenueExpense.SetRange("Invoice Line No.", PurchLine."Line No.");
        SerialNoRevenueExpense.ModifyAll("Posted Invoice No.", PurchInvHeader."No.", true);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnBeforeUpdateAmounts, '', false, false)]
    local procedure "Purchase Line_OnBeforeUpdateAmounts"(var PurchaseLine: Record "Purchase Line"; xPurchaseLine: Record "Purchase Line"; CurrentFieldNo: Integer; var IsHandled: Boolean)
    begin
        CheckDistributedQtyOnPurchaseLine(PurchaseLine);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnBeforeValidateType, '', false, false)]
    local procedure "Purchase Line_OnBeforeValidateType"(var PurchaseLine: Record "Purchase Line"; xPurchaseLine: Record "Purchase Line"; CurrentFieldNo: Integer; var IsHandled: Boolean)
    begin
        CheckDistributedQtyOnPurchaseLine(PurchaseLine);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure "Purchase Line_OnDeleteOnBeforeTestStatusOpen"(var PurchaseLine: Record "Purchase Line"; var IsHandled: Boolean)
    begin
        CheckDistributedQtyOnPurchaseLine(PurchaseLine);
    end;

    local procedure CheckDistributedQtyOnPurchaseLine(var PurchLine: Record "Purchase Line")
    begin
        PurchLine.CalcFields("Distributed Quantity ERK");
        if PurchLine."Distributed Quantity ERK" = 0 then
            exit;

        Error(DistributedQtyMustBeZeroErr);
    end;

    local procedure CreateCarCarrierRevenueExpenseFromVehicleRevenueExpenseHeader(var TempVehicleRevExpWorksheetHdr: Record "Vehicle Rev/Exp. Worksheet Hdr"; CarCarrierNo: Code[20]; var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    begin
        CarCarrRevenueExpense.Init();
        CarCarrRevenueExpense."Document No." := CarCarrierNo;
        CarCarrRevenueExpense.Insert(true);

        CarCarrRevenueExpense.Validate(Type, TempVehicleRevExpWorksheetHdr.Type);
        CarCarrRevenueExpense.Validate("No.", TempVehicleRevExpWorksheetHdr."Item No.");
        CarCarrRevenueExpense.Validate("Variant Code", TempVehicleRevExpWorksheetHdr."Variant Code");
        CarCarrRevenueExpense.Validate(Quantity, 1);
        CarCarrRevenueExpense.Validate("Posting Date", TempVehicleRevExpWorksheetHdr."Posting Date");
        CarCarrRevenueExpense.Validate("Source No.", TempVehicleRevExpWorksheetHdr."Source No.");
        CarCarrRevenueExpense.Validate("Currency Code", TempVehicleRevExpWorksheetHdr."Currency Code");
        CarCarrRevenueExpense.Validate("Your Reference", TempVehicleRevExpWorksheetHdr."Your Reference");
        CarCarrRevenueExpense.Validate("Port Code", TempVehicleRevExpWorksheetHdr."Port Code");
        CarCarrRevenueExpense.Validate("External Document No.", TempVehicleRevExpWorksheetHdr."External Document No.");
    end;

    #endregion Purchase Documents

    var
        Item: Record Item;
        ConfirmManagement: Codeunit "Confirm Management";
        CarCarrierManagement: Codeunit "Car Carrier Management ERK";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        ExportManagement: Codeunit "Export Management ERK";
        InternalVoucherMngt: Codeunit "Internal Voucher Mngt. ERK";
        NoLinesFoundErr: Label 'No lines found in Vehicle Rev/Exp. Worksheet Line';
        DistributedQtyMustBeZeroErr: Label 'Distributed Quantity must be 0.';
}
