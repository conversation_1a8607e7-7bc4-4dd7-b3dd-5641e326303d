page 60042 "Loading/Discharging Worksheet"
{
    ApplicationArea = All;
    Caption = 'Loading/Discharging Worksheet';
    PageType = Worksheet;
    SourceTable = "Loading/Discharging Worksheet";
    AutoSplitKey = true;
    DelayedInsert = true;
    SourceTableTemporary = true;
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(OperationInformation)
            {
                Caption = 'Operation Information';

                field(DocumentNo; DocumentNo)
                {
                    Caption = 'Document No.';
                    ToolTip = 'Specifies the value of the Document No. field.';
                    Editable = false;
                }
                field(CustomerNo; CustomerNo)
                {
                    Caption = 'Customer No.';
                    ToolTip = 'Specifies the value of the Customer No. field.';
                    Editable = false;
                }
                // field(Type; Type)
                // {
                //     Caption = 'Type';
                //     ToolTip = 'Specifies the value of the Type field.';
                // }
                field(LoadingPort; LoadingPort)
                {
                    Caption = 'Loading Port';
                    ToolTip = 'Specifies the value of the Port field.';
                    Editable = false;
                }
                field(DischargePort; DischargePort)
                {
                    Caption = 'Discharge Port';
                    ToolTip = 'Specifies the value of the Port field.';
                    Editable = false;
                }
                field(ToLoadVehicleQty; ToLoadVehicleQty)
                {
                    Caption = 'To Load Vehicle Quantity';
                    ToolTip = 'Specifies the value of the To Load Vehicle Quantity field.';
                    Editable = false;
                }
                field(DoNotCreateDischargeOrder; DoNotCreateDischargeOrder)
                {
                    Caption = 'Do Not Create Discharge Order';
                    ToolTip = 'Specifies the value of the Do Not Create Discharge Order field.';

                    trigger OnValidate()
                    begin
                        Rec.ModifyAll(Rec."Do Not Create Discharge Order", DoNotCreateDischargeOrder, false);
                    end;
                }
            }
            group(VehicleEnterance)
            {
                Caption = 'Vehicle Enterance';

                repeater(General)
                {
                    field("Serial No."; Rec."Serial No.")
                    {
                    }
                    field("Carline Code"; Rec."Model Version Code")
                    {
                    }
                    field("Truck Plate ERK"; Rec."Truck Plate ERK")
                    {
                    }
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(RegisterOperation)
            {
                Caption = 'Register Operation';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Registered;
                PromotedOnly = true;
                ToolTip = 'Executes the Register Operation action.';

                trigger OnAction()
                begin
                    CarCarrierManagement.RegisterLoadDischarge(Rec);
                    CurrPage.Close();
                end;
            }
        }
    }
    procedure SetGlobalVars(parDocumentNo: Code[20]; parCustomerNo: Code[20]; parLoadingPort: Code[10]; parDischargePort: Code[10]; parOperationDateTime: DateTime; parDocumentLineNo: Integer; parDocumentLineDetailNo: Integer)
    begin
        DocumentNo := parDocumentNo;
        CustomerNo := parCustomerNo;
        LoadingPort := parLoadingPort;
        OperationDateTime := parOperationDateTime;
        DischargePort := parDischargePort;
        DocumentLineNo := parDocumentLineNo;
        DocumentLineDetailNo := parDocumentLineDetailNo;
        //CarCarrierLineDetail := parCarCarrierLineDetail;
    end;

    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        Rec."Document No." := DocumentNo;
        Rec."Customer No." := CustomerNo;
        Rec."Loading Port" := LoadingPort;
        Rec."Operation Date-Time" := OperationDateTime;
        Rec."Discharge Port" := DischargePort;
        Rec."Document Line No." := DocumentLineNo;
        Rec."Document Line Detail No." := DocumentLineDetailNo;
        Rec."Do Not Create Discharge Order" := DoNotCreateDischargeOrder;
    end;

    trigger OnAfterGetCurrRecord()
    begin
        CalcualteToLoadVehicleQuantity(Rec);
    end;

    procedure CalcualteToLoadVehicleQuantity(var LoadingDischargingWorksheet: Record "Loading/Discharging Worksheet"): Integer
    begin
        ToLoadVehicleQty := LoadingDischargingWorksheet.Count();
    end;

    var //CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        CarCarrierManagement: Codeunit "Car Carrier Management ERK";
        DocumentNo: Code[20];
        CustomerNo: Code[20];
        LoadingPort: Code[10];
        OperationDateTime: DateTime;
        DischargePort: Code[10];
        ToLoadVehicleQty: Integer;
        DocumentLineNo: Integer;
        DocumentLineDetailNo: Integer;
        DoNotCreateDischargeOrder: Boolean;
}
