report 60012 "Vehicle Notes ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle Notes';
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem(SerialNoInformation; "Serial No. Information")
        {
            RequestFilterFields = "Serial No.", "Current Location Code ERK", "Current Bin Code ERK";

            column(SerialNo; "Serial No.")
            {
            }
            column(Brand_Code_ERK; "Brand Code ERK")
            {
            }
            column(Model_Code_ERK; "Model Code ERK")
            {
            }
            column(Fuel_Type_ERK; "Fuel Type ERK")
            {
            }
            column(Colour_Name_ERK; "Colour Name ERK")
            {
            }
            column(Model_Version_ERK; "Model Version ERK")
            {
            }
            column(Current_Location_Code_ERK; "Current Location Code ERK")
            {
            }
            column(Current_Bin_Code_ERK; "Current Bin Code ERK")
            {
            }
            dataitem("Record Link"; "Record Link")
            {
                DataItemTableView = where(Type = const(Type::Note));
                CalcFields = Note;

                column(Note; RecordLinkManagement.ReadNote("Record Link"))
                {
                }
                trigger OnPreDataItem()
                begin
                    "Record Link".SetRange("Record ID", RecRef.RecordId());
                end;
            }
            trigger OnAfterGetRecord()
            begin
                RecRef.GetTable(SerialNoInformation);
            end;
        }
    }
    var //NoteManagement: Codeunit "Note Management ERK";
        RecordLinkManagement: Codeunit "Record Link Management";
        RecRef: RecordRef;
}
