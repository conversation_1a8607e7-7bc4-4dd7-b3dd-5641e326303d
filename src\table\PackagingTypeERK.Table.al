table 60004 "Packaging Type ERK"
{
    Caption = 'Packaging Type';
    DataClassification = CustomerContent;
    DrillDownPageId = "Packaging Type List ERK";
    LookupPageId = "Packaging Type List ERK";

    fields
    {
        field(1; Code; Code[10])
        {
            Caption = 'Code';
            NotBlank = true;
            ToolTip = 'Specifies the value of the Code field.';
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(3; Factor; Decimal)
        {
            Caption = 'Factor';
            ToolTip = 'Specifies the value of the Factor field.';
        }
    }
    keys
    {
        key(PK; Code)
        {
            Clustered = true;
        }
    }
}
