page 60151 "Dredge Document ERK"
{
    ApplicationArea = All;
    Caption = 'Dredge Document';
    PageType = Document;
    SourceTable = "Dredge Header ERK";
    UsageCategory = None;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = (Rec.Status <> Rec.Status::Completed);

                field("No."; Rec."No.")
                {
                    ShowMandatory = true;
                }
                field(Status; Rec.Status)
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                    ShowMandatory = (Rec.Status = Rec.Status::Active) or (Rec.Status = Rec.Status::Completed);
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Department Code"; Rec."Department Code")
                {
                    ShowMandatory = true;
                }
                field("Department Name"; Rec."Department Name")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Port Code"; Rec."Port Code")
                {
                    ShowMandatory = true;
                }
                field("Port Name"; Rec."Port Name")
                {
                }
                field("Operation Starting Date"; Rec."Operation Starting Date")
                {
                    ShowMandatory = (Rec.Status = Rec.Status::Active) or (Rec.Status = Rec.Status::Completed);
                }
                field("Operation Ending Date"; Rec."Operation Ending Date")
                {
                    ShowMandatory = (Rec.Status = Rec.Status::Completed);
                }
            }
            group(FinancialInformation)
            {
                Caption = 'Financial Information';

                field("Expected Revenue (ACY)"; Rec."Expected Revenue (ACY)")
                {
                }
                field("Actual Revenue (ACY)"; Rec."Actual Revenue (ACY)")
                {
                }
                field("Expected Expense (ACY)"; Rec."Expected Expense (ACY)")
                {
                }
                field("Actual Expense (ACY)"; Rec."Actual Expense (ACY)")
                {
                }
                field(ExpectedProfitACY; Rec."Expected Revenue (ACY)" - Rec."Expected Expense (ACY)")
                {
                    Caption = 'Expected Profit (ACY)';
                    ToolTip = 'Specifies the expected profit in Additional Currency.';
                }
                field(ActualProfitACY; Rec."Actual Revenue (ACY)" - Rec."Actual Expense (ACY)")
                {
                    Caption = 'Actual Profit (ACY)';
                    ToolTip = 'Specifies the actual profit in Additional Currency.';
                }
            }
            part(Lines; "Dredge Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Editable = (Rec.Status <> Rec.Status::Completed);
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                Caption = 'Attachments';
                SubPageLink = "Table ID" = const(Database::"Dredge Header ERK"), "No." = field("No.");
            }
        }
    }

    actions
    {
        area(Processing)
        {
            //     action(CreatePurchaseInvoice)
            //     {
            //         Caption = 'Create Purchase Invoice';
            //         Image = NewPurchaseInvoice;
            //         Promoted = true;
            //         PromotedCategory = Process;
            //         PromotedOnly = true;
            //         ToolTip = 'Creates a purchase invoice from the selected dredge lines.';

            //         trigger OnAction()
            //         var
            //             DredgeLine: Record "Dredge Line ERK";
            //             DredgeLinesPage: Page "Dredge Lines ERK";
            //             NoEligibleExpenseLinesErr: Label 'No eligible expense lines found for creating purchase invoices.';
            //         begin
            //             DredgeLine.SetRange("Document No.", Rec."No.");
            //             DredgeLine.SetRange(Type, DredgeLine.Type::Expense);
            //             DredgeLine.SetFilter("Unposted Invoice No.", '=%1', '');
            //             DredgeLine.SetFilter("Posted Invoice No.", '=%1', '');

            //             if not DredgeLine.FindSet() then
            //                 Error(NoEligibleExpenseLinesErr);

            //             DredgeLinesPage.SetTableView(DredgeLine);
            //             DredgeLinesPage.SetRecord(DredgeLine);
            //             DredgeLinesPage.LookupMode := true;
            //             if DredgeLinesPage.RunModal() = Action::LookupOK then
            //                 Message('Purchase invoice creation would be implemented here.');
            //         end;
            //     }
            //     action(CreateSalesInvoice)
            //     {
            //         Caption = 'Create Sales Invoice';
            //         Image = NewSalesInvoice;
            //         Promoted = true;
            //         PromotedCategory = Process;
            //         PromotedOnly = true;
            //         ToolTip = 'Creates a sales invoice from the selected dredge lines.';

            //         trigger OnAction()
            //         var
            //             DredgeLine: Record "Dredge Line ERK";
            //             DredgeLinesPage: Page "Dredge Lines ERK";
            //             NoEligibleRevenueLinesErr: Label 'No eligible revenue lines found for creating sales invoices.';
            //         begin
            //             DredgeLine.SetRange("Document No.", Rec."No.");
            //             DredgeLine.SetRange(Type, DredgeLine.Type::Revenue);
            //             DredgeLine.SetFilter("Unposted Invoice No.", '=%1', '');
            //             DredgeLine.SetFilter("Posted Invoice No.", '=%1', '');

            //             if not DredgeLine.FindSet() then
            //                 Error(NoEligibleRevenueLinesErr);

            //             DredgeLinesPage.SetTableView(DredgeLine);
            //             DredgeLinesPage.SetRecord(DredgeLine);
            //             DredgeLinesPage.LookupMode := true;
            //             if DredgeLinesPage.RunModal() = Action::LookupOK then
            //                 Message('Sales invoice creation would be implemented here.');
            //         end;
            //     }
        }
        area(Navigation)
        {
            action(DredgeLines)
            {
                Caption = 'View All Lines';
                Image = AllLines;
                RunObject = page "Dredge Lines ERK";
                RunPageLink = "Document No." = field("No.");
                ToolTip = 'View all dredge lines for this header.';
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
            }
        }
    }
}
