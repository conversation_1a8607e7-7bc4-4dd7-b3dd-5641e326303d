table 60020 "Port Operation Header ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Port Operation Header';
    DrillDownPageId = "Port Operation Card ERK";
    LookupPageId = "Port Operation List ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                //NoSeries: Codeunit NoSeriesManagement;
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Port Operation No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(3; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            TableRelation = "Ship ERK";
            ToolTip = 'Specifies the value of the Ship No. field.';
            trigger OnValidate()
            begin
                ErkHoldingBasicFunctions.ShipMandatoryChecks("Ship No.", true);
            end;
        }
        field(4; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Ship ERK".Name where("No." = field("Ship No.")));
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
        field(5; "Port Code"; Code[10])
        {
            Caption = 'Port Code';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Port Code field.';
            trigger OnValidate()
            begin
                Rec.Validate("Port Name", VoyageMangement.GetEntryExitDescriptionFromEntryExitCode("Port Code"));
            end;
        }
        field(7; "Port Name"; Text[100])
        {
            Caption = 'Port Name';
            ToolTip = 'Specifies the value of the Port Name field.';
        }
        // field(6; "Starting Date"; Date)
        // {
        //     Caption = 'Starting Date';
        // }
        field(8; Completed; Boolean)
        {
            Caption = 'Completed';
            Editable = false;
            ToolTip = 'Specifies the value of the Completed field.';
            trigger OnValidate()
            var
                PortOperationLine: Record "Port Operation Line ERK";
                AllLinesMustBeCompletedErr: Label 'All lines must be completed';
                ActualTonnageErr: Label 'If Ship No. is selected, Actual Tonnage must be filled.';
            begin
                if Completed then begin
                    PortOperationLine.SetRange("Document No.", Rec."No.");
                    PortOperationLine.SetRange(Completed, false);
                    if not PortOperationLine.IsEmpty() then
                        Error(AllLinesMustBeCompletedErr);
                    if "Ship No." <> '' then
                        if "Actual Tonnage" <= 0 then
                            Error(ActualTonnageErr);
                end;
            end;
        }
        field(9; "Declared Quantity (Ton)"; Decimal)
        {
            Caption = 'Declared Quantity (Ton)';
            DecimalPlaces = 0 : 3;
            ToolTip = 'Specifies the value of the Declared Quantity (KG) field.';
            // Editable = false;
            // FieldClass = FlowField;
            // CalcFormula = sum("Port Operation Line ERK".Tonnage where("Document No." = field("No.")));
        }
        field(10; "Common Operation"; Boolean)
        {
            Caption = 'Common Operation';
            Editable = false;
            ToolTip = 'Specifies the value of the Common Operation field.';
        }
        field(11; "Operation Starting Time"; DateTime)
        {
            Caption = 'Operation Starting Time';
            ToolTip = 'Specifies the value of the Operation Starting Time field.';
        }
        field(12; "Operation Ending Time"; DateTime)
        {
            Caption = 'Operation Ending Time';
            ToolTip = 'Specifies the value of the Operation Ending Time field.';
        }
        field(13; "Actual Docking Time"; DateTime)
        {
            Caption = 'Actual Docking Time';
            ToolTip = 'Specifies the value of the Actual Docking Time field.';
        }
        field(14; "Actual Departure Time"; DateTime)
        {
            Caption = 'Actual Departure Time';
            ToolTip = 'Specifies the value of the Actual Departure Time field.';
        }
        field(15; "Dock No."; Code[10])
        {
            Caption = 'Dock No.';
            ToolTip = 'Specifies the value of the Dock No. field.';
        }
        field(16; "Actual Tonnage"; Decimal)
        {
            Caption = 'Actual Tonnage';
            DecimalPlaces = 0 : 3;
            ToolTip = 'Specifies the value of the Actual Tonnage field.';
        }
        field(6; "Third Party"; Boolean)
        {
            Caption = 'Third Party';
            ToolTip = 'Specifies the value of the Third Party field.';
        }
        field(17; "Deparment Name"; Text[50])
        {
            Caption = 'Deparment Name';
            ToolTip = 'Specifies the value of the Deparment Name field.';
        }
        field(18; "Contract No."; Code[20])
        {
            Caption = 'Contract No.';
            ToolTip = 'Specifies the value of the Contract No. field.';
            TableRelation = "Port Operation Contract Header";
        }

    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        //NoSeriesManagement: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Port Operation No. Series");
            //NoSeriesManagement.InitSeries(ErkHoldingSetup."Port Operation No. Series", xRec."No. Series", 0D, "No.", "No. Series");
            "No. Series" := ErkHoldingSetup."Port Operation No. Series";
            if NoSeries.AreRelated(ErkHoldingSetup."Port Operation No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;
    end;

    trigger OnDelete()
    var
        PortOperationLine: Record "Port Operation Line ERK";
    begin
        PortOperationLine.SetRange("Document No.", Rec."No.");
        PortOperationLine.DeleteAll(true);
    end;

    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
