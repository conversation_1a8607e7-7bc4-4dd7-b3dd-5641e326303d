report 60003 "Ankes Report ERK"
{
    ApplicationArea = All;
    Caption = 'Ankes Report';
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem(Integer; Integer)
        {
            DataItemTableView = where(Number = const(1));

            column(HistoricTL; CalculateOpenningBalancesByDateAndCurrencyCode(StartingDate - 1, ''))
            {
            }
            column(HistoricUSD; CalculateOpenningBalancesByDateAndCurrencyCode(StartingDate - 1, 'USD'))
            {
            }
            column(HistoricEUR; CalculateOpenningBalancesByDateAndCurrencyCode(StartingDate - 1, 'EUR'))
            {
            }
            column(USDExchangeRate; 1 / CurrencyExchangeRate.ExchangeRate(EndingDate, 'USD'))
            {
            }
            column(EURExchangeRate; 1 / CurrencyExchangeRate.ExchangeRate(EndingDate, 'EUR'))
            {
            }
            column(StartingDate; StartingDate)
            {
            }
            column(EndingDate; EndingDate)
            {
            }
        }
        dataitem("Vendor Ledger Entry"; "Vendor Ledger Entry")
        {
            DataItemTableView = where("Document Type" = filter(Payment | Refund | " "));

            column(VendorName_VendorLedgerEntry; "Vendor Name")
            {
            }
            column(CurrencyCode_VendorLedgerEntry; "Currency Code")
            {
            }
            column(Amount_VendorLedgerEntry; Amount)
            {
            }
            column(Amount__LCY_VendorLedgerEntry; "Amount (LCY)")
            {
            }
            column(SourceCode_VendorLedgerEntry; "Source Code")
            {
            }
            trigger OnPreDataItem()
            begin
                "Vendor Ledger Entry".SetRange("Posting Date", StartingDate, EndingDate);
            end;
        }
        dataitem("Cust. Ledger Entry"; "Cust. Ledger Entry")
        {
            DataItemTableView = where("Document Type" = filter(Payment | Refund | " "), "Customer Posting Group" = filter('<>136-DIGERCESITLIALAC'));

            column(CustomerName_CustLedgerEntry; "Customer Name")
            {
            }
            column(CurrencyCode_CustLedgerEntry; "Currency Code")
            {
            }
            column(Amount_CustLedgerEntry; Amount)
            {
            }
            column(Amount__LCY_CustomerLedgerEntry; "Amount (LCY)")
            {
            }
            column(SourceCode_CustLedgerEntry; "Source Code")
            {
            }
            trigger OnPreDataItem()
            begin
                "Cust. Ledger Entry".SetRange("Posting Date", StartingDate, EndingDate);
            end;
        }
        dataitem("Bank Account Ledger Entry"; "Bank Account Ledger Entry")
        {
            column(Amount_BankAccountLedgerEntry; Amount)
            {
            }
            column(BankAccountNo_BankAccountLedgerEntry; "Bank Account No.")
            {
            }
            column(CurrencyCode_BankAccountLedgerEntry; "Currency Code")
            {
            }
            column(PostingDate_BankAccountLedgerEntry; "Posting Date")
            {
            }
            column(BalAccountType_BankAccountLedgerEntry; "Bal. Account Type")
            {
            }
            column(BalAccountNo_BankAccountLedgerEntry; "Bal. Account No.")
            {
            }
            column(CreditAmount_BankAccountLedgerEntry; "Credit Amount")
            {
            }
            column(DebitAmount_BankAccountLedgerEntry; "Debit Amount")
            {
            }
            column(SourceName; GetSourceNameFromBankAccountLedgerEntry("Bank Account Ledger Entry"))
            {
            }
            column(USDExchangeRateLoop; 1 / CurrencyExchangeRate.ExchangeRate(EndingDate, 'USD'))
            {
            }
            column(EURExchangeRateLoop; 1 / CurrencyExchangeRate.ExchangeRate(EndingDate, 'EUR'))
            {
            }
            column(AmountLCY_BankAccountLedgerEntry; "Amount (LCY)")
            {
            }
            column(CreditAmountLCY_BankAccountLedgerEntry; "Credit Amount (LCY)")
            {
            }
            column(DebitAmountLCY_BankAccountLedgerEntry; "Debit Amount (LCY)")
            {
            }
            dataitem("Bank Account"; "Bank Account")
            {
                DataItemLink = "No." = field("Bank Account No.");

                column(Name_BankAccount; Name)
                {
                }
            }
            trigger OnPreDataItem()
            begin
                "Bank Account Ledger Entry".SetRange("Posting Date", StartingDate, EndingDate);
            end;
        }
        dataitem("Currency Exchange Rate"; "Currency Exchange Rate")
        {
            column(StartingDate_CurrencyExchangeRate; "Starting Date")
            {
            }
            column(CurrencyCode_CurrencyExchangeRate; "Currency Code")
            {
            }
            column(RelationalAdjmtExchRateAmt_CurrencyExchangeRate; "Relational Adjmt Exch Rate Amt")
            {
            }
            trigger OnPreDataItem()
            begin
                "Currency Exchange Rate".SetRange("Starting Date", EndingDate);
            end;
        }
        dataitem("Bank Account Ledger Entry 2"; "Bank Account Ledger Entry")
        {
            column(Amount_BankAccountLedgerEntry2; Amount)
            {
            }
            column(BankAccountNo_BankAccountLedgerEntry2; "Bank Account No.")
            {
            }
            column(CurrencyCode_BankAccountLedgerEntry2; "Currency Code")
            {
            }
            column(PostingDate_BankAccountLedgerEntry2; "Posting Date")
            {
            }
            column(BalAccountType_BankAccountLedgerEntry2; "Bal. Account Type")
            {
            }
            column(BalAccountNo_BankAccountLedgerEntry2; "Bal. Account No.")
            {
            }
            column(CreditAmount_BankAccountLedgerEntry2; "Credit Amount")
            {
            }
            column(DebitAmount_BankAccountLedgerEntry2; "Debit Amount")
            {
            }
            column(SourceName2; GetSourceNameFromBankAccountLedgerEntry("Bank Account Ledger Entry"))
            {
            }
            column(USDExchangeRateLoop2; 1 / CurrencyExchangeRate.ExchangeRate(EndingDate, 'USD'))
            {
            }
            column(EURExchangeRateLoop2; 1 / CurrencyExchangeRate.ExchangeRate(EndingDate, 'EUR'))
            {
            }
            column(AmountLCY_BankAccountLedgerEntry2; "Amount (LCY)")
            {
            }
            column(CreditAmountLCY_BankAccountLedgerEntry2; "Credit Amount (LCY)")
            {
            }
            column(DebitAmountLCY_BankAccountLedgerEntry2; "Debit Amount (LCY)")
            {
            }
            dataitem("Bank Account 2"; "Bank Account")
            {
                DataItemLink = "No." = field("Bank Account No.");

                column(Name_BankAccount2; Name)
                {
                }
            }
            trigger OnPreDataItem()
            begin
                "Bank Account Ledger Entry 2".SetRange("Posting Date", 0D, EndingDate);
            end;
        }
        dataitem("Company Information"; "Company Information")
        {
            CalcFields = Picture;

            column(Picture_CompanyInformation; Picture)
            {
            }
        }
    }
    requestpage
    {
        layout
        {
            area(Content)
            {
                group(Filters)
                {
                    Caption = 'Filters';

                    field(StartingDateFiter; StartingDate)
                    {
                        ApplicationArea = All;
                        Caption = 'Starting Date';
                        ToolTip = 'Specifies the value of the User Date Filter field.';
                        ShowMandatory = true;
                    }
                    field(EndingDateFilter; EndingDate)
                    {
                        ApplicationArea = All;
                        Caption = 'Ending Date';
                        ToolTip = 'Specifies the value of the User Date Filter field.';
                        ShowMandatory = true;
                    }
                }
            }
        }
        trigger OnOpenPage()
        begin
            StartingDate := WorkDate();
            EndingDate := WorkDate();
        end;
    }
    local procedure CalculateOpenningBalancesByDateAndCurrencyCode(ParamStartingDate: Date; CurrencyCode: Code[10]): Decimal
    var
        BankAccountLedgerEntry: Record "Bank Account Ledger Entry";
        BankAccountNoFilterLbl: Label '102*';
    begin
        BankAccountLedgerEntry.SetRange("Posting Date", 0D, ParamStartingDate);
        BankAccountLedgerEntry.SetRange("Currency Code", CurrencyCode);
        BankAccountLedgerEntry.SetFilter("Bank Account No.", BankAccountNoFilterLbl);
        BankAccountLedgerEntry.CalcSums(Amount);
        exit(BankAccountLedgerEntry.Amount);
    end;

    local procedure GetSourceNameFromBankAccountLedgerEntry(BankAccountLedgerEntry: Record "Bank Account Ledger Entry"): Text[100]
    var
        Customer: Record Customer;
        Vendor: Record Vendor;
    begin
        case BankAccountLedgerEntry."Bal. Account Type" of
            BankAccountLedgerEntry."Bal. Account Type"::Customer:
                begin
                    Customer.Get(BankAccountLedgerEntry."Bal. Account No.");
                    exit(Customer.Name);
                end;
            BankAccountLedgerEntry."Bal. Account Type"::Vendor:
                begin
                    Vendor.Get(BankAccountLedgerEntry."Bal. Account No.");
                    exit(Vendor.Name);
                end;
            else
                exit('');
        end;
    end;

    trigger OnInitReport()
    begin
        StartingDate := WorkDate();
        EndingDate := WorkDate();
    end;

    var
        CurrencyExchangeRate: Record "Currency Exchange Rate";
        StartingDate: Date;
        EndingDate: Date;
}
