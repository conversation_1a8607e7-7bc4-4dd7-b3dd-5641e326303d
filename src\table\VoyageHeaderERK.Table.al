table 60009 "Voyage Header ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Voyage Header';
    DrillDownPageId = "Voyage List ERK";
    LookupPageId = "Voyage List ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Voyage No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(3; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            TableRelation = "Ship ERK";
            ToolTip = 'Specifies the value of the Ship No. field.';
            trigger OnValidate()
            var
                Ship: Record "Ship ERK";
            begin
                if not Ship.Get("Ship No.") then
                    "Ship Name" := ''
                else
                    "Ship Name" := Ship.Name;

                ErkHoldingBasicFunctions.ShipMandatoryChecks("Ship No.", false);
            end;
        }
        field(4; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(5; "Loading Port Code"; Code[10])
        {
            Caption = 'Loading Port Code';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Loading Port Code field.';
            trigger OnValidate()
            begin
                Rec.Validate("Loading Port Name", VoyageMangement.GetEntryExitDescriptionFromEntryExitCode("Loading Port Code"));
                // VoyageLine.SetRange("Document No.", Rec."No.");
                // VoyageLine.ModifyAll("Loading Port Code", Rec."Loading Port Code");
            end;
        }
        field(7; "Loading Port Name"; Text[100])
        {
            Caption = 'Loading Port Name';
            ToolTip = 'Specifies the value of the Loading Port Name field.';
            // trigger OnValidate()
            // begin
            //     VoyageLine.SetRange("Document No.", Rec."No.");
            //     VoyageLine.ModifyAll("Loading Port Name", Rec."Loading Port Name");
            // end;
        }
        field(9; "Loading Port Departure Date"; Date)
        {
            Caption = 'Loading Port Departure Date';
            ToolTip = 'Specifies the value of the Loading Port Departure Date field.';
            // trigger OnValidate()
            // begin
            //     VoyageLine.SetRange("Document No.", Rec."No.");
            //     VoyageLine.ModifyAll("Loading Port Departure Date", Rec."Loading Port Departure Date");
            // end;
        }
        field(11; "Loading Port Arrival Date"; Date)
        {
            Caption = 'Starting Date';
            ToolTip = 'Specifies the value of the Loading Port Arrival Date field.';
            // trigger OnValidate()
            // begin
            //     VoyageLine.SetRange("Document No.", Rec."No.");
            //     VoyageLine.ModifyAll("Loading Port Arrival Date", Rec."Loading Port Arrival Date");
            // end;
        }
        field(6; "Discharge Port Code"; Code[10])
        {
            Caption = 'Discharge Port Code';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Discharge Port Code field.';
            trigger OnValidate()
            begin
                Rec.Validate("Discharge Port Name", VoyageMangement.GetEntryExitDescriptionFromEntryExitCode("Discharge Port Code"));
                // VoyageLine.SetRange("Document No.", Rec."No.");
                // VoyageLine.ModifyAll("Discharge Port Code", Rec."Discharge Port Code");
            end;
        }
        field(8; "Discharge Port Name"; Text[100])
        {
            Caption = 'Discharge Port Name';
            ToolTip = 'Specifies the value of the Discharge Port Name field.';
            // trigger OnValidate()
            // begin
            //     VoyageLine.SetRange("Document No.", Rec."No.");
            //     VoyageLine.ModifyAll("Discharge Port Name", Rec."Discharge Port Name");
            // end;
        }
        field(10; "Discharge Port Departure Date"; Date)
        {
            Caption = 'Discharge Port Departure Date';
            ToolTip = 'Specifies the value of the Discharge Port Departure Date field.';
            //trigger OnValidate()
            // begin
            //     VoyageLine.SetRange("Document No.", Rec."No.");
            //     VoyageLine.ModifyAll("Discharge Port Departure Date", Rec."Discharge Port Departure Date");
            // end;
        }
        field(12; "Discharge Port Arrival Date"; Date)
        {
            Caption = 'Discharge Port Arrival Date';
            ToolTip = 'Specifies the value of the Discharge Port Arrival Date field.';
            // trigger OnValidate()
            // begin
            //     VoyageLine.SetRange("Document No.", Rec."No.");
            //     VoyageLine.ModifyAll("Discharge Port Arrival Date", Rec."Discharge Port Arrival Date");
            // end;
        }
        // field(13; Type; enum "Voyage Type ERK")
        // {
        //     Caption = 'Type';
        // }
        // field(13; "Departure Fuel Quantity"; Decimal)
        // {
        //     Caption = 'Departure Fuel Quantity';
        //     trigger OnValidate()
        //     begin
        //         "Consumption IFO Quantity" := "Departure Fuel Quantity" - "Arrival Fuel Quantity";
        //     end;
        // }
        // field(14; "Arrival Fuel Quantity"; Decimal)
        // {
        //     Caption = 'Arrival Fuel Quantity';
        //     trigger OnValidate()
        //     begin
        //         TestField("Departure Fuel Quantity");
        //         "Consumption IFO Quantity" := "Departure Fuel Quantity" - "Arrival Fuel Quantity";
        //     end;
        // }
        field(15; "Consumption IFO Quantity"; Decimal)
        {
            Caption = 'Consumption IFO Quantity';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Consumption Fuel Quantity field.';
        }
        field(16; "Empty Trip"; Boolean)
        {
            Caption = 'Empty Trip';
            ToolTip = 'Specifies the value of the Empty Trip field.';
        }
        field(17; "Consumption MGO Quantity"; Decimal)
        {
            Caption = 'Consumption MGO Quantity';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Consumption MGO Quantity field.';
        }
        // field(18; "Total Fuel Cost"; Decimal)
        // {
        //     Caption = 'Total Fuel Cost';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Value Entry"."Cost Amount (Actual)" where("Global Dimension 2 Code" = field("No."), "Item Ledger Entry Type" = const(Sale), "Sales Amount (Actual)" = const(0), "Cost Amount (Non-Invtbl.)" = const(0), "Gen. Prod. Posting Group" = filter('GEMIYAKIT')));
        // }
        // field(19; "Total Hire Cost"; Decimal)
        // {
        //     Caption = 'Total Hire Cost';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Value Entry"."Cost Amount (Actual)" where("Global Dimension 2 Code" = field("No."), "Item Ledger Entry Type" = const(Sale), "Sales Amount (Actual)" = const(0), "Gen. Prod. Posting Group" = filter('OP-GEMIKIRALAMA')));
        // }
        // field(20; "Total Revenue"; Decimal)
        // {
        //     Caption = 'Total Revenue';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Value Entry"."Sales Amount (Actual)" where("Global Dimension 2 Code" = field("No."), "Item Ledger Entry Type" = const(Sale), "Sales Amount (Actual)" = filter(<> 0)));
        // }
        // field(21; "Total Expenses"; Decimal)
        // {
        //     Caption = 'Total Expenses';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Value Entry"."Cost Amount (Non-Invtbl.)" where("Global Dimension 2 Code" = field("No."), "Item Ledger Entry Type" = const(Purchase), "Cost Amount (Non-Invtbl.)" = filter(<> 0)));
        // }
        field(22; "Total Fuel Cost (ACY)"; Decimal)
        {
            Caption = 'Total Fuel Cost (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Value Entry"."Cost Amount (Actual) (ACY)" where("Global Dimension 2 Code" = field("No."), "Item Ledger Entry Type" = const(Sale), "Sales Amount (Actual)" = const(0), "Cost Amount (Non-Invtbl.)" = const(0), "Gen. Prod. Posting Group" = filter('GEMIYAKIT')));
            ToolTip = 'Specifies the value of the Total Fuel Cost (ACY) field.';
        }
        field(23; "Total Hire Cost (ACY)"; Decimal)
        {
            Caption = 'Total Hire Cost (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Value Entry"."Cost Amount (Actual) (ACY)" where("Global Dimension 2 Code" = field("No."), "Item Ledger Entry Type" = const(Sale), "Sales Amount (Actual)" = const(0), "Gen. Prod. Posting Group" = filter('OP-GEMIKIRALAMA')));
            ToolTip = 'Specifies the value of the Total Hire Cost (ACY) field.';
        }
        // field(24; "Total Revenue (ACY)"; Decimal)
        // {
        //     Caption = 'Total Revenue (ACY)';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Value Entry"."Sales Amount (Actual)" where("Global Dimension 2 Code" = field("No."), "Item Ledger Entry Type" = const(Sale), "Sales Amount (Actual)" = filter(<> 0)));
        // }
        // field(25; "Total Expenses (ACY)"; Decimal)
        // {
        //     Caption = 'Total Expenses (ACY)';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Value Entry"."Cost Amount (Non-Invtbl.)(ACY)" where("Global Dimension 2 Code" = field("No."), "Item Ledger Entry Type" = const(Purchase), "Cost Amount (Non-Invtbl.)" = filter(<> 0)));
        // }
        field(13; "Ending Date"; Date)
        {
            Caption = 'Ending Date';
            ToolTip = 'Specifies the value of the Ending Date field.';
        }
        field(14; "Reason Code"; Code[10])
        {
            Caption = 'Reason Code';
            TableRelation = "Reason Code";
            ToolTip = 'Specifies the value of the Reason Code field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Reason Description");
            end;
        }
        field(24; "Reason Description"; Text[100])
        {
            Caption = 'Reason Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Reason Code".Description where(Code = field("Reason Code")));
            ToolTip = 'Specifies the value of the Reason Description field.';
        }
        field(26; "Total Voyage Expense (ACY)"; Decimal)
        {
            Caption = 'Total Voyage Expense (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Voyage Expense ERK"."Line Amount (ACY)" where("Document No." = field("No."), "Consumption Line" = const(false), "Is Cancelled" = const(false), Posted = const(true)));
            ToolTip = 'Specifies the value of the Total Voyage Expense (ACY) field.';
        }
        field(25; "Total Load Expense (ACY)"; Decimal)
        {
            Caption = 'Total Load Expense (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Voyage Line Detail ERK"."Line Amount (ACY)" where("Document No." = field("No."), "Is Cancelled" = const(false), Type = const(Expense), Posted = const(true)));
            ToolTip = 'Specifies the value of the Total Load Expense (ACY) field.';
        }
        field(27; "Total Voyage Revenue (ACY)"; Decimal)
        {
            Caption = 'Total Voyage Revenue (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Voyage Line Detail ERK"."Line Amount (ACY)" where("Document No." = field("No."), "Is Cancelled" = const(false), Type = const(Revenue), Posted = const(true)));
            ToolTip = 'Specifies the value of the Total Voyage Revenue (ACY) field.';
        }
        field(18; "Total Unposted Revenue (ACY)"; Decimal)
        {
            Caption = 'Total Unposted Revenue (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Voyage Line Detail ERK"."Line Amount (ACY)" where("Document No." = field("No."), Posted = const(false), Type = const(Revenue)));
            ToolTip = 'Specifies the value of the Total Unposted Revenue (ACY) field.';
        }
        field(19; "Total Unposted Load Exp. (ACY)"; Decimal)
        {
            Caption = 'Total Unposted Load Expense (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Voyage Line Detail ERK"."Line Amount (ACY)" where("Document No." = field("No."), Posted = const(false), Type = const(Expense)));
            ToolTip = 'Specifies the value of the Total Unposted Load Expense (ACY) field.';
        }
        field(20; "Total Unposted Voyage Exp(ACY)"; Decimal)
        {
            Caption = 'Total Unposted Voyage Expense (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Voyage Expense ERK"."Line Amount (ACY)" where("Document No." = field("No."), Posted = const(false), "Consumption Line" = const(false), "Is Cancelled" = const(false)));
            ToolTip = 'Specifies the value of the Total Unposted Voyage Expense (ACY) field.';
        }
        field(21; "Department Code"; Code[20])
        {
            Caption = 'Departmant';
            TableRelation = "Dimension Value".Code;
            ToolTip = 'Specifies the value of the Departmant field.';
        }

        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        //NoSeries: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Voyage No. Series");
            //NoSeries.InitSeries(ErkHoldingSetup."Voyage No. Series", xRec."No. Series", 0D, "No.", "No. Series");
            "No. Series" := ErkHoldingSetup."Voyage No. Series";
            if NoSeries.AreRelated(ErkHoldingSetup."Voyage No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;
    end;

    trigger OnDelete()
    var
        VoyageLine: Record "Voyage Line ERK";
    begin
        VoyageLine.SetRange("Document No.", Rec."No.");
        VoyageLine.DeleteAll(true);
    end;

    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
