codeunit 60029 "Car Carr Rev/Exp Rename ERK"
{
    procedure ClearDocumentNoAndReassignLineNumbers(var CarCarrRevenueExpenseRecords: Record "Car Carr. Revenue/Expense ERK")
    var
        NextLineNo: Integer;
        ProcessedCount: Integer;
        TotalSerialNoUpdated: Integer;
        ProcessedMsg: Label '%1 records processed. Document No. cleared and Line No. reassigned. %2 related Serial No. records updated.', Comment = '%1=Count of main records, %2=Count of serial no records';
    begin
        if CarCarrRevenueExpenseRecords.IsEmpty() then
            exit;

        // Get the next available Line No. from records with empty Document No.
        NextLineNo := GetNextLineNoForEmptyDocumentNo();

        // Process each record in the set
        CarCarrRevenueExpenseRecords.FindSet(true);
        repeat
            // Clear the Document No. and assign new Line No.
            TotalSerialNoUpdated += ProcessSingleRecord(CarCarrRevenueExpenseRecords, NextLineNo);
            NextLineNo += 10000; // Increment by 10000 for next record
            ProcessedCount += 1;
        until CarCarrRevenueExpenseRecords.Next() = 0;

        Message(ProcessedMsg, ProcessedCount, TotalSerialNoUpdated);
    end;

    local procedure GetNextLineNoForEmptyDocumentNo(): Integer
    var
        EmptyDocCarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        // Find records with empty Document No. and get the highest Line No.
        EmptyDocCarCarrRevenueExpense.SetRange("Document No.", '');
        if EmptyDocCarCarrRevenueExpense.FindLast() then
            exit(EmptyDocCarCarrRevenueExpense."Line No." + 10000)
        else
            exit(10000); // Start from 10000 if no records with empty Document No. exist
    end;

    local procedure ProcessSingleRecord(var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK"; NewLineNo: Integer): Integer
    var
        OldDocumentNo: Code[20];
        OldLineNo: Integer;
    begin
        // Store original key values before rename
        OldDocumentNo := CarCarrRevenueExpense."Document No.";
        OldLineNo := CarCarrRevenueExpense."Line No.";

        // Rename the record to clear Document No. and assign new Line No.
        // This changes both primary key fields in one operation
        CarCarrRevenueExpense.Rename('', NewLineNo);        // Update related Serial No. Revenue/Expense records and return count
        exit(UpdateRelatedSerialNoRevenueExpenseRecords(OldDocumentNo, OldLineNo, '', NewLineNo));
    end;

    local procedure UpdateRelatedSerialNoRevenueExpenseRecords(OldDocumentNo: Code[20]; OldLineNo: Integer; NewDocumentNo: Code[20]; NewLineNo: Integer): Integer
    var
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
        UpdatedCount: Integer;
    begin
        // Find all Serial No. Revenue/Expense records related to the old Car Carrier Revenue/Expense record
        SerialNoRevenueExpense.SetRange("Document No.", OldDocumentNo);
        SerialNoRevenueExpense.SetRange("Car Carrier Rev/Exp Line No.", OldLineNo);

        if SerialNoRevenueExpense.FindSet(true) then
            repeat
                // Update the references to point to the renamed record
                SerialNoRevenueExpense."Document No." := NewDocumentNo;
                SerialNoRevenueExpense."Car Carrier Rev/Exp Line No." := NewLineNo;
                SerialNoRevenueExpense.Modify(true);
                UpdatedCount += 1;
            until SerialNoRevenueExpense.Next() = 0;


        exit(UpdatedCount);
    end;

    /// <summary>
    /// Gets the next available Line No. for records with empty Document No.
    /// This is a public function that can be used to check the next Line No. without processing records.
    /// </summary>
    /// <returns>Next available Line No. for empty Document No. records</returns>
    procedure GetNextAvailableLineNoForEmptyDocumentNo(): Integer
    begin
        exit(GetNextLineNoForEmptyDocumentNo());
    end;

    /// <summary>
    /// Counts the number of records with empty Document No.
    /// </summary>
    /// <returns>Count of records with empty Document No.</returns>
    procedure CountRecordsWithEmptyDocumentNo(): Integer
    var
        EmptyDocCarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    begin
        EmptyDocCarCarrRevenueExpense.SetRange("Document No.", '');
        exit(EmptyDocCarCarrRevenueExpense.Count());
    end;
}
