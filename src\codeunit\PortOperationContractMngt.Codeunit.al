codeunit 60024 "Port Operation Contract Mngt."
{
    procedure PopulateLineDetailFromContract(var PortOperationLineDetail: Record "Port Operation Line Detail ERK"; ChosenContractNo: Code[20])
    var
        PortOperationLine: Record "Port Operation Line ERK";
        PortOperationHeader: Record "Port Operation Header ERK";
        PortOperationContractHeader: Record "Port Operation Contract Header";
        PortOperationContractLine: Record "Port Operation Contract Line";
        PriceDate: Date;
        EffectiveContractNo: Code[20];
    begin
        // Check if we have the required document no and line no
        if (PortOperationLineDetail."Document No." = '') or (PortOperationLineDetail."Document Line No." = 0) then
            exit;

        if PortOperationLineDetail.Type <> PortOperationLineDetail.Type::Revenue then
            exit;

        // Get the port operation line to find the contract no
        if not PortOperationLine.Get(PortOperationLineDetail."Document No.", PortOperationLineDetail."Document Line No.") then
            exit;

        // Determine contract to use
        if ChosenContractNo <> '' then
            EffectiveContractNo := ChosenContractNo
        else begin
            if PortOperationLine."Contract No." = '' then begin
                if not PortOperationHeader.Get(PortOperationLineDetail."Document No.") then
                    exit;

                if PortOperationHeader."Contract No." = '' then
                    exit;

                PortOperationLine."Contract No." := PortOperationHeader."Contract No.";
            end;
            EffectiveContractNo := PortOperationLine."Contract No.";
        end;

        // Get the contract header
        if not PortOperationContractHeader.Get(EffectiveContractNo) then
            exit;

        // Determine price date - use posting date if available, otherwise workdate
        if PortOperationLineDetail."Posting Date" <> 0D then
            PriceDate := PortOperationLineDetail."Posting Date"
        else
            PriceDate := WorkDate();

        // Find matching contract line based on item, variant, parent load type and date validity
        PortOperationContractLine.Reset();
        PortOperationContractLine.SetRange("Document No.", EffectiveContractNo);
        PortOperationContractLine.SetRange("No.", PortOperationLineDetail."No.");

        // Filter by variant if specified
        if PortOperationLineDetail."Variant Code" <> '' then
            PortOperationContractLine.SetRange("Variant Code", PortOperationLineDetail."Variant Code");

        // Filter by parent load type if specified
        if PortOperationLineDetail."Parent Load Type" <> '' then
            PortOperationContractLine.SetRange("Parent Load Type", PortOperationLineDetail."Parent Load Type");

        // Find valid contract line for the price date
        if PortOperationContractLine.FindSet() then
            repeat
                // Check if the price date is within the contract line's date range
                if IsDateInPriceValidityPeriod(PriceDate, PortOperationContractLine."Starting Date", PortOperationContractLine."Ending Date") then begin
                    // Populate the line detail with contract information
                    PortOperationLineDetail."Unit Price" := PortOperationContractLine."Unit Price";
                    PortOperationLineDetail."Currency Code" := PortOperationContractLine."Currency Code";
                    PortOperationLineDetail."Unit of Measure Code" := PortOperationContractLine."Unit of Measure Code";

                    // Set the next invoice period if available
                    if Format(PortOperationContractLine."Invoicing Period") <> '' then begin
                        PortOperationLineDetail."Next Invoice Period" := PortOperationContractLine."Invoicing Period";
                        if PortOperationLineDetail."Posting Date" <> 0D then
                            PortOperationLineDetail."Next Invoice Date" := CalcDate(PortOperationContractLine."Invoicing Period", PortOperationLineDetail."Posting Date");
                    end;

                    // Recalculate line amount after updating price and currency
                    PortOperationLineDetail.Validate("Unit Price");
                    exit;  // Exit once we find a valid contract line
                end;
            until PortOperationContractLine.Next() = 0;
    end;

    // Method to update all line details after contract change
    procedure UpdateLineDetailsFromContract(PortOperationLine: Record "Port Operation Line ERK"; ChosenContractNo: Code[20])
    var
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
    begin
        // Find all related line details
        PortOperationLineDetail.SetRange("Document No.", PortOperationLine."Document No.");
        PortOperationLineDetail.SetRange("Document Line No.", PortOperationLine."Line No.");
        PortOperationLineDetail.SetRange(Type, PortOperationLineDetail.Type::Revenue);

        // Only update details that haven't been invoiced yet
        PortOperationLineDetail.SetFilter("Invoice No.", '%1', '');

        if PortOperationLineDetail.FindSet() then
            repeat
                PopulateLineDetailFromContract(PortOperationLineDetail, ChosenContractNo);
                PortOperationLineDetail.Modify(true);
            until PortOperationLineDetail.Next() = 0;
    end;

    // Helper function to check if a date is within a validity period
    local procedure IsDateInPriceValidityPeriod(CheckDate: Date; StartingDate: Date; EndingDate: Date): Boolean
    begin
        // If no starting date, or CheckDate >= StartingDate
        if (StartingDate = 0D) or (CheckDate >= StartingDate) then
            // If no ending date, or CheckDate <= EndingDate
            if (EndingDate = 0D) or (CheckDate <= EndingDate) then
                exit(true);

        exit(false);
    end;
}