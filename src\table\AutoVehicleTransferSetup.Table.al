table 60042 "Auto.Vehicle Transfer Setup"
{
    Caption = 'Auto.Vehicle Transfer Setup';
    DataClassification = CustomerContent;
    DrillDownPageId = "Auto.Vehicle Transfer Setup";
    LookupPageId = "Auto.Vehicle Transfer Setup";

    fields
    {
        field(1; "To Location Code"; Code[10])
        {
            Caption = 'To Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the To Location Code field.';
        }
        field(2; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("To Location Code"));
            ToolTip = 'Specifies the value of the To Bin Code field.';
        }
        field(3; "New To Location Code"; Code[10])
        {
            Caption = 'New To Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the New To Location Code field.';
        }
        field(4; "New To Bin Code"; Code[20])
        {
            Caption = 'New To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("New To Location Code"));
            ToolTip = 'Specifies the value of the New To Bin Code field.';
        }
        field(5; "New Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'New Operation Type';
            ToolTip = 'Specifies the value of the New Operation Type field.';
        }
        field(6; "Valid for Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Valid for Operation Type';
            ToolTip = 'Specifies the value of the Valid for Operation Type field.';
        }

    }
    keys
    {
        key(PK; "Valid for Operation Type", "To Location Code", "To Bin Code", "New To Location Code", "New To Bin Code")
        {
            Clustered = true;
        }
    }
}
