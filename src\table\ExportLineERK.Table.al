table 60002 "Export Line ERK"
{
    Caption = 'Export Line';
    DataClassification = CustomerContent;
    LookupPageId = "Export Lines ERK";
    DrillDownPageId = "Export Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(4; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(5; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(6; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(7; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(8; "Unit Price"; Decimal)
        {
            Caption = 'Unit Price';
            DecimalPlaces = 0 : 7;
            ToolTip = 'Specifies the value of the Unit Price field.';
        }
        field(9; "Unit Cost"; Decimal)
        {
            Caption = 'Unit Cost';
            DecimalPlaces = 0 : 7;
            ToolTip = 'Specifies the value of the Unit Cost field.';
        }
        field(11; Brand; Code[10])
        {
            Caption = 'Brand';
            TableRelation = Manufacturer.Code;
            ToolTip = 'Specifies the value of the Brand field.';
        }
        field(12; "Purchase Line Amount"; Decimal)
        {
            Caption = 'Purchase Line Amount';
            ToolTip = 'Specifies the value of the Purchase Line Amount field.';
        }
        field(13; "Sales Line Amount"; Decimal)
        {
            Caption = 'Sales Line Amount';
            ToolTip = 'Specifies the value of the Sales Line Amount field.';
        }
        field(14; "Country/Region of Origin Code"; Code[10])
        {
            Caption = 'Country/Region of Origin Code';
            ToolTip = 'Specifies the value of the Country/Region of Origin Code field.';
        }
        field(15; "Load Quantity"; Decimal)
        {
            Caption = 'Load Quantity';
            ToolTip = 'Specifies the value of the Load Quantity field.';
        }
        field(16; "Vendor No."; Code[20])
        {
            Caption = 'Vendor No.';
            TableRelation = Vendor."No.";
            ToolTip = 'Specifies the value of the Vendor No. field.';
        }
        field(17; "Unit Cost (LCY)"; Decimal)
        {
            Caption = 'Unit Cost (LCY)';
            DecimalPlaces = 0 : 7;
            ToolTip = 'Specifies the value of the Unit Cost (LCY) field.';
        }
        field(18; "Vendor Name"; Text[100])
        {
            Caption = 'Vendor Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Vendor No.")));
            ToolTip = 'Specifies the value of the Vendor Name field.';
        }
        field(19; "Unit Price (LCY)"; Decimal)
        {
            Caption = 'Unit Price (LCY)';
            DecimalPlaces = 0 : 7;
            ToolTip = 'Specifies the value of the Unit Price (LCY) field.';
        }
        field(20; "Purchase Due Date"; Date)
        {
            Caption = 'Purchase Due Date';
            ToolTip = 'Specifies the value of the Purchase Due Date field.';
        }
        field(21; "Purchase Shipment Method Code"; Code[10])
        {
            Caption = 'Purchase Shipment Method Code';
            TableRelation = "Shipment Method".Code;
            ToolTip = 'Specifies the value of the Purchase Shipment Method Code field.';
        }
        field(22; "Purchase Currency Code"; Code[10])
        {
            Caption = 'Purchase Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Purchase Currency Code field.';
        }
        field(23; "Purchase Receipt No."; Code[20])
        {
            Caption = 'Purchase Receipt No.';
            ToolTip = 'Specifies the value of the Purchase Receipt No. field.';
            trigger OnLookup()
            var
                PurchaseReceiptHeader: Record "Purch. Rcpt. Header";
            begin
                PurchaseReceiptHeader.Get("Purchase Receipt No.");
                Page.Run(Page::"Posted Purchase Receipt", PurchaseReceiptHeader);
            end;
        }
        field(24; "Blanket Sales Order No."; Code[50])
        {
            Caption = 'Blanket Sales Order No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Export Header ERK"."Blanket Sales Order No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Blanket Sales Order No. field.';
        }
        field(25; "Sales Currency Code"; Code[10])
        {
            Caption = 'Sales Currency COde';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Export Header ERK"."Sales Currency Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Sales Currency COde field.';
        }
        field(26; "Purchase Date"; Date)
        {
            Caption = 'Purchase Date';
            ToolTip = 'Specifies the value of the Purchase Date field.';
        }
        field(27; "Quantity Received"; Decimal)
        {
            Caption = 'Quantity Received';
            ToolTip = 'Specifies the value of the Quantity Received field.';
        }
        field(28; "Tariff No."; Code[20])
        {
            Caption = 'Tariff No.';
            TableRelation = "Tariff Number";
            AllowInCustomizations = Never;
        }
        field(10; "Loading Date"; Date)
        {
            Caption = 'Loading Date';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Export Header ERK"."Loading Date" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Export Date field.';
        }
        field(29; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Export Header ERK"."Customer No." where("No." = field("Document No.")));
            AllowInCustomizations = Never;
        }
        field(30; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Customer No.")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(31; "UoM International Std. Code"; Code[10])
        {
            Caption = 'UoM International Std. Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Unit of Measure"."International Standard Code" where(Code = field("Unit of Measure Code")));
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(SK; "Item No.", "Variant Code", "Load Quantity")
        {
        }
    }
    trigger OnInsert()
    var
        ExportLine: Record "Export Line ERK";
    begin
        if Rec."Line No." = 0 then begin
            ExportLine.SetRange("Document No.", Rec."Document No.");
            if not ExportLine.FindLast() then
                Rec."Line No." := 10000
            else
                Rec."Line No." := ExportLine."Line No." + 10000;
        end;
    end;
}
