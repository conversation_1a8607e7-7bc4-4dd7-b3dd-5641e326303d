page 60153 "Dredge Lines ERK"
{
    ApplicationArea = All;
    Caption = 'Dredge Lines';
    PageType = List;
    SourceTable = "Dredge Line ERK";
    UsageCategory = Lists;
    Editable = false;
    DeleteAllowed = true;
    InsertAllowed = true;
    ModifyAllowed = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                    Visible = false;
                }
                field(Type; Rec.Type)
                {
                }
                field("Source No"; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure"; Rec."Unit of Measure")
                {
                }
                field("Unit Price/Cost"; Rec."Unit Price/Cost")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field(Amount; Rec.Amount)
                {
                }
                field("Amount ACY"; Rec."Amount ACY")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Operation Date"; Rec."Operation Date")
                {
                }
                field("External Document No"; Rec."External Document No")
                {
                }
                field("Unposted Invoice No."; Rec."Unposted Invoice No.")
                {
                }
                field("Posted Invoice No."; Rec."Posted Invoice No.")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ViewDredgeHeader)
            {
                Caption = 'View Dredge Header';
                Image = Document;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'View the header for this dredge line.';
                RunObject = page "Dredge Document ERK";
                RunPageLink = "No." = field("Document No.");
            }

            action(CreatePurchaseInvoice)
            {
                Caption = 'Create Purchase Invoice';
                Image = NewPurchaseInvoice;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Creates a purchase invoice from the selected dredge lines.';

                trigger OnAction()
                begin
                    // Code to create purchase invoice would go here
                    // Similar to the Car Carrier implementation
                    Message('Purchase invoice creation would be implemented here.');
                end;
            }

            action(CreateSalesInvoice)
            {
                Caption = 'Create Sales Invoice';
                Image = NewSalesInvoice;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Creates a sales invoice from the selected dredge lines.';

                trigger OnAction()
                begin
                    // Code to create sales invoice would go here
                    // Similar to the Car Carrier implementation
                    Message('Sales invoice creation would be implemented here.');
                end;
            }
        }
    }
}