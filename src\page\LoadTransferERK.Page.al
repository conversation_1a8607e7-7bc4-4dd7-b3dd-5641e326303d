page 60040 "Load Transfer ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Load Transfer';
    PageType = StandardDialog;
    SourceTable = "Load Transer ERK";
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            group(FromInformation)
            {
                Caption = 'Transfer-from Information';
                Editable = false;

                field("From Location Code"; Rec."From Location Code")
                {
                }
                field("From Bin Code"; Rec."From Bin Code")
                {
                }
                field("From Bill-to Customer No."; Rec."From Bill-to Customer No.")
                {
                }
                field("From Load Owner No."; Rec."From Load Owner No.")
                {
                }
                field("From Operation Line No."; Rec."From Operation Line No.")
                {
                }
            }
            group(TransfertoInformation)
            {
                Caption = 'Transfer-to Information';

                field("To Location Code"; Rec."To Location Code")
                {
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                }
                field("To Bill-to Customer No."; Rec."To Bill-to Customer No.")
                {
                }
                field("To Load Owner No."; Rec."To Load Owner No.")
                {
                }
                field("To Contract No."; Rec."To Contract No.")
                {
                }
                // field("To Operation Line No."; Rec."To Operation Line No.")
                // {
                //     ToolTip = 'Specifies the value of the To Operation Line No. field.';
                //     Editable = false;
                // }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                }
            }
        }
    }
    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction = CloseAction::OK then
            PortOperationManagement.CreatePortOperationLineDetailForLoadTransfer(Rec);
    end;

    var
        PortOperationManagement: Codeunit "Port Operation Management ERK";
}
