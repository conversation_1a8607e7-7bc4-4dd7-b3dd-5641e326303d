page 60018 "Voyage Card ERK"
{
    ApplicationArea = All;
    Caption = 'Voyage Card';
    PageType = Card;
    SourceTable = "Voyage Header ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                // field(Type; Rec."Type")
                // {
                //     ToolTip = 'Specifies the value of the Type field.';
                //     trigger OnValidate()
                //     begin
                //         //CurrPage.Update();
                //     end;
                // }
                field(Description; Rec.Description)
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Department Code"; Rec."Department Code")
                {
                }
                // field("Departure Fuel Quantity"; Rec."Departure Fuel Quantity")
                // {
                //     ToolTip = 'Specifies the value of the Departure Fuel Quantity field.';
                // }
                // field("Arrival Fuel Quantity"; Rec."Arrival Fuel Quantity")
                // {
                //     ToolTip = 'Specifies the value of the Arrival Fuel Quantity field.';
                // }
                field("Consumption Fuel Quantity"; Rec."Consumption IFO Quantity")
                {
                }
                field("Consumption MGO Quantity"; Rec."Consumption MGO Quantity")
                {
                }
                field("Reason Code"; Rec."Reason Code")
                {
                }
                field("Reason Description"; Rec."Reason Description")
                {
                }
            }
            group(PortInformation)
            {
                Caption = 'Port Information';

                group(LoadingPort)
                {
                    Caption = 'Loading Port';

                    field("Loading Port Code"; Rec."Loading Port Code")
                    {
                    }
                    field("Loading Port Name"; Rec."Loading Port Name")
                    {
                    }
                    field("Loading Port Arrival Date"; Rec."Loading Port Arrival Date")
                    {
                    }
                    field("Loading Port Departure Date"; Rec."Loading Port Departure Date")
                    {
                    }
                }
                group(DischargePort)
                {
                    Caption = 'Discharge Port';
                    Visible = not Rec."Empty Trip";

                    field("Discharge Port Code"; Rec."Discharge Port Code")
                    {
                    }
                    field("Discharge Port Name"; Rec."Discharge Port Name")
                    {
                    }
                    field("Discharge Port Arrival Date"; Rec."Discharge Port Arrival Date")
                    {
                    }
                    field("Discharge Port Departure Date"; Rec."Discharge Port Departure Date")
                    {
                    }
                    field("Ending Date"; Rec."Ending Date")
                    {
                    }
                }
            }
            part(Lines; "Voyage Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Visible = not Rec."Empty Trip";
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreatePurchaseInvoice)
            {
                Caption = 'Create Purchase Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NewDocument;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Purchase Invoice action.';
                Visible = false;

                trigger OnAction()
                begin
                    VoyageMangement.CreatePurchaseInvoiceFromVoyageHeader(Rec);
                end;
            }
            action(VoyageExpenses)
            {
                Caption = 'Voyage Expenses';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ProjectExpense;
                PromotedOnly = true;
                ToolTip = 'Executes the Voyage Expenses action.';
                RunObject = page "Voyage Expense List ERK";
                RunPageLink = "Document No." = field("No.");
            }
            action(CreateFuelConsumptionInvoice)
            {
                Caption = 'Create IFO Consumption Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ConsumptionJournal;
                ToolTip = 'Executes the Create Fuel Consumption Invoice action.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    VoyageMangement.CreateIFOConsumptionExpenseLine(Rec);
                end;
            }
            action(CreateMGOlConsumptionInvoice)
            {
                Caption = 'Create MGO Consumption Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ConsumptionJournal;
                ToolTip = 'Executes the Create Fuel Consumption Invoice action.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    VoyageMangement.CreateMGOConsumptionExpenseLine(Rec);
                end;
            }
            action(CreateHireExpense)
            {
                Caption = 'Create Hire Expense';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = AbsenceCalendar;
                ToolTip = 'Executes the Create Hire Expense action.';

                trigger OnAction()
                begin
                    VoyageMangement.CreateHireExpenseLine(Rec);
                end;
            }
            action(VoyageLinesDetails)
            {
                Caption = 'Voyage Line Details',;
                Image = TaxDetail;
                ToolTip = 'Executes the Voyage Line Details action.';
                RunObject = page "Voyage Line Detail List ERK";
                RunPageLink = "Document No." = field("No.");
                PromotedOnly = true;
                Promoted = true;
                PromotedCategory = Process;
            }
        }
    }
    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
    // trigger OnOpenPage()
    // begin
    //     case Rec.Type of
    //         Rec.Type::"Ro-Ro":
    //             PortVisible := true;
    //         Rec.Type::"Car Carrier":
    //             PortVisible := false;
    //     end;
    // end;
    // trigger OnAfterGetCurrRecord()
    // begin
    //     case Rec.Type of
    //         Rec.Type::"Ro-Ro":
    //             PortVisible := true;
    //         Rec.Type::"Car Carrier":
    //             PortVisible := false;
    //     end;
    // end;
    // var
    //     PortVisible: Boolean;
}
