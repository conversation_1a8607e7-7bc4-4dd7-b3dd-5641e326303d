table 60056 "Car Carrier Order Line ERK"
{
    Caption = 'Car Carrier Order Line';
    DataClassification = CustomerContent;
    LookupPageId = "Car Carrier Order Lines ERK";
    DrillDownPageId = "Car Carrier Order Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "Load Type"; Code[50])
        {
            Caption = 'Load Type';
            TableRelation = "CC Order Load Type ERK".Code;
            ToolTip = 'Specifies the value of the Load Type field.';
            trigger OnValidate()
            begin
                Rec.TestField("Planned Car Carrier No.", '');
                Rec.Validate("Pre-Load Quantity", 0);
                Rec.Validate("Model Code", '');
                Rec.Validate("Brand Code", '');
                Rec.Validate("Serial No.", '');
                Rec.Validate("Width (cm)", 0);
                Rec.Validate("Length (cm)", 0);
                Rec.Validate("Height (cm)", 0);
                Rec.Validate(Status, Status::" ");

                //Rec.Validate("Planned Car Carrier No.", '');
            end;
        }
        field(4; "Pre-Load Quantity"; Decimal)
        {
            Caption = 'Pre-Load Quantity';
            ToolTip = 'Specifies the value of the Pre-Load Quantity field.';
            trigger OnValidate()
            begin
                Rec.TestField("Planned Car Carrier No.", '');

                if Rec."Pre-Load Quantity" <> 1 then
                    Rec.Validate("Serial No.", '');

                Rec.Validate("Unit Area (m2)");
                Rec.Validate("Unit Volume (m3)");
                Rec.Validate("Unit Gross Weight (kg)");
            end;
        }
        field(5; "Width (cm)"; Decimal)
        {
            Caption = 'Width (cm)';
            ToolTip = 'Specifies the value of the Width (cm) field.';
            trigger OnValidate()
            begin
                Rec.Validate("Unit Area (m2)", Rec."Width (cm)" * Rec."Length (cm)" / 10000);
                Rec.Validate("Unit Volume (m3)", Rec."Unit Area (m2)" * Rec."Height (cm)" / 100);
            end;
        }
        field(6; "Length (cm)"; Decimal)
        {
            Caption = 'Length (cm)';
            ToolTip = 'Specifies the value of the Length (cm) field.';
            trigger OnValidate()
            begin
                Rec.Validate("Unit Area (m2)", Rec."Width (cm)" * Rec."Length (cm)" / 10000);
                Rec.Validate("Unit Volume (m3)", Rec."Unit Area (m2)" * Rec."Height (cm)" / 100);
            end;
        }
        field(7; "Height (cm)"; Decimal)
        {
            Caption = 'Height (cm)';
            ToolTip = 'Specifies the value of the Height (cm) field.';
            trigger OnValidate()
            begin
                Rec.Validate("Unit Area (m2)", Rec."Width (cm)" * Rec."Length (cm)" / 10000);
                Rec.Validate("Unit Volume (m3)", Rec."Unit Area (m2)" * Rec."Height (cm)" / 100);
            end;
        }
        field(8; "Unit Area (m2)"; Decimal)
        {
            Caption = 'Unit Area (m2)';
            ToolTip = 'Specifies the value of the Unit Area (m2) field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Area (m2)", Rec."Unit Area (m2)" * Rec."Pre-Load Quantity");
            end;
        }
        field(9; "Unit Volume (m3)"; Decimal)
        {
            Caption = 'Unit Volume (m3)';
            ToolTip = 'Specifies the value of the Unit Volume (m3) field.';
            trigger OnValidate()
            begin
                Rec.TestField("Planned Car Carrier No.", '');
                Rec.Validate("Line Volume (m3)", "Unit Volume (m3)" * Rec."Pre-Load Quantity");
            end;
        }
        field(10; "Line Area (m2)"; Decimal)
        {
            Caption = 'Line Area (m2)';
            ToolTip = 'Specifies the value of the Line Area field.';
        }
        field(11; "Unit Gross Weight (kg)"; Decimal)
        {
            Caption = 'Unit Gross Weight (kg)';
            ToolTip = 'Specifies the value of the Unit Gross Weight (kg) field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Gross Weight (kg)", Rec."Unit Gross Weight (kg)" * Rec."Pre-Load Quantity");
            end;
        }
        field(12; "Line Gross Weight (kg)"; Decimal)
        {
            Caption = 'Line Gross Weight (kg)';
            ToolTip = 'Specifies the value of the Line Gross Weight (kg) field.';
        }
        field(13; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(14; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            TableRelation = "Serial No. Information"."Serial No.";
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(15; "Model Code"; Code[40])
        {
            Caption = 'Model Code';
            TableRelation = "Model ERK".Code;
            ToolTip = 'Specifies the value of the Model Code field.';
            trigger OnValidate()
            var
                Model: Record "Model ERK";
            begin
                Rec.TestField("Planned Car Carrier No.", '');
                if not Model.Get(Rec."Model Code") then
                    exit;

                Rec.Validate("Width (cm)", Model."Width (mm)" / 10);
                Rec.Validate("Length (cm)", Model."Length (mm)" / 10);
                Rec.Validate("Height (cm)", Model."Height (mm)" / 10);
                Rec.Validate("Unit Gross Weight (kg)", Model."Gross Weight (kg)");
                Rec.Validate("Brand Code", Model."Brand Code");
            end;
        }
        field(16; "Brand Code"; Code[30])
        {
            Caption = 'Brand Code';
            ToolTip = 'Specifies the value of the Brand Code field.';
        }
        field(17; "Line Volume (m3)"; Decimal)
        {
            Caption = 'Line Volume (m3)';
            ToolTip = 'Specifies the value of the Line Volume (m3) field.';
        }
        // field(18; "Imported Quantity"; Integer)
        // {
        //     Caption = 'Imported Quantity';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = count("Car Carrier Order Vehicle ERK" where("Document No." = field("Document No."), "Document Line No." = field("Line No.")));
        // }
        field(19; "Planned Car Carrier No."; Code[20])
        {
            Caption = 'Planned Car Carrier No.';
            TableRelation = "Car Carrier Header ERK"."No." where(Status = filter('<>Completed'));
            ToolTip = 'Specifies the value of the Planned Car Carrier No. field.';
            trigger OnValidate()
            var
                CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
                DeleteCarCarrierOrderLoadDetailsBeforeChangeErr: Label 'You have delete Load Details before changing Planned Car Carrier No. field.';
            begin
                Rec.CalcFields(Type, Vehicle);
                CarCarrierOrderLineDtl.SetRange("Document No.", Rec."Document No.");
                CarCarrierOrderLineDtl.SetRange("Planned Car Carrier No.", xRec."Planned Car Carrier No.");
                if not CarCarrierOrderLineDtl.IsEmpty() then
                    Error(DeleteCarCarrierOrderLoadDetailsBeforeChangeErr);
                Rec.CalcFields("Ship Name");
                if Rec."Planned Car Carrier No." = '' then begin
                    Rec.Validate(Status, Status::" ");
                    //CarCarrierOrderMngt.DeleteCarCarrierRevenueExpenseLinesFromCarCarrierOrderLine(Rec);
                    exit;
                end;

                Rec.TestField("Pre-Load Quantity");
                Rec.TestField("Load Type");
                // Rec.TestField("Est. Fuel Cost Amount");
                // Rec.TestField("Est. Hire Cost Amount");
                // Rec.TestField("Est. Other Cost Amount");
                // Rec.TestField("Est. Revenue Amount");
                if (Rec.Type = Rec.Type::Spot) and (Rec.Vehicle) then begin
                    Rec.TestField("Model Code");
                    Rec.TestField("Brand Code");
                    Rec.TestField("Unit Volume (m3)");
                end
                else
                    if (Rec.Type = Rec.Type::Spot) and (not Rec.Vehicle) then
                        Rec.TestField("Unit Volume (m3)")
                    else // if (Rec.Type = Rec.Type::Contract) and Rec.Vehicle then begin
                         // end
                         //else
                        if (Rec.Type = Rec.Type::Contract) and not Rec.Vehicle then
                            Rec.TestField("Unit Volume (m3)");
                Rec.Validate(Status, Status::Planned);
                //CarCarrierOrderMngt.CreateUpdateCarCarrierRevenueExpenseFromCarCarrierOrderLine(Rec, xRec);
            end;
        }
        field(20; Type; Enum "Car Carrier Order Type ERK")
        {
            Caption = 'Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK".Type where("No." = field("Document No.")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(21; Status; Enum "Car Carrier Order Line Status")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
            // trigger OnValidate()
            // begin
            //     if "Planned Car Carrier No." <> '' then
            //         Rec.Validate(Status, Status::Planned)
            //     else
            //         Rec.Validate(Status, Rec.Status::" ");
            // end;
        }
        field(22; Vehicle; Boolean)
        {
            Caption = 'Vehicle';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("CC Order Load Type ERK".Vehicle where(Code = field("Load Type")));
            AllowInCustomizations = Always;
        }
        field(23; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ship Name" where("No." = field("Planned Car Carrier No.")));
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(24; "Order Status"; Enum "Car Carrier Order Status ERK")
        {
            Caption = 'Order Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK".Status where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Order Status field.';
        }
        field(25; "Loading Port Code"; Code[10])
        {
            Caption = 'Loading Port Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Loading Port Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Loading Port Code field.';
        }
        field(26; "Loading Post Description"; Text[100])
        {
            Caption = 'Loading Post Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Loading Port Code")));
            ToolTip = 'Specifies the value of the Loading Post Description field.';
        }
        field(18; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Customer Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(27; "Discharge Port Code"; Code[10])
        {
            Caption = 'Loading Port Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Discharge Port Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Loading Port Code field.';
        }
        field(28; "Discharge Post Description"; Text[100])
        {
            Caption = 'Discharge Post Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Discharge Port Code")));
            ToolTip = 'Specifies the value of the Discharge Post Description field.';
        }
        field(29; "Your Reference"; Text[35])
        {
            Caption = 'Your Reference';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Your Reference" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Your Reference field.';
        }
        field(30; "Booking No."; Code[20])
        {
            Caption = 'Booking No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Booking No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Booking No. field.';
        }
        field(31; "Transshipment Allowed"; Boolean)
        {
            Caption = 'Transshipment Allowed';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Trans Shipment Allowed" where("No." = field("Document No.")));
            ToolTip = 'Specifies whether transshipment is allowed for this order.';
        }
        field(32; "Transshipment Port"; Code[10])
        {
            Caption = 'Transshipment Port';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Order Header ERK"."Transshipment Port" where("No." = field("Document No.")));
            ToolTip = 'Specifies the transshipment port code if transshipment is allowed.';
        }

        // field(30; "Est. Revenue Amount"; Decimal)
        // {
        //     Caption = 'Est. Revenue Amount';

        //     trigger OnValidate()
        //     begin
        //         ErkHoldingSetup.GetRecordOnce();
        //         ErkHoldingSetup.TestField("Est. Revenue Item No.");
        //         Rec.TestField("Planned Car Carrier No.", '');
        //     end;
        // }
        // field(31; "Est. Revenue Currency Code"; Code[10])
        // {
        //     Caption = 'Est. Revenue Currency Code';
        //     TableRelation = Currency.Code;

        //     trigger OnValidate()
        //     begin
        //         Rec.TestField("Planned Car Carrier No.", '');
        //     end;
        // }
        // field(32; "Est. Fuel Cost Amount"; Decimal)
        // {
        //     Caption = 'Est. Fuel Cost Amount';

        //     trigger OnValidate()
        //     begin
        //         ErkHoldingSetup.GetRecordOnce();
        //         ErkHoldingSetup.TestField("Est. Fuel Item No.");
        //         Rec.TestField("Planned Car Carrier No.", '');
        //     end;
        // }
        // field(33; "Est. Fuel Cost Currency Code"; Code[10])
        // {
        //     Caption = 'Est. Fuel Cost Currency Code';
        //     TableRelation = Currency.Code;

        //     trigger OnValidate()
        //     begin
        //         Rec.TestField("Planned Car Carrier No.", '');
        //     end;
        // }
        // field(34; "Est. Hire Cost Amount"; Decimal)
        // {
        //     Caption = 'Est. Hire Cost Amount';

        //     trigger OnValidate()
        //     begin
        //         ErkHoldingSetup.GetRecordOnce();
        //         ErkHoldingSetup.TestField("Est. Hire Item No.");
        //         Rec.TestField("Planned Car Carrier No.", '');
        //     end;
        // }
        // field(35; "Est. Hire Cost Currency Code"; Code[10])
        // {
        //     Caption = 'Est. Hire Cost Currency Code';
        //     TableRelation = Currency.Code;

        //     trigger OnValidate()
        //     begin
        //         Rec.TestField("Planned Car Carrier No.", '');
        //     end;
        // }
        // field(36; "Est. Other Cost Amount"; Decimal)
        // {
        //     Caption = 'Est. Other Cost Amount';

        //     trigger OnValidate()
        //     begin
        //         ErkHoldingSetup.GetRecordOnce();
        //         ErkHoldingSetup.TestField("Est. Other Cost Item No.");
        //         Rec.TestField("Planned Car Carrier No.", '');
        //     end;
        // }
        // field(37; "Est. Other Cost Currency Code"; Code[10])
        // {
        //     Caption = 'Est. Other Cost Currency Code';
        //     TableRelation = Currency.Code;

        //     trigger OnValidate()
        //     begin
        //         Rec.TestField("Planned Car Carrier No.", '');
        //     end;
        // }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
        CarCarrierOrderHeader: Record "Car Carrier Order Header ERK";
    begin
        CarCarrierOrderLine.SetRange("Document No.", Rec."Document No.");
        if CarCarrierOrderLine.FindLast() then
            Rec."Line No." := CarCarrierOrderLine."Line No." + 10000
        else
            Rec."Line No." := 10000;

        CarCarrierOrderHeader.Get(Rec."Document No.");
        CarCarrierOrderHeader.Validate(Status, CarCarrierOrderHeader.Status::"In Progress");
        CarCarrierOrderHeader.Modify(true);
    end;

    trigger OnDelete()
    var
        CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
        DeleteCarCarrierOrderLoadDetailsBeforeChangeErr: Label 'You have delete Load Details before changing Planned Car Carrier No. field.';
        CanNotDeleteErr: Label 'You can not delete a line with status Planned.';
    begin
        CarCarrierOrderLineDtl.SetRange("Document No.", Rec."Document No.");
        CarCarrierOrderLineDtl.SetRange("Planned Car Carrier No.", Rec."Planned Car Carrier No.");
        if not CarCarrierOrderLineDtl.IsEmpty() then
            Error(DeleteCarCarrierOrderLoadDetailsBeforeChangeErr);
        if Rec.Status = Rec.Status::Planned then
            Error(CanNotDeleteErr);
    end;

    var
    //ErkHoldingSetup: Record "Erk Holding Setup ERK";
    //CarCarrierOrderMngt: Codeunit "Car Carrier Order Mngt. ERK";
}
