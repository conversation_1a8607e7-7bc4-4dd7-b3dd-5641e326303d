tableextension 60011 "Serial No. Information ERK" extends "Serial No. Information"
{
    fields
    {
        field(60000; "Brand Code ERK"; Code[30])
        {
            Caption = 'Brand Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Brand Code field.';
            //TableRelation = Manufacturer.Code;
        }
        // field(60001; "Model Name ERK"; Code[20])
        // {
        //     Caption = 'Model Name';
        // }
        field(60002; "Model Version ERK"; Code[100])
        {
            Caption = 'Model Version';
            TableRelation = "Model Version ERK".Code;
            ToolTip = 'Specifies the value of the Model Version field.';
            trigger OnValidate()
            var
                ModelVersion: Record "Model Version ERK";
            begin
                ModelVersion.SetRange(Code, Rec."Model Version ERK");
                if ModelVersion.FindFirst() then
                    Rec.Validate("Model Code ERK", ModelVersion."Model Code");
            end;
        }
        field(60003; "Model Code ERK"; Code[40])
        {
            Caption = 'Model Code';
            TableRelation = "Model ERK".Code;
            Editable = false;
            ToolTip = 'Specifies the value of the Model Code field.';
            trigger OnValidate()
            var
                Model: Record "Model ERK";
            begin
                SerialNoManagement.CalculateAreaFromSerialNoInformation(Rec);
                SerialNoManagement.CalculateVolumeFromSerialNoInformation(Rec);
                SerialNoManagement.CalcualteGrossWeightFromSerialNoInformation(Rec);

                if Model.Get(Rec."Model Code ERK") then
                    Rec.Validate("Brand Code ERK", Model."Brand Code");
            end;
        }
        field(60004; "Colour Name ERK"; Code[100])
        {
            Caption = 'Colour Name';
            ToolTip = 'Specifies the value of the Colour Name field.';
        }
        field(60005; "Fuel Type ERK"; Enum "Fuel Type ERK")
        {
            Caption = 'Fuel Type';
            ToolTip = 'Specifies the value of the Fuel Type field.';
        }
        field(60006; "Engine ID ERK"; Code[10])
        {
            Caption = 'Engine ID';
            ToolTip = 'Specifies the value of the Engine ID field.';
        }
        field(60007; "Gross Weight (KG) ERK"; Decimal)
        {
            Caption = 'Gross Weight (KG)';
            Editable = false;
            ToolTip = 'Specifies the value of the Gross Weight (KG) field.';
        }
        field(60008; "Footprint (m2) ERK"; Decimal)
        {
            Caption = 'Footprint (m2)';
            Editable = false;
            ToolTip = 'Specifies the value of the Footprint (m2) field.';
        }
        field(60009; "TSE ERK"; Boolean)
        {
            Caption = 'TSE';
            ToolTip = 'Specifies the value of the TSE field.';
        }
        field(60010; "Customs Declaration No. ERK"; Code[20])
        {
            Caption = 'Customs Declaration No.';
            ToolTip = 'Specifies the value of the Customs Declaration No. field.';
        }
        field(60011; "Customs Dec. Line No. ERK"; Integer)
        {
            Caption = 'Customs Declaration Line No.';
            ToolTip = 'Specifies the value of the Customs Declaration Line No. field.';
        }
        field(60012; "Current Location Code ERK"; Code[10])
        {
            Caption = 'Current Location Code';
            //Editable = false;
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Current Location Code field.';
        }
        field(60013; "Current Bin Code ERK"; Code[20])
        {
            Caption = 'Current Bin Code';
            //Editable = false;
            TableRelation = Bin.Code where("Location Code" = field("Current Location Code ERK"));
            ToolTip = 'Specifies the value of the Current Bin Code field.';
        }
        field(60014; "Civil Area ERK"; Boolean)
        {
            Caption = 'Civil Area';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Bin."Civil Area ERK" where("Location Code" = field("Current Location Code ERK"), Code = field("Current Bin Code ERK")));
            ToolTip = 'Specifies the value of the Civil Area field.';
        }
        field(60015; "Customs Registration Date ERK"; Date)
        {
            Caption = 'Customs Registration Date';
            ToolTip = 'Specifies the value of the Customs Registration Date field.';
        }
        field(60016; "Car Carrier Ledger Entries ERK"; Integer)
        {
            Caption = 'Car Carrier Ledger Entries';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Serial No." = field("Serial No.")));
            ToolTip = 'Specifies the value of the Car Carrier Ledger Entries field.';
        }
        field(60017; "Vehicle Trns Ledg. Entries ERK"; Integer)
        {
            Caption = 'Vehicle Transfer Ledger Entries';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Ledger Entry" where("Serial No." = field("Serial No.")));
            ToolTip = 'Specifies the value of the Vehicle Transfer Ledger Entries field.';
        }
        field(60018; "Nav. Process Required ERK"; Boolean)
        {
            Caption = 'Navigation Process Required';
            ToolTip = 'Specifies the value of the Navigation Process Required field.';
        }
        field(60019; "Volume (m3) ERK"; Decimal)
        {
            Caption = 'Volume (m3)';
            Editable = false;
            ToolTip = 'Specifies the value of the Volume (m3) field.';
        }
        field(60001; "Commercial Blockage ERK"; Boolean)
        {
            Caption = 'Commercial Blockage';
            ToolTip = 'Specifies the value of the Commercial Blockage field.';
        }
        // field(60020; "Charge Cable Should Exist ERK"; Boolean)
        // {
        //     Caption = 'Charge Cable Should Exist';
        // }
        field(60020; "Grupage No. ERK"; Code[20])
        {
            Caption = 'Grupage No.';
            ToolTip = 'Specifies the value of the Grupage No. field.';
        }
        field(60021; "Grupage Date ERK"; Date)
        {
            Caption = 'Grupage Date';
            ToolTip = 'Specifies the value of the Grupage Date field.';
        }
        field(60022; "Grupage Ship-to Name ERK"; Text[100])
        {
            Caption = 'Grupage Ship-to Name';
            ToolTip = 'Specifies the value of the Grupage Ship-to Name field.';
        }
        field(60023; "Grupage Ship-to City ERK"; Code[35])
        {
            Caption = 'Grupage Ship-to City';
            TableRelation = "Post Code".City;
            ToolTip = 'Specifies the value of the Grupage Ship-to City field.';
        }
        field(60024; "Grupage Bin Code ERK"; Code[20])
        {
            Caption = 'Grupage Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Grupage Location Code ERK"));
            ToolTip = 'Specifies the value of the Grupage Bin Code field.';
        }
        field(60025; "Grupage Ship-to Address ERK"; Text[250])
        {
            Caption = 'Grupage Ship-to Address';
            ToolTip = 'Specifies the value of the Grupage Ship-to Address field.';
        }
        field(60026; "Grupage Location Code ERK"; Code[20])
        {
            Caption = 'Grupage Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Grupage Location Code field.';
        }
        field(60027; "Print Grupage Label ERK"; Boolean)
        {
            Caption = 'Print Grupage';
            ToolTip = 'Specifies the value of the Print Grupage field.';
        }
        field(60028; "Temp. Traffic Document No. ERK"; Code[20])
        {
            Caption = 'Temp. Traffic Document No.';
            ToolTip = 'Specifies the temporary traffic document number.';
        }
        field(60029; "Temp. License Plate No. ERK"; Code[20])
        {
            Caption = 'Temp. License Plate No.';
            ToolTip = 'Specifies the temporary license plate number.';
            TableRelation = "Temporary License Plate ERK"."No.";
        }
        field(60030; "Temp. Driver Full Name ERK"; Text[100])
        {
            Caption = 'Temp. Driver Full Name';
            ToolTip = 'Specifies the full name of the temporary driver.';
            TableRelation = "Driver Information ERK"."Driver Full Name";
        }
        field(60031; "Summary Declaration No. ERK"; Code[20])
        {
            Caption = 'Summary Declaration No.';
            ToolTip = 'Specifies the summary declaration number.';
        }
        field(60032; "Nav Upload Succesful ERK"; Boolean)
        {
            Caption = 'Navigation Upload Succesful';
            ToolTip = 'Specifies the value of the Navigation Upload Succesful field.';
        }
        field(60033; "Bill of Lading No. ERK"; Code[20])
        {
            Caption = 'Bill of Lading No.';
            ToolTip = 'Specifies the value of the Bill of Lading No. field.';
        }
        field(60034; "Truck Plate ERK"; Code[10])
        {
            Caption = 'Truck Plate';
            ToolTip = 'Specifies the value of the Truck Plate field.';
        }
        field(60035; "Driver Full Name ERK"; Text[100])
        {
            Caption = 'Driver Full Name';
            ToolTip = 'Specifies the value of the Driver Full Name field.';
        }
        field(60036; "Trailer Plate ERK"; Code[10])
        {
            Caption = 'Trailer Plate';
            ToolTip = 'Specifies the value of the Trailer Plate field.';
        }
        field(60037; "Note to Add ERK"; Text[2048])
        {
            Caption = 'Note to Add';
            ToolTip = 'Specifies a note to add to this Serial No. record.';
        }
    }
    keys
    {
        key(SK; "Current Location Code ERK", "Current Bin Code ERK")
        {
        }
        key(SK2; "Serial No.")
        {
        }
    }
    var
        SerialNoManagement: Codeunit "Serial No. Management ERK";
}
