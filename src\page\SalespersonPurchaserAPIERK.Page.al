#pragma warning disable LC0062
page 60105 "SalespersonPurchaser API ERK"
#pragma warning restore LC0062
{
    PageType = API;
    Caption = 'Salesperson/Purchaser API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'salespersonPurchasers';
    EntitySetName = 'salespersonPurchasers';
    SourceTable = "Salesperson/Purchaser";
    DelayedInsert = true;
    ODataKeyFields = SystemId;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(id; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(code; Rec.Code)
                {
                    Caption = 'Code';
                }
                field(displayName; Rec.Name)
                {
                    Caption = 'Name';
                }
                field(blocked; Rec.Blocked)
                {
                    Caption = 'Blocked';
                }
            }
        }
    }
}