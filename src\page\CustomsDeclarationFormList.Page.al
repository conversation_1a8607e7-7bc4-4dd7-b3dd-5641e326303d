page 60129 "Customs Declaration Form List"
{
    ApplicationArea = All;
    Caption = 'Customs Declaration Form List';
    PageType = List;
    SourceTable = "Export Header ERK";
    UsageCategory = ReportsAndAnalysis;
    Editable = false;
    ShowFilter = true;


    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(LineOrderNo; LineOrderNo)
                {
                    Caption = 'Line Order No.';
                    ToolTip = 'Specifies the line order number of the Customs Declaration Form.';
                }
                field("Customs Declaration No. FF"; Rec."Customs Declaration No. FF")
                {
                }
                field("Sales Currency Code"; Rec."Sales Currency Code")
                {
                }
                field("Export Invoice Amount"; Rec."Export Invoice Amount")
                {
                }
                field(Type; CDFLbl)
                {
                    Caption = 'Type';
                    ToolTip = 'Specifies the type of the Customs Declaration Form.';
                }
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        LineOrderNo += 1;
    end;

    var
        CDFLbl: Label 'CDF';
        LineOrderNo: Integer;
}