page 60060 "PDI Documents ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'PDI Documents';
    PageType = List;
    SourceTable = "PDI Header ERK";
    UsageCategory = Documents;
    Editable = false;
    CardPageId = "PDI Document ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Operation Starting Date-Time"; Rec."Operation Starting Date-Time")
                {
                }
                field("Operation Ending Date-Time"; Rec."Operation Ending Date-Time")
                {
                }
                field("Assigned User ID"; Rec."Responsible Name")
                {
                }
                field("Manufacturer Code"; Rec."Brand Code")
                {
                }
                field("Carline Code"; Rec."Model Code")
                {
                }
                field("Color Name"; Rec."Color Name")
                {
                }
                field("Fuel Type"; Rec."Fuel Type")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}
