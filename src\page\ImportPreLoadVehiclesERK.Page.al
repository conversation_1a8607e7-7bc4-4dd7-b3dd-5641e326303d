page 60087 "Import Pre-Load Vehicles ERK"
{
    ApplicationArea = All;
    Caption = 'Import Pre-Load Vehicles';
    PageType = Worksheet;
    SourceTable = "Car Carrier Order Vehicle ERK";
    UsageCategory = None;
    SourceTableTemporary = true;
    // AutoSplitKey = true;
    DelayedInsert = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                    Editable = false;
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(RegisterPreLoadList)
            {
                Caption = 'Register Pre-Load List';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Register Pre-Load List action.';
                Image = Registered;
                PromotedIsBig = true;
                PromotedOnly = true;

                trigger OnAction()
                begin
                    CarCarrierOrderMngt.RegisterPreLoadVehicles(Rec, CarCarrierOrderLineDtl);
                    CurrPage.Close();
                end;
            }
        }
    }
    procedure SetCarCarrierOrderLineDetailRecord(ParCarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK")
    begin
        CarCarrierOrderLineDtl := ParCarCarrierOrderLineDtl;
    end;

    trigger OnInit()
    begin
        EntryNo := 0;
    end;
    // trigger OnNewRecord(BelowxRec: Boolean)
    // begin
    //     Rec."Entry No." := EntryNo + 1;
    //     EntryNo += 1;
    // end;
    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    begin
        Rec."Entry No." := EntryNo + 1;
        EntryNo += 1;
    end;

    var
        CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
        CarCarrierOrderMngt: Codeunit "Car Carrier Order Mngt. ERK";
        EntryNo: Integer;
}
