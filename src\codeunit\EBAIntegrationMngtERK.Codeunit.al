codeunit 60019 "EBA Integration Mngt. ERK"
{
    trigger OnRun()
    begin
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Inbound Invoice Management INF", OnAfterGetInvoiceLinesInfo, '', false, false)]
    local procedure "Inbound Invoice Management INF_OnAfterGetInvoiceLinesInfo"(NodeList: XmlNodeList; var EInvHeader: Record "E-Invoice Header INF")
    begin

        GetInvoice((CopyStr(UserId(), 1, 50)), EInvHeader."Responsibility Center", EInvHeader, true);
        if EInvHeader."EBA Status ERK" = EInvHeader."EBA Status ERK"::"Waiting for Approval" then
            EInvHeader."EBA Status ERK" := EInvHeader."EBA Status ERK"::"Ready for EBA";
    end;

    local procedure CheckGetInvoiceViewRequest()
    begin
        CompanyInformation.Get();
        CompanyInformation.TestField("VAT Registration No.");
        CompanyInformation.TestField("E-Invoice GB AliasINF");
    end;

    local procedure MakeGetInvoiceViewRequest(UUID: Text; Alias: Text; VKN: Text; Type: Text; DocType: Text): Text
    var
        XmlDoc: XmlDocument;
        EnvelopeXmlNode: XmlNode;
        BodyXmlNode: XmlNode;
        RequestXmlNode: XmlNode;
        TempXmlNode: XmlNode;
        RootXmlelement: XmlElement;
        RequestText: Text;

    begin
        //ein ve soapenv prefix
        //ein namespace = http:/fitcons.com/eInvoice/
        //soapenv namespace = http://schemas.xmlsoap.org/soap/envelope/

        CheckgetRAWUserListRequest();

        CreateEnvelope(EnvelopeXmlNode, BodyXmlNode);
        XMLUtilityFunction.AddElement(BodyXmlNode, 'getInvoiceViewRequest', '', 'ein', RequestXmlNode, 'http:/fitcons.com/eInvoice/');
        XMLUtilityFunction.AddElement(RequestXmlNode, 'UUID', UUID, 'ein', TempXmlNode, 'http:/fitcons.com/eInvoice/');
        XMLUtilityFunction.AddElement(RequestXmlNode, 'Identifier', Alias, 'ein', TempXmlNode, 'http:/fitcons.com/eInvoice/');
        XMLUtilityFunction.AddElement(RequestXmlNode, 'VKN_TCKN', VKN, 'ein', TempXmlNode, 'http:/fitcons.com/eInvoice/');
        XMLUtilityFunction.AddElement(RequestXmlNode, 'Type', Type, 'ein', TempXmlNode, 'http:/fitcons.com/eInvoice/');
        XMLUtilityFunction.AddElement(RequestXmlNode, 'DocType', DocType, 'ein', TempXmlNode, 'http:/fitcons.com/eInvoice/');

        XmlDoc.Add(EnvelopeXmlNode);
        XmlDoc.GetRoot(RootXmlelement);
        RootXmlelement.WriteTo(RequestText);
        exit(RequestText);
    end;

    local procedure CheckgetRAWUserListRequest()
    begin
        CompanyInformation.Get();
        CompanyInformation.TestField("VAT Registration No.");
        CompanyInformation.TestField("E-Invoice PK AliasINF");
    end;

    local procedure CreateEnvelope(var EnvelopeXmlNode: XmlNode; var BodyXmlNode: XmlNode)
    var
        XmlDoc: XmlDocument;
        RootXmlNode: XmlNode;
        HeaderXmlNode: XmlNode;
    begin
        //ein ve soapenv prefix
        //ein namespace = http:/fitcons.com/eInvoice/
        //soapenv namespace = http://schemas.xmlsoap.org/soap/envelope/

        XMLUtilityFunction.AddRootElement(XmlDoc, 'root', RootXmlNode, '', '');
        XMLUtilityFunction.AddAttribute(RootXmlNode, '', 'soapenv', 'http://schemas.xmlsoap.org/soap/envelope/', 'http://schemas.xmlsoap.org/soap/envelope/');
        XMLUtilityFunction.AddElement(RootXmlNode, 'Envelope', '', 'soapenv', EnvelopeXmlNode, 'http://schemas.xmlsoap.org/soap/envelope/');
        XMLUtilityFunction.AddAttribute(EnvelopeXmlNode, '', 'soapenv', 'http://schemas.xmlsoap.org/soap/envelope/', 'http://schemas.xmlsoap.org/soap/envelope/');
        XMLUtilityFunction.AddAttribute(EnvelopeXmlNode, '', 'ein', 'http:/fitcons.com/eInvoice/', 'http:/fitcons.com/eInvoice/');
        XMLUtilityFunction.AddElement(EnvelopeXmlNode, 'Header', '', 'soapenv', HeaderXmlNode, 'http://schemas.xmlsoap.org/soap/envelope/');
        XMLUtilityFunction.AddElement(EnvelopeXmlNode, 'Body', '', 'soapenv', BodyXmlNode, 'http://schemas.xmlsoap.org/soap/envelope/');
    end;

    [TryFunction]
#pragma warning disable AA0137
    local procedure GetInvoice(User_ID: Code[50]; ResponsibilityCenterCode: Code[10]; var LocalEInvoiceHeader: Record "E-Invoice Header INF"; LocalHideDialogs: Boolean)
#pragma warning restore AA0137
    var
        TempBlob: Codeunit "Temp Blob";
        FunctionExecutedWithoutError: Boolean;
        ResponseInStream: InStream;
        DocDataXmlNode: XmlNode;
        XmlDoc: XmlDocument;
        ResponseOutStream: OutStream;
        RootXmlNode: XmlNode;
        HttpResponseMessageWS: HttpResponseMessage;
        RequestText: Text;
        EInvViewOStr: OutStream;
        PDFBase64: Text;
    begin
        EInvoiceSetup.Get();
        //EInvoiceSetup.TestField("E-Invoice Integrator Code");
        EInvoiceSetup.TestField("E-Invoice Integration UserName");
        EInvoiceSetup.TestField("E-Invoice Integration Password");
        EInvoiceSetup.TestField("E-Invoice LCY Code");

        EInvoiceIntegrator.Get(EInvoiceSetup."E-Invoice Integrator Code");
        EInvoiceIntegrator.TestField("E-Invoice Integration URL INF");
        EInvoiceIntegrator.TestField("E-Archive Integration URL INF");

        CheckGetInvoiceViewRequest();
        RequestText := MakeGetInvoiceViewRequest(LocalEInvoiceHeader.UUID, LocalEInvoiceHeader.GetAliasValue(), CompanyInformation."VAT Registration No.", LocalEInvoiceHeader.GetTypeParameterValue(), 'PDF');
        //SetGlobals(EInvoiceIntegrator."E-Invoice Integration URL INF", 'getInvoiceView');
        FunctionExecutedWithoutError := SendRequest(HttpResponseMessageWS, ResponsibilityCenterCode, RequestText, TempBlob, ResponseInStream, ResponseOutStream, 'getInvoiceView', 'POST');
        if FunctionExecutedWithoutError then begin
            TempBlob.CreateInStream(ResponseInStream);
            XmlDocument.ReadFrom(ResponseInStream, XmlDoc);
            XMLUtilityFunction.RemoveNamespacesUtf16(XmlDoc.AsXmlNode(), RootXmlNode);
            RootXmlNode.SelectSingleNode('Envelope/Body/getInvoiceViewResponse/DocData', DocDataXmlNode);
            //XMLUtilityFunction.FromBase64String(DocDataXmlNode.AsXmlElement().InnerText, TempBlob);
            PDFBase64 := DocDataXmlNode.AsXmlElement().InnerText();

            // TempBlob2.CreateOutStream(Base64PDFFileOStream);
            // Base64PDFFileOStream.WriteText(Base64PDFFile);

            //TempBlob.CreateInStream(PDFInStream);

            Clear(EInvViewOStr);
            LocalEInvoiceHeader."Invoice View PDF File".CreateOutStream(EInvViewOStr, TextEncoding::UTF8);
            EInvViewOStr.WriteText(PDFBase64);
            //CopyStream(EInvViewOStr, PDFInStream);
        end;
    end;

    local procedure SendRequest(var HttpResponseMessageWS: HttpResponseMessage; ResponsibilityCenterCode: Code[10]; RequestText: Text; var TempBlob: Codeunit "Temp Blob"; var ResponseInStream: InStream; var ResponseOutStream: OutStream; SoapAction: Text; HttpMethot: Code[20]): Boolean
    var
        EDocumentInternalReq: Record "E-Document Internal Req. INF";
        EDocumentInternalResp: Record "E-Document Internal Resp. INF";
        DataCompression: Codeunit "Data Compression";
        HttpContentWS: HttpContent;
        HttpHeadersWS: HttpHeaders;
        DefaultHttpHeadersWS: HttpHeaders;
        HttpClientWS: HttpClient;
        HttpRequestMessageWS: HttpRequestMessage;

    begin

        HttpClientWS.Timeout := 300000;
        EDocumentInternalReq.Get(CopyStr(UserId(), 1, 50));
        EDocumentInternalReq.UpdateXMLRequestBlobInEDocInternalReq(RequestText);

        HttpContentWS.WriteFrom(RequestText);
        HttpContentWS.GetHeaders(HttpHeadersWS);

        HttpHeadersWS.Clear();
        HttpHeadersWS.Add('Content-Type', 'text/xml;charset=UTF-8');
        HttpHeadersWS.Add('SOAPAction', SoapAction);
        HttpRequestMessageWS.Content := HttpContentWS;

        HttpRequestMessageWS.SetRequestUri(EInvoiceIntegrator."E-Invoice Integration URL INF");

        HttpRequestMessageWS.Method := HttpMethot;

        DefaultHttpHeadersWS := HttpClientWS.DefaultRequestHeaders();

        DefaultHttpHeadersWS.Add('Accept-Encoding', 'gzip,deflate');
        EInvoiceManagement.GetIntegrationUserNameAndPassWord(ResponsibilityCenterCode, IntegrationCompanyCode, IntegrationUserName, IntegrationPassword);
        XMLUtilityFunction.AddHttpBasicAuthHeader(ResponsibilityCenterCode, DefaultHttpHeadersWS, IntegrationUserName, IntegrationPassword);
        DefaultHttpHeadersWS.Add('Connection', 'Keep-Alive');
        DefaultHttpHeadersWS.Add('User-Agent', 'Apache-HttpClient/4.1.1 (java 1.5)');

        HttpClientWS.Send(HttpRequestMessageWS, HttpResponseMessageWS);
        Clear(TempBlob);
        TempBlob.CreateInStream(ResponseInStream);
        HttpResponseMessageWS.Content().ReadAs(ResponseInStream);

        GlobalError := Format(HttpResponseMessageWS.HttpStatusCode()) + ' ' + HttpResponseMessageWS.ReasonPhrase();

        EDocumentInternalResp.Get(CopyStr(UserId(), 1, 50));
        EDocumentInternalResp.UpdateXMLResponseBlobInEDocInternalResp(ResponseInStream);

        if DataCompression.IsGZip(ResponseInStream) then begin
            Clear(TempBlob);
            TempBlob.CreateOutStream(ResponseOutStream);

            DataCompression.GZipDecompress(ResponseInStream, ResponseOutStream);

            TempBlob.CreateInStream(ResponseInStream);
            exit(true);
        end else
            if (HttpResponseMessageWS.HttpStatusCode() = 200) then begin
                TempBlob.CreateOutStream(ResponseOutStream);
                CopyStream(ResponseOutStream, ResponseInStream);

                exit(true);
            end
            else begin
                TempBlob.CreateOutStream(ResponseOutStream);
                CopyStream(ResponseOutStream, ResponseInStream);
                exit(false);
            end;
    end;

    procedure SetPurchaseEInvoiceType(TaxNo: Code[20]; PostingDate: Date) InvoiceType: Enum "E-Document Inv CrMemo Type INF"
    var
        EInvoiceRegisteredLiable: Record "E-Invoice Registrd Liable INF";
    begin
        if TaxNo = '' then
            exit(InvoiceType::" ");

        if PostingDate = 0D then
            exit(InvoiceType::" ");

        CompanyInformation.Get();

        if CompanyInformation."E-Inv. Liability Start Date INF" = 0D then
            exit(InvoiceType::"Pre-Printed");

        if PostingDate < CompanyInformation."E-Inv. Liability Start Date INF" then
            exit(InvoiceType::"Pre-Printed");

        EInvoiceRegisteredLiable.Reset();
        EInvoiceRegisteredLiable.SetRange("Tax No.", TaxNo);
        EInvoiceRegisteredLiable.SetFilter("Label Last Updated Date Time", '<=%1', CreateDateTime(PostingDate, 0T));
        if not EInvoiceRegisteredLiable.IsEmpty() then
            InvoiceType := InvoiceType::"E-Invoice"
        else
            InvoiceType := InvoiceType::"E-Archive";

    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnAfterValidateEvent, "VAT Registration No.", true, false)]
    local procedure PurchaseHeader_OnAfterValidateEvent_VRN(var Rec: Record "Purchase Header")
    begin
        Rec."Invoice Type INF ERK" := SetPurchaseEInvoiceType(Rec."VAT Registration No.", Rec."Posting Date");

    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnAfterValidateEvent, "VAT Registration No.", true, false)]
    local procedure SalesHeader_OnAfterValidateEvent_VRN(var Rec: Record "Sales Header")
    begin
        Rec."Invoice Type INF ERK" := SetPurchaseEInvoiceType(Rec."VAT Registration No.", Rec."Posting Date");

    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnAfterValidateEvent, "Posting Date", true, false)]
    local procedure PurchaseHeader_OnAfterValidateEvent_PD(var Rec: Record "Purchase Header")
    begin
        Rec."Invoice Type INF ERK" := SetPurchaseEInvoiceType(Rec."VAT Registration No.", Rec."Posting Date");

    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnAfterValidateEvent, "Posting Date", true, false)]
    local procedure SalesHeader_OnAfterValidateEvent_PD(var Rec: Record "Sales Header")
    begin
        Rec."Invoice Type INF ERK" := SetPurchaseEInvoiceType(Rec."VAT Registration No.", Rec."Posting Date");

    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnAfterValidateEvent, "Buy-from Vendor No.", true, false)]
    local procedure PurchaseHeader_OnAfterValidateEvent_VN(var Rec: Record "Purchase Header")
    begin
        Rec."Invoice Type INF ERK" := SetPurchaseEInvoiceType(Rec."VAT Registration No.", Rec."Posting Date");

    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnAfterValidateEvent, "Sell-to Customer No.", true, false)]
    local procedure SalesHeader_OnAfterValidateEvent_VN(var Rec: Record "Sales Header")
    begin
        Rec."Invoice Type INF ERK" := SetPurchaseEInvoiceType(Rec."VAT Registration No.", Rec."Posting Date");

    end;


    procedure ImportInvoiceViewFile(var PurchaseHeader: Record "Purchase Header")
    var
        BlobRef: Codeunit "Temp Blob";
        FileManagement: Codeunit "File Management";
        RecordRef: RecordRef;
        ServerFileName: Text;

    begin

        ServerFileName := FileManagement.BLOBImportWithFilter(BlobRef, 'Import Invoice View File', '', 'All files (*.*)|*.*', 'All files (*.*)|*.*');

        RecordRef.GetTable(PurchaseHeader);
        BlobRef.ToRecordRef(RecordRef, PurchaseHeader.FieldNo("Invoice View PDF File ERK"));
        RecordRef.SetTable(PurchaseHeader);

    end;

    procedure ImportInvoiceViewFileSales(var SalesHeader: Record "Sales Header")
    var
        BlobRef: Codeunit "Temp Blob";
        FileManagement: Codeunit "File Management";
        RecordRef: RecordRef;
        ServerFileName: Text;

    begin

        ServerFileName := FileManagement.BLOBImportWithFilter(BlobRef, 'Import Invoice View File', '', 'All files (*.*)|*.*', 'All files (*.*)|*.*');

        RecordRef.GetTable(SalesHeader);
        BlobRef.ToRecordRef(RecordRef, SalesHeader.FieldNo("Invoice View PDF File ERK"));
        RecordRef.SetTable(SalesHeader);

    end;

    procedure ImportInvoiceView(var PurchaseHeader: Record "Purchase Header")
    var
        Base64Convert: Codeunit "Base64 Convert";
        Base64EncodedFile: BigText;
        FileInStream: InStream;
        Base64OutStream: OutStream;


    begin
        ImportInvoiceViewFile(PurchaseHeader);
        PurchaseHeader.CalcFields("Invoice View PDF File ERK");
        if not PurchaseHeader."Invoice View PDF File ERK".HasValue() then
            exit;


        PurchaseHeader."Invoice View PDF File ERK".CreateInStream(FileInStream, TextEncoding::UTF8);
        Base64EncodedFile.AddText(Base64Convert.ToBase64(FileInStream));

        Clear(PurchaseHeader."Invoice View PDF File ERK");


        PurchaseHeader."Invoice View PDF File ERK".CreateOutStream(Base64OutStream, TextEncoding::UTF8);
        Base64EncodedFile.Write(Base64OutStream);


    end;

    procedure ImportInvoiceViewSales(var SalesHeader: Record "Sales Header")
    var
        Base64Convert: Codeunit "Base64 Convert";
        Base64EncodedFile: BigText;
        FileInStream: InStream;
        Base64OutStream: OutStream;


    begin
        ImportInvoiceViewFileSales(SalesHeader);
        SalesHeader.CalcFields("Invoice View PDF File ERK");
        if not SalesHeader."Invoice View PDF File ERK".HasValue() then
            exit;


        SalesHeader."Invoice View PDF File ERK".CreateInStream(FileInStream, TextEncoding::UTF8);
        Base64EncodedFile.AddText(Base64Convert.ToBase64(FileInStream));

        Clear(SalesHeader."Invoice View PDF File ERK");


        SalesHeader."Invoice View PDF File ERK".CreateOutStream(Base64OutStream, TextEncoding::UTF8);
        Base64EncodedFile.Write(Base64OutStream);


    end;

    local procedure ExcludeEBAStatus(var Rec: Record "Purchase Header")
    var
        Vendor: Record Vendor;
        VendorPostingGroup: Record "Vendor Posting Group";
        CodeIsFound: Boolean;
    begin
        if not ErkHoldingSetup.Get() then begin
            Rec.Validate("EBA Status ERK", Rec."EBA Status ERK"::"Waiting for Approval");
            exit;
        end;

        if not Vendor.Get(Rec."Pay-to Vendor No.") then begin
            Rec.Validate("EBA Status ERK", Rec."EBA Status ERK"::"Waiting for Approval");
            exit;
        end;

        if ErkHoldingSetup."Excluded VPG Code for EBA" <> '' then begin

            VendorPostingGroup.Reset();
            VendorPostingGroup.SetFilter(Code, ErkHoldingSetup."Excluded VPG Code for EBA");
            if VendorPostingGroup.FindSet() then
                repeat
                    if Vendor."Vendor Posting Group" = VendorPostingGroup.Code then
                        CodeIsFound := true;
                until (VendorPostingGroup.Next() = 0) or (CodeIsFound);

            if CodeIsFound then
                Rec.Validate("EBA Status ERK", Rec."EBA Status ERK"::" ")
            else
                Rec.Validate("EBA Status ERK", Rec."EBA Status ERK"::"Waiting for Approval");

        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnAfterValidateEvent, "Pay-to Vendor No.", false, false)]
    local procedure PurchaseHeader_OnAfterValidate_PayToVendorNo(var Rec: Record "Purchase Header"; var xRec: Record "Purchase Header")
    begin
        ExcludeEBAStatus(Rec);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnAfterValidateEvent, "Vendor Posting Group", false, false)]
    local procedure PurchaseHeader_OnAfterValidate_VendorPostingGr(var Rec: Record "Purchase Header"; var xRec: Record "Purchase Header")
    begin
        ExcludeEBAStatus(Rec);
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnBeforePostPurchaseDoc, '', false, false)]
    local procedure "Purch.-Post_OnBeforePostPurchaseDoc"(var Sender: Codeunit "Purch.-Post"; var PurchaseHeader: Record "Purchase Header"; PreviewMode: Boolean; CommitIsSupressed: Boolean; var HideProgressWindow: Boolean; var ItemJnlPostLine: Codeunit "Item Jnl.-Post Line"; var IsHandled: Boolean)
    var
        NotApprovedErr: Label 'EBA Status is not approved';
    begin

        if (PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::"Return Order") or
        (PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::"Credit Memo") then
            exit;

        if not PurchaseHeader.Invoice then
            exit;

        if (PurchaseHeader."EBA Status ERK" <> PurchaseHeader."EBA Status ERK"::Approved) and
        (PurchaseHeader."EBA Status ERK" <> PurchaseHeader."EBA Status ERK"::" ") then
            Error(NotApprovedErr);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnAfterValidateEvent, "Your Reference", false, false)]
    local procedure OnAfterValidateEventPurchaseOrder(var Rec: Record "Purchase Header")
    begin
        if Rec.IsTemporary() then
            exit;
        Rec."EBA Your Reference ERK" := Rec."Your Reference";

    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnAfterValidateEvent, "Your Reference", false, false)]
    local procedure OnAfterValidateEventSales(var Rec: Record "Sales Header")
    begin
        if Rec.IsTemporary() then
            exit;
        Rec."EBA Your Reference ERK" := Rec."Your Reference";

    end;


    var
        CompanyInformation: Record "Company Information";
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        EInvoiceSetup: Record "E-Invoice Setup INF";
        EInvoiceIntegrator: Record "E-Document Integrator INF";
        XMLUtilityFunction: Codeunit "XML Utility Function INF";
        EInvoiceManagement: Codeunit "E-Invoice Management INF";
        GlobalError: Text;
        IntegrationCompanyCode: Text[250];
        IntegrationPassword: Text[250];
        IntegrationUserName: Text[250];
}