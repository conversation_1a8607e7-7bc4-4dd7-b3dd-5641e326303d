report 60001 "Packing List ERK"
{
    ApplicationArea = All;
    Caption = 'Packing List';
    UsageCategory = ReportsAndAnalysis;
    RDLCLayout = 'PackingList.rdlc';

    dataset
    {
        dataitem(ContainerHeaderERK; "Container Header ERK")
        {
            CalcFields = "Total Box Quantity", "Total Net Weight (KG)";

            column(ExportNo; "Export No.")
            {
            }
            column(No; "No.")
            {
            }
            column(ConcanatedContainerNo; ExportManagement.ConcanateContainerNosFromExportNo(ContainerHeaderERK."Export No."))
            {
            }
            column(CustomerName; "Customer Name")
            {
            }
            column(TotalBoxQuantity; "Total Box Quantity")
            {
            }
            column(TotalNetWeightKG; "Total Net Weight (KG)")
            {
            }
            column(TotalGrossWeightKG; "Total Gross Weight (KG)")
            {
            }
            dataitem("Export Header ERK"; "Export Header ERK")
            {
                DataItemLink = "No." = field("Export No.");

                column(ConsigneeShiptoAddress_ExportHeaderERK; "Consignee Ship-to Address")
                {
                }
                column(ConsigneeShiptoAddress2_ExportHeaderERK; "Consignee Ship-to Address 2")
                {
                }
                column(ConsigneeShiptoCity_ExportHeaderERK; "Consignee Ship-to City")
                {
                }
                column(ConsigneeShiptoCode_ExportHeaderERK; "Consignee Ship-to Code")
                {
                }
                column(ConsigneeShiptoCountry_ExportHeaderERK; "Consignee Ship-to Country")
                {
                }
                column(ConsigneeShiptoCounty_ExportHeaderERK; "Consignee Ship-to County")
                {
                }
                column(ConsigneeShiptoName_ExportHeaderERK; "Consignee Ship-to Name")
                {
                }
                column(ConsigneeShiptoName2_ExportHeaderERK; "Consignee Ship-to Name 2")
                {
                }
                column(NotifyShiptoAddress_ExportHeaderERK; "Consignee Ship-to Address")
                {
                }
                column(NotifyShiptoAddress2_ExportHeaderERK; "Consignee Ship-to Address 2")
                {
                }
                column(NotifyShiptoCity_ExportHeaderERK; "Consignee Ship-to City")
                {
                }
                column(NotifyShiptoCode_ExportHeaderERK; "Consignee Ship-to Code")
                {
                }
                column(NotifyShiptoCountryRegion_ExportHeaderERK; ErkHoldingBasicFunctions.GetCountryRegionNameInCurrentLanguage("Consignee Ship-to Country"))
                {
                }
                column(NotifyShiptoCounty_ExportHeaderERK; "Consignee Ship-to County")
                {
                }
                column(NotifyShiptoName_ExportHeaderERK; "Consignee Ship-to Name")
                {
                }
                column(NotifyShiptoName2_ExportHeaderERK; "Consignee Ship-to Name 2")
                {
                }
                column(LoadingDate_ExportHeaderERK; "Loading Date")
                {
                }
                column(ExportGrossWeight; ExportManagement.CalculateExportTotalGrossWeight("Export Header ERK"))
                {
                }
            }
            dataitem("Container Line ERK"; "Container Line ERK")
            {
                DataItemLink = "Container No." = field("No."), "Export No." = field("Export No.");

                column(ExportNo_ContainerLineERK; "Export No.")
                {
                }
                column(ContainerNo_ContainerLineERK; "Container No.")
                {
                }
                column(LineNo_ContainerLineERK; "Line No.")
                {
                }
                column(ItemNo_ContainerLineERK; "Item No.")
                {
                }
                column(VariantCode_ContainerLineERK; "Variant Code")
                {
                }
                column(Description_ContainerLineERK; ExportManagement.GetItemTranslation("Item No.", "Variant Code", 'ENU'))
                {
                }
                column(Specification_ContainerLineERK; Specification)
                {
                }
                column(Quantity_ContainerLineERK; Quantity)
                {
                }
                column(UnitofMeasureCode_ContainerLineERK; ErkHoldingBasicFunctions.GetTranslatedUnitOfMeasureDescription("Unit of Measure Code", 'ENU'))
                {
                }
                column(BoxQuantity_ContainerLineERK; "Box Quantity")
                {
                }
                column(PaletteQuantity_ContainerLineERK; "Palette Quantity")
                {
                }
                column(PackageType_ContainerLineERK; "Package Type")
                {
                }
                column(GrossWeightKG_ContainerLineERK; "Gross Weight (KG)")
                {
                }
                column(NetWeightKG_ContainerLineERK; "Net Weight (KG)")
                {
                }
                column(EDocumentPackagingTypeName_ContainerLineERK; ErkHoldingBasicFunctions.GetEDocumentPackageTypeNameFromCode("Packaging Type Code"))
                {
                }
            }
        }
    }
    var
        ExportManagement: Codeunit "Export Management ERK";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
