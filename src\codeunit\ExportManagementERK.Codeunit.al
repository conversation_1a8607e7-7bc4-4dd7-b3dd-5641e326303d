codeunit 60000 "Export Management ERK"
{
    SingleInstance = true;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Blanket Sales Order to Order", OnAfterRun, '', false, false)]
    local procedure OnAfterRun(var SalesHeader: Record "Sales Header"; var SalesOrderHeader: Record "Sales Header")
    begin
        CreateExportHeaderFromBlanketSalesOrder(SalesHeader, SalesOrderHeader);
        CreateFOBCommentLines(SalesHeader, SalesOrderHeader);
    end;

    procedure CreateFOBCommentLines(var SalesHeader: Record "Sales Header"; var SalesOrderHeader: Record "Sales Header")
    var
        SalesCommentLine: Record "Sales Comment Line";
        SalesCommentLineLineNo: Integer;
    begin
        SalesOrderHeader.CalcFields(Amount);
        SalesCommentLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesCommentLine.SetRange("No.", SalesOrderHeader."No.");
        if SalesCommentLine.FindLast() then
            SalesCommentLineLineNo := SalesCommentLine."Line No." + 10000
        else
            SalesCommentLineLineNo := 10000;
        SalesCommentLine.Init();
        SalesCommentLine."Document Type" := SalesOrderHeader."Document Type";
        SalesCommentLine."No." := SalesOrderHeader."No.";
        SalesCommentLine."Line No." := SalesCommentLineLineNo;
        SalesCommentLine.Comment := 'FOB: ' + Format(SalesOrderHeader.Amount - SalesHeader."Freight ERK") + ' ' + SalesHeader."Currency Code";
        SalesCommentLine."Transfer to E-Invoice INF" := true;
        SalesCommentLine.Insert(true);
        SalesHeader."Freight ERK" := 0;
    end;

    local procedure CreateExportHeaderFromBlanketSalesOrder(var SalesHeader: Record "Sales Header"; var SalesOrderHeader: Record "Sales Header")
    var
        ExportHeader: Record "Export Header ERK";
        ConfirmQst: Label 'Do you want to open Export Card?';
        CreationOrder: Integer;
    begin
        ExportHeader.SetCurrentKey("Creation Order");
        ExportHeader.SetRange("Blanket Sales Order No.", SalesHeader."No.");
        if ExportHeader.FindLast() then
            CreationOrder := ExportHeader."Creation Order" + 1
        else
            CreationOrder := 1;
        // if EntryExitPoint.Get(SalesHeader."Exit Point") then begin
        //     PostCode.SetRange(Code, SalesHeader."Exit Point");
        //     CountryofArrival := EntryExitPoint.
        // end;
        GeneralLedgerSetup.GetRecordOnce();
        CreateExportHeader(SalesHeader, SalesOrderHeader, ExportHeader, CreationOrder);
        CreateExportLinesFromSalesOrder(ExportHeader, SalesOrderHeader);
        CreateDimensionValueCodeForExport(ExportHeader);
        SalesOrderHeader.Validate("Shortcut Dimension 1 Code", ExportHeader."No.");
        SalesOrderHeader.Modify(true);
        if not ConfirmManagement.GetResponseOrDefault(ConfirmQst, true) then
            exit;
        Page.Run(Page::"Export Card ERK", ExportHeader);
    end;

    local procedure CreateExportLinesFromSalesOrder(ExportHeader: Record "Export Header ERK"; SalesOrderHeader: Record "Sales Header")
    var
        ExportLine: Record "Export Line ERK";
        SalesLine: Record "Sales Line";
        BlanketSalesLine: Record "Sales Line";
        Item: Record Item;
        ItemVariant: Record "Item Variant";
        // UnitPriceLCY: Decimal;
        NoLinesFoundErr: Label 'No Sales Order Line found.';
    begin
        SalesLine.SetRange("Document Type", SalesOrderHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesOrderHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if not SalesLine.FindSet(false) then
            Error(NoLinesFoundErr);
        repeat
            BlanketSalesLine.Get(BlanketSalesLine."Document Type"::"Blanket Order", SalesLine."Blanket Order No.", SalesLine."Blanket Order Line No.");
            Item.Get(SalesLine."No.");
            ExportLine.Init();
            ExportLine."Document No." := ExportHeader."No.";
            ExportLine."Line No." := SalesLine."Line No.";
            ExportLine.Insert(true);
            ExportLine.Validate("Item No.", SalesLine."No.");
            ExportLine.Validate("Variant Code", SalesLine."Variant Code");
            if SalesLine."Variant Code" <> '' then begin
                ItemVariant.Get(SalesLine."No.", SalesLine."Variant Code");
                ExportLine.Validate(Brand, ItemVariant."Brand ERK");
            end;
            ExportLine.Validate("Item Description", SalesLine.Description);
            ExportLine.Validate(Quantity, BlanketSalesLine.Quantity);
            ExportLine.Validate("Load Quantity", SalesLine.Quantity);
            ExportLine.Validate("Unit of Measure Code", SalesLine."Unit of Measure Code");
            ExportLine.Validate("Unit Price", SalesLine."Unit Price");
            // ExportLine.Validate("Unit Price (LCY)",);
            ExportLine.Validate("Sales Line Amount", SalesLine."Line Amount");
            ExportLine.Validate("Country/Region of Origin Code", Item."Country/Region of Origin Code");
            ExportLine.Validate("Tariff No.", Item."Tariff No.");
            ExportLine.Modify(true);
        until SalesLine.Next() = 0;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPurchRcptLineInsert, '', false, false)]
    local procedure OnAfterPurchRcptLineInsert(PurchaseLine: Record "Purchase Line"; var PurchRcptLine: Record "Purch. Rcpt. Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSupressed: Boolean; PurchInvHeader: Record "Purch. Inv. Header"; var TempTrackingSpecification: Record "Tracking Specification" temporary; PurchRcptHeader: Record "Purch. Rcpt. Header"; TempWhseRcptHeader: Record "Warehouse Receipt Header"; xPurchLine: Record "Purchase Line"; var TempPurchLineGlobal: Record "Purchase Line" temporary)
    begin
        UpdateExportHeaderAndLineAfterPostPurchaseOrder(PurchaseLine);
    end;

    local procedure UpdateExportHeaderAndLineAfterPostPurchaseOrder(PurchaseLine: Record "Purchase Line")
    var
        //ExportHeader: Record "Export Header ERK";
        ExportLine: Record "Export Line ERK";
        PurchaseHeader: Record "Purchase Header";
    //CalculatedUnitCost: Decimal;
    //NoLinesFoundErr: Label 'No Export Line found for Item No.: %1', Comment = '%1="Purchase Line"."No."';
    begin
        if PurchaseLine."Export No. ERK" = '' then
            exit;

        if PurchaseLine."Export Line No. ERK" = 0 then
            exit;

        PurchaseHeader.Get(PurchaseLine."Document Type", PurchaseLine."Document No.");
        ExportLine.Get(PurchaseLine."Export No. ERK", PurchaseLine."Export Line No. ERK");
        ExportLine.Validate("Vendor No.", PurchaseHeader."Buy-from Vendor No.");

        // ExportLine.Validate("Purchase Due Date", PurchaseHeader."Due Date");
        // ExportLine.Validate("Purchase Shipment Method Code", PurchaseHeader."Shipment Method Code");
        // ExportLine.Validate("Purchase Currency Code", PurchaseHeader."Currency Code");
        // ExportLine.Validate("Purchase Receipt No.", PurchRcptLine."Document No.");
        // CalculatedUnitCost := ((ExportLine."Unit Cost" * ExportLine."Quantity Received") + (PurchaseLine."Direct Unit Cost" * PurchRcptLine.Quantity)) / (ExportLine."Quantity Received" + PurchRcptLine.Quantity);
        // ExportLine.Validate("Unit Cost", CalculatedUnitCost);
        // ExportLine.Validate("Quantity Received", ExportLine."Quantity Received" + PurchRcptLine.Quantity);
        // ExportLine.Validate("Purchase Line Amount", ExportLine."Unit Cost" * ExportLine."Quantity Received");
        // ExportLine.Validate("Purchase Date", PurchRcptLine."Posting Date");
        ExportLine.Modify(true);
    end;

    procedure CreateDimensionValueCodeForExport(ExportHeader: Record "Export Header ERK")
    var
        DimensionValue: Record "Dimension Value";
    begin
        ErkHoldingSetup.GetRecordOnce();
        if ErkHoldingSetup."Dimension Code For Export" = '' then
            exit;
        if not DimensionValue.Get(ErkHoldingSetup."Dimension Code For Export", ExportHeader."No.") then begin
            DimensionValue.Init();
            DimensionValue."Dimension Code" := ErkHoldingSetup."Dimension Code For Export";
            DimensionValue.Code := ExportHeader."No.";
            DimensionValue.Insert(true);
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnAfterValidateShortcutDimCode, '', false, false)]
    local procedure OnAfterValidateShortcutDimCode(var PurchaseLine: Record "Purchase Line"; var xPurchaseLine: Record "Purchase Line"; FieldNumber: Integer; var ShortcutDimCode: Code[20])
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;

        if FieldNumber <> 1 then
            exit;

        if ShortcutDimCode <> 'DUMMY' then
            PurchaseLine.Validate("Export No. ERK", ShortcutDimCode);

        if ShortcutDimCode = '' then
            PurchaseLine."Export Line No. ERK" := 0;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterPostSalesDoc, '', false, false)]
    local procedure OnAfterPostSalesDoc(var SalesHeader: Record "Sales Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; SalesShptHdrNo: Code[20]; RetRcpHdrNo: Code[20]; SalesInvHdrNo: Code[20]; SalesCrMemoHdrNo: Code[20]; CommitIsSuppressed: Boolean; InvtPickPutaway: Boolean; var CustLedgerEntry: Record "Cust. Ledger Entry"; WhseShip: Boolean; WhseReceiv: Boolean; PreviewMode: Boolean)
    begin
        PopulateExportPostedInvoiceNoField(SalesHeader, SalesInvHdrNo);
    end;

    local procedure PopulateExportPostedInvoiceNoField(SalesHeader: Record "Sales Header"; SalesInvHdrNo: Code[20])
    var
        ExportHeader: Record "Export Header ERK";
        CurrencyExchangeRate: Record "Currency Exchange Rate";
    begin
        ExportHeader.SetRange("Sales Order No.", SalesHeader."Shortcut Dimension 1 Code");
        if not ExportHeader.FindLast() then
            exit;

        GeneralLedgerSetup.GetRecordOnce();
        ExportHeader.Validate("Posted Sales Invoice No.", SalesInvHdrNo);
        ExportHeader.Validate("Curr. Exchange Rate Date (ACY)", SalesHeader."Posting Date");
        ExportHeader.Validate("Currency Exchange Rate (ACY)", 1 / CurrencyExchangeRate.ExchangeRate(ExportHeader."Curr. Exchange Rate Date (ACY)", GeneralLedgerSetup."Additional Reporting Currency"));
        ExportHeader.Modify(true);
    end;

    procedure CalculateTotalPaidAmountLCY(ExportHeader: Record "Export Header ERK"): Decimal
    var
        CustLedgerEntry: Record "Cust. Ledger Entry";
        ExitValue: Decimal;
    begin
        CustLedgerEntry.SetAutoCalcFields("Amount (LCY)");
        CustLedgerEntry.SetRange("Customer No.", ExportHeader."Customer No.");
        CustLedgerEntry.SetRange("External Document No.", ExportHeader."Blanket Sales Order No.");
        CustLedgerEntry.SetRange("Document Type", CustLedgerEntry."Document Type"::Payment);
        if not CustLedgerEntry.FindSet(false) then
            exit(0);

        repeat
            ExitValue += CustLedgerEntry."Amount (LCY)";
        until CustLedgerEntry.Next() = 0;

        exit(ExitValue * -1);
    end;

    procedure CalculateTotalPaidAmountACY(ExportHeader: Record "Export Header ERK"): Decimal
    var
        GLEntry: Record "G/L Entry";
    begin
        GLEntry.SetRange("Source Type", GLEntry."Source Type"::Customer);
        GLEntry.SetRange("Source No.", ExportHeader."Customer No.");
        GLEntry.SetRange("Bal. Account Type", GLEntry."Bal. Account Type"::Customer);
        GLEntry.SetRange("Document Type", GLEntry."Document Type"::Payment);
        GLEntry.SetRange("External Document No.", ExportHeader."Blanket Sales Order No.");
        GLEntry.CalcSums("Additional-Currency Amount");

        exit(GLEntry."Additional-Currency Amount");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Blanket Sales Order to Order", OnBeforeInsertSalesOrderHeader, '', false, false)]
    local procedure OnBeforeInsertSalesOrderHeader(var SalesOrderHeader: Record "Sales Header"; var BlanketOrderSalesHeader: Record "Sales Header")
    begin
        UpdateSalesOrderNo(SalesOrderHeader, BlanketOrderSalesHeader);
    end;

    local procedure UpdateSalesOrderNo(var SalesOrderHeader: Record "Sales Header"; var BlanketOrderSalesHeader: Record "Sales Header")
    var
        ExportHeader: Record "Export Header ERK";
        SalesHeader: Record "Sales Header";
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        BlanketOrderSalesHeader.TestField("Company Bank Account Code");
        EInvoiceSetup.Get();
        EInvoiceSetup.TestField("E-Invoice Profile for Export");
        if EInvoiceSetup."E-Invoice Profile for Export" = BlanketOrderSalesHeader."E-Invoice Profile ID INF" then
            BlanketOrderSalesHeader.TestField("Freight ERK");
        ExportHeader.SetCurrentKey("Creation Order");
        ExportHeader.SetRange("Blanket Sales Order No.", BlanketOrderSalesHeader."No.");
        if ExportHeader.FindLast() then
            SalesOrderHeader."No." := CopyStr(BlanketOrderSalesHeader."No." + '-' + Format(ExportHeader."Creation Order" + 1), 1, MaxStrLen(SalesOrderHeader."No."))
        else
            SalesOrderHeader."No." := CopyStr(BlanketOrderSalesHeader."No." + '-1', 1, MaxStrLen(SalesOrderHeader."No."));
        if SalesHeader.Get(SalesHeader."Document Type"::Order, SalesOrderHeader."No.") then
            SalesHeader.Delete(true);
    end;

    local procedure PopulateEInvPackagingDetails(var SalesHeader: Record "Sales Header"; var EInvLine: Record "E-Inv. Ln. Goods&Services INF")
    var
        ContainerLine: Record "Container Line ERK";
        EInvLinePackageDetail: Record "E-Inv. Line Package Detail INF";
        EInvLinePackageDetailLineNo: Integer;
    begin
        EInvLinePackageDetailLineNo := 0;
        EInvLine.Reset();
        EInvLine.SetRange("Document Type", EInvLine."Document Type"::"Unposted Sales Invoice");
        EInvLine.SetRange("Document No.", SalesHeader."No.");
        EInvLine.FindSet(false);
        repeat
            ContainerLine.SetRange("Export No.", EInvLine."Document No.");
            ContainerLine.SetRange("Export Line No.", EInvLine."Line No.");
            if ContainerLine.FindSet(false) then
                repeat
                    EInvLinePackageDetail.SetRange("Document Type", EInvLine."Document Type");
                    EInvLinePackageDetail.SetRange("Document No.", EInvLine."Document No.");
                    EInvLinePackageDetail.SetRange("Line No.", EInvLine."Line No.");
                    if EInvLinePackageDetail.FindLast() then
                        EInvLinePackageDetailLineNo := EInvLinePackageDetail."Detail Line No." + 1
                    else
                        EInvLinePackageDetailLineNo := 1;
                    ContainerLine.TestField("Packaging Type Code");
                    EInvLinePackageDetail.Init();
                    EInvLinePackageDetail."Document Type" := EInvLine."Document Type";
                    EInvLinePackageDetail."Document No." := EInvLine."Document No.";
                    EInvLinePackageDetail."Line No." := EInvLine."Line No.";
                    EInvLinePackageDetail."Detail Line No." := EInvLinePackageDetailLineNo;
                    EInvLinePackageDetail."Actual Package ID" := Format(EInvLinePackageDetailLineNo);
                    EInvLinePackageDetail.Quantity := ContainerLine."Box Quantity";
                    EInvLinePackageDetail."Packging Type Code" := ContainerLine."Packaging Type Code";
                    EInvLinePackageDetail.Insert(false);
                until ContainerLine.Next() = 0;
        until EInvLine.Next() = 0;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Blnkt Sales Ord. to Ord. (Y/N)", OnAfterCreateSalesOrder, '', false, false)]
    local procedure OnAfterCreateSalesOrder(var SalesHeader: Record "Sales Header"; var SkipMessage: Boolean)
    begin
        SkipMessage := true;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnBeforeConfirmUpdateAllLineDim, '', false, false)]
    local procedure OnBeforeConfirmUpdateAllLineDim(var SalesHeader: Record "Sales Header"; var xSalesHeader: Record "Sales Header"; NewParentDimSetID: Integer; OldParentDimSetID: Integer; var Confirmed: Boolean; var IsHandled: Boolean)
    begin
        IsHandled := true;
        Confirmed := true;
    end;

    procedure GetBrandFromItemAndVariantCode(ItemNo: Code[20]; VariantCode: Code[10]): Code[10]
    var
        ItemVariant: Record "Item Variant";
    begin
        if not ItemVariant.Get(ItemNo, VariantCode) then
            exit;
        exit(ItemVariant."Brand ERK");
    end;

    procedure GetSpecificationFromItemAndVariantCode(ItemNo: Code[20]; VariantCode: Code[10]): Code[10]
    var
        ItemVariant: Record "Item Variant";
    begin
        if not ItemVariant.Get(ItemNo, VariantCode) then
            exit;
        exit(ItemVariant."Special Feature ERK");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Blnkt Purch Ord. to Ord. (Y/N)", OnAfterCreatePurchOrder, '', false, false)]
    local procedure OnAfterCreatePurchOrder(var PurchaseHeader: Record "Purchase Header"; var SkipMessage: Boolean)
    begin
        SkipMessage := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Blanket Purch. Order to Order", OnAfterRun, '', false, false)]
    local procedure OnAfterRun_BlanketPurchOrdertoOrder(var PurchaseHeader: Record "Purchase Header"; var PurchOrderHeader: Record "Purchase Header")
    begin
        ClearDimensionValuesAndReceivePurchaseOrder(PurchaseHeader, PurchOrderHeader);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnValidatePostingDateOnBeforeCheckNeedUpdateCurrencyFactor, '', false, false)]
    local procedure OnValidatePostingDateOnBeforeCheckNeedUpdateCurrencyFactor(var SalesHeader: Record "Sales Header"; var IsConfirmed: Boolean; var NeedUpdateCurrencyFactor: Boolean; xSalesHeader: Record "Sales Header")
    begin
        IsConfirmed := true;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnBeforeConfirmUpdateCurrencyFactor, '', false, false)]
    local procedure OnBeforeConfirmUpdateCurrencyFactor(var SalesHeader: Record "Sales Header"; var HideValidationDialog: Boolean)
    begin
        HideValidationDialog := true;
    end;

    procedure ConvertAmountToACY(Date: Date; CurrencyCode: Code[10]; Amount: Decimal): Decimal
    var
        CurrencyExchangeRate: Record "Currency Exchange Rate";
    begin
        GeneralLedgerSetup.GetRecordOnce();
        GeneralLedgerSetup.TestField("Additional Reporting Currency");
        case CurrencyCode of
            '':
                exit(CurrencyExchangeRate.ExchangeAmtLCYToFCY(Date, GeneralLedgerSetup."Additional Reporting Currency", Amount, CurrencyExchangeRate.ExchangeRate(Date, GeneralLedgerSetup."Additional Reporting Currency")));
            GeneralLedgerSetup."Additional Reporting Currency":
                exit(Amount);
            else
                exit(CurrencyExchangeRate.ExchangeAmtFCYToFCY(Date, CurrencyCode, GeneralLedgerSetup."Additional Reporting Currency", Amount));
        end;
    end;

    procedure CalculateTotalPurchaseCostACY(ExportHeader: Record "Export Header ERK"): Decimal
    var
        ExportLine: Record "Export Line ERK";
        TotalPurchaseCostACY: Decimal;
    begin
        ExportLine.SetRange("Document No.", ExportHeader."No.");
        ExportLine.FindSet();
        repeat
            TotalPurchaseCostACY += ConvertAmountToACY(ExportLine."Purchase Date", ExportLine."Purchase Currency Code", ExportLine."Purchase Line Amount");
        until ExportLine.Next() = 0;
        exit(TotalPurchaseCostACY);
    end;

    procedure CalculateTotalItemChargeAmountACY(ExportHeader: Record "Export Header ERK"): Decimal
    var
        ValueEntry: Record "Value Entry";
    begin
        ValueEntry.SetRange("Global Dimension 1 Code", ExportHeader."No.");
        ValueEntry.SetFilter("Item Charge No.", '<>%1', '');
        ValueEntry.CalcSums("Cost Amount (Non-Invtbl.)(ACY)");
        exit(-ValueEntry."Cost Amount (Non-Invtbl.)(ACY)");
    end;

    [EventSubscriber(ObjectType::Page, Page::"Get Shipment Lines", OnCreateLinesOnAfterSalesGetShptSetSalesHeader, '', false, false)]
    local procedure OnCreateLinesOnAfterSalesGetShptSetSalesHeader(var Sender: Page "Get Shipment Lines"; var SalesHeader: Record "Sales Header"; var SalesShipmentLine: Record "Sales Shipment Line")
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        SalesHeader.Validate("Shortcut Dimension 1 Code", SalesShipmentLine."Shortcut Dimension 1 Code");
        SalesHeader.Modify(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", OnBeforeCustLedgEntryInsert, '', false, false)]
    local procedure OnBeforeCustLedgEntryInsert(var CustLedgerEntry: Record "Cust. Ledger Entry"; var GenJournalLine: Record "Gen. Journal Line"; GLRegister: Record "G/L Register"; var TempDtldCVLedgEntryBuf: Record "Detailed CV Ledg. Entry Buffer"; var NextEntryNo: Integer)
    var
        ExportLedgerEntry: Record "Export Ledger Entry ERK";
        Customer: Record Customer;
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        if GenJournalLine."Document Type" <> GenJournalLine."Document Type"::Payment then
            exit;
        if not Customer.Get(GenJournalLine."Account No.") then
            exit;
        if GenJournalLine."Journal Template Name" = 'CEKSENET' then
            exit;
        ExportLedgerEntry.Init();
        ExportLedgerEntry.Insert(true);
        ExportLedgerEntry."Posting Date" := GenJournalLine."Posting Date";
        ExportLedgerEntry."Document No." := GenJournalLine."Document No.";
        ExportLedgerEntry."Document Type" := GenJournalLine."Document Type";
        ExportLedgerEntry."Customer No." := GenJournalLine."Account No.";
        ExportLedgerEntry."Customer Name" := Customer.Name;
        ExportLedgerEntry.Amount := GenJournalLine.Amount;
        ExportLedgerEntry."Remaining Amout" := GenJournalLine.Amount;
        ExportLedgerEntry."Source Cust. Ledger Entry No." := CustLedgerEntry."Entry No.";
        ExportLedgerEntry."Currency Code" := GenJournalLine."Currency Code";
        ExportLedgerEntry."Assignable Amount" := GenJournalLine.Amount;
        ExportLedgerEntry."External Document No." := GenJournalLine."External Document No.";
        ExportLedgerEntry."Blanket Sales Order No." := CopyStr(GenJournalLine."External Document No.", 1, MaxStrLen(ExportLedgerEntry."Blanket Sales Order No."));
        if GenJournalLine."Currency Factor" <> 0 then
            ExportLedgerEntry."Currency Exchange Rate" := 1 / GenJournalLine."Currency Factor";
        if GenJournalLine."Shortcut Dimension 1 Code" <> 'DUMMY' then begin
            ExportLedgerEntry.Validate("Amount to Assign", -ExportLedgerEntry."Assignable Amount");
            PostApplicationExportCard(ExportLedgerEntry, GenJournalLine."Shortcut Dimension 1 Code");
        end;
        ExportLedgerEntry.Modify(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Reverse", OnReverseCustLedgEntryOnBeforeInsertCustLedgEntry, '', false, false)]
    local procedure OnReverseCustLedgEntryOnBeforeInsertCustLedgEntry(var NewCustLedgerEntry: Record "Cust. Ledger Entry"; CustLedgerEntry: Record "Cust. Ledger Entry"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    var
        ExportLedgerEntry: Record "Export Ledger Entry ERK";
        ErrorErr: Label 'At least one Export has been assigned for this payment. In order to do this, no assignment must have been made.';
    begin
        if NewCustLedgerEntry.IsTemporary() then
            exit;
        if NewCustLedgerEntry."Document Type" <> NewCustLedgerEntry."Document Type"::Payment then
            exit;
        ExportLedgerEntry.SetRange("Source Cust. Ledger Entry No.", CustLedgerEntry."Entry No.");
        if not ExportLedgerEntry.FindFirst() then
            exit;
        if ExportLedgerEntry."Remaining Amout" <> ExportLedgerEntry.Amount then
            Error(ErrorErr);
        ExportLedgerEntry.Delete(true);
        // CustLedgerEntry.CalcFields(Amount);
        // ExportLedgerEntry.Init();
        // ExportLedgerEntry.Insert(true);
        // ExportLedgerEntry."Posting Date" := NewCustLedgerEntry."Posting Date";
        // ExportLedgerEntry."Document No." := NewCustLedgerEntry."Document No.";
        // ExportLedgerEntry."Document Type" := NewCustLedgerEntry."Document Type";
        // ExportLedgerEntry."Customer No." := NewCustLedgerEntry."Customer No.";
        // ExportLedgerEntry.Amount := -CustLedgerEntry.Amount;
        // ExportLedgerEntry."Remaining Amout" := -CustLedgerEntry.Amount;
        // ExportLedgerEntry."Source Cust. Ledger Entry No." := NewCustLedgerEntry."Entry No.";
        // ExportLedgerEntry."Currency Code" := NewCustLedgerEntry."Currency Code";
        // if NewCustLedgerEntry."Original Currency Factor" <> 0 then
        //     ExportLedgerEntry."Currency Exchange Rate" := 1 / NewCustLedgerEntry."Original Currency Factor";
        // ExportLedgerEntry.Modify(true);
    end;

    procedure PostApplicationExportCard(var ExportLedgerEntry: Record "Export Ledger Entry ERK"; ExportNo: Code[20])
    var
        NewExportLedgerEntry: Record "Export Ledger Entry ERK";
        ExportHeader: Record "Export Header ERK";
        ErrorErr: Label 'The amount to be assigned cannot be greater than the remaining amount of the Export.';
        Error2Err: Label 'The amount to be assigned cannot be greater than the remaining amount of the payment line.';
        Error3Err: Label 'Export and payment currency must be the same.';
        PaymentApplicationSuccesfullMsg: Label 'Payment application succesful.';
    begin
        ExportLedgerEntry.TestField("Amount to Assign");
        ExportLedgerEntry.TestField("Document Type", ExportLedgerEntry."Document Type"::Payment);
        if ExportLedgerEntry."Amount to Assign" > CalculateExportRemainingAmount(ExportNo) then
            Error(ErrorErr);
        if -ExportLedgerEntry."Remaining Amout" < ExportLedgerEntry."Amount to Assign" then
            Error(Error2Err);
        ExportHeader.Get(ExportNo);
        if ExportLedgerEntry."Currency Code" <> ExportHeader."Sales Currency Code" then
            Error(Error3Err);
        ExportHeader.CalcFields("Total Sales Amount", "Customer Name");
        NewExportLedgerEntry.Init();
        NewExportLedgerEntry.Insert(true);
        NewExportLedgerEntry."Posting Date" := ExportHeader."Loading Date";
        NewExportLedgerEntry."Document No." := ExportHeader."No.";
        NewExportLedgerEntry."Document Type" := NewExportLedgerEntry."Document Type"::Invoice;
        NewExportLedgerEntry."Customer No." := ExportHeader."Customer No.";
        NewExportLedgerEntry."Customer Name" := ExportHeader."Customer Name";
        NewExportLedgerEntry.Amount := ExportHeader."Total Sales Amount";
        NewExportLedgerEntry."Remaining Amout" := CalculateExportRemainingAmount(ExportNo) - ExportLedgerEntry."Amount to Assign";
        NewExportLedgerEntry."Assignable Amount" := CalculateExportRemainingAmount(ExportNo) - ExportLedgerEntry."Amount to Assign";
        NewExportLedgerEntry."Assigned Amount" := ExportLedgerEntry."Amount to Assign";
        NewExportLedgerEntry."Currency Code" := ExportHeader."Sales Currency Code";
        NewExportLedgerEntry."Source Export Ledg. Entry No." := ExportLedgerEntry."Entry No.";
        NewExportLedgerEntry."Blanket Sales Order No." := ExportHeader."Blanket Sales Order No.";
        NewExportLedgerEntry.Modify(true);
        ExportLedgerEntry."Assigned Amount" += ExportLedgerEntry."Amount to Assign";
        ExportLedgerEntry."Remaining Amout" += ExportLedgerEntry."Amount to Assign";
        ExportLedgerEntry."Assignable Amount" := ExportLedgerEntry."Remaining Amout";
        ExportLedgerEntry."Amount to Assign" := 0;
        ExportLedgerEntry.Modify(true);
        Message(PaymentApplicationSuccesfullMsg);
    end;

    procedure UnApplyExportCard(var ExportLedgerEntry: Record "Export Ledger Entry ERK")
    var
        PaymentExportLedgerEntry: Record "Export Ledger Entry ERK";
        SuccesMsg: Label 'Unapply process succesful.';
    begin
        ExportLedgerEntry.TestField("Document Type", ExportLedgerEntry."Document Type"::Invoice);
        //PaymentExportLedgerEntry.SetRange("Entry No.", ExportLedgerEntry."Source Export Ledg. Entry No.");
        //PaymentExportLedgerEntry.FindFirst();
        PaymentExportLedgerEntry.Get(ExportLedgerEntry."Source Export Ledg. Entry No.");
        PaymentExportLedgerEntry."Remaining Amout" -= ExportLedgerEntry."Assigned Amount";
        PaymentExportLedgerEntry."Assignable Amount" -= ExportLedgerEntry."Assigned Amount";
        PaymentExportLedgerEntry."Assigned Amount" -= ExportLedgerEntry."Assigned Amount";
        PaymentExportLedgerEntry.Modify(false);
        ExportLedgerEntry.Delete(true);
        Message(SuccesMsg);
    end;

    procedure CalculateExportRemainingAmount(ExportNo: Code[20]): Decimal
    var
        ExportHeader: Record "Export Header ERK";
        ExportLedgerEntry: Record "Export Ledger Entry ERK";
    begin
        ExportHeader.Get(ExportNo);
        ExportHeader.CalcFields("Total Sales Amount");
        ExportLedgerEntry.SetRange("Document No.", ExportHeader."No.");
        ExportLedgerEntry.CalcSums("Assigned Amount");
        exit(ExportHeader."Total Sales Amount" - ExportLedgerEntry."Assigned Amount");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnAfterSetCompanyBankAccount, '', false, false)]
    local procedure OnAfterSetCompanyBankAccount(var SalesHeader: Record "Sales Header"; xSalesHeader: Record "Sales Header")
    var
        Customer: Record Customer;
    begin
        Customer.Get(SalesHeader."Bill-to Customer No.");
        SalesHeader.Validate("Company Bank Account Code", Customer."Company Bank Account Code ERK");
    end;

    procedure DeleteExportCard(SalesHeader: Record "Sales Header")
    var
        ExportHeader: Record "Export Header ERK";
        ExportLedgerEntry: Record "Export Ledger Entry ERK";
        AppliedPaymentsErr: Label 'You need to unapply all payment applications for %1', Comment = '%1 = "Export Header ERK"."No."';
    begin
        // if not CompanyExtensionMapMngt.ExportManagementIsActive() then
        //     exit;
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        if SalesHeader."Document Type" <> SalesHeader."Document Type"::Order then
            exit;
        if not ExportHeader.Get(SalesHeader."No.") then
            exit;
        ExportLedgerEntry.SetRange("Document No.", ExportHeader."No.");
        if not ExportLedgerEntry.IsEmpty() then
            Error(AppliedPaymentsErr, ExportHeader."No.");
        ExportHeader.Delete(true);
    end;

    procedure GetItemTranslation(ItemNo: Code[20]; VariantCode: Code[10]; LanguageCode: Code[10]): Text[100]
    var
        ItemTranslation: Record "Item Translation";
        ItemVariant: Record "Item Variant";
        Item: Record Item;
    begin
        if ItemTranslation.Get(ItemNo, VariantCode, LanguageCode) then
            exit(ItemTranslation.Description);
        if ItemVariant.Get(ItemNo, VariantCode) then
            exit(ItemVariant.Description);
        if Item.Get(ItemNo) then
            exit(Item.Description);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnValidateQtyToReceiveOnAfterCheck, '', false, false)]
    local procedure OnValidateQtyToReceiveOnAfterCheck(var PurchaseLine: Record "Purchase Line"; CallingFieldNo: Integer; var IsHandled: Boolean)
    var
        ExportLine: Record "Export Line ERK";
        ErrorErr: Label 'Blanket Purchase Order Line Qty. to Recevice can not be greater than %1. Its value is %2', Comment = '%1= Outstanding Qty.; %2="Purchase Line"."Qty. to Receive"';
    begin
        if not ExportLine.Get(PurchaseLine."Export No. ERK", PurchaseLine."Export Line No. ERK") then
            exit;
        if (ExportLine."Load Quantity" - ExportLine."Quantity Received") < PurchaseLine."Qty. to Receive" then
            Error(ErrorErr, ExportLine."Load Quantity" - ExportLine."Quantity Received", PurchaseLine."Qty. to Receive");
    end;

    procedure CalculateExportTotalNetWeight(ExportHeader: Record "Export Header ERK"): Decimal
    var
        ContainerHeader: Record "Container Header ERK";
        ReturnValue: Decimal;
    begin
        ContainerHeader.SetAutoCalcFields("Total Net Weight (KG)");
        ContainerHeader.SetRange("Export No.", ExportHeader."No.");
        if not ContainerHeader.FindSet(false) then
            exit(0);
        repeat
            ReturnValue += ContainerHeader."Total Net Weight (KG)";
        until ContainerHeader.Next() = 0;
        exit(ReturnValue);
    end;

    procedure CalculateExportTotalGrossWeight(ExportHeader: Record "Export Header ERK"): Decimal
    var
        ContainerHeader: Record "Container Header ERK";
    begin
        ContainerHeader.SetRange("Export No.", ExportHeader."No.");
        ContainerHeader.CalcSums("Total Gross Weight (KG)");
        exit(ContainerHeader."Total Gross Weight (KG)");
    end;

    procedure CalculateOutstandingQuantityOnExportLine(ExportLine: Record "Export Line ERK"): Decimal
    begin
        exit(ExportLine."Load Quantity" - ExportLine."Quantity Received");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create NAV EInv SlsUnpstd INF", OnAfterCreateEInvoiceLineFromCurrentSalesLine, '', false, false)]
    local procedure OnAfterCreateEInvoiceLinesFromSalesHeader(var SalesHeader: Record "Sales Header"; var SalesLine: Record "Sales Line"; var EInvLine: Record "E-Inv. Ln. Goods&Services INF")
    begin
        PopulateEInvPackagingDetails(SalesHeader, EInvLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create NAV EInv SlsUnpstd INF", OnBeforeCloseEInvoiceNAVDocumentCreation, '', false, false)]
    local procedure "Create NAV EInv SlsUnpstd INF_OnBeforeCloseEInvoiceNAVDocumentCreation"(var EInvoiceHeader: Record "E-Invoice Header INF"; var SalesHeader: Record "Sales Header")
    begin
        InsertEExportComments(EInvoiceHeader, SalesHeader);
        SetEInvoiceNoInExportHeader(SalesHeader);
    end;

    local procedure InsertEExportComments(EInvoiceHeader: Record "E-Invoice Header INF"; SalesHeader: Record "Sales Header")
    var
        EInvoiceHeaderComment: Record "E-Invoice Header Comment INF";
        BankAccount: Record "Bank Account";
        ExportHeader: Record "Export Header ERK";
        ExportLine: Record "Export Line ERK";
        Vendor: Record Vendor;
        ExportNetWeight: Decimal;
        ExportGrossWeight: Decimal;
        EInvoiceHeaderCommentLineNo: Integer;
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        if ExportHeader.Get(SalesHeader."No.") then begin
            ExportNetWeight := CalculateExportTotalNetWeight(ExportHeader);
            ExportGrossWeight := CalculateExportTotalGrossWeight(ExportHeader);
        end;
        ExportLine.SetRange("Document No.", ExportHeader."No.");
        if ExportLine.FindFirst() then
            if Vendor.Get(ExportLine."Vendor No.") then
                Vendor.CalcFields("Tax Area Description ERK");
        EInvoiceHeader.CalcFields("Payable Amount");
        EInvoiceHeaderComment.SetRange("Document Type", EInvoiceHeader."Document Type");
        EInvoiceHeaderComment.SetRange("Document No.", EInvoiceHeader."No.");
        if EInvoiceHeaderComment.FindLast() then
            EInvoiceHeaderCommentLineNo := EInvoiceHeaderComment."Comment Line No." + 1
        else
            EInvoiceHeaderCommentLineNo := 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Sevkiyat Yöntemi: ' + EInvoiceHeader."Delivery Terms";
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Taşıma Yöntemi: ' + SalesHeader."Transport Method";
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Para Birimi: ' + EInvoiceHeader."Document Currency Code" + ' - Döviz Kuru: ' + Format(EInvoiceHeader."Currency Exchange Rate");
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Fatura Tutarı: ' + Format(EInvoiceHeader."Payable Amount");
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        BankAccount.Get(SalesHeader."Company Bank Account Code");
        BankAccount.CalcFields("Branch Name INF", "Bank Name INF");
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Banka: ' + BankAccount."Bank Name INF" + ' Şube: ' + BankAccount."Branch Name INF" + ' SWIFT: ' + BankAccount."SWIFT Code" + ' IBAN: ' + BankAccount.IBAN;
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Kap Adeti: ' + GetTotalPackageCountWithPackageTypeFromExportHeader(ExportHeader);
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Net Ağırlık: ' + Format(ExportNetWeight) + ' KG';
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Brüt Ağırlık: ' + Format(ExportGrossWeight) + ' KG';
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := CopyStr('Alıcı: ' + ExportHeader."Consignee Ship-to Name" + ' ' + ExportHeader."Consignee Ship-to Address" + ' ' + ExportHeader."Consignee Ship-to Address 2" + ' ' + ExportHeader."Consignee Ship-to County" + ' ' + ExportHeader."Consignee Ship-to City" + ' ' + ExportHeader."Consignee Ship-to Country", 1, MaxStrLen(EInvoiceHeaderComment."Comment Text"));
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'NOT: ' + Vendor.Name + ' ' + Vendor."Tax Area Description ERK" + ' ' + Vendor."VAT Registration No.";
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Sipariş Türü: 9';
        EInvoiceHeaderComment.Insert(true);
        EInvoiceHeaderCommentLineNo += 1;
        EInvoiceHeaderComment.Init();
        EInvoiceHeaderComment."Document Type" := EInvoiceHeader."Document Type";
        EInvoiceHeaderComment."Document No." := EInvoiceHeader."No.";
        EInvoiceHeaderComment."Comment Line No." := EInvoiceHeaderCommentLineNo;
        EInvoiceHeaderComment."Comment Text" := 'Gideceği Ülke: ' + ErkHoldingBasicFunctions.GetCountryRegionNameInCurrentLanguage(SalesHeader."Ship-to Country/Region Code");
        EInvoiceHeaderComment.Insert(true);
    end;

    local procedure GetTotalPackageCountWithPackageTypeFromExportHeader(ExportHeader: Record "Export Header ERK"): Text
    var
        ContainerLine: Record "Container Line ERK";
        EDocumentPackagingType: Record "E-Document Packaging Type INF";
        ReturnValueText: Text;
        ReturnValueDecimal: Decimal;
    begin
        ContainerLine.SetRange("Export No.", ExportHeader."No.");
        if not ContainerLine.FindSet(false) then
            exit('');
        if not EDocumentPackagingType.Get(ContainerLine."Packaging Type Code") then
            exit('');
        repeat
            ReturnValueDecimal += ContainerLine."Box Quantity";
        until ContainerLine.Next() = 0;
        ReturnValueText := Format(ReturnValueDecimal) + ' ' + EDocumentPackagingType.Name;
        exit(ReturnValueText);
    end;

    procedure SetEInvoiceNoInExportHeader(SalesHeader: Record "Sales Header")
    var
        ExportHeader: Record "Export Header ERK";
    begin
        if not ExportHeader.Get(SalesHeader."No.") then
            exit;
        ExportHeader.Validate("E-Export No.", CopyStr(SalesHeader."External Document No.", 1, 20));
        ExportHeader.Modify(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::ReportManagement, OnSelectReportLayoutCode, '', false, false)]
    local procedure OnSelectReportLayoutCode(ObjectId: Integer; var LayoutCode: Text; var LayoutType: Option; var IsHandled: Boolean)
    begin
        case ObjectId of
            60002:
                begin
                    LayoutCode := GlobalLayoutCode;
                    LayoutType := 1;
                    IsHandled := true;
                end;
        end;
    end;

    procedure SetGlobalLayoutCode(LayoutCode: Text)
    begin
        GlobalLayoutCode := LayoutCode;
    end;

    procedure ConcanateContainerNosFromExportNo(ExportNo: Code[20]): Text
    var
        ContainerHeaderERK: Record "Container Header ERK";
        ConcanatedContainerNo: Text;
    begin
        ContainerHeaderERK.SetRange("Export No.", ExportNo);
        if ContainerHeaderERK.FindSet() then begin
            repeat
                ConcanatedContainerNo += ContainerHeaderERK."No." + ', ';
            until ContainerHeaderERK.Next() = 0;
            ConcanatedContainerNo := CopyStr(ConcanatedContainerNo, 1, StrLen(ConcanatedContainerNo) - 2);
            exit(ConcanatedContainerNo);
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnBeforeGetCust, '', false, false)]
    local procedure OnBeforeGetCust(var SalesHeader: Record "Sales Header"; var Customer: Record Customer; CustNo: Code[20])
    begin
        EExportCustomerMandatoryFieldChecks(SalesHeader, Customer, CustNo);
    end;

    local procedure EExportCustomerMandatoryFieldChecks(var SalesHeader: Record "Sales Header"; var Customer: Record Customer; CustNo: Code[20])
    var
        ShipmentMethod: Record "Shipment Method";
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        if not (SalesHeader."Document Type" in [SalesHeader."Document Type"::"Blanket Order", SalesHeader."Document Type"::Order, SalesHeader."Document Type"::Invoice]) then
            exit;
        Customer.Get(CustNo);
        EInvoiceSetup.Get();
        EInvoiceSetup.TestField("E-Invoice Profile for Export");
        Customer.TestField("E-Invoice Profile ID INF");
        if Customer."E-Invoice Profile ID INF" <> EInvoiceSetup."E-Invoice Profile for Export" then
            exit;
        // Customer.TestField(Name);
        // Customer.TestField(Address);
        // Customer.TestField("Country/Region Code");
        // Customer.TestField(City);
        // Customer.TestField(County);
        // Customer.TestField("Registration Name INF");
        // Customer.TestField("VAT Registration No.");
        // Customer.TestField("Shipment Method Code");
        // Customer.TestField("Gen. Bus. Posting Group");
        // Customer.TestField("Customer Posting Group");
        //Customer.TestField("Company Bank Account Code ERK");
        ShipmentMethod.Get(Customer."Shipment Method Code");
        ShipmentMethod.TestField("E-Doc. Delivery Term Code INF");
    end;

    local procedure OnAfterAssignItemValuesEExportMandatoryChecks(var Item: Record Item; var SalesHeader: Record "Sales Header")
    var
        VATPostingSetup: Record "VAT Posting Setup";
        TransportMethod: Record "Transport Method";
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        if not (SalesHeader."Document Type" in [SalesHeader."Document Type"::"Blanket Order", SalesHeader."Document Type"::Order, SalesHeader."Document Type"::Invoice]) then
            exit;
        EInvoiceSetup.Get();
        EInvoiceSetup.TestField("VAT Bus.Post.Grp.for Export");
        if SalesHeader."VAT Bus. Posting Group" <> EInvoiceSetup."VAT Bus.Post.Grp.for Export" then
            exit;
        Item.TestField("Tariff No.");
        Item.TestField("Gen. Prod. Posting Group");
        Item.TestField("VAT Prod. Posting Group");
        if Item.Type = Item.Type::Inventory then
            Item.TestField("Inventory Posting Group");
        VATPostingSetup.Get(SalesHeader."VAT Bus. Posting Group", Item."VAT Prod. Posting Group");
        VATPostingSetup.TestField("E-Document Type Code INF");
        VATPostingSetup.TestField("E-Document Exemption Code INF");
        VATPostingSetup.TestField("E-Doc. Exemption Type Code INF");
        SalesHeader.TestField("Transport Method");
        TransportMethod.Get(SalesHeader."Transport Method");
        TransportMethod.TestField("E-Doc. Transport Mode Code INF");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnAfterAssignItemValues, '', false, false)]
    local procedure OnAfterAssignItemValues(var SalesLine: Record "Sales Line"; Item: Record Item; SalesHeader: Record "Sales Header"; var xSalesLine: Record "Sales Line"; CurrentFieldNo: Integer)
    begin
        OnAfterAssignItemValuesEExportMandatoryChecks(Item, SalesHeader);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Reference Management", OnEnterSalesItemReferenceOnAfterFillDescriptionFromItemVariant, '', false, false)]
    local procedure "Item Reference Management_OnEnterSalesItemReferenceOnAfterFillDescriptionFromItemVariant"(var SalesLine: Record "Sales Line"; var ItemVariant: Record "Item Variant")
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        ItemVariant.TestField("Country/Region of Origin ERK");
        SalesLine.Validate("Country/Region Origin INF", ItemVariant."Country/Region of Origin ERK");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnBeforePostPurchaseDoc, '', false, false)]
    local procedure "Purch.-Post_OnBeforePostPurchaseDoc"(var Sender: Codeunit "Purch.-Post"; var PurchaseHeader: Record "Purchase Header"; PreviewMode: Boolean; CommitIsSupressed: Boolean; var HideProgressWindow: Boolean; var ItemJnlPostLine: Codeunit "Item Jnl.-Post Line"; var IsHandled: Boolean)
    begin
        IncomingEInvoiceConfirmation(PurchaseHeader);
    end;

    local procedure IncomingEInvoiceConfirmation(var PurchaseHeader: Record "Purchase Header")
    var
        ProcessAbortedErr: Label 'Process aborted.';
        ConfirmQst: Label 'Incoming E-Invoice No. is empty. Do you want to continue posting process?';
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        if (not PurchaseHeader.Invoice) then
            exit;
        if PurchaseHeader."Incoming E-Invoice No. ERK" = '' then
            if not ConfirmManagement.GetResponseOrDefault(ConfirmQst, false) then
                Error(ProcessAbortedErr);
    end;

    local procedure CreateExportHeader(var SalesHeader: Record "Sales Header"; var SalesOrderHeader: Record "Sales Header"; var ExportHeader: Record "Export Header ERK"; CreationOrder: Integer)
    var
        CurrencyExchangeRate: Record "Currency Exchange Rate";
        BankAccount: Record "Bank Account";
        EntryExitPoint: Record "Entry/Exit Point";
        PostCode: Record "Post Code";
    begin
        SalesHeader.TestField("Company Bank Account Code");
        Clear(ExportHeader);
        ExportHeader.Init();
        ExportHeader."No." := SalesOrderHeader."No.";
        ExportHeader.Insert(true);
        ExportHeader.Validate("Blanket Sales Order No.", SalesHeader."No.");
        ExportHeader.Validate("Blanket Order Date", SalesHeader."Document Date");
        ExportHeader.Validate("Sales Order No.", SalesOrderHeader."No.");
        ExportHeader.Validate("Customer No.", SalesHeader."Sell-to Customer No.");
        ExportHeader.Validate("Sales Due Date", SalesOrderHeader."Due Date");
        ExportHeader.Validate("Sales Currency Code", SalesHeader."Currency Code");
        ExportHeader.Validate("Sales Payment Method Code", SalesOrderHeader."Payment Method Code");
        ExportHeader.Validate("Sales Shipment Method Code", SalesOrderHeader."Shipment Method Code");
        ExportHeader.Validate("Transport Method", SalesOrderHeader."Transport Method");
        ExportHeader.Validate("Creation Order", CreationOrder);
        ExportHeader.Validate("Consignee Ship-to Code", SalesHeader."Ship-to Code");
        ExportHeader.Validate("Port of Departure", SalesHeader."Exit Point");
        ExportHeader.Validate("Port of Arrival", SalesHeader."Port of Arrival ERK");
        ExportHeader.Validate("Notify Ship-to Code", SalesHeader."Notify Ship-to Code ERK");
        ExportHeader.Validate("Curr. Exchange Rate Date (ACY)", ExportHeader."Loading Date");
        ExportHeader.Validate("Sales Payment Terms Code", SalesOrderHeader."Payment Terms Code");
        ExportHeader.Validate("Currency Exchange Rate (ACY)", 1 / CurrencyExchangeRate.ExchangeRate(ExportHeader."Loading Date", GeneralLedgerSetup."Additional Reporting Currency"));
        if BankAccount.Get(SalesHeader."Company Bank Account Code") then begin
            BankAccount.CalcFields("Branch Name INF", "Bank Name INF");
            ExportHeader.Validate("Bank Account Name", BankAccount."Bank Name INF");
            ExportHeader.Validate("Branch Name", BankAccount."Branch Name INF");
            ExportHeader.Validate(IBAN, BankAccount.IBAN);
            ExportHeader.Validate("SWIFT Code", BankAccount."SWIFT Code");
        end;
        if SalesOrderHeader."Notify Ship-to Code ERK" = '' then begin
            ExportHeader."Notify Ship-to Name" := SalesOrderHeader."Ship-to Name";
            ExportHeader."Consignee Ship-to Name" := SalesOrderHeader."Ship-to Name";
            ExportHeader."Notify Ship-to Name 2" := SalesOrderHeader."Ship-to Name 2";
            ExportHeader."Consignee Ship-to Name 2" := SalesOrderHeader."Ship-to Name 2";
            ExportHeader."Notify Ship-to Address" := SalesOrderHeader."Ship-to Address";
            ExportHeader."Consignee Ship-to Address" := SalesOrderHeader."Ship-to Address";
            ExportHeader."Notify Ship-to Address 2" := SalesOrderHeader."Ship-to Address 2";
            ExportHeader."Consignee Ship-to Address 2" := SalesOrderHeader."Ship-to Address 2";
            ExportHeader."Notify Ship-to City" := SalesOrderHeader."Ship-to City";
            ExportHeader."Consignee Ship-to City" := SalesOrderHeader."Ship-to City";
            ExportHeader."Notify Ship-to Country/Region" := SalesOrderHeader."Ship-to Country/Region Code";
            ExportHeader."Consignee Ship-to Country" := SalesOrderHeader."Ship-to Country/Region Code";
            ExportHeader."Notify Ship-to County" := SalesOrderHeader."Ship-to County";
            ExportHeader."Consignee Ship-to County" := SalesOrderHeader."Ship-to County";
        end;
        if EntryExitPoint.Get(SalesOrderHeader."Exit Point") then begin
            PostCode.SetRange(Code, EntryExitPoint."Post Code INF");
            if PostCode.FindFirst() then
                ExportHeader.Validate("Country of Departure", PostCode."Country/Region Code");
        end;
        if EntryExitPoint.Get(SalesOrderHeader."Port of Arrival ERK") then begin
            PostCode.SetRange(Code, EntryExitPoint."Post Code INF");
            if PostCode.FindFirst() then
                ExportHeader.Validate("Country of Arrival", PostCode."Country/Region Code");
        end;
        ExportHeader.Modify(true);
    end;

    local procedure ClearDimensionValuesAndReceivePurchaseOrder(var PurchaseHeader: Record "Purchase Header"; var PurchOrderHeader: Record "Purchase Header")
    var
        PurchaseLine: Record "Purchase Line";
    begin
        if not CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then
            exit;
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.FindSet();
        repeat
            PurchaseLine.Validate("Shortcut Dimension 1 Code", '');
            PurchaseLine."Export Line No. ERK" := 0;
            PurchaseLine.Modify(true);
        until PurchaseLine.Next() = 0;
        PurchOrderHeader.Receive := true;
        Codeunit.Run(Codeunit::"Purch.-Post", PurchOrderHeader);
    end;
    // procedure UnblockChecksForCustomer(Customer: Record Customer)
    // begin
    //     if Customer.Blocked = Customer.Blocked::All then
    //         exit;
    //     Customer.TestField(Name);
    //     Customer.TestField(Address);
    //     Customer.TestField(City);
    //     Customer.TestField("Customer Posting Group");
    //     Customer.TestField("Payment Terms Code");
    //     Customer.TestField("Country/Region Code");
    //     Customer.TestField("VAT Registration No.");
    //     Customer.TestField("Gen. Bus. Posting Group");
    //     Customer.TestField("E-Invoice Profile ID INF");
    //     Customer.TestField("Partner Type");
    //     Customer.TestField("VAT Bus. Posting Group");
    //     Customer.TestField("Tax Area Code");
    //     Customer.TestField("Post Code");
    //     Customer.TestField(County);
    //     Customer.TestField("Salesperson Code");
    //     Customer.TestField(Contact);
    //     Customer.TestField("Phone No.");
    //     Customer.TestField("E-Mail");
    //     //Customer.TestField("Payment Method Code");
    //     // if CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then begin
    //     //     Customer.TestField("Phone No.");
    //     //     Customer.TestField("E-Mail");
    //     //     //Customer.TestField("Language Code");
    //     //     //Customer.TestField("Invoice Disc. Code");
    //     // end;
    // end;
    // procedure UnblockChecksForVendor(Vendor: Record Vendor)
    // var
    //     VendorPostingGroup: Record "Vendor Posting Group";
    // begin
    //     if Vendor.Blocked = Vendor.Blocked::All then
    //         exit;
    //     VendorPostingGroup.Get(Vendor."Vendor Posting Group");
    //     if VendorPostingGroup."Ignore Vendor Field Ctrl ERK" then
    //         exit;
    //     Vendor.TestField(Name);
    //     Vendor.TestField(Address);
    //     Vendor.TestField(City);
    //     Vendor.TestField("Vendor Posting Group");
    //     Vendor.TestField("Payment Terms Code");
    //     Vendor.TestField("Country/Region Code");
    //     Vendor.TestField("VAT Registration No.");
    //     Vendor.TestField("Gen. Bus. Posting Group");
    //     Vendor.TestField("E-Invoice Profile ID INF");
    //     Vendor.TestField("Partner Type");
    //     Vendor.TestField("VAT Bus. Posting Group");
    //     Vendor.TestField("Tax Area Code");
    //     Vendor.TestField("Post Code");
    //     Vendor.TestField(County);
    //     Vendor.TestField("Purchaser Code");
    //     Vendor.TestField(Contact);
    //     Vendor.TestField("Phone No.");
    //     Vendor.TestField("E-Mail");
    //     // if CompanyExtensionMapMngt.IsExportManagementAreaEnabled() then begin
    //     //     // Vendor.TestField("Phone No.");
    //     //     // Vendor.TestField("E-Mail");
    //     //     //Vendor.TestField("Language Code");
    //     //     //Vendor.TestField("Invoice Disc. Code");
    //     // end;
    // end;
    // [EventSubscriber(ObjectType::Table, Database::"Customer", OnAfterValidateEvent, Blocked, true, true)]
    // local procedure OnAfterValidateEvent_Blocked_Customer(var Rec: Record Customer; var xRec: Record Customer; CurrFieldNo: Integer)
    // begin
    //     UnblockChecksForCustomer(Rec);
    // end;
    // [EventSubscriber(ObjectType::Table, Database::"Vendor", OnAfterValidateEvent, Blocked, true, true)]
    // local procedure OnAfterValidateEvent_Blocked_Vendor(var Rec: Record Vendor; var xRec: Record Vendor; CurrFieldNo: Integer)
    // begin
    //     UnblockChecksForVendor(Rec);
    // end;
    procedure BlockCustomer(var Customer: Record Customer)
    begin
        Customer.Validate(Blocked, Customer.Blocked::All);
    end;

    procedure BlockVendor(var Vendor: Record Vendor)
    begin
        Vendor.Validate(Blocked, Vendor.Blocked::All);
    end;

    [EventSubscriber(ObjectType::Table, Database::Customer, OnAfterOnInsert, '', true, true)]
    local procedure OnAfterOnInsert_Customer(var Customer: Record Customer; xCustomer: Record Customer)
    begin
        BlockCustomer(Customer);
    end;

    [EventSubscriber(ObjectType::Table, Database::Vendor, OnAfterOnInsert, '', true, true)]
    local procedure OnAfterOnInsert_Vendor(var Vendor: Record Vendor)
    begin
        BlockVendor(Vendor);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"E-Shipment Check Bef. Post INF", OnAfterSetShipmentType, '', false, false)]
    local procedure OnAfterSetShipmentType(var DocumentHeader: Variant; var ReturnValue: Integer)
    var
        SalesHeader: Record "Sales Header";
        DocumentRecRef: RecordRef;
    begin
        // if PreviewMode = true then
        //     exit;
        DocumentRecRef.GetTable(DocumentHeader);
        if DocumentRecRef.Number() <> 36 then
            exit;
        SalesHeader := DocumentHeader;
        if SalesHeader."E-Invoice Profile ID INF" <> 'IHRACAT' then
            exit;
        SalesHeader.Validate("Shipment Type INF", SalesHeader."Shipment Type INF"::Normal);
        DocumentHeader := SalesHeader;
        ReturnValue := 0;
    end;
    // procedure UpdateMCTInformation()
    // var
    //     ExportHeader: Record "Export Header ERK";
    //     EInvoiceHeader: Record "E-Invoice Header INF";
    // begin
    //     ExportHeader.SetRange("Customs Declaration No.", '');
    //     if not ExportHeader.FindSet(true) then
    //         exit;
    //     repeat
    //         EInvoiceHeader.SetRange("No.", ExportHeader."E-Export No.");
    //         if EInvoiceHeader.FindFirst() then
    //             if EInvoiceHeader."Export Date" <> 0D then begin
    //                 ExportHeader.Validate("Customs Declaration No.", EInvoiceHeader."MCT Registration No.");
    //                 ExportHeader.Validate("MCT Actual Export Date", EInvoiceHeader."Export Date");
    //                 ExportHeader.Modify(true);
    //             end;
    //     until ExportHeader.Next() = 0;
    // end;
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPostPurchLine, '', false, false)]
    local procedure Purch_Post_OnAfterPostPurchLine(var PurchaseHeader: Record "Purchase Header"; var PurchaseLine: Record "Purchase Line"; CommitIsSupressed: Boolean; var PurchInvLine: Record "Purch. Inv. Line"; var PurchCrMemoLine: Record "Purch. Cr. Memo Line"; var PurchInvHeader: Record "Purch. Inv. Header"; var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var PurchLineACY: Record "Purchase Line"; GenJnlLineDocType: Enum "Gen. Journal Document Type"; GenJnlLineDocNo: Code[20]; GenJnlLineExtDocNo: Code[35]; SrcCode: Code[10]; xPurchaseLine: Record "Purchase Line")
    var
        ExportHeader: Record "Export Header ERK";
        ExportLine: Record "Export Line ERK";
        CalculatedUnitCost: Decimal;
    begin
        if PurchaseHeader.Receive and not PurchaseHeader.Invoice then
            exit;
        if PurchaseLine.Type <> PurchaseLine.Type::Item then
            exit;
        if not (ExportHeader.Get(PurchaseLine."Export No. ERK")) or not (ExportHeader.Get(PurchaseLine."Shortcut Dimension 1 Code")) then
            exit;
        if ExportHeader."Vendor Invoice No." = '' then begin
            ExportHeader."Vendor Invoice No." := PurchInvHeader."Vendor Invoice No.";
            ExportHeader.Modify(true);
        end;
        if ExportLine.Get(PurchaseLine."Export No. ERK", PurchaseLine."Export Line No. ERK") then begin
            ExportLine.Validate("Vendor No.", PurchaseHeader."Buy-from Vendor No.");
            ExportLine.Validate("Purchase Due Date", PurchaseHeader."Due Date");
            ExportLine.Validate("Purchase Shipment Method Code", PurchaseHeader."Shipment Method Code");
            ExportLine.Validate("Purchase Currency Code", PurchaseHeader."Currency Code");
            //ExportLine.Validate("Purchase Receipt No.", PurchRcptLine."Document No.");
            CalculatedUnitCost := ((ExportLine."Unit Cost" * ExportLine."Quantity Received") + (PurchaseLine."Direct Unit Cost" * PurchInvLine.Quantity)) / (ExportLine."Quantity Received" + PurchInvLine.Quantity);
            ExportLine.Validate("Unit Cost", CalculatedUnitCost);
            ExportLine.Validate("Quantity Received", ExportLine."Quantity Received" + PurchInvLine.Quantity);
            ExportLine.Validate("Purchase Line Amount", ExportLine."Unit Cost" * ExportLine."Quantity Received");
            ExportLine.Validate("Purchase Date", PurchInvHeader."Posting Date");
            ExportLine.Modify(true);
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnValidateVariantCodeOnAfterChecks, '', false, false)]
    local procedure "Sales Line_OnValidateVariantCodeOnAfterChecks"(var SalesLine: Record "Sales Line"; xSalesLine: Record "Sales Line"; CallingFieldNo: Integer)
    var
        ItemVariant: Record "Item Variant";
    begin
        if not ItemVariant.Get(SalesLine."No.", SalesLine."Variant Code") then
            exit;

        if ItemVariant."Tariff No. ERK" <> '' then
            SalesLine.Validate("Tariff No. INF", ItemVariant."Tariff No. ERK");
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        GeneralLedgerSetup: Record "General Ledger Setup";
        EInvoiceSetup: Record "E-Invoice Setup INF";
        CompanyExtensionMapMngt: Codeunit "Company - Extension Map. Mngt.";
        ConfirmManagement: Codeunit "Confirm Management";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        GlobalLayoutCode: Text;
}
