page 60002 "Export List ERK"
{
    ApplicationArea = ExportManagementERK;
    Caption = 'Export List';
    PageType = List;
    SourceTable = "Export Header ERK";
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Export Card ERK";
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Blanket Sales Order No."; Rec."Blanket Sales Order No.")
                {
                }
                field("Sales Order No."; Rec."Sales Order No.")
                {
                }
                field("E-Export No."; Rec."E-Export No.")
                {
                }
                field("Posted Sales Invoice No."; Rec."Posted Sales Invoice No.")
                {
                }
                field("Actual Export Date FlowField"; Rec."Actual Export Date FlowField")
                {
                }
                field("Customs Declaration No. FF"; Rec."Customs Declaration No. FF")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field("Loading Date"; Rec."Loading Date")
                {
                }
                field("Document Date"; Rec."Document Date")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Vendor No."; Rec."Vendor No.")
                {
                }
                field("Vendor Name"; Rec."Vendor Name")
                {
                }
                field("Vendor Invoice No."; Rec."Vendor Invoice No.")
                {
                }
                field("Blanket Order Date"; Rec."Blanket Order Date")
                {
                }
                field("Sales Due Date"; Rec."Sales Due Date")
                {
                }
                field("Sales Shipment Method Code"; Rec."Sales Shipment Method Code")
                {
                }
                field("Sales Payment Method Code"; Rec."Sales Payment Method Code")
                {
                }
                field("Sales Currency Code"; Rec."Sales Currency Code")
                {
                    ToolTip = 'Specifies the value of the Sales Currency Code field.';
                }
                field("Export Invoice Amount"; Rec."Export Invoice Amount")
                {
                }
                field("Blanket Sales Order Amount"; Rec."Blanket Sales Order Amount")
                {
                }
                field("Total Paid Amount (LCY)"; ExportManagement.CalculateTotalPaidAmountLCY(Rec))
                {
                    Caption = 'Total Paid Amount (LCY)';
                    ToolTip = 'Specifies the value of the Total Paid Amount (LCY) field.';

                    trigger OnDrillDown()
                    var
                        CustLedgerEntry: Record "Cust. Ledger Entry";
                    begin
                        CustLedgerEntry.SetRange("Customer No.", Rec."Customer No.");
                        CustLedgerEntry.SetRange("External Document No.", Rec."Blanket Sales Order No.");
                        CustLedgerEntry.SetRange("Document Type", CustLedgerEntry."Document Type"::Payment);
                        Page.Run(Page::"Customer Ledger Entries", CustLedgerEntry);
                    end;
                }
                field("Total Paid Amount (ACY)"; ExportManagement.CalculateTotalPaidAmountACY(Rec))
                {
                    Caption = 'Total Paid Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Paid Amount (ACY) field.';

                    trigger OnDrillDown()
                    var
                        GLEntry: Record "G/L Entry";
                    begin
                        GLEntry.SetRange("Source Type", GLEntry."Source Type"::Customer);
                        GLEntry.SetRange("Source No.", Rec."Customer No.");
                        GLEntry.SetRange("Bal. Account Type", GLEntry."Bal. Account Type"::Customer);
                        GLEntry.SetRange("Document Type", GLEntry."Document Type"::Payment);
                        GLEntry.SetRange("External Document No.", Rec."Blanket Sales Order No.");
                        Page.Run(Page::"General Ledger Entries", GLEntry);
                    end;
                }
                field("Estimated Time of Delivery"; Rec."Estimated Time of Delivery")
                {
                }
                field("Port of Arrival"; Rec."Port of Arrival")
                {
                }
                field("Port of Arrival Description"; Rec."Port of Arrival Description")
                {
                }
                field("Port of Departure"; Rec."Port of Departure")
                {
                }
                field("Port of Departure Description"; Rec."Port of Departure Description")
                {
                }
                field("Estimated Time of Departure"; Rec."Estimated Time of Departure")
                {
                }
                field("Bill of Lading No."; Rec."Bill of Lading No.")
                {
                }
                field("Consignee Ship-to Name"; Rec."Consignee Ship-to Name")
                {
                }
                field("Payment Amount"; Rec."Payment Amount")
                {
                }
                field(RemainingAmount; ExportManagement.CalculateExportRemainingAmount(Rec."No."))
                {
                    Caption = 'Remaining Amount';
                    ToolTip = 'Specifies the value of the Remaining Amount field.';
                }
                field("Total Assigned Amount"; Rec."Total Assigned Amount")
                {
                }
                field("Bank Account Name"; Rec."Bank Account Name")
                {
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                field("Branch Name"; Rec."Branch Name")
                {
                }
                field("Consignee Ship-to Address"; Rec."Consignee Ship-to Address")
                {
                }
                field("Consignee Ship-to Address 2"; Rec."Consignee Ship-to Address 2")
                {
                }
                field("Consignee Ship-to City"; Rec."Consignee Ship-to City")
                {
                }
                field("Consignee Ship-to Code"; Rec."Consignee Ship-to Code")
                {
                }
                field("Consignee Ship-to Country"; Rec."Consignee Ship-to Country")
                {
                }
                field("Consignee Ship-to County"; Rec."Consignee Ship-to County")
                {
                }
                field("Consignee Ship-to Name 2"; Rec."Consignee Ship-to Name 2")
                {
                }
                field("Container Quantity"; Rec."Container Quantity")
                {
                }
                field("Country of Arrival"; Rec."Country of Arrival")
                {
                }
                field("Country of Departure"; Rec."Country of Departure")
                {
                }
                field("Creation Order"; Rec."Creation Order")
                {
                }
                field("Curr. Exchange Rate Date (ACY)"; Rec."Curr. Exchange Rate Date (ACY)")
                {
                }
                field("Currency Exchange Rate (ACY)"; Rec."Currency Exchange Rate (ACY)")
                {
                }
                // field("MCT Actual Export Date"; Rec."MCT Actual Export Date")
                // {
                //     ToolTip = 'Specifies the value of the MCT Actual Export Date field.';
                // }
                // field("Customs Declaration No."; Rec."Customs Declaration No.")
                // {
                //     ToolTip = 'Specifies the value of the Customs Declaration No. field.';
                // }
                field(IBAN; Rec.IBAN)
                {
                }
                field("Load Description"; Rec."Load Description")
                {
                }
                field("No. Series"; Rec."No. Series")
                {
                }
                field("Notify Ship-to Address"; Rec."Notify Ship-to Address")
                {
                }
                field("Notify Ship-to Address 2"; Rec."Notify Ship-to Address 2")
                {
                }
                field("Notify Ship-to City"; Rec."Notify Ship-to City")
                {
                }
                field("Notify Ship-to Code"; Rec."Notify Ship-to Code")
                {
                }
                field("Notify Ship-to Country/Region"; Rec."Notify Ship-to Country/Region")
                {
                }
                field("Notify Ship-to County"; Rec."Notify Ship-to County")
                {
                }
                field("Notify Ship-to Name"; Rec."Notify Ship-to Name")
                {
                }
                field("Notify Ship-to Name 2"; Rec."Notify Ship-to Name 2")
                {
                }
                field("Sales Payment Terms Code"; Rec."Sales Payment Terms Code")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Shipping Agent"; Rec."Shipping Agent")
                {
                }
                field("SWIFT Code"; Rec."SWIFT Code")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
                field("Total Sales Amount"; Rec."Total Sales Amount")
                {
                    ToolTip = 'Specifies the value of the Total Sales Amount field.';
                }
                field("Transport Method"; Rec."Transport Method")
                {
                }
                field(TotalItemChargeAmountACY; ExportManagement.CalculateTotalItemChargeAmountACY(Rec))
                {
                    Caption = 'Total Item Charge Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Item Charge Amount (ACY) field.';
                }
                field(TotalSalesAmountACY; ExportManagement.ConvertAmountToACY(Rec."Curr. Exchange Rate Date (ACY)", Rec."Sales Currency Code", Rec."Total Sales Amount"))
                {
                    Caption = 'Total Sales Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Sales Amount (ACY) field.';
                }
                field(TotalPurchaseAmountACY; ExportManagement.CalculateTotalPurchaseCostACY(Rec))
                {
                    Caption = 'Total Purchase Amount (ACY);';
                    ToolTip = 'Specifies the value of the Total Purchase Amount (ACY); field.';
                }
            }
        }
    }
    actions
    {
        //     area(Processing)
        //     {
        //         action(UpdateMCTInfos)
        //         {
        //             ApplicationArea = All;
        //             Caption = 'Update MCT Infos';
        //             Image = UpdateXML;
        //             Promoted = true;
        //             PromotedCategory = Process;
        //             PromotedOnly = true;
        //             ToolTip = 'Executes the Update MCT Infos action.';
        //             trigger OnAction()
        //             begin
        //                 ExportManagement.UpdateMCTInformation();
        //             end;
        //         }
        //     }
    }
    var
        ExportManagement: Codeunit "Export Management ERK";
}
