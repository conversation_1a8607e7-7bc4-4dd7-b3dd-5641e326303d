codeunit 60008 "Customs Operation Management"
{
    procedure CreateCustomsOperationLineForDeclaritionType(CustomsOperationHeader: Record "Customs Operation Header ERK")
    var
        CustomsOperationLine: Record "Customs Operation Line ERK";
    begin
        if CustomsOperationHeader.IsTemporary() then
            exit;
        CustomsOperationLine.Init();
        CustomsOperationLine."Document No." := CustomsOperationHeader."No.";
        CustomsOperationLine.Insert(true);
        CustomsOperationLine.Validate("No.", CustomsOperationHeader."Declaration Type");
        CustomsOperationLine.Validate("Variant Code", GetVariantCodeFromCustomsOfficeName(CustomsOperationHeader."Exit Customs"));
        CustomsOperationLine.Validate("Unit Price", GetSalesPrice(CustomsOperationLine."No.", CustomsOperationLine."Variant Code", CustomsOperationHeader."Bill-to Customer No.", CustomsOperationHeader."Registration Date"));
        CustomsOperationLine.Validate(Quantity, 1);
        CustomsOperationLine.Modify(false);
    end;

    local procedure GetVariantCodeFromCustomsOfficeName(CustomsOfficeName: Text): Code[10]
    var
        StartingPos: Integer;
        EndingPos: Integer;
        ReturnValueText: Text;
        ReturnValueCode: Code[10];
    begin
        StartingPos := StrPos(CustomsOfficeName, '-') + 1;
        if StartingPos = 0 then
            exit('');
        EndingPos := StrLen(CustomsOfficeName);
        ReturnValueText := CopyStr(CustomsOfficeName, StartingPos, EndingPos);
        ReturnValueCode := CopyStr(ReturnValueText, 1, MaxStrLen(ReturnValueCode));
        ReturnValueCode := DelChr(ReturnValueCode, '=', ' ');
        exit(ReturnValueCode);
    end;

    procedure CreateStampTaxLine(var CustomsOperationHeader: Record "Customs Operation Header ERK")
    var
        CustomsOperationLine: Record "Customs Operation Line ERK";
    begin
        if CustomsOperationHeader.IsTemporary() then
            exit;
        if CustomsOperationHeader."Stamp Tax Line No." <> 0 then begin
            ErkHoldingSetup.GetRecordOnce();
            ErkHoldingSetup.TestField("Stamp Tax Item No.");
            CustomsOperationLine.Init();
            CustomsOperationLine."Document No." := CustomsOperationHeader."No.";
            CustomsOperationLine.Insert(true);
            CustomsOperationLine.Validate("No.", ErkHoldingSetup."Stamp Tax Item No.");
            CustomsOperationLine.Validate("Unit Price", GetSalesPrice(CustomsOperationLine."No.", CustomsOperationLine."Variant Code", '', CustomsOperationHeader."Registration Date"));
            CustomsOperationLine.Validate(Quantity, 1);
            CustomsOperationLine.Modify(false);
        end;
    end;

    procedure GetSalesPrice(ItemNo: Code[20]; VariantCode: Code[10]; CustomerNo: Code[20]; PriceDate: Date): Decimal
    var
        PriceListLine: Record "Price List Line";
    begin
        PriceListLine.SetRange("Asset No.", ItemNo);
        PriceListLine.SetRange("Variant Code", VariantCode);
        PriceListLine.SetRange("Source No.", CustomerNo);
        PriceListLine.SetFilter("Starting Date", '<=%1', PriceDate);
        PriceListLine.SetFilter("Ending Date", '>=%1', PriceDate);
        if not PriceListLine.FindFirst() then
            exit(0);
        exit(PriceListLine."Unit Price");
    end;

    procedure CreateCustomsOperationFromWorksheet(var TempCustomsOperationHeader: Record "Customs Operation Header ERK")
    var
        CustomsOperationHeader: Record "Customs Operation Header ERK";
        ConfirmQst: Label '%1 Customs Operation will be created. Do you want to continue?', Comment = '%1= TempCustomsOperationHeader';
        ConfirmTxt: Text;
    begin
        TempCustomsOperationHeader.FindSet(true);
        ConfirmTxt := StrSubstNo(ConfirmQst, TempCustomsOperationHeader.Count());
        if not (ConfirmManagement.GetResponseOrDefault(ConfirmTxt, true)) then
            exit;
        repeat //CustomsOperationHeader.TestField("Bill-to Customer No.");
            CustomsOperationHeader.Init();
            CustomsOperationHeader.TransferFields(TempCustomsOperationHeader);
            CustomsOperationHeader.Insert(true);
            CustomsOperationHeader.Validate("Declaration Type");
            CustomsOperationHeader.Validate("Stamp Tax Line No.");
            TempCustomsOperationHeader.Delete(false);
        until TempCustomsOperationHeader.Next() = 0;
    end;

    procedure CalculateLineAmount(CustomsOperationLine: Record "Customs Operation Line ERK"): Decimal
    begin
        exit(CustomsOperationLine.Quantity * CustomsOperationLine."Unit Price");
    end;
    #region Create Sales Invoices
    procedure CreateSalesInvoiceFromSelectedCustomsOperationHeaders(var CustomsOperationHeader: Record "Customs Operation Header ERK")
    var
        SalesHeader: Record "Sales Header";
        xCustomsOperationHeader: Record "Customs Operation Header ERK";
        SuccesMsg: Label 'Sales Invoice No.: %1 has been succesfully created.', Comment = '%1="Sales Header"."No."';
        ERKNo: Integer;
    begin
        CustomsOperationHeader.FindSet(true);
        CustomsOperationHeader.TestField("Bill-to Customer No.");
        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", CustomsOperationHeader."Bill-to Customer No.");
        SalesHeader.Validate("Shortcut Dimension 1 Code", CustomsOperationHeader."Shortcut Dimension 1 Code");
        SalesHeader.Modify(true);
        xCustomsOperationHeader := CustomsOperationHeader;
        ERKNo := 0;
        repeat
            CustomsOperationHeader.TestField("Bill-to Customer No.", xCustomsOperationHeader."Bill-to Customer No.");
            CustomsOperationHeader.TestField("Unposted Sales Invoice No.", '');
            CustomsOperationHeader.TestField("Exit Customs", xCustomsOperationHeader."Exit Customs");
            CustomsOperationHeader.TestField("Customer Representative", xCustomsOperationHeader."Customer Representative");
            CustomsOperationLineLoop(CustomsOperationHeader, SalesHeader, ERKNo);
            ERKNo += 1;
            xCustomsOperationHeader := CustomsOperationHeader;
        until CustomsOperationHeader.Next() = 0;
        ProcessSalesLinesForEInvoiceLineCombining(SalesHeader);
        Message(SuccesMsg, SalesHeader."No.");
    end;

    procedure ProcessSalesLinesForEInvoiceLineCombining(SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        RepresentativeSalesLine: Record "Sales Line";
        RepresantativeLineNo: Integer;
    begin
        RepresentativeSalesLine.SetRange("Document Type", SalesHeader."Document Type");
        RepresentativeSalesLine.SetRange("Document No.", SalesHeader."No.");
        if RepresentativeSalesLine.FindLast() then
            RepresantativeLineNo := RepresentativeSalesLine."Line No." + 10000
        else
            RepresantativeLineNo := 10000;
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if SalesLine.FindSet(true) then
            repeat
                RepresentativeSalesLine.SetRange("Document Type", SalesHeader."Document Type");
                RepresentativeSalesLine.SetRange("Document No.", SalesHeader."No.");
                RepresentativeSalesLine.SetRange(Type, SalesLine.Type::"Representative COI INF");
                RepresentativeSalesLine.SetRange("Invoice Line Combine Type INF", RepresentativeSalesLine."Invoice Line Combine Type INF"::"Representative Line");
                RepresentativeSalesLine.SetRange("No.", SalesLine."No.");
                RepresentativeSalesLine.SetRange("Unit Price", SalesLine."Unit Price");
                if not RepresentativeSalesLine.FindFirst() then begin
                    RepresentativeSalesLine.Init();
                    RepresentativeSalesLine."Document Type" := SalesHeader."Document Type";
                    RepresentativeSalesLine."Document No." := SalesHeader."No.";
                    RepresentativeSalesLine."Line No." := RepresantativeLineNo;
                    RepresentativeSalesLine.Insert(true);
                    RepresentativeSalesLine.Validate(Type, RepresentativeSalesLine.Type::"Representative COI INF");
                    RepresentativeSalesLine.Validate("No.", SalesLine."No.");
                    RepresentativeSalesLine.Validate(Description, SalesLine.Description);
                    RepresentativeSalesLine.Validate("Unit of Measure Code", SalesLine."Unit of Measure Code");
                    RepresentativeSalesLine.Validate("Unit Price", SalesLine."Unit Price");
                    RepresentativeSalesLine.Validate("Invoice Line Combine Type INF", RepresentativeSalesLine."Invoice Line Combine Type INF"::"Representative Line");
                    RepresentativeSalesLine.Modify(true);
                    RepresantativeLineNo += 10000;
                end;
                SalesLine.Validate("Invoice Line Combine Type INF", SalesLine."Invoice Line Combine Type INF"::"Add To Representative Line (Add Quantity and Amount)");
                SalesLine.Validate("Add This Line To Line INF", RepresentativeSalesLine."Line No.");
                SalesLine.Modify(true);
            until SalesLine.Next() = 0;
    end;

    procedure CreateSeperateSalesInvoicesFromCustomsHeader(var CustomsOperationHeader: Record "Customs Operation Header ERK")
    var
        SalesHeader: Record "Sales Header";
        ConfirmQst: Label '%1 Invoices will be created. Do you want to continue?', Comment = '%1=HeaderCount';
        SuccesMsg: Label '%1 Invoices has been created.', Comment = '%1="Customs Operation Header ERK".Count()';
        ConfirmTextQst: Text;
        ERKNo: Integer;
    begin
        ConfirmTextQst := StrSubstNo(ConfirmQst, CustomsOperationHeader.Count());
        if not ConfirmManagement.GetResponseOrDefault(ConfirmTextQst, true) then
            exit;
        CustomsOperationHeader.FindSet(true);
        repeat
            ERKNo := 0;
            CustomsOperationHeader.TestField("Bill-to Customer No.");
            CustomsOperationHeader.TestField("Unposted Sales Invoice No.", '');
            Clear(SalesHeader);
            SalesHeader.Init();
            SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
            SalesHeader.Insert(true);
            SalesHeader.Validate("Sell-to Customer No.", CustomsOperationHeader."Bill-to Customer No.");
            SalesHeader.Validate("Shortcut Dimension 1 Code", CustomsOperationHeader."Shortcut Dimension 1 Code");
            SalesHeader.Modify(true);
            CustomsOperationLineLoop(CustomsOperationHeader, SalesHeader, ERKNo);
        until CustomsOperationHeader.Next() = 0;
        ProcessSalesLinesForEInvoiceLineCombining(SalesHeader);
        Message(SuccesMsg, CustomsOperationHeader.Count());
    end;

    local procedure CreateSalesLineFromCustomsOperationLine(var SalesHeader: Record "Sales Header"; var CustomsOperationLine: Record "Customs Operation Line ERK")
    var
        SalesLine: Record "Sales Line";
        SalesLineLineNo: Integer;
        AmountZeroErrorInfoErr: Label '%1 %2 can not be %3', Comment = '%1="Customs Operation Line ERK".TableCaption; %2=FieldCaption("Line Amount"); %3=Amount';
        AmountZeroErrorInfoText: Text;
    begin
        AmountZeroErrorInfoText := StrSubstNo(AmountZeroErrorInfoErr, CustomsOperationLine.TableCaption(), CustomsOperationLine.FieldCaption("Line Amount"), '0');
        if CustomsOperationLine."Line Amount" = 0 then
            Error(ErrorInfo.Create(AmountZeroErrorInfoText, true));
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        if SalesLine.FindLast() then
            SalesLineLineNo := SalesLine."Line No." + 10000
        else
            SalesLineLineNo := 10000;
        SalesLine.Init();
        SalesLine."Document Type" := SalesHeader."Document Type";
        SalesLine."Document No." := SalesHeader."No.";
        SalesLine."Line No." := SalesLineLineNo;
        SalesLine.Insert(true);
        SalesLine.Validate(Type, SalesLine.Type::Item);
        SalesLine.Validate("No.", CustomsOperationLine."No.");
        SalesLine.Validate("Variant Code", CustomsOperationLine."Variant Code");
        SalesLine.Validate(Quantity, CustomsOperationLine.Quantity);
        SalesLine.Validate("Unit Price", CustomsOperationLine."Unit Price");
        SalesLine.Validate("Invoice Line Combine Type INF", SalesHeader."DefInvoiceLineCombineType INF");
        SalesLine.Modify(true);
    end;

    local procedure CreateSalesComments(CustomsOperationHeader: Record "Customs Operation Header ERK"; SalesHeader: Record "Sales Header"; ERKNo: Integer)
    var
        SalesCommentLine: Record "Sales Comment Line";
        Item: Record Item;
        SalesCommentLineNo: Integer;
        CombinedLineIndicatorNote: Text[80];
        DeclarationTypeName: Text[100];
    begin
        // <cbc:Note>#ERKROW####0 </cbc:Note>
        // <cbc:Note>#ERKNOTE####0 02.01.19</cbc:Note>
        // <cbc:Note>#ERKNOTE####0 19330100IM000156</cbc:Note>
        // <cbc:Note>#ERKNOTE####0 ÖZET BEYAN</cbc:Note>
        // <cbc:Note>#ERKNOTE####0 Mersin Gümrük Müdürlüğü</cbc:Note>
        // <cbc:Note>#ERKNOTE####0 52W</cbc:Note>
        // <cbc:Note>#ERKNOTE####0 YILMAZ ÜĞÜTEN</cbc:Note>
        // <cbc:Note>#ERKNOTE####0 BELSTAR DENİZCİLİK</cbc:Note>
        // <cbc:Note>#ERKNOTE####0 MERSİN LİMANI</cbc:Note>
        // <cbc:Note>#ERKNOTE####0 2690018132</cbc:Note>
        // <cbc:Note>#ERKROW####1 </cbc:Note>
        if not Item.Get(CustomsOperationHeader."Declaration Type") then
            DeclarationTypeName := ''
        else
            DeclarationTypeName := Item.Description;
        SalesCommentLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesCommentLine.SetRange("No.", SalesHeader."No.");
        if SalesCommentLine.FindLast() then
            SalesCommentLineNo := SalesCommentLine."Line No." + 10000
        else
            SalesCommentLineNo := 10000;
        CombinedLineIndicatorNote := ERKNOTETTok + Format(ERKNo);
        InsertSalesCommentLine(ERKROWTok + Format(ERKNo), SalesHeader, SalesCommentLine, SalesCommentLineNo);
        InsertSalesCommentLine(CombinedLineIndicatorNote + Format(CustomsOperationHeader."Registration Date"), SalesHeader, SalesCommentLine, SalesCommentLineNo);
        InsertSalesCommentLine(CopyStr(CombinedLineIndicatorNote + CustomsOperationHeader.MRN, 1, 80), SalesHeader, SalesCommentLine, SalesCommentLineNo);
        InsertSalesCommentLine(CopyStr(CombinedLineIndicatorNote + DeclarationTypeName, 1, 80), SalesHeader, SalesCommentLine, SalesCommentLineNo);
        InsertSalesCommentLine(CopyStr(CombinedLineIndicatorNote + CustomsOperationHeader."Exit Customs", 1, MaxStrLen(SalesCommentLine.Comment)), SalesHeader, SalesCommentLine, SalesCommentLineNo);
        InsertSalesCommentLine(CopyStr(CombinedLineIndicatorNote + CustomsOperationHeader."Truck License Plate", 1, 80), SalesHeader, SalesCommentLine, SalesCommentLineNo);
        InsertSalesCommentLine(CopyStr(CombinedLineIndicatorNote + CustomsOperationHeader."Customer Representative", 1, 80), SalesHeader, SalesCommentLine, SalesCommentLineNo);
        InsertSalesCommentLine(CopyStr(CombinedLineIndicatorNote + CustomsOperationHeader.Sender, 1, 80), SalesHeader, SalesCommentLine, SalesCommentLineNo);
        InsertSalesCommentLine(CopyStr(CombinedLineIndicatorNote + CustomsOperationHeader.Receiver, 1, 80), SalesHeader, SalesCommentLine, SalesCommentLineNo);
    end;

    local procedure InsertSalesCommentLine(CommentText: Text[80]; var SalesHeader: Record "Sales Header"; var SalesCommentLine: Record "Sales Comment Line"; var SalesCommentLineNo: Integer)
    begin
        SalesCommentLine.Init();
        SalesCommentLine."Document Type" := SalesHeader."Document Type";
        SalesCommentLine."No." := SalesHeader."No.";
        SalesCommentLine."Line No." := SalesCommentLineNo;
        SalesCommentLine.Insert(true);
        SalesCommentLine.Validate(Comment, CommentText);
        SalesCommentLine.Validate("Transfer to E-Invoice INF", true);
        SalesCommentLine.Modify(true);
        SalesCommentLineNo += 10000;
    end;

    local procedure CustomsOperationLineLoop(var CustomsOperationHeader: Record "Customs Operation Header ERK"; var SalesHeader: Record "Sales Header"; ERKNo: Integer)
    var
        CustomsOperationLine: Record "Customs Operation Line ERK";
    begin
        CustomsOperationLine.SetRange("Document No.", CustomsOperationHeader."No.");
        if CustomsOperationLine.FindSet(false) then
            repeat
                CreateSalesLineFromCustomsOperationLine(SalesHeader, CustomsOperationLine);
            until CustomsOperationLine.Next() = 0;
        CustomsOperationHeader.Validate("Unposted Sales Invoice No.", SalesHeader."No.");
        CustomsOperationHeader.Modify(true);
        CreateSalesComments(CustomsOperationHeader, SalesHeader, ERKNo);
    end;
    #endregion Create Sales Invoices
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting(var SalesHeader: Record "Sales Header"; var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; var SkipDelete: Boolean; CommitIsSuppressed: Boolean; EverythingInvoiced: Boolean; var TempSalesLineGlobal: Record "Sales Line" temporary)
    var
        SalesLine: Record "Sales Line";
        CustomsOperationHeader: Record "Customs Operation Header ERK";
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if SalesLine.IsEmpty() then
            exit;
        CustomsOperationHeader.SetRange("Unposted Sales Invoice No.", SalesHeader."No.");
        CustomsOperationHeader.ModifyAll("Posted Sales Invoice No.", SalesInvoiceHeader."No.", false);
        CustomsOperationHeader.ModifyAll("E-Invoice No.", SalesInvoiceHeader."External Document No.", false);
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        ConfirmManagement: Codeunit "Confirm Management";
        ERKNOTETTok: Label '#ERKNOTE####', Locked = true;
        ERKROWTok: Label '#ERKROW####', Locked = true;
}
