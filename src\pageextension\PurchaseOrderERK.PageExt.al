pageextension 60028 "Purchase Order ERK" extends "Purchase Order"
{
    layout
    {
        addbefore("Vendor Invoice No.")
        {
            field("Ignore Inc.E-Inv. Controls ERK"; Rec."Ignore Inc.E-Inv. Controls ERK")
            {
                ApplicationArea = All;
            }
            field("Incoming E-Invoice No. ERK"; Rec."Incoming E-Invoice No. ERK")
            {
                ApplicationArea = All;
            }
            field("Invoice Type INF ERK"; Rec."Invoice Type INF ERK")
            {
                ApplicationArea = All;
            }
            field("EBA Status ERK"; Rec."EBA Status ERK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the EBA Status field.';
            }
            field("PDFExistERK ERK"; Rec.GetPDFFileData())
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the PDF Exist field.';
                Caption = 'PDF File';
                Editable = false;


                trigger OnAssistEdit()
                begin
                    Message(Rec.GetPDFFileData());
                end;
            }
        }
        addlast(General)
        {
            field("Requested Receipt Date Text ERK"; Rec."Requested Receipt Text ERK")
            {
                ApplicationArea = All;
            }
        }
    }

    actions
    {
        addafter("O&rder")
        {
            action("ImportInvoicePDFFileERK ERK")
            {
                ApplicationArea = All;
                Caption = 'Import Invoice PDF File';
                Image = Document;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Imports the invoice PDF file.';

                trigger OnAction()
                var
                    EBAIntegrationMngt: Codeunit "EBA Integration Mngt. ERK";
                begin
                    EBAIntegrationMngt.ImportInvoiceView(Rec);
                end;
            }
        }
    }
}
