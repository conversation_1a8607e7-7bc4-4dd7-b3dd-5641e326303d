table 60036 "PDI Line ERK"
{
    Caption = 'PDI Line';
    DataClassification = CustomerContent;
    LookupPageId = "PDI Lines ERK";
    DrillDownPageId = "PDI Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; Code; Code[10])
        {
            Caption = 'Code';
            ToolTip = 'Specifies the value of the Code field.';
        }
        field(4; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(5; Indent; Integer)
        {
            Caption = 'Indent';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Indent field.';
        }
        field(6; Result; Code[20])
        {
            Caption = 'Result';
            TableRelation = "PDI Line Result ERK".Code;
            ToolTip = 'Specifies the value of the Result field.';
        }
        field(7; Comment; Text[250])
        {
            Caption = 'Comment';
            ToolTip = 'Specifies the value of the Comment field.';
        }
        field(8; "Parent Code"; Code[10])
        {
            Caption = 'Parent Code';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Parent Code field.';
        }

        // FlowFields from Header
        field(50; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Serial No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the serial number from the header.';
        }
        field(51; "Operation Starting Date-Time"; DateTime)
        {
            Caption = 'Operation Starting Date-Time';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Operation Starting Date-Time" where("No." = field("Document No.")));
            ToolTip = 'Specifies the operation starting date and time from the header.';
        }
        field(52; "Operation Ending Date-Time"; DateTime)
        {
            Caption = 'Operation Ending Date-Time';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Operation Ending Date-Time" where("No." = field("Document No.")));
            ToolTip = 'Specifies the operation ending date and time from the header.';
        }
        field(53; "Responsible Name"; Code[50])
        {
            Caption = 'Responsible Name';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Responsible Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the responsible name from the header.';
        }
        field(54; "Brand Code"; Code[10])
        {
            Caption = 'Brand Code';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Brand Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the brand code from the header.';
        }
        field(55; "Model Code"; Code[40])
        {
            Caption = 'Model Code';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Model Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the model code from the header.';
        }
        field(56; "Color Name"; Code[100])
        {
            Caption = 'Color Name';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Color Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the color name from the header.';
        }
        field(57; "Fuel Type"; Enum "Fuel Type ERK")
        {
            Caption = 'Fuel Type';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Fuel Type" where("No." = field("Document No.")));
            ToolTip = 'Specifies the fuel type from the header.';
        }
        // field(58; Damaged; Boolean)
        // {
        //     Caption = 'Damaged';
        //     FieldClass = FlowField;
        //     Editable = false;
        //     CalcFormula = lookup("PDI Header ERK".Damaged where("No." = field("Document No.")));
        // }
        field(59; "Header Notes"; Text[250])
        {
            Caption = 'Header Notes';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK".Notes where("No." = field("Document No.")));
            ToolTip = 'Specifies the notes from the header.';
        }
        field(60; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Location Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the location code from the header.';
        }
        field(61; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."To Bin Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the to bin code from the header.';
        }
        field(62; Saved; Boolean)
        {
            Caption = 'Saved';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK".Saved where("No." = field("Document No.")));
            ToolTip = 'Specifies if the header document is saved.';
        }
        field(63; "Saved Atleast Once"; Boolean)
        {
            Caption = 'Saved Atleast Once';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("PDI Header ERK"."Saved Atleast Once" where("No." = field("Document No.")));
            ToolTip = 'Specifies if the header document has been saved at least once.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        PDILine: Record "PDI Line ERK";
    begin
        PDILine.SetRange("Document No.", Rec."Document No.");
        if PDILine.FindLast() then
            Rec."Line No." := PDILine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}
