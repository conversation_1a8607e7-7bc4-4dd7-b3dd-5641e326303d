page 60154 "Dredge Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Dredge Lines';
    PageType = ListPart;
    SourceTable = "Dredge Line ERK";
    AutoSplitKey = true;
    DelayedInsert = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document Line No."; Rec."Document Line No.")
                {
                    ToolTip = 'Specifies the value of the Line No. field.';
                    Visible = false;
                }
                field(Type; Rec.Type)
                {
                }
                field("Source No"; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure"; Rec."Unit of Measure")
                {
                }
                field("Unit Price/Cost"; Rec."Unit Price/Cost")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field(Amount; Rec.Amount)
                {
                }
                field("Amount ACY"; Rec."Amount ACY")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Operation Date"; Rec."Operation Date")
                {
                }
                field("External Document No"; Rec."External Document No")
                {
                }
                field("Unposted Invoice No."; Rec."Unposted Invoice No.")
                {
                    Editable = false;
                    // Visible = false; // Made visible by removing this line or setting to true

                    trigger OnDrillDown()
                    var
                        SalesHeader: Record "Sales Header";
                        PurchaseHeader: Record "Purchase Header";
                        DredgeLineType: Enum "Dredge Line Type ERK";
                    begin
                        Rec.TestField("Unposted Invoice No.");
                        if (Rec.Type = DredgeLineType::Revenue) or (Rec.Type = DredgeLineType::"Expected Revenue") then begin
                            if SalesHeader.Get(SalesHeader."Document Type"::Invoice, Rec."Unposted Invoice No.") then
                                PageManagement.PageRun(SalesHeader)
                        end
                        else
                            if (Rec.Type = DredgeLineType::Expense) or (Rec.Type = DredgeLineType::"Expected Expense") then
                                if PurchaseHeader.Get(PurchaseHeader."Document Type"::Invoice, Rec."Unposted Invoice No.") then
                                    PageManagement.PageRun(PurchaseHeader);
                    end;
                }
                field("Posted Invoice No."; Rec."Posted Invoice No.")
                {
                    Editable = false;
                    // Visible = false; // Made visible by removing this line or setting to true

                    trigger OnDrillDown()
                    var
                        SalesInvoiceHeader: Record "Sales Invoice Header";
                        PurchInvHeader: Record "Purch. Inv. Header";
                        SalesCrMemoHeader: Record "Sales Cr.Memo Header";
                        PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr.";
                        DredgeLineType: Enum "Dredge Line Type ERK";
                    begin
                        Rec.TestField("Posted Invoice No.");
                        if (Rec.Type = DredgeLineType::Revenue) or (Rec.Type = DredgeLineType::"Expected Revenue") then begin
                            if SalesInvoiceHeader.Get(Rec."Posted Invoice No.") then
                                PageManagement.PageRun(SalesInvoiceHeader)
                            else
                                if SalesCrMemoHeader.Get(Rec."Posted Invoice No.") then
                                    PageManagement.PageRun(SalesCrMemoHeader)
                        end
                        else
                            if (Rec.Type = DredgeLineType::Expense) or (Rec.Type = DredgeLineType::"Expected Expense") then
                                if PurchInvHeader.Get(Rec."Posted Invoice No.") then
                                    PageManagement.PageRun(PurchInvHeader)
                                else
                                    if PurchCrMemoHdr.Get(Rec."Posted Invoice No.") then
                                        PageManagement.PageRun(PurchCrMemoHdr);
                    end;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(CreatePuchaseInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Puchase Invoice';
                ToolTip = 'Creates a purchase invoice from the selected dredge lines.';
                Image = Invoice;

                trigger OnAction()
                var
                    DredgeLine: Record "Dredge Line ERK";
                begin
                    if Rec."Posting Date" < 20240101D then
                        Error(DateErr);
                    CurrPage.SetSelectionFilter(DredgeLine);
                    DredgeManagement.CreatePurchaseInvoice(DredgeLine);
                end;
            }
            action(CreateSalesInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Sales Invoice';
                ToolTip = 'Creates a sales invoice from the selected dredge lines.';
                Image = SalesInvoice;

                trigger OnAction()
                var
                    DredgeLine: Record "Dredge Line ERK";
                begin
                    if Rec."Posting Date" < 20240101D then
                        Error(DateErr);
                    CurrPage.SetSelectionFilter(DredgeLine);
                    DredgeManagement.CreateSalesInvoice(DredgeLine);
                end;
            }
        }
    }

    var
        DredgeManagement: Codeunit "Dredge Management ERK";
        PageManagement: Codeunit "Page Management";
        DateErr: Label 'Posting date must be greater than or equal to 01.01.24.';
}
