pageextension 60049 "Posted Purchase Invoices ERK" extends "Posted Purchase Invoices"
{
    layout
    {
        addafter("No.")
        {
            field("Pre-Assigned No. ERK"; Rec."Pre-Assigned No.")
            {
                ToolTip = 'Specifies the value of the Pre-Assigned No. field.';
                ApplicationArea = All;
            }
            field("Currency Factor ERK"; Rec."Currency Factor")
            {
                ToolTip = 'Specifies the value of the Currency Factor field.';
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addafter("Update Document")
        {
            action("UpdateIncomingEInvoices ERK")
            {
                ApplicationArea = All;
                Caption = 'Update Incoming E-Invoices', Comment = 'TRK="YourLanguageCaption"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = UpdateXML;
                ToolTip = 'Executes the Update Incoming E-Invoices action.';

                trigger OnAction()
                var
                    PurchInvHeader: Record "Purch. Inv. Header";
                begin
                    CurrPage.SetSelectionFilter(PurchInvHeader);
                    ErkHoldingBasicFunctions.UpdateIncomingDocumentCreatedFieldOnEInvoiceHeader(PurchInvHeader);
                end;
            }
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
