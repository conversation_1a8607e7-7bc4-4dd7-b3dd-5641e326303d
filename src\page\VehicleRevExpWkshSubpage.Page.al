page 60073 "Vehicle Rev/Exp Wksh. Subpage"
{
    ApplicationArea = All;
    Caption = 'Vehicle Revenue/Expense Wksh. Subpage';
    PageType = ListPart;
    SourceTable = "Vehicle Rev/Exp Worksheet Line";
    SourceTableTemporary = true;
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Unit Price"; Rec."Unit Amount")
                {
                }
                field("Car Carrier No."; Rec."Car Carrier No.")
                {
                }
            }
        }
    }
    procedure SetRecord(var TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line" temporary)
    begin
        Rec.FindSet(false);
        repeat
            TempVehicleRevExpWorksheetLine.Init();
            TempVehicleRevExpWorksheetLine.TransferFields(Rec);
            TempVehicleRevExpWorksheetLine.Insert(false);
        until Rec.Next() = 0;
    end;

    procedure UpdateUnitAmount(var TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line" temporary)
    begin
        TempVehicleRevExpWorksheetLine.FindSet(false);
        repeat
            Rec.Get(TempVehicleRevExpWorksheetLine."Document No.", TempVehicleRevExpWorksheetLine."Line No.");
            Rec."Unit Amount" := TempVehicleRevExpWorksheetLine."Unit Amount";
            Rec.Modify(false);
        until TempVehicleRevExpWorksheetLine.Next() = 0;
    end;

    procedure UpdateCarCarrierNo(var TempVehicleRevExpWorksheetLine: Record "Vehicle Rev/Exp Worksheet Line" temporary)
    begin
        TempVehicleRevExpWorksheetLine.FindSet(false);
        repeat
            Rec.Get(TempVehicleRevExpWorksheetLine."Document No.", TempVehicleRevExpWorksheetLine."Line No.");
            Rec."Car Carrier No." := TempVehicleRevExpWorksheetLine."Car Carrier No.";
            Rec.Modify(false);
        until TempVehicleRevExpWorksheetLine.Next() = 0;
    end;
}
