pageextension 60067 "Warehouse Employees ERK" extends "Warehouse Employees"
{
    layout
    {
        addafter("Location Code")
        {
            field("External Company ERK"; Rec."External Company ERK")
            {
                ApplicationArea = All;
            }
        }
        addlast(Control1)
        {
            field("Multiple Reading Allowed ERK"; Rec."Multiple Reading Allowed ERK")
            {
                ApplicationArea = All;
            }
            field("Discharge ERK"; Rec."Discharge ERK")
            {
                ApplicationArea = All;
            }
            field("Loading ERK"; Rec."Loading ERK")
            {
                ApplicationArea = All;
            }
            field("Addressing ERK"; Rec."Addressing ERK")
            {
                ApplicationArea = All;
            }
            field("Customs Exit ERK"; Rec."Customs Exit ERK")
            {
                ApplicationArea = All;
            }
            field("Transfer ERK"; Rec."Transfer ERK")
            {
                ApplicationArea = All;
            }
            field("PDI Exit ERK"; Rec."PDI Exit ERK")
            {
                ApplicationArea = All;
            }
            field("Nav Exit ERK"; Rec."Nav Exit ERK")
            {
                ApplicationArea = All;
            }
            field("Vehicle Entry ERK"; Rec."Vehicle Entry ERK")
            {
                ApplicationArea = All;
            }
            field("Dealer Dispatch ERK"; Rec."Dealer Dispatch ERK")
            {
                ApplicationArea = All;
            }
            field("PDI Entry ERK"; Rec."PDI Entry ERK")
            {
                ApplicationArea = All;
            }
            field("Damage Exit ERK"; Rec."Damage Exit ERK")
            {
                ApplicationArea = All;
            }
            field("Wash ERK"; Rec."Wash ERK")
            {
                ApplicationArea = All;
            }
            field("Stock Taking ERK"; Rec."Stock Taking ERK")
            {
                ApplicationArea = All;
            }
            field("Grupage Dealer Dispatch ERK"; Rec."Grupage Dealer Dispatch ERK")
            {
                ApplicationArea = All;
            }
            field("Dispatch Preparation ERK"; Rec."Dispatch Preparation ERK")
            {
                ApplicationArea = All;
            }
        }
    }
}