pageextension 60015 "Item Card ERK" extends "Item Card"
{
    layout
    {
        addafter("No.")
        {
            field("No. 2 ERK"; Rec."No. 2")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the No. 2 field.';
            }
        }
        addbefore("Net Weight")
        {
            field("Units per Parcel ERK"; Rec."Units per Parcel")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Units per Parcel field.';
            }
        }
        addlast(content)
        {
            group("ErkPort ERK")
            {
                Caption = 'Erk Port';

                field("Car Carr. Rev/Exp Port Man ERK"; Rec."Car Carr. Rev/Exp Port Man ERK")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
    actions
    {
        addafter("Ledger E&ntries")
        {
            action("RelatedExportLines ERK")
            {
                ApplicationArea = All;
                Caption = 'Related Export Lines';
                Image = ItemLedger;
                RunObject = page "Export Lines ERK";
                RunPageLink = "Item No." = field("No.");
                ToolTip = 'Executes the Related Export Lines action.';
            }
        }
    }
}
