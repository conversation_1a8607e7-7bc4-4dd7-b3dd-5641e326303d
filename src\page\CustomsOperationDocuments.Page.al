page 60054 "Customs Operation Documents"
{
    ApplicationArea = CustomsOperationERK;
    Caption = 'Customs Operation Documents';
    PageType = List;
    SourceTable = "Customs Operation Header ERK";
    UsageCategory = Documents;
    Editable = false;
    CardPageId = "Customs Operation Document ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                {
                }
                field("Bill-To Customer Name"; Rec."Bill-To Customer Name")
                {
                }
                field("Declaration Type"; Rec."Declaration Type")
                {
                }
                field("Regstration Date"; Rec."Registration Date")
                {
                }
                field("Stamp Tax Line No."; Rec."Stamp Tax Line No.")
                {
                }
                field(MRN; Rec.MRN)
                {
                }
                field("Truck License Plate"; Rec."Truck License Plate")
                {
                }
                field("Trailer License Plate"; Rec."Trailer License Plate")
                {
                }
                field("Container No."; Rec."Container No.")
                {
                }
                field("Exit Customs"; Rec."Exit Customs")
                {
                    ToolTip = 'Specifies the value of the Exit Customs field.';
                }
                field(Sender; Rec.Sender)
                {
                }
                field(Receiver; Rec.Receiver)
                {
                }
                field("Customer Representative"; Rec."Customer Representative")
                {
                }
                field("Unposted Sales Invoice No."; Rec."Unposted Sales Invoice No.")
                {
                }
                field("Posted Sales Invoice No."; Rec."Posted Sales Invoice No.")
                {
                }
                field("E-Invoice No."; Rec."E-Invoice No.")
                {
                }
                field("Truck Carnet No."; Rec."Truck Carnet No.")
                {
                }
                field("Total Amount"; Rec."Total Amount")
                {
                }
                field("Invoicing Group Code"; Rec."Invoicing Group Code")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateSalesInvoiceFromSelectedLines)
            {
                ApplicationArea = All;
                Caption = 'Create Sales Invoice From Selected Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = CreateDocument;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Sales Invoice From Selected Lines action.';

                trigger OnAction()
                var
                    CustomsOperationHeader: Record "Customs Operation Header ERK";
                begin
                    CurrPage.SetSelectionFilter(CustomsOperationHeader);
                    CustomsOperationManagement.CreateSalesInvoiceFromSelectedCustomsOperationHeaders(CustomsOperationHeader);
                end;
            }
            action(CreateSeperateSalesInvoiceFromSelectedLines)
            {
                ApplicationArea = All;
                Caption = 'Create Seperate Sales Invoice From Selected Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Documents;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Sales Invoice From Selected Lines action.';

                trigger OnAction()
                var
                    CustomsOperationHeader: Record "Customs Operation Header ERK";
                begin
                    CurrPage.SetSelectionFilter(CustomsOperationHeader);
                    CustomsOperationManagement.CreateSeperateSalesInvoicesFromCustomsHeader(CustomsOperationHeader);
                end;
            }
        }
    }
    var
        CustomsOperationManagement: Codeunit "Customs Operation Management";
}
