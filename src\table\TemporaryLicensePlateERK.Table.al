table 60067 "Temporary License Plate ERK"
{
    Caption = 'Temporary License Plate';
    DataClassification = CustomerContent;
    DrillDownPageId = "Temporary License Plates ERK";
    LookupPageId = "Temporary License Plates ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the unique identifier of the Temporary License Plate record.';
            NotBlank = true;
        }
        field(2; "Voucher No."; Code[20])
        {
            Caption = 'Voucher No.';
            ToolTip = 'Specifies the voucher number of the Temporary License Plate record.';
        }
        field(3; "Start Date"; Date)
        {
            Caption = 'Start Date';
            ToolTip = 'Specifies the date when the Temporary License Plate record starts.';
        }
        field(4; "End Date"; Date)
        {
            Caption = 'End Date';
            ToolTip = 'Specifies the date when the Temporary License Plate record ends.';
        }
        field(5; "Policy No."; Code[20])
        {
            Caption = 'Policy No.';
            ToolTip = 'Specifies the policy number of the Temporary License Plate record.';
        }
        field(6; "Policy Start Date"; Date)
        {
            Caption = 'Policy Start Date';
            ToolTip = 'Specifies the start date of the policy of the Temporary License Plate record.';
        }
        field(7; "Policy End Date"; Date)
        {
            Caption = 'Policy End Date';
            ToolTip = 'Specifies the end date of the policy of the Temporary License Plate record.';
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
}