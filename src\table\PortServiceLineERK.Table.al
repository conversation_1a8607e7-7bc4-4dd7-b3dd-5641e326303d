table 60078 "Port Service Line ERK"
{
    Caption = 'Port Service Line';
    DataClassification = CustomerContent;
    DrillDownPageId = "Port Service Lines ERK";
    LookupPageId = "Port Service Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            TableRelation = "Port Service Header ERK"."No.";
            ToolTip = 'Specifies the document number.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the line number.';
        }
        field(3; Type; Enum "Voyage Line Detail Type ERK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the type of the line.';
        }
        field(4; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = if (Type = const(Expense)) Vendor
            else if (Type = const(Revenue)) Customer;
            ToolTip = 'Specifies the source number.';

            trigger OnValidate()
            var
                Customer: Record Customer;
                Vendor: Record Vendor;
            begin
                case Type of
                    "Voyage Line Detail Type ERK"::Revenue:
                        if Customer.Get("Source No.") then begin
                            Rec."Source Name" := Customer.Name;
                            Rec.Validate("Currency Code", Customer."Currency Code");
                        end;
                    "Voyage Line Detail Type ERK"::Expense:
                        if Vendor.Get("Source No.") then begin
                            Rec."Source Name" := Vendor.Name;
                            Rec.Validate("Currency Code", Vendor."Currency Code");
                        end;
                end;
            end;
        }
        field(5; "Source Name"; Text[100])
        {
            Caption = 'Source Name';
            ToolTip = 'Specifies the source name.';
        }
        field(6; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the item number.';

            trigger OnValidate()
            var
                Item: Record Item;
            begin
                Rec."Item Description" := '';
                Rec."Unit of Measure Code" := '';

                if Item.Get("Item No.") then begin
                    Rec."Item Description" := Item.Description;
                    Rec."Unit of Measure Code" := Item."Base Unit of Measure";
                end;
            end;
        }
        field(7; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the item description.';
        }
        field(8; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DecimalPlaces = 0 : 3;
            ToolTip = 'Specifies the quantity.';

            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", Quantity * "Unit Price");
            end;
        }
        field(9; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            TableRelation = "Item Unit of Measure".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the unit of measure code.';
        }
        field(10; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the currency code.';

            trigger OnValidate()
            var
                CurrExchRate: Record "Currency Exchange Rate";
            begin
                if "Currency Code" <> '' then begin
                    CurrExchRate.FindCurrency("Posting Date", "Currency Code", 1);
                    "Currency Factor" := CurrExchRate."Exchange Rate Amount" / CurrExchRate."Relational Exch. Rate Amount";
                end else
                    "Currency Factor" := 0;

                UpdateLineAmount();
            end;
        }
        field(11; "Unit Price"; Decimal)
        {
            Caption = 'Unit Price';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the unit price.';

            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", Quantity * "Unit Price");
            end;
        }
        field(12; "VAT Product Posting Group"; Code[20])
        {
            Caption = 'VAT Product Posting Group';
            TableRelation = "VAT Product Posting Group";
            ToolTip = 'Specifies the VAT product posting group.';
        }
        field(13; "Line Amount"; Decimal)
        {
            Caption = 'Line Amount';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the line amount.';

            trigger OnValidate()
            begin
                UpdateLineAmountACY();
            end;
        }
        field(14; "Line Amount (ACY)"; Decimal)
        {
            Caption = 'Line Amount (ACY)';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the line amount in additional currency.';
        }
        field(15; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the posting date.';

            trigger OnValidate()
            begin
                UpdateLineAmountACY();
            end;
        }
        field(16; "Invoice Currency Code"; Code[10])
        {
            Caption = 'Invoice Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Invoice Currency Code field.';
        }
        field(17; "Currency Factor"; Decimal)
        {
            Caption = 'Currency Factor';
            DecimalPlaces = 0 : 5;
            AllowInCustomizations = Always;
        }
        field(18; "Invoice No."; Code[20])
        {
            Caption = 'Invoice No.';
            ToolTip = 'Specifies the invoice number.';
        }
        field(19; "Posted Invoice No."; Code[20])
        {
            Caption = 'Posted Invoice No.';
            ToolTip = 'Specifies the posted invoice number.';
        }
        field(20; "External Document No."; Code[35])
        {
            Caption = 'External Document No.';
            ToolTip = 'Specifies the external document number.';
        }
        field(21; "Is Cancelled"; Boolean)
        {
            Caption = 'Is Cancelled';
            ToolTip = 'Specifies whether the line is cancelled.';
        }
    }

    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        PortServiceLine: Record "Port Service Line ERK";
    begin
        PortServiceLine.SetRange("Document No.", Rec."Document No.");
        if PortServiceLine.FindLast() then
            Rec."Line No." := PortServiceLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnModify()
    begin
        TestField("Posted Invoice No.", '');
        TestField("Invoice No.", '');
        TestField("Is Cancelled", false);
    end;

    trigger OnDelete()
    begin
        TestField("Posted Invoice No.", '');
        TestField("Invoice No.", '');
        TestField("Is Cancelled", false);
    end;

    local procedure UpdateLineAmountACY()
    begin
        "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", "Line Amount");
    end;

    local procedure UpdateLineAmount()
    begin
        Validate("Line Amount", Quantity * "Unit Price");
    end;

    var
        ExportManagement: Codeunit "Export Management ERK";
}