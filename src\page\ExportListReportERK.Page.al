page 60086 "Export List Report ERK"
{
    ApplicationArea = All;
    Caption = 'Export List Report';
    PageType = List;
    SourceTable = "Export Header ERK";
    UsageCategory = ReportsAndAnalysis;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Consignee Ship-to Name"; Rec."Consignee Ship-to Name")
                {
                }
                field("Bill of Lading No."; Rec."Bill of Lading No.")
                {
                }
                field("Container Quantity"; Rec."Container Quantity")
                {
                }
                field("Estimated Time of Departure"; Rec."Estimated Time of Departure")
                {
                }
                field("Estimated Time of Delivery"; Rec."Estimated Time of Delivery")
                {
                }
                field("Port of Arrival Description"; Rec."Port of Arrival Description")
                {
                }
                field("E-Export No."; Rec."E-Export No.")
                {
                }
                field("Actual Export Date FlowField"; Rec."Actual Export Date FlowField")
                {
                }
                field("Customs Declaration No. FF"; Rec."Customs Declaration No. FF")
                {
                }
                field("Vendor Name"; Rec."Vendor Name")
                {
                }
                field(TotalSalesAmountACY; ExportManagement.ConvertAmountToACY(Rec."Curr. Exchange Rate Date (ACY)", Rec."Sales Currency Code", Rec."Total Sales Amount"))
                {
                    Caption = 'Total Sales Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Sales Amount (ACY) field.';
                }
                field(TotalItemChargeAmountACY; ExportManagement.CalculateTotalItemChargeAmountACY(Rec))
                {
                    Caption = 'Total Item Charge Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Item Charge Amount (ACY) field.';
                }
                field(TotalPurchaseAmountACY; ExportManagement.CalculateTotalPurchaseCostACY(Rec))
                {
                    Caption = 'Total Purchase Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Purchase Amount (ACY); field.';
                }
                field(TotalProfitLossAmountACY; ExportManagement.ConvertAmountToACY(Rec."Curr. Exchange Rate Date (ACY)", Rec."Sales Currency Code", Rec."Total Sales Amount") - ExportManagement.CalculateTotalItemChargeAmountACY(Rec) - ExportManagement.CalculateTotalPurchaseCostACY(Rec))
                {
                    Caption = 'Total Profit/Loss Amount (ACY)';
                    ToolTip = 'Specifies the value of the Total Profit/Loss Amount (ACY) field.';
                }
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        Rec.CalcFields("Total Sales Amount");
    end;

    var
        ExportManagement: Codeunit "Export Management ERK";
}
