table 60013 "Car Carrier Header ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Car Carrier Header';
    DrillDownPageId = "Car Carrier List ERK";
    LookupPageId = "Car Carrier List ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            Editable = false;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
                NoSeries: Codeunit "No. Series";
                CanNotChangeNoErr: Label 'You can not change Car Carrier No. when invoices created.';
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Car Carrier No. Series");
                    "No. Series" := '';
                end;
                CarCarrRevenueExpense.SetRange("Document No.", xRec."No.");
                CarCarrRevenueExpense.SetFilter("Unposted Invoice No.", '<>%1', '');
                if not CarCarrRevenueExpense.IsEmpty() then
                    Error(CanNotChangeNoErr);
            end;
        }
        field(2; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            TableRelation = "Ship ERK";
            ToolTip = 'Specifies the value of the Ship No. field.';
            trigger OnValidate()
            var
                Ship: Record "Ship ERK";
                ShipTypeDimensionSetup: Record "Ship Type - Dimension Setup";
                StatusEmptyErr: Label 'You can not change Ship No. when status is empty.';
            begin
                if Rec.Status = Rec.Status::" " then
                    Error(StatusEmptyErr);

                ErkHoldingBasicFunctions.ShipMandatoryChecks("Ship No.", false);

                if not Ship.Get("Ship No.") then
                    "Ship Name" := ''
                else
                    "Ship Name" := Ship.Name;

                Rec.CalcFields("Ship Type");
                ShipTypeDimensionSetup.Get(Rec."Ship Type");
                ShipTypeDimensionSetup.TestField("Dimension Value Code");
                Rec.Validate("Shortcut Dimension 1 Code", ShipTypeDimensionSetup."Dimension Value Code");
            end;
        }
        field(3; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(4; "Starting Date-Time"; DateTime)
        {
            Caption = 'Starting Date-Time';
            ToolTip = 'Specifies the value of the Starting Date field.';
            trigger OnValidate()
            var
                BallastCarCarrierHeader: Record "Car Carrier Header ERK";
                BallastVoyageExistErr: Label 'This voyage because has a Ballast Trip: %1. You have to delete it to change Starting Date-Time.', Comment = '%1="Car Carrier Header ERK"."No."';
            begin
                if TypeHelper.CompareDateTime(xRec."Starting Date-Time", Rec."Starting Date-Time") <> 0 then begin
                    BallastCarCarrierHeader.SetRange("Next Car Carrier No.", Rec."No.");
                    if BallastCarCarrierHeader.FindFirst() then
                        Error(BallastVoyageExistErr, BallastCarCarrierHeader."No.");
                end;
            end;
        }
        field(5; "Ending Date-Time"; DateTime)
        {
            Caption = 'Ending Date-Time';
            ToolTip = 'Specifies the value of the Ending Date field.';
            //Editable = false;
        }
        field(6; "Ship Type"; Enum "Ship Type ERK")
        {
            Caption = 'Ship Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Ship ERK"."Ship Type" where("No." = field("Ship No.")));
            ToolTip = 'Specifies the value of the Ship Type field.';
        }
        field(7; "Total Loaded Quantity"; Integer)
        {
            Caption = 'Total Loaded Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Loaded Quantity field.';
        }
        field(9; "Total Discharged Quantity"; Integer)
        {
            Caption = 'Total Discharged Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Document No." = field("No."), "Discharge DateTime" = filter(<> 0DT)));
            ToolTip = 'Specifies the value of the Total Discharged Quantity field.';
        }
        field(8; Status; Enum "Car Carrier Status ERK")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
            trigger OnValidate()
            begin
                OnAfterValidate_Status_CarCarrierHeader();
            end;
        }
        field(10; "Starting Port"; Code[10])
        {
            Caption = 'Starting Port';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Starting Port field.';
            trigger OnValidate()
            var
                EntryExitPoint: Record "Entry/Exit Point";
            begin
                if not EntryExitPoint.Get(Rec."Starting Port") then
                    Rec.Validate("Starting Port Cluster Code", '')
                else begin
                    EntryExitPoint.TestField("Port Cluster Code ERK");
                    Rec.Validate("Starting Port Cluster Code", EntryExitPoint."Port Cluster Code ERK");
                end;
            end;
        }
        field(17; "Starting Port Description"; Text[100])
        {
            Caption = 'Starting Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Starting Port")));
            ToolTip = 'Specifies the value of the Starting Port Description field.';
        }
        field(11; "Ending Port"; Code[10])
        {
            Caption = 'Ending Port';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Starting Port field.';
            trigger OnValidate()
            var
                EntryExitPoint: Record "Entry/Exit Point";
            begin
                if not EntryExitPoint.Get(Rec."Ending Port") then
                    Rec.Validate("Ending Port Cluster Code", '')
                else begin
                    EntryExitPoint.TestField("Port Cluster Code ERK");
                    Rec.Validate("Ending Port Cluster Code", EntryExitPoint."Port Cluster Code ERK");
                end;
            end;
        }
        field(18; "Ending Port Description"; Text[100])
        {
            Caption = 'Ending Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Ending Port")));
            ToolTip = 'Specifies the value of the Ending Port Description field.';
        }
        field(12; "Planned Starting Date"; Date)
        {
            Caption = 'Planned Starting Date';
            ToolTip = 'Specifies the value of the Planned Starting Date field.';
        }
        field(13; "Planned Ending Date"; Date)
        {
            Caption = 'Planned Ending Date';
            ToolTip = 'Specifies the value of the Planned Ending Date field.';
        }
        // field(14; "Total Fuel Consumption (MGO)"; Decimal)
        // {
        //     Caption = 'Total Fuel Consumption (MGO)';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Car Carrier Line ERK"."Fuel Ship Cons. Qty. (MGO)" where("Document No." = field("No.")));
        //     AllowInCustomizations = Never;
        // }
        // field(15; "Fuel Ship Cons. Qty. (IFO)"; Decimal)
        // {
        //     Caption = 'Fuel Consumption Qty. (IFO)';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Car Carrier Line ERK"."Fuel Ship Cons. Qty. (IFO)" where("Document No." = field("No.")));
        //     AllowInCustomizations = Never;
        // }
        field(16; "Shortcut Dimension 1 Code"; Code[20])
        {
            CaptionClass = '1,2,1';
            Caption = 'Shortcut Dimension 1 Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1), Blocked = const(false));
            ToolTip = 'Specifies the value of the Shortcut Dimension 1 Code field.';
        }
        // field(19; "Departure Port Fuel Qty. (IFO)"; Decimal)
        // {
        //     Caption = 'Departure Port Fuel Qty. (IFO)';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Car Carrier Line ERK"."Departure Port Fuel Qty. (IFO)" where("Document No." = field("No.")));
        // }
        // field(20; "Departure Port Fuel Qty. (MGO)"; Decimal)
        // {
        //     Caption = 'Departure Port Fuel Qty. (MGO)';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Car Carrier Line ERK"."Departure Port Fuel Qty. (MGO)" where("Document No." = field("No.")));
        // }
        field(21; "Expected Revenue (ACY)"; Decimal)
        {
            Caption = 'Expected Revenue (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Car Carr. Revenue/Expense ERK"."Line Amount (ACY)" where("Document No." = field("No."), Type = const(Revenue), "Posted Invoice No." = const('')));
            ToolTip = 'Specifies the value of the Expected Revenue (ACY) field.';
        }
        field(22; "Expected Expense (ACY)"; Decimal)
        {
            Caption = 'Expected Expense (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Car Carr. Revenue/Expense ERK"."Line Amount (ACY)" where("Document No." = field("No."), Type = filter(Expense | Consumption), "Posted Invoice No." = const('')));
            ToolTip = 'Specifies the value of the Expected Expense (ACY) field.';
        }
        field(23; "Actual Revenue (ACY)"; Decimal)
        {
            Caption = 'Actual Revenue (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Car Carr. Revenue/Expense ERK"."Line Amount (ACY)" where("Document No." = field("No."), Type = const(Revenue), "Posted Invoice No." = filter(<> '')));
            ToolTip = 'Specifies the value of the Actual Revenue (ACY) field.';
        }
        field(24; "Actual Expense (ACY)"; Decimal)
        {
            Caption = 'Actual Expense (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Car Carr. Revenue/Expense ERK"."Line Amount (ACY)" where("Document No." = field("No."), Type = filter(Expense | Consumption), "Posted Invoice No." = filter(<> '')));
            ToolTip = 'Specifies the value of the Actual Expense (ACY) field.';
        }
        field(25; "Previous Car Carrier No."; Code[20])
        {
            Caption = 'Previous Car Carrier No.';
            ToolTip = 'Previous Car Carrier No. for this voyage.';
        }
        field(26; "Next Car Carrier No."; Code[20])
        {
            Caption = 'Next Car Carrier No.';
            ToolTip = 'Next Car Carrier No. for this voyage.';
        }
        // field(27; "Ballast Created"; Boolean)
        // {
        //     Caption = 'Ballast Created';
        // }
        field(27; "Voyage Duration (Hour)"; Decimal)
        {
            Caption = 'Voyage Duration (Hour)';
            Editable = false;
            ToolTip = 'Specifies the value of the Voyage Duration (Hour) field.';
        }
        field(28; "Starting Fuel (IFO)"; Decimal)
        {
            Caption = 'Starting Fuel (IFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Starting Fuel (IFO) field.';
            trigger OnValidate()
            begin
                Rec.Validate("Fuel Consumption Calculated", false);
            end;
        }
        field(29; "Starting Fuel (MGO)"; Decimal)
        {
            Caption = 'Starting Fuel (MGO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Starting Fuel (MGO) field.';
            trigger OnValidate()
            begin
                Rec.Validate("Fuel Consumption Calculated", false);
            end;
        }
        field(14; "Starting Fuel (HSFO)"; Decimal)
        {
            Caption = 'Starting Fuel (HSFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Starting Fuel (HSFO) field.';
            trigger OnValidate()
            begin
                Rec.Validate("Fuel Consumption Calculated", false);
            end;
        }
        field(108; "Starting Fuel (LNG)"; Decimal)
        {
            Caption = 'Starting Fuel (LNG)';
            MinValue = 0;

            trigger OnValidate()
            begin
                Rec.Validate("Fuel Consumption Calculated", false);
            end;
        }
        field(30; "Fuel Consumption Calculated"; Boolean)
        {
            Caption = 'Fuel Consumption Calculated';
            Editable = false;
            ToolTip = 'Specifies the value of the Fuel Consumption Calculated field.';
        }
        field(31; "Sequence Order No."; Integer)
        {
            Caption = 'Sequence Order No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Sequence Order No. field.';
        }
        field(32; "Voyage Type"; Enum "Car Carrier Voyage Type ERK")
        {
            Caption = 'Voyage Type';
            ToolTip = 'Specifies the value of the Voyage Type field.';
            trigger OnValidate()
            begin
                Rec.TestField("No.", '');
                ErkHoldingSetup.GetRecordOnce();
                ErkHoldingSetup.TestField("Ferry Car Carrier Nos");
                ErkHoldingSetup.TestField("Car Carrier No. Series");
                ErkHoldingSetup.TestField("Ballast No. Series");
                ErkHoldingSetup.TestField("Ferry Ballast No. Series");

                if Rec."Voyage Type" = "Car Carrier Voyage Type ERK"::"Road Transport" then
                    ErkHoldingSetup.TestField("Road Transport Nos");

                case Rec."Voyage Type" of
                    "Car Carrier Voyage Type ERK"::"Ballast - Ferry":
                        begin
                            Rec."No. Series" := ErkHoldingSetup."Ferry Ballast No. Series";
                            if NoSeries.AreRelated(ErkHoldingSetup."Ferry Ballast No. Series", xRec."No. Series") then
                                Rec."No. Series" := xRec."No. Series";
                            Rec."No." := NoSeries.GetNextNo("No. Series");

                            Rec.Validate("Voyage Category", Rec."Voyage Category"::Ferry);
                        end;
                    "Car Carrier Voyage Type ERK"::"Ballast - Ro-Ro":
                        begin
                            Rec."No. Series" := ErkHoldingSetup."Ballast No. Series";
                            if NoSeries.AreRelated(ErkHoldingSetup."Ballast No. Series", xRec."No. Series") then
                                Rec."No. Series" := xRec."No. Series";
                            Rec."No." := NoSeries.GetNextNo("No. Series");
                        end;
                    "Car Carrier Voyage Type ERK"::"Normal - Ferry":
                        begin
                            Rec."No. Series" := ErkHoldingSetup."Ferry Car Carrier Nos";
                            if NoSeries.AreRelated(ErkHoldingSetup."Ferry Car Carrier Nos", xRec."No. Series") then
                                Rec."No. Series" := xRec."No. Series";
                            Rec."No." := NoSeries.GetNextNo("No. Series");

                            Rec.Validate("Voyage Category", Rec."Voyage Category"::Ferry);
                        end;
                    "Car Carrier Voyage Type ERK"::"Normal - Ro-Ro":
                        begin
                            Rec."No. Series" := ErkHoldingSetup."Car Carrier No. Series";
                            if NoSeries.AreRelated(ErkHoldingSetup."Car Carrier No. Series", xRec."No. Series") then
                                Rec."No. Series" := xRec."No. Series";
                            Rec."No." := NoSeries.GetNextNo("No. Series");
                        end;
                    "Car Carrier Voyage Type ERK"::"Road Transport":
                        begin
                            Rec."No. Series" := ErkHoldingSetup."Road Transport Nos";
                            if NoSeries.AreRelated(ErkHoldingSetup."Road Transport Nos", xRec."No. Series") then
                                Rec."No. Series" := xRec."No. Series";
                            Rec."No." := NoSeries.GetNextNo("No. Series");

                            Rec.Validate("Voyage Category", Rec."Voyage Category"::Road);
                        end;
                end;
            end;
        }
        field(15; "Sequence Order Text"; Text[100])
        {
            Caption = 'Sequence Order Text';
            Editable = false;
            ToolTip = 'Specifies the value of the Sequence Order Text field.';
        }
        field(19; "Starting Port Cluster Code"; Code[10])
        {
            Caption = 'Starting Port Cluster Code';
            TableRelation = "Port Cluster ERK".Code;
            Editable = false;
            AllowInCustomizations = Always;

            trigger OnValidate()
            var
                PortCluster: Record "Port Cluster ERK";
            begin
                if PortCluster.Get(Rec."Starting Port Cluster Code") then
                    Rec.Validate("Starting Port Cluster Desc.", PortCluster.Description)
                else
                    Rec."Starting Port Cluster Desc." := '';
            end;
        }
        field(20; "Starting Port Cluster Desc."; Text[100])
        {
            Caption = 'Starting Port Cluster Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Starting Port Cluster Description field.';
        }
        field(33; "Ending Port Cluster Code"; Code[10])
        {
            Caption = 'Ending Port Cluster Code';
            TableRelation = "Port Cluster ERK".Code;
            Editable = false;
            AllowInCustomizations = Always;

            trigger OnValidate()
            var
                PortCluster: Record "Port Cluster ERK";
            begin
                if PortCluster.Get(Rec."Ending Port Cluster Code") then
                    Rec.Validate("Ending Port Cluster Desc.", PortCluster.Description)
                else
                    Rec."Ending Port Cluster Desc." := '';
            end;
        }
        field(34; "Ending Port Cluster Desc."; Text[100])
        {
            Caption = 'Ending Port Cluster Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Ending Port Cluster Description field.';
        }
        field(35; "Voyage No."; Code[20])
        {
            Caption = 'Voyage No.';
            ToolTip = 'Specifies the value of the Voyage No. field.';
        }
        field(36; "Voyage Category"; Enum "Car Carrier Voyage Category")
        {
            Caption = 'Voyage Category';
            ToolTip = 'Specifies the value of the Voyage Category field.';
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
        key(SK; "Ship No.", "Starting Date-Time", Status)
        {
        }
    }
    // trigger OnInsert()
    // var
    //     //ErkHoldingSetup: Record "Erk Holding Setup ERK";
    //     NoSeriesManagement: Codeunit NoSeriesManagement;
    // begin
    //     if "No." = '' then begin
    //         ErkHoldingSetup.Get();
    //         ErkHoldingSetup.TestField("Car Carrier No. Series");
    //         NoSeriesManagement.InitSeries(ErkHoldingSetup."Car Carrier No. Series", xRec."No. Series", 0D, "No.", "No. Series");
    //     end;
    // end;
    // trigger OnModify()
    // var
    //     BallastCarCarrierHeader: Record "Car Carrier Header ERK";
    //     BallastVoyageExistErr: Label 'This voyage because has a Ballast Trip: %1. You have to delete it to change Starting Date-Time.', Comment = '%1="Car Carrier Header ERK"."No."';
    // begin
    //     if xRec."Starting Date-Time" <> Rec."Starting Date-Time" then begin
    //         BallastCarCarrierHeader.SetRange("Next Car Carrier No.", Rec."No.");
    //         if BallastCarCarrierHeader.FindFirst() then
    //             Error(BallastVoyageExistErr, BallastCarCarrierHeader."No.");
    //     end;
    // end;
    trigger OnDelete()
    var
        NextCarCarrierHeader: Record "Car Carrier Header ERK";
        CarCarrierLine: Record "Car Carrier Line ERK";
        CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
        StatusErr: Label 'Completed Car Carrier documents can not be deleted.';
        RevenueExpenseLineExistErr: Label 'Car Carrier document has related revenue/expense line. You need to delete that lines before deleting this car carrier document.';
    begin
        if Rec."Previous Car Carrier No." = '' then
            if Status = Status::Completed then
                Error(StatusErr)
            else
                if Rec."Next Car Carrier No." <> '' then begin
                    NextCarCarrierHeader.Get(Rec."Next Car Carrier No.");
                    NextCarCarrierHeader.TestField(Status, Status::Active);
                end;
        CarCarrRevenueExpense.SetRange("Document No.", Rec."No.");
        if not CarCarrRevenueExpense.IsEmpty() then
            Error(RevenueExpenseLineExistErr);
        CarCarrierLine.SetRange("Document No.", Rec."No.");
        CarCarrierLine.DeleteAll(true);
    end;
    // procedure AssistEdit(OldCarCarrierHeader: Record "Car Carrier Header ERK"): Boolean
    // var
    //     CarCarrierHeader: Record "Car Carrier Header ERK";
    //     NoSeriesManagement: Codeunit NoSeriesManagement;
    // begin
    //     CarCarrierHeader := Rec;
    //     ErkHoldingSetup.Get();
    //     ErkHoldingSetup.TestField("Car Carrier No. Series");
    //     if NoSeriesManagement.SelectSeries(ErkHoldingSetup."Car Carrier No. Series", OldCarCarrierHeader."No. Series", CarCarrierHeader."No. Series") then begin
    //         NoSeriesManagement.SetSeries(CarCarrierHeader."No.");
    //         Rec := CarCarrierHeader;
    //         exit(true);
    //     end;
    // end;
    local procedure OnAfterValidate_Status_CarCarrierHeader()
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
        CarCarrierLine: Record "Car Carrier Line ERK";
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        MultipleActiveShipErr: Label 'Ship No.: %1 has active car carrier document. You need to complete that car carrier document before activating this document.', Comment = '%1="Car Carrier Header ERK"."Ship No."';
        LoadedQtyErr: Label '%1 in Car Carrier Line Details can not be zero.', Comment = '%1=FieldCaption("Loaded Quantity")';
    //EmptyErr: Label '%1 can not be empty or zero.', Comment = '%1=FieldCaption("Departure Date")';
    begin
        case Status of
            Status::Planned:
                begin
                    Rec.TestField("No.");
                    Rec.TestField("Voyage Category");
                end;
            Status::Active:
                begin
                    Rec.TestField("No.");
                    Rec.TestField("Ship No.");

                    CarCarrierHeader.SetRange(Status, Status::Active);
                    CarCarrierHeader.SetRange("Ship No.", Rec."Ship No.");
                    if not CarCarrierHeader.IsEmpty() then
                        Error(MultipleActiveShipErr, Rec."Ship No.");
                end;
            Status::Completed:
                begin
                    xRec.TestField(Status, Status::Active);
                    Rec.TestField("No.");
                    CarCarrierLine.SetRange("Document No.", Rec."No.");
                    CarCarrierLine.FindLast();
                    Rec.Validate("Ending Port", CarCarrierLine."Arrival Port");
                    if (Rec."Voyage Type" = Rec."Voyage Type"::"Normal - Ferry") or (Rec."Voyage Type" = Rec."Voyage Type"::"Normal - Ro-Ro") then begin
                        CarCarrierLineDetail.SetCurrentKey("Discharge End Date-Time");
                        CarCarrierLineDetail.SetRange("Document No.", Rec."No.");
                        CarCarrierLineDetail.FindLast();
                        CarCarrierLineDetail.TestField("Discharge End Date-Time");
                        if TypeHelper.CompareDateTime(CarCarrierHeader."Ending Date-Time", CarCarrierLineDetail."Discharge End Date-Time") < 0 then
                            Rec.Validate("Ending Date-Time", CarCarrierLineDetail."Discharge End Date-Time");

                        CarCarrierLineDetail.SetRange("Loaded Quantity", 0);
                        if not CarCarrierLineDetail.IsEmpty() then
                            Error(LoadedQtyErr, CarCarrierLineDetail.FieldCaption("Loaded Quantity"));
                    end;

                    Rec.TestField("Starting Port");
                    Rec.TestField("Ending Port");
                    Rec.TestField("Starting Date-Time");
                    Rec.TestField("Ending Date-Time");

                    CarCarrierLine.Reset();
                    CarCarrierLine.SetRange("Document No.", Rec."No.");
                    CarCarrierLine.FindSet(false);
                    repeat
                        CarCarrierLine.TestField("Departure Port");
                        CarCarrierLine.TestField("Departure Date-Time");
                        CarCarrierLine.TestField("Arrival Port");
                        CarCarrierLine.TestField("Arrival Date-Time");
                    until CarCarrierLine.Next() = 0;
                end;
        end;
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        TypeHelper: Codeunit "Type Helper";
        NoSeries: Codeunit "No. Series";
}
