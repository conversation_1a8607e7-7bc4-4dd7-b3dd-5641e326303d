﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="BalanceCaption">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>BalanceCaption</Value>
        </Values>
      </DefaultValue>
      <Prompt>BalanceCaption</Prompt>
    </ReportParameter>
    <ReportParameter Name="Name1_CustCaption">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>Name1_CustCaption</Value>
        </Values>
      </DefaultValue>
      <Prompt>Name1_CustCaption</Prompt>
    </ReportParameter>
    <ReportParameter Name="No_CustCaption">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>No_CustCaption</Value>
        </Values>
      </DefaultValue>
      <Prompt>No_CustCaption</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="CompanyName">
          <DataField>CompanyName</DataField>
        </Field>
        <Field Name="FormatEndingDate">
          <DataField>FormatEndingDate</DataField>
        </Field>
        <Field Name="PostingDate">
          <DataField>PostingDate</DataField>
        </Field>
        <Field Name="PrintAmountInLCY">
          <DataField>PrintAmountInLCY</DataField>
        </Field>
        <Field Name="TableCaptnCustFilter">
          <DataField>TableCaptnCustFilter</DataField>
        </Field>
        <Field Name="CustFilter">
          <DataField>CustFilter</DataField>
        </Field>
        <Field Name="AgingByDueDate">
          <DataField>AgingByDueDate</DataField>
        </Field>
        <Field Name="AgedbyDocumnetDate">
          <DataField>AgedbyDocumnetDate</DataField>
        </Field>
        <Field Name="HeaderText5">
          <DataField>HeaderText5</DataField>
        </Field>
        <Field Name="HeaderText4">
          <DataField>HeaderText4</DataField>
        </Field>
        <Field Name="HeaderText3">
          <DataField>HeaderText3</DataField>
        </Field>
        <Field Name="HeaderText2">
          <DataField>HeaderText2</DataField>
        </Field>
        <Field Name="HeaderText1">
          <DataField>HeaderText1</DataField>
        </Field>
        <Field Name="PrintDetails">
          <DataField>PrintDetails</DataField>
        </Field>
        <Field Name="AgedAccReceivableCptn">
          <DataField>AgedAccReceivableCptn</DataField>
        </Field>
        <Field Name="CurrReportPageNoCptn">
          <DataField>CurrReportPageNoCptn</DataField>
        </Field>
        <Field Name="AllAmtinLCYCptn">
          <DataField>AllAmtinLCYCptn</DataField>
        </Field>
        <Field Name="AgedOverdueAmtCptn">
          <DataField>AgedOverdueAmtCptn</DataField>
        </Field>
        <Field Name="CLEEndDateAmtLCYCptn">
          <DataField>CLEEndDateAmtLCYCptn</DataField>
        </Field>
        <Field Name="CLEEndDateDueDateCptn">
          <DataField>CLEEndDateDueDateCptn</DataField>
        </Field>
        <Field Name="CLEEndDateDocNoCptn">
          <DataField>CLEEndDateDocNoCptn</DataField>
        </Field>
        <Field Name="CLEEndDatePstngDateCptn">
          <DataField>CLEEndDatePstngDateCptn</DataField>
        </Field>
        <Field Name="CLEEndDateDocTypeCptn">
          <DataField>CLEEndDateDocTypeCptn</DataField>
        </Field>
        <Field Name="OriginalAmtCptn">
          <DataField>OriginalAmtCptn</DataField>
        </Field>
        <Field Name="TotalLCYCptn">
          <DataField>TotalLCYCptn</DataField>
        </Field>
        <Field Name="NewPagePercustomer">
          <DataField>NewPagePercustomer</DataField>
        </Field>
        <Field Name="GrandTotalCLE5RemAmt">
          <DataField>GrandTotalCLE5RemAmt</DataField>
        </Field>
        <Field Name="GrandTotalCLE5RemAmtFormat">
          <DataField>GrandTotalCLE5RemAmtFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE4RemAmt">
          <DataField>GrandTotalCLE4RemAmt</DataField>
        </Field>
        <Field Name="GrandTotalCLE4RemAmtFormat">
          <DataField>GrandTotalCLE4RemAmtFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE3RemAmt">
          <DataField>GrandTotalCLE3RemAmt</DataField>
        </Field>
        <Field Name="GrandTotalCLE3RemAmtFormat">
          <DataField>GrandTotalCLE3RemAmtFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE2RemAmt">
          <DataField>GrandTotalCLE2RemAmt</DataField>
        </Field>
        <Field Name="GrandTotalCLE2RemAmtFormat">
          <DataField>GrandTotalCLE2RemAmtFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE1RemAmt">
          <DataField>GrandTotalCLE1RemAmt</DataField>
        </Field>
        <Field Name="GrandTotalCLE1RemAmtFormat">
          <DataField>GrandTotalCLE1RemAmtFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLEAmtLCY">
          <DataField>GrandTotalCLEAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLEAmtLCYFormat">
          <DataField>GrandTotalCLEAmtLCYFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE1CustRemAmtLCY">
          <DataField>GrandTotalCLE1CustRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE2CustRemAmtLCY">
          <DataField>GrandTotalCLE2CustRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE3CustRemAmtLCY">
          <DataField>GrandTotalCLE3CustRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE4CustRemAmtLCY">
          <DataField>GrandTotalCLE4CustRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE5CustRemAmtLCY">
          <DataField>GrandTotalCLE5CustRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE1AmtLCY">
          <DataField>GrandTotalCLE1AmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE1AmtLCYFormat">
          <DataField>GrandTotalCLE1AmtLCYFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE5PctRemAmtLCY">
          <DataField>GrandTotalCLE5PctRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE3PctRemAmtLCY">
          <DataField>GrandTotalCLE3PctRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE2PctRemAmtLCY">
          <DataField>GrandTotalCLE2PctRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE1PctRemAmtLCY">
          <DataField>GrandTotalCLE1PctRemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE5RemAmtLCY">
          <DataField>GrandTotalCLE5RemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE5RemAmtLCYFormat">
          <DataField>GrandTotalCLE5RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE4RemAmtLCY">
          <DataField>GrandTotalCLE4RemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE4RemAmtLCYFormat">
          <DataField>GrandTotalCLE4RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE3RemAmtLCY">
          <DataField>GrandTotalCLE3RemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE3RemAmtLCYFormat">
          <DataField>GrandTotalCLE3RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE2RemAmtLCY">
          <DataField>GrandTotalCLE2RemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE2RemAmtLCYFormat">
          <DataField>GrandTotalCLE2RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="GrandTotalCLE1RemAmtLCY">
          <DataField>GrandTotalCLE1RemAmtLCY</DataField>
        </Field>
        <Field Name="GrandTotalCLE1RemAmtLCYFormat">
          <DataField>GrandTotalCLE1RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="PageGroupNo">
          <DataField>PageGroupNo</DataField>
        </Field>
        <Field Name="CustomerPhoneNoCaption">
          <DataField>CustomerPhoneNoCaption</DataField>
        </Field>
        <Field Name="CustomerContactCaption">
          <DataField>CustomerContactCaption</DataField>
        </Field>
        <Field Name="SalespersonCodeERK">
          <DataField>SalespersonCodeERK</DataField>
        </Field>
        <Field Name="SalespersonNameERK">
          <DataField>SalespersonNameERK</DataField>
        </Field>
        <Field Name="PaymentTermsCodeERK">
          <DataField>PaymentTermsCodeERK</DataField>
        </Field>
        <Field Name="Name1_Cust">
          <DataField>Name1_Cust</DataField>
        </Field>
        <Field Name="No_Cust">
          <DataField>No_Cust</DataField>
        </Field>
        <Field Name="CustomerPhoneNo">
          <DataField>CustomerPhoneNo</DataField>
        </Field>
        <Field Name="CustomerContactName">
          <DataField>CustomerContactName</DataField>
        </Field>
        <Field Name="CLEEndDateRemAmtLCY">
          <DataField>CLEEndDateRemAmtLCY</DataField>
        </Field>
        <Field Name="CLEEndDateRemAmtLCYFormat">
          <DataField>CLEEndDateRemAmtLCYFormat</DataField>
        </Field>
        <Field Name="AgedCLE1RemAmtLCY">
          <DataField>AgedCLE1RemAmtLCY</DataField>
        </Field>
        <Field Name="AgedCLE1RemAmtLCYFormat">
          <DataField>AgedCLE1RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="AgedCLE2RemAmtLCY">
          <DataField>AgedCLE2RemAmtLCY</DataField>
        </Field>
        <Field Name="AgedCLE2RemAmtLCYFormat">
          <DataField>AgedCLE2RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="AgedCLE3RemAmtLCY">
          <DataField>AgedCLE3RemAmtLCY</DataField>
        </Field>
        <Field Name="AgedCLE3RemAmtLCYFormat">
          <DataField>AgedCLE3RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="AgedCLE4RemAmtLCY">
          <DataField>AgedCLE4RemAmtLCY</DataField>
        </Field>
        <Field Name="AgedCLE4RemAmtLCYFormat">
          <DataField>AgedCLE4RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="AgedCLE5RemAmtLCY">
          <DataField>AgedCLE5RemAmtLCY</DataField>
        </Field>
        <Field Name="AgedCLE5RemAmtLCYFormat">
          <DataField>AgedCLE5RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="CLEEndDateAmtLCY">
          <DataField>CLEEndDateAmtLCY</DataField>
        </Field>
        <Field Name="CLEEndDateAmtLCYFormat">
          <DataField>CLEEndDateAmtLCYFormat</DataField>
        </Field>
        <Field Name="CLEEndDueDate">
          <DataField>CLEEndDueDate</DataField>
        </Field>
        <Field Name="CLEEndDateDocNo">
          <DataField>CLEEndDateDocNo</DataField>
        </Field>
        <Field Name="CLEDocType">
          <DataField>CLEDocType</DataField>
        </Field>
        <Field Name="CLEPostingDate">
          <DataField>CLEPostingDate</DataField>
        </Field>
        <Field Name="AgedCLE5TempRemAmt">
          <DataField>AgedCLE5TempRemAmt</DataField>
        </Field>
        <Field Name="AgedCLE5TempRemAmtFormat">
          <DataField>AgedCLE5TempRemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE4TempRemAmt">
          <DataField>AgedCLE4TempRemAmt</DataField>
        </Field>
        <Field Name="AgedCLE4TempRemAmtFormat">
          <DataField>AgedCLE4TempRemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE3TempRemAmt">
          <DataField>AgedCLE3TempRemAmt</DataField>
        </Field>
        <Field Name="AgedCLE3TempRemAmtFormat">
          <DataField>AgedCLE3TempRemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE2TempRemAmt">
          <DataField>AgedCLE2TempRemAmt</DataField>
        </Field>
        <Field Name="AgedCLE2TempRemAmtFormat">
          <DataField>AgedCLE2TempRemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE1TempRemAmt">
          <DataField>AgedCLE1TempRemAmt</DataField>
        </Field>
        <Field Name="AgedCLE1TempRemAmtFormat">
          <DataField>AgedCLE1TempRemAmtFormat</DataField>
        </Field>
        <Field Name="RemAmt_CLEEndDate">
          <DataField>RemAmt_CLEEndDate</DataField>
        </Field>
        <Field Name="RemAmt_CLEEndDateFormat">
          <DataField>RemAmt_CLEEndDateFormat</DataField>
        </Field>
        <Field Name="CLEEndDate_ExtDocNo">
          <DataField>CLEEndDate_ExtDocNo</DataField>
        </Field>
        <Field Name="CLEEndDate">
          <DataField>CLEEndDate</DataField>
        </Field>
        <Field Name="CLEEndDateFormat">
          <DataField>CLEEndDateFormat</DataField>
        </Field>
        <Field Name="Name_Cust">
          <DataField>Name_Cust</DataField>
        </Field>
        <Field Name="TotalCLE1AmtLCY">
          <DataField>TotalCLE1AmtLCY</DataField>
        </Field>
        <Field Name="TotalCLE1AmtLCYFormat">
          <DataField>TotalCLE1AmtLCYFormat</DataField>
        </Field>
        <Field Name="TotalCLE1RemAmtLCY">
          <DataField>TotalCLE1RemAmtLCY</DataField>
        </Field>
        <Field Name="TotalCLE1RemAmtLCYFormat">
          <DataField>TotalCLE1RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="TotalCLE2RemAmtLCY">
          <DataField>TotalCLE2RemAmtLCY</DataField>
        </Field>
        <Field Name="TotalCLE2RemAmtLCYFormat">
          <DataField>TotalCLE2RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="TotalCLE3RemAmtLCY">
          <DataField>TotalCLE3RemAmtLCY</DataField>
        </Field>
        <Field Name="TotalCLE3RemAmtLCYFormat">
          <DataField>TotalCLE3RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="TotalCLE4RemAmtLCY">
          <DataField>TotalCLE4RemAmtLCY</DataField>
        </Field>
        <Field Name="TotalCLE4RemAmtLCYFormat">
          <DataField>TotalCLE4RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="TotalCLE5RemAmtLCY">
          <DataField>TotalCLE5RemAmtLCY</DataField>
        </Field>
        <Field Name="TotalCLE5RemAmtLCYFormat">
          <DataField>TotalCLE5RemAmtLCYFormat</DataField>
        </Field>
        <Field Name="CurrrencyCode">
          <DataField>CurrrencyCode</DataField>
        </Field>
        <Field Name="TotalCLE5RemAmt">
          <DataField>TotalCLE5RemAmt</DataField>
        </Field>
        <Field Name="TotalCLE5RemAmtFormat">
          <DataField>TotalCLE5RemAmtFormat</DataField>
        </Field>
        <Field Name="TotalCLE4RemAmt">
          <DataField>TotalCLE4RemAmt</DataField>
        </Field>
        <Field Name="TotalCLE4RemAmtFormat">
          <DataField>TotalCLE4RemAmtFormat</DataField>
        </Field>
        <Field Name="TotalCLE3RemAmt">
          <DataField>TotalCLE3RemAmt</DataField>
        </Field>
        <Field Name="TotalCLE3RemAmtFormat">
          <DataField>TotalCLE3RemAmtFormat</DataField>
        </Field>
        <Field Name="TotalCLE2RemAmt">
          <DataField>TotalCLE2RemAmt</DataField>
        </Field>
        <Field Name="TotalCLE2RemAmtFormat">
          <DataField>TotalCLE2RemAmtFormat</DataField>
        </Field>
        <Field Name="TotalCLE1RemAmt">
          <DataField>TotalCLE1RemAmt</DataField>
        </Field>
        <Field Name="TotalCLE1RemAmtFormat">
          <DataField>TotalCLE1RemAmtFormat</DataField>
        </Field>
        <Field Name="TotalCLE1Amt">
          <DataField>TotalCLE1Amt</DataField>
        </Field>
        <Field Name="TotalCLE1AmtFormat">
          <DataField>TotalCLE1AmtFormat</DataField>
        </Field>
        <Field Name="TotalCheck">
          <DataField>TotalCheck</DataField>
        </Field>
        <Field Name="CurrNo">
          <DataField>CurrNo</DataField>
        </Field>
        <Field Name="TempCurrCode">
          <DataField>TempCurrCode</DataField>
        </Field>
        <Field Name="AgedCLE6RemAmt">
          <DataField>AgedCLE6RemAmt</DataField>
        </Field>
        <Field Name="AgedCLE6RemAmtFormat">
          <DataField>AgedCLE6RemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE1RemAmt">
          <DataField>AgedCLE1RemAmt</DataField>
        </Field>
        <Field Name="AgedCLE1RemAmtFormat">
          <DataField>AgedCLE1RemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE2RemAmt">
          <DataField>AgedCLE2RemAmt</DataField>
        </Field>
        <Field Name="AgedCLE2RemAmtFormat">
          <DataField>AgedCLE2RemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE3RemAmt">
          <DataField>AgedCLE3RemAmt</DataField>
        </Field>
        <Field Name="AgedCLE3RemAmtFormat">
          <DataField>AgedCLE3RemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE4RemAmt">
          <DataField>AgedCLE4RemAmt</DataField>
        </Field>
        <Field Name="AgedCLE4RemAmtFormat">
          <DataField>AgedCLE4RemAmtFormat</DataField>
        </Field>
        <Field Name="AgedCLE5RemAmt">
          <DataField>AgedCLE5RemAmt</DataField>
        </Field>
        <Field Name="AgedCLE5RemAmtFormat">
          <DataField>AgedCLE5RemAmtFormat</DataField>
        </Field>
        <Field Name="CurrSpecificationCptn">
          <DataField>CurrSpecificationCptn</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>