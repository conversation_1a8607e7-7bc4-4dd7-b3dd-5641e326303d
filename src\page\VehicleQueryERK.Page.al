page 60052 "Vehicle Query ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Vehicle Query';
    PageType = Card;
    UsageCategory = None;
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            field(SerialNo; SerialNo)
            {
                Caption = 'Serial No.';
                ToolTip = 'Specifies the value of the Serial No. field.';
                ExtendedDatatype = Barcode;

                trigger OnValidate()
                var
                    VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                    VehicleTransferLine: Record "Vehicle Transfer Line ERK";
                    QueryResultMsg: Label 'Location Code: %1\\Bin Code: %2\\To Location Code: %3\\To Bin Code: %4\\Brand Code: %5\\Model Code: %6\\Color: %7', Comment = '%1="Serial No. Information"."Current Location Code ERK"; %2="Serial No. Information"."Current Bin Code ERK"; %3="Vehicle Transfer Header ERK"."To Location Code"; %4="Vehicle Transfer Header ERK"."To Bin Code", %5="Serial No. Information"."Brand Code ERK", %6="Serial No. Information"."Model Code ERK", %7="Serial No. Information"."Color ERK"';
                    ErrorErr: Label 'There is no vehicle with serial no. %1.', Comment = '%1=SerialNo';
                    SerialNoText: Text;
                    CharacterLenghtErr: Label 'Serial No. must be at least 8 characters long.';
                begin
                    //OnBeforeValidateSerialNo(SerialNo);

                    if SerialNo = '' then
                        exit;
                    if StrLen(SerialNo) < 8 then
                        Error(CharacterLenghtErr);
                    SerialNoText := '*' + SerialNo;
                    SerialNoInformation.SetFilter("Serial No.", SerialNoText);
                    if not SerialNoInformation.FindFirst() then
                        Error(ErrorErr, SerialNo);
                    if DeclarationQuery then
                        VehicleTransferManagement.InsertVehicleQueryLedgerEntry(SerialNoInformation)
                    else begin
                        VehicleTransferLine.SetRange("Serial No.", SerialNo);
                        VehicleTransferLine.SetRange(Processed, false);
                        if VehicleTransferLine.FindFirst() then
                            VehicleTransferHeader.Get(VehicleTransferLine."Document No.");
                        Message(QueryResultMsg, SerialNoInformation."Current Location Code ERK",
                                                SerialNoInformation."Current Bin Code ERK",
                                                VehicleTransferHeader."To Location Code",
                                                VehicleTransferHeader."To Bin Code",
                                                SerialNoInformation."Brand Code ERK",
                                                SerialNoInformation."Model Code ERK",
                                                SerialNoInformation."Colour Name ERK");
                    end;
                    SerialNo := '';
                end;
            }
        }
    }
    procedure SetDeclarationQuery(ParamDeclarationQuery: Boolean)
    begin
        DeclarationQuery := ParamDeclarationQuery;
    end;

    // [IntegrationEvent(false, false)]
    // local procedure OnBeforeValidateSerialNo(var SerialNo: Code[50])
    // begin
    // end;

    var
        SerialNoInformation: Record "Serial No. Information";
        VehicleTransferManagement: Codeunit "Vehicle Transfer Management";
        DeclarationQuery: Boolean;
        SerialNo: Code[50];
}
