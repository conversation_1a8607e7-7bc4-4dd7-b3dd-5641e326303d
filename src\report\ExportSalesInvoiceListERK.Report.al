report 60018 "Export Sales Invoice List ERK"
{
    ApplicationArea = All;
    Caption = 'Export Sales Invoice List';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(ExportHeaderERK; "Export Header ERK")
        {
            CalcFields = "Customs Declaration No. FF", "Customer Name";
            DataItemTableView = where("Customs Declaration No. FF" = filter(<> ''));
            RequestFilterFields = "Actual Export Date FlowField";
            column(CustomsDeclarationNoFF; "Customs Declaration No. FF")
            {
            }
            column(DocumentDate; "Document Date")
            {
            }
            column(PostedSalesInvoiceNo; "Posted Sales Invoice No.")
            {
            }
            column(CustomerName_ExportHeaderERK; "Customer Name")
            {
            }
            dataitem(ExportLineERK; "Export Line ERK")
            {
                DataItemLink = "Document No." = field("No.");
                CalcFields = "UoM International Std. Code";
                column(LineOrderNo; LineOrderNo)
                {
                }
                column(TariffNo; "Tariff No.")
                {
                }
                column(Quantity; Quantity)
                {
                }
                // column(VendorName; "Vendor Name")
                // {
                // }
                column(UoMInternationalStdCode_ExportLineERK; "UoM International Std. Code")
                {
                }
                trigger OnAfterGetRecord()
                begin
                    LineOrderNo += 1;
                end;
            }
        }
    }
    var
        LineOrderNo: Integer;
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(Processing)
    //         {
    //         }
    //     }
    // }
}