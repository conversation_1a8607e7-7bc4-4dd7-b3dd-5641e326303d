page 60124 "Port Operation Contract Subpag"
{
    ApplicationArea = All;
    Caption = 'Port Operation Contract Subpag';
    PageType = ListPart;
    SourceTable = "Port Operation Contract Line";
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Parent Load Type"; Rec."Parent Load Type")
                {
                }
                field("Sub Load Type"; Rec."Sub Load Type")
                {
                }
                field("Processed Load"; Rec."Processed Load")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }

                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field("Starting Date"; Rec."Starting Date")
                {
                }
                field("Ending Date"; Rec."Ending Date")
                {
                }
                field("Unit Price"; Rec."Unit Price")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Invoicing Period"; Rec."Invoicing Period")
                {
                }
            }
        }
    }
}