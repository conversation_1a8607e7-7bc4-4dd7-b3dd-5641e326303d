tableextension 60004 "Sales Header ERK" extends "Sales Header"
{
    fields
    {
        field(60000; "Port of Arrival ERK"; Code[10])
        {
            Caption = 'Port of Arrival';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Port of Arrival field.';
        }
        field(60001; "Notify Ship-to Code ERK"; Code[10])
        {
            Caption = 'Notify Ship-to Code';
            TableRelation = "Ship-to Address".Code where("Customer No." = field("Sell-to Customer No."));
            ToolTip = 'Specifies the value of the Notify Ship-to Code field.';
            trigger OnValidate()
            var
                ShiptoAddress: Record "Ship-to Address";
            begin
                if not ShiptoAddress.Get("Sell-to Customer No.", "Notify Ship-to Code ERK") then
                    "Notify Ship-to Name ERK" := ''
                // "Notify Ship-to Name 2" := '';
                // "Notify Ship-to Address" := '';
                // "Notify Ship-to Address 2" := '';
                // "Notify Ship-to County" := '';
                // "Notify Ship-to City" := '';
                // "Notify Ship-to Country/Region" := '';
                else
                    "Notify Ship-to Name ERK" := ShiptoAddress.Name;
                // "Notify Ship-to Name 2" := ShiptoAddress."Name 2";
                // "Notify Ship-to Address" := ShiptoAddress.Address;
                // "Notify Ship-to Address 2" := ShiptoAddress."Address 2";
                // "Notify Ship-to County" := ShiptoAddress.Count()y;
                // "Notify Ship-to City" := ShiptoAddress.City;
                // "Notify Ship-to Country/Region" := ShiptoAddress."Country/Region Code";
            end;
        }
        field(60002; "Notify Ship-to Name ERK"; Text[100])
        {
            Caption = 'Notify Ship-to Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Notify Ship-to Name field.';
        }
        field(60003; "Estimated Time of Delivery ERK"; Code[20])
        {
            Caption = 'Estimated Time of Delivery';
            ToolTip = 'Specifies the value of the Estimated Time of Delivery field.';
        }
        field(60004; "Payment Amount ERK"; Decimal)
        {
            Caption = 'Payment Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Export Ledger Entry ERK".Amount where("Customer No." = field("Sell-to Customer No."), "Document Type" = const(Payment), "Blanket Sales Order No." = field("No.")));
            ToolTip = 'Specifies the value of the Payment Amount field.';
        }
        field(60005; "Applied Amount ERK"; Decimal)
        {
            Caption = 'Applied Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Export Ledger Entry ERK"."Assigned Amount" where("Customer No." = field("Sell-to Customer No."), "Document Type" = const(Invoice), "Blanket Sales Order No." = field("No.")));
            ToolTip = 'Specifies the value of the Applied Amount field.';
        }
        field(60006; "Completed ERK"; Boolean)
        {
            Caption = 'Completed';
            ToolTip = 'Specifies the value of the Completed field.';
        }
        field(60007; "Freight ERK"; Decimal)
        {
            Caption = 'Freight';
            ToolTip = 'Specifies the value of the Freight field.';
        }
        field(60012; "EBA Status ERK"; Enum "EBA Status ERK")
        {
            Caption = 'EBA Status';
            ToolTip = 'Specifies EBA Status field.';
            InitValue = 1;
            //Editable = false;

            trigger OnValidate()
            begin
                Rec.TestField("Invoice Type INF ERK");
                if ("EBA Status ERK" = "EBA Status ERK"::"Ready for EBA") and ("Invoice Type INF ERK" = "Invoice Type INF ERK"::"E-Archive") then begin
                    Rec.TestField("External Document No.");
                    Rec.TestField("Invoice View PDF File ERK");

                end;
            end;
        }
        field(60009; "Invoice View PDF File ERK"; Blob)
        {
            Caption = 'Invoice View PDF File';
        }
        field(60010; "Invoice Type INF ERK"; Enum "E-Document Inv CrMemo Type INF")
        {
            Caption = 'Invoice Type';
            ToolTip = 'Specifies the value of the Invoice Type field.';

            trigger OnValidate()
            begin

                if ("EBA Status ERK" = "EBA Status ERK"::"Ready for EBA") and ("Invoice Type INF ERK" = "Invoice Type INF ERK"::"E-Archive") then begin
                    Rec.TestField("External Document No.");
                    Rec.TestField("Invoice View PDF File ERK");

                end;
            end;
        }
        field(60011; "EBA Your Reference ERK"; Text[35])
        {
            Caption = 'Your Reference';
            AllowInCustomizations = Never;

            trigger OnValidate()
            begin
                Rec."Your Reference" := Rec."EBA Your Reference ERK";
            end;

        }
        field(60008; "Created From Car Carrier ERK"; Boolean)
        {
            Caption = 'Created From Car Carrier Rev./Exp.';
            ToolTip = 'Specifies if this invoice was created from Car Carrier Revenue/Expense data.';
        }
    }

    procedure GetPDFFileData() PDFFileData: Text
    var
        TypeHelper: Codeunit "Type Helper";
        InStr: InStream;

    begin
        CalcFields("Invoice View PDF File ERK");
        "Invoice View PDF File ERK".CreateInStream(InStr, TextEncoding::UTF8);
        if not TypeHelper.TryReadAsTextWithSeparator(InStr, TypeHelper.LFSeparator(), PDFFileData) then
            PDFFileData := 'Error reading PDF file data.';
    end;
}
