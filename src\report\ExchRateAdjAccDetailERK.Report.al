report 60022 "ExchRate Adj. Acc. Detail ERK"
{
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;
    DefaultLayout = RDLC;
    RDLCLayout = './src/report/ExchRateAdjAccDetail.rdl';
    PreviewMode = PrintLayout;
    DataAccessIntent = ReadOnly;
    Caption = 'Exch. Rate Adj. Acc. Detail';

    dataset
    {
        dataitem(Currency; Currency)
        {
            RequestFilterFields = "Date Filter";
            column(CurrCode; Code)
            {

            }

            dataitem(Customer; Customer)
            {
                //DataItemLink = "Currency Code" = field(Code), "Date Filter" = field("Date Filter");
                DataItemTableView = sorting("No.");

                column(No_Customer; "No.")
                {
                }
                column(Name_Customer; Name)
                {
                }
                column(Customer_Net_Change; "Net Change")
                {

                }
                column(NetChangeLCY_Customer; "Net Change (LCY)")
                {
                }
                column(CalcTotalAppAmountCustomer; CalcTotalAppAmount)
                { }

                trigger OnPreDataItem()
                begin
                    Customer.SetFilter("Date Filter", Currency.GetFilter("Date Filter"));
                    Customer.SetFilter("Currency Filter", Currency.Code);
                end;

                trigger OnAfterGetRecord()
                var
                    DetailedCustLedgEntry: Record "Detailed Cust. Ledg. Entry";
                begin
                    CalcFields("Net Change", "Net Change (LCY)");

                    CalcTotalAppAmount := 0;
                    DetailedCustLedgEntry.Reset();
                    DetailedCustLedgEntry.SetRange("Customer No.", Customer."No.");
                    DetailedCustLedgEntry.SetFilter("Posting Date", Currency.GetFilter("Date Filter"));
                    DetailedCustLedgEntry.SetFilter("Currency Code", Currency.Code);
                    DetailedCustLedgEntry.SetFilter("Entry Type", '%1|%2|%3|%4', DetailedCustLedgEntry."Entry Type"::"Realized Loss",
                     DetailedCustLedgEntry."Entry Type"::"Realized Gain",
                     DetailedCustLedgEntry."Entry Type"::"Unrealized Loss", DetailedCustLedgEntry."Entry Type"::"Unrealized Gain");
                    DetailedCustLedgEntry.CalcSums("Amount (LCY)");
                    CalcTotalAppAmount := DetailedCustLedgEntry."Amount (LCY)";

                    if ("Net Change" = 0) and (CalcTotalAppAmount = 0) then
                        CurrReport.Skip();

                end;

            }

            dataitem(Vendor; Vendor)
            {
                //DataItemLink = "Currency Code" = field(Code), "Date Filter" = field("Date Filter");
                DataItemTableView = sorting("No.");
                column(No_Vendor; "No.")
                {
                }
                column(Name_Vendor; Name)
                {
                }
                column(Vendor_Net_Change; "Net Change")
                {

                }
                column(NetChangeLCY_Vendor; "Net Change (LCY)")
                {
                }
                column(CalcTotalAppAmountVendor; CalcTotalAppAmount)
                { }


                trigger OnPreDataItem()
                begin
                    Vendor.SetFilter("Date Filter", Currency.GetFilter("Date Filter"));
                    Vendor.SetFilter("Currency Filter", Currency.Code);
                end;

                trigger OnAfterGetRecord()
                var
                    DetailedVendorLedgEntry: Record "Detailed Vendor Ledg. Entry";
                begin
                    CalcFields("Net Change", "Net Change (LCY)");

                    CalcTotalAppAmount := 0;
                    DetailedVendorLedgEntry.Reset();
                    DetailedVendorLedgEntry.SetRange("Vendor No.", Vendor."No.");
                    DetailedVendorLedgEntry.SetFilter("Posting Date", Currency.GetFilter("Date Filter"));
                    DetailedVendorLedgEntry.SetFilter("Currency Code", Currency.Code);
                    DetailedVendorLedgEntry.SetFilter("Entry Type", '%1|%2|%3|%4', DetailedVendorLedgEntry."Entry Type"::"Realized Loss",
                     DetailedVendorLedgEntry."Entry Type"::"Realized Gain",
                     DetailedVendorLedgEntry."Entry Type"::"Unrealized Loss", DetailedVendorLedgEntry."Entry Type"::"Unrealized Gain");
                    DetailedVendorLedgEntry.CalcSums("Amount (LCY)");
                    CalcTotalAppAmount := DetailedVendorLedgEntry."Amount (LCY)";

                    if ("Net Change" = 0) and (CalcTotalAppAmount = 0) then
                        CurrReport.Skip();

                end;

            }

            dataitem("Bank Account"; "Bank Account")
            {
                //DataItemLink = "Currency Code" = field(Code), "Date Filter" = field("Date Filter");
                DataItemTableView = sorting("No.");
                column(No_Bank; "No.")
                {
                }
                column(Name_Bank; Name)
                {
                }
                column(Bank_Net_Change; "Net Change")
                {

                }
                column(NetChangeLCY_Bank; "Net Change (LCY)")
                {
                }
                column(CalcTotalAppAmountBank; CalcTotalAppAmount)
                { }


                trigger OnPreDataItem()
                begin
                    "Bank Account".SetFilter("Date Filter", Currency.GetFilter("Date Filter"));
                    "Bank Account".SetFilter("Currency Code", Currency.Code);
                end;

                trigger OnAfterGetRecord()
                var
                    BankAccountLedgerEntry: Record "Bank Account Ledger Entry";
                begin
                    CalcFields("Net Change", "Net Change (LCY)");

                    CalcTotalAppAmount := 0;
                    BankAccountLedgerEntry.Reset();
                    BankAccountLedgerEntry.SetRange("Bank Account No.", "Bank Account"."No.");
                    BankAccountLedgerEntry.SetFilter("Posting Date", Currency.GetFilter("Date Filter"));
                    BankAccountLedgerEntry.SetFilter("Currency Code", Currency.Code);
                    BankAccountLedgerEntry.SetFilter(Amount, '%1', 0);
                    BankAccountLedgerEntry.SetFilter("Amount (LCY)", '<>%1', 0);
                    BankAccountLedgerEntry.CalcSums("Amount (LCY)");
                    CalcTotalAppAmount := BankAccountLedgerEntry."Amount (LCY)";

                    if ("Net Change" = 0) and (CalcTotalAppAmount = 0) then
                        CurrReport.Skip();

                end;

            }


        }
    }



    requestpage
    {
        AboutTitle = 'Teaching tip title';
        AboutText = 'Teaching tip content';
        layout
        {
            area(Content)
            {
                // group(GroupName)
                // {

                // }
            }
        }

        // actions
        // {
        //     area(processing)
        //     {
        //         action(LayoutName)
        //         {

        //         }
        //     }
        // }
    }

    // rendering
    // {
    //     layout(LayoutName)
    //     {
    //         Type = Excel;
    //         LayoutFile = 'mySpreadsheet.xlsx';
    //     }
    // }

    labels
    {
        ReportName = 'Exchange Rate Adjustment Account Detail';

    }
    var
        CalcTotalAppAmount: Decimal;


}