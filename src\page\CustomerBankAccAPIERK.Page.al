#pragma warning disable LC0062
page 60118 "CustomerBankAccAPI ERK"
#pragma warning restore LC0062
{
    PageType = API;
    Caption = 'Customer Bank Account API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'customerBankAccounts';
    EntitySetName = 'customerBankAccounts';
    SourceTable = "Customer Bank Account";
    DelayedInsert = true;
    ODataKeyFields = SystemId;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(id; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(code; Rec.Code)
                {
                    Caption = 'Code';
                }
                field(customerNumber; Rec."Customer No.")
                {
                    Caption = 'Customer No.';
                }
                field(displayName; Rec.Name)
                {
                    Caption = 'Name';
                }
                field(name2; Rec."Name 2")
                {
                    Caption = 'Name 2';
                }
                field(address; Rec.Address)
                {
                    Caption = 'Address';
                }
                field(address2; Rec."Address 2")
                {
                    Caption = 'Address 2';
                }
                field(city; Rec.City)
                {
                    Caption = 'City';
                }
                field(county; Rec.County)
                {
                    Caption = 'County';
                }
                field(countryRegionCode; Rec."Country/Region Code")
                {
                    Caption = 'Country/Region Code';
                }
                field(postCode; Rec."Post Code")
                {
                    Caption = 'Post Code';
                }
                field(currencyCode; Rec."Currency Code")
                {
                    Caption = 'Currency Code';
                }
                field(iban; Rec.IBAN)
                {
                    Caption = 'IBAN';
                }
                field(swiftCode; Rec."SWIFT Code")
                {
                    Caption = 'SWIFT Code';
                }
                field(faxNumber; Rec."Fax No.")
                {
                    Caption = 'Fax No.';
                }
                field(eMail; Rec."E-Mail")
                {
                    Caption = 'Email';
                }
#pragma warning disable AL0432
                field(homePage; Rec."Home Page")
#pragma warning restore AL0432
                {
                    Caption = 'Home Page';
                }
                field(phoneNumber; Rec."Phone No.")
                {
                    Caption = 'Phone No.';
                }
                field(bankCodeINF; Rec."Bank Code INF")
                {
                    Caption = 'Bank Code';
                }
                field(branchCodeINF; Rec."Branch Code INF")
                {
                    Caption = 'Branch Code';
                }
                field(bankAccountNumber; Rec."Bank Account No.")
                {
                    Caption = 'Bank Account No.';
                }
                field(bankClearingCode; Rec."Bank Clearing Code")
                {
                    Caption = 'Bank Clearing Code';
                }
                field(bankClearingStandard; Rec."Bank Clearing Standard")
                {
                    Caption = 'Bank Clearing Standard';
                }
            }
        }
    }
}