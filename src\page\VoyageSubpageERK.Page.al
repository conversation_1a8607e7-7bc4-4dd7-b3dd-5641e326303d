page 60019 "Voyage Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Voyage Subpage';
    PageType = ListPart;
    SourceTable = "Voyage Line ERK";
    AutoSplitKey = true;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Customer No."; Rec."Customer No.")
                {
                    Editable = IsVoyageLineEditable;
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    Editable = IsVoyageLineEditable;
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    Editable = IsVoyageLineEditable;
                }
                // field("Total Line Amount"; Rec."Revenue Amount")
                // {
                //     ToolTip = 'Specifies the value of the Total Line Amount field.';
                //     Editable = IsVoyageLineEditable;
                // }
                field("RevenueAmount(ACY)"; VoyageMangement.CalculateVoyageLineRevenueAmountACY(Rec))
                {
                    Caption = 'Revenue Amount (ACY)';
                    ToolTip = 'Specifies the value of the Revenue Amount (ACY) field.';
                }
                field("Expense Amount"; Rec."Expense Amount")
                {
                }
                field("Consignee No."; Rec."Consignee No.")
                {
                }
                field("Consignee Name"; Rec."Consignee Name")
                {
                }
                field("Loader/Exporter No."; Rec."Loader/Exporter No.")
                {
                    //Editable = IsVoyageLineEditable;
                }
                field("Loader/Exporter Name"; Rec."Loader/Exporter Name")
                {
                    //Editable = IsVoyageLineEditable;
                }
                field("Shipment Method Code"; Rec."Shipment Method Code")
                {
                    //Editable = IsVoyageLineEditable;
                }
                field("Shipper Ship-to Code"; Rec."Shipper Ship-to Code")
                {
                    //Editable = IsVoyageLineEditable;
                }
                field("Shipper Ship-to Name"; Rec."Shipper Ship-to Name")
                {
                    //Editable = IsVoyageLineEditable;
                }
                field("Load Type"; Rec."Load Type")
                {
                }
                // field("Weight (Ton)"; Rec."Weight (Ton)")
                // {
                //     ToolTip = 'Specifies the value of the Weight (Tonnage) field.';
                // }
                field(CalculateVoyageExpenseDistrubitionACY; VoyageMangement.CalculateVoyageExpenseDistrubitionACY(Rec))
                {
                    Caption = 'Voyage Expense Distribution (ACY)';
                    ToolTip = 'Specifies the value of the Voyage Expense Distribution field.';
                }
                // field("Loading Port Code"; Rec."Loading Port Code")
                // {
                //     ToolTip = 'Specifies the value of the Loading Port Code field.';
                //     Visible = PortVisible;
                // }
                // field("Discharge Port Code"; Rec."Discharge Port Code")
                // {
                //     ToolTip = 'Specifies the value of the Discharge Port Code field.';
                //     Visible = PortVisible;
                // }
                // field("Loading Port Name"; Rec."Loading Port Name")
                // {
                //     ToolTip = 'Specifies the value of the Loading Port Name field.';
                //     Visible = PortVisible;
                // }
                // field("Discharge Port Name"; Rec."Discharge Port Name")
                // {
                //     ToolTip = 'Specifies the value of the Discharge Port Name field.';
                //     Visible = PortVisible;
                // }
                // field("Loading Port Departure Date"; Rec."Loading Port Departure Date")
                // {
                //     ToolTip = 'Specifies the value of the Loading Port Departure Date field.';
                //     Visible = PortVisible;
                // }
                // field("Discharge Port Departure Date"; Rec."Discharge Port Departure Date")
                // {
                //     ToolTip = 'Specifies the value of the Discharge Port Departure Date field.';
                //     Visible = PortVisible;
                // }
                // field("Loading Port Arrival Date"; Rec."Loading Port Arrival Date")
                // {
                //     ToolTip = 'Specifies the value of the Loading Port Arrival Date field.';
                //     Visible = PortVisible;
                // }
                // field("Discharge Port Arrival Date"; Rec."Discharge Port Arrival Date")
                // {
                //     ToolTip = 'Specifies the value of the Discharge Port Arrival Date field.';
                //     Visible = PortVisible;
                // }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(VoyageLinesDetails)
            {
                Caption = 'Voyage Line Details',;
                Image = TaxDetail;
                ToolTip = 'Executes the Voyage Line Details action.';
                RunObject = page "Voyage Line Detail List ERK";
                RunPageLink = "Document No." = field("Document No."), "Document Line No." = field("Line No.");
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        IsVoyageLineEditable := true;
        VoyageMangement.SetVoyageLineEditable(Rec, IsVoyageLineEditable);
    end;

    trigger OnAfterGetCurrRecord()
    begin
        IsVoyageLineEditable := true;
        VoyageMangement.SetVoyageLineEditable(Rec, IsVoyageLineEditable);
    end;

    trigger OnOpenPage()
    begin
        IsVoyageLineEditable := true;
    end;

    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        IsVoyageLineEditable: Boolean;
}
