table 60050 "Vehicle Rev/Exp Worksheet Line"
{
    Caption = 'Vehicle Rev/Exp Worksheet Line';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(3; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(4; "Unit Amount"; Decimal)
        {
            Caption = 'Unit Amount';
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the value of the Unit Price field.';
        }
        field(5; "Car Carrier No."; Code[20])
        {
            Caption = 'Car Carrier No.';
            ToolTip = 'Specifies the car carrier number.';
            TableRelation = "Car Carrier Header ERK"."No.";
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    begin
        if Rec."Car Carrier No." = '' then begin
            CarCarrierLedgerEntry.SetRange("Serial No.", Rec."Serial No.");
            if CarCarrierLedgerEntry.FindFirst() then
                Rec."Car Carrier No." := CarCarrierLedgerEntry."Document No.";
        end;
    end;
}
