page 60030 "Voyage Account Card ERK"
{
    ApplicationArea = All;
    Caption = 'Voyage Account Card';
    PageType = Card;
    SourceTable = "Voyage Account ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                field(Name; Rec.Name)
                {
                }
                field("Name 2"; Rec."Name 2")
                {
                }
                field(Address; Rec.Address)
                {
                }
                field("Address 2"; Rec."Address 2")
                {
                }
                field("Post Code"; Rec."Post Code")
                {
                }
                field(City; Rec.City)
                {
                }
                field("Country/Region Code"; Rec."Country/Region Code")
                {
                }
                field("Car Carrier Related"; Rec."Car Carrier Related")
                {
                }
            }
        }
    }
}
