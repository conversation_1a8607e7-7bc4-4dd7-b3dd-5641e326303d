pageextension 60036 "Purchase Invoice ERK" extends "Purchase Invoice"
{
    layout
    {
        addbefore("Vendor Invoice No.")
        {
            field("Ignore Inc.E-Inv. Controls ERK"; Rec."Ignore Inc.E-Inv. Controls ERK")
            {
                ApplicationArea = All;
            }
            field("Incoming E-Invoice No. ERK"; Rec."Incoming E-Invoice No. ERK")
            {
                ApplicationArea = All;
            }
            field("VAT Registration No. ERK"; Rec."VAT Registration No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies VAT Registration No.';
            }
            field("Invoice Type INF ERK"; Rec."Invoice Type INF ERK")
            {
                ApplicationArea = All;
            }
            field("Created From Car Carrier ERK"; Rec."Created From Car Carrier ERK")
            {
                ApplicationArea = All;
                Editable = false;
            }
            field("EBA Status ERK"; Rec."EBA Status ERK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the EBA Status field.';
            }
            field("PDFExistERK ERK"; Rec.GetPDFFileData())
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the PDF Exist field.';
                Caption = 'PDF File';
                Editable = false;


                trigger OnAssistEdit()
                begin
                    Message(Rec.GetPDFFileData());
                end;
            }
        }
    }

    actions
    {
        addafter("&Invoice")
        {
            action("ImportInvoicePDFFileERK ERK")
            {
                ApplicationArea = All;
                Caption = 'Import Invoice PDF File';
                Image = Document;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Imports the invoice PDF file.';

                trigger OnAction()
                var
                    EBAIntegrationMngt: Codeunit "EBA Integration Mngt. ERK";
                begin
                    EBAIntegrationMngt.ImportInvoiceView(Rec);
                end;
            }
        }
    }
}
