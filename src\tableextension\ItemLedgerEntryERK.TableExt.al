tableextension 60027 "Item Ledger Entry ERK" extends "Item Ledger Entry"
{
    fields
    {
        field(60000; "Date Filter ERK"; Date)
        {
            Caption = 'Date Filter';
            FieldClass = FlowFilter;
        }
        field(60001; "Invoiced Qty. By Date ERK"; Decimal)
        {
            Caption = 'Invoiced Qty. By Date';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Value Entry"."Invoiced Quantity" where("Item Ledger Entry No." = field("Entry No."), "Posting Date" = field("Date Filter ERK")));
            ToolTip = 'Specifies the value of the Invoiced Qty. By Date field.';
        }
        field(60002; "E-Export Invoice No. ERK"; Code[20])
        {
            Caption = 'E-Export Invoice No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Value Entry"."Document No." where("Item Ledger Entry No." = field("Entry No."), "Document Type" = const("Sales Invoice")));
            ToolTip = 'Specifies the value of the E-Export Invoice No. field.';
        }
    }
}
