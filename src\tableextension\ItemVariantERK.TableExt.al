tableextension 60002 "Item Variant ERK" extends "Item Variant"
{
    fields
    {
        field(60000; "Brand ERK"; Code[10])
        {
            Caption = 'Brand';
            TableRelation = Manufacturer.Code;
            ToolTip = 'Specifies the value of the Brand field.';
        }
        field(60001; "Special Feature ERK"; Code[10])
        {
            Caption = 'Special Feature';
            TableRelation = "Special Feature ERK";
            ToolTip = 'Specifies the value of the Special Feature field.';
        }
        field(60002; "Packaging Type ERK"; Code[10])
        {
            Caption = 'Packaging Type';
            TableRelation = "Packaging Type ERK";
            ToolTip = 'Specifies the value of the Packaging Type field.';
        }
        field(60003; "Country/Region of Origin ERK"; Code[10])
        {
            Caption = 'Country/Region of Origin Code';
            TableRelation = "Country/Region";
            ToolTip = 'Specifies the value of the Country/Region of Origin Code field.';
        }
        field(60004; "Tariff No. ERK"; Code[20])
        {
            Caption = 'Tariff No.';
            TableRelation = "Tariff Number";
            ValidateTableRelation = false;
            ToolTip = 'Specifies the value of the Tariff No. field.';
            trigger OnValidate()
            var
                TariffNumber: Record "Tariff Number";
            begin
                if "Tariff No. ERK" = '' then
                    exit;

                if (not TariffNumber.WritePermission()) or
                   (not TariffNumber.ReadPermission())
                then
                    exit;

                if TariffNumber.Get("Tariff No. ERK") then
                    exit;

                TariffNumber.Init();
                TariffNumber."No." := "Tariff No. ERK";
                TariffNumber.Insert(true);
            end;
        }
    }
    fieldgroups
    {
        addlast(DropDown; "Brand ERK", "Special Feature ERK")
        {
        }
    }
}
