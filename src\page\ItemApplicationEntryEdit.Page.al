page 60126 "Item Application Entry - Edit"
{
    ApplicationArea = All;
    Caption = 'Item Application Entry - Edit';
    PageType = List;
    SourceTable = "Item Application Entry";
    UsageCategory = Administration;
    Permissions = tabledata "Item Application Entry" = d;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
#pragma warning disable AA0218
                field("Entry No."; Rec."Entry No.")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Item Ledger Entry No."; Rec."Item Ledger Entry No.")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Inbound Item Entry No."; Rec."Inbound Item Entry No.")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Outbound Item Entry No."; Rec."Outbound Item Entry No.")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field(Quantity; Rec.Quantity)
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Posting Date"; Rec."Posting Date")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Transferred-from Entry No."; Rec."Transferred-from Entry No.")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Creation Date"; Rec."Creation Date")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Created By User"; Rec."Created By User")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Last Modified Date"; Rec."Last Modified Date")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Last Modified By User"; Rec."Last Modified By User")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Cost Application"; Rec."Cost Application")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Output Completely Invd. Date"; Rec."Output Completely Invd. Date")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field("Outbound Entry is Updated"; Rec."Outbound Entry is Updated")
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field(SystemCreatedAt; Rec.SystemCreatedAt)
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field(SystemCreatedBy; Rec.SystemCreatedBy)
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field(SystemId; Rec.SystemId)
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field(SystemModifiedAt; Rec.SystemModifiedAt)
#pragma warning restore AA0218
                {
                }
#pragma warning disable AA0218
                field(SystemModifiedBy; Rec.SystemModifiedBy)
#pragma warning restore AA0218
                {
                }
            }
        }
    }
}