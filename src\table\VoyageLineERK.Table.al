table 60010 "Voyage Line ERK"
{
    Caption = 'Voyage Line';
    DataClassification = CustomerContent;
    DrillDownPageId = "Voyage Lines ERK";
    LookupPageId = "Voyage Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Voyage No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Customer No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
            begin
                Rec."Customer Name" := VoyageMangement.GetCustomerNameFromCustomerNo("Customer No.");
                if Customer.Get(Rec."Customer No.") then
                    Rec.Validate("Currency Code", Customer."Currency Code")
                else
                    Rec.Validate("Currency Code", '');
            end;
        }
        field(4; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(5; "Loader/Exporter No."; Code[20])
        {
            Caption = 'Loader/Exporter No.';
            TableRelation = "Voyage Account ERK"."No.";
            ToolTip = 'Specifies the value of the Loader/Exporter No. field.';
        }
        field(6; "Loader/Exporter Name"; Text[100])
        {
            Caption = 'Loader/Exporter Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Voyage Account ERK".Name where("No." = field("Loader/Exporter No.")));
            ToolTip = 'Specifies the value of the Loader/Exporter Name field.';
        }
        field(7; "Shipment Method Code"; Code[10])
        {
            Caption = 'Shipment Method Code';
            TableRelation = "Shipment Method";
            ToolTip = 'Specifies the value of the Shipment Method Code field.';
        }
        field(8; "Shipper Ship-to Code"; Code[20])
        {
            Caption = 'Shipper Ship-to Code';
            TableRelation = "Voyage Account ERK"."No.";
            ToolTip = 'Specifies the value of the Shipper Ship-to Code field.';
        }
        field(9; "Shipper Ship-to Name"; Text[100])
        {
            Caption = 'Shipper Ship-to Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Voyage Account ERK".Name where("No." = field("Shipper Ship-to Code")));
            ToolTip = 'Specifies the value of the Shipper Ship-to Name field.';
        }
        field(10; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        // field(11; "Revenue Amount"; Decimal)
        // {
        //     Caption = 'Revenue Amount';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Voyage Line Detail ERK"."Line Amount" where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = const(Revenue)));
        // }
        field(12; "Load Type"; Code[50])
        {
            Caption = 'Load Type';
            TableRelation = "Load Type ERK";
            ToolTip = 'Specifies the value of the Load Type field.';
        }
        field(13; "Consignee No."; Code[20])
        {
            Caption = 'Consignee No.';
            TableRelation = "Voyage Account ERK"."No.";
            ToolTip = 'Specifies the value of the Consignee No. field.';
        }
        field(14; "Consignee Name"; Text[100])
        {
            Caption = 'Consignee Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Voyage Account ERK".Name where("No." = field("Consignee No.")));
            ToolTip = 'Specifies the value of the Consignee Name field.';
        }
        // field(15; "Weight (Ton)"; Decimal)
        // {
        //     Caption = 'Weight (Ton)';
        // }
        field(16; "Expense Amount"; Decimal)
        {
            Caption = 'Expense Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Voyage Line Detail ERK"."Line Amount" where("Document No." = field("Document No."), "Document Line No." = field("Line No."), Type = const(Expense)));
            ToolTip = 'Specifies the value of the Revenue Amount field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        VoyageLine: Record "Voyage Line ERK";
    begin
        VoyageLine.SetRange("Document No.", Rec."Document No.");
        if VoyageLine.FindLast() then
            Rec."Line No." := VoyageLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnDelete()
    var
        VoyageLineDetail: Record "Voyage Line Detail ERK";
    begin
        VoyageLineDetail.SetRange("Document No.", Rec."Document No.");
        VoyageLineDetail.SetRange("Document Line No.", Rec."Line No.");
        VoyageLineDetail.DeleteAll(true);
    end;

    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
}
