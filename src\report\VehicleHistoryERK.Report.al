report 60016 "Vehicle History ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle History';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem("Vehicle Transfer Ledger Entry"; "Vehicle Transfer Ledger Entry")
        {
            column(EntryNo_VehicleTransferLedgerEntry; "Entry No.")
            {
            }
            column(FromLocationCode_VehicleTransferLedgerEntry; "From Location Code")
            {
            }
            column(FromBinCode_VehicleTransferLedgerEntry; "From Bin Code")
            {
            }
            column(ToLocationCode_VehicleTransferLedgerEntry; "To Location Code")
            {
            }
            column(ToBinCode_VehicleTransferLedgerEntry; "To Bin Code")
            {
            }
            column(OperationDateTime_VehicleTransferLedgerEntry; "Operation Date-Time")
            {
            }
            column(OperationType_VehicleTransferLedgerEntry; "Operation Type")
            {
            }
            column(SerialNo_VehicleTransferLedgerEntry; "Serial No.")
            {
            }
            column(DocumentNo_VehicleTransferLedgerEntry; "Document No.")
            {
            }
            column(DocumentLineNo_VehicleTransferLedgerEntry; "Document Line No.")
            {
            }
            column(FromBinDescription_VehicleTransferLedgerEntry; "From Bin Description")
            {
            }
            column(ToBinDescription_VehicleTransferLedgerEntry; "To Bin Description")
            {
            }
            column(ShippingAgentCode_VehicleTransferLedgerEntry; "Shipping Agent Code")
            {
            }
            column(SystemCreatedAt_VehicleTransferLedgerEntry; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy_VehicleTransferLedgerEntry; SystemCreatedBy)
            {
            }
            column(SystemId_VehicleTransferLedgerEntry; SystemId)
            {
            }
            column(SystemModifiedAt_VehicleTransferLedgerEntry; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy_VehicleTransferLedgerEntry; SystemModifiedBy)
            {
            }
        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(Processing)
    //         {
    //         }
    //     }
    // }
    var
}