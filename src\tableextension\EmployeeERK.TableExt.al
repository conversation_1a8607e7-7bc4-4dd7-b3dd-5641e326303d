tableextension 60018 "Employee ERK" extends Employee
{
    fields
    {
        field(60000; "Job Title Code ERK"; Code[10])
        {
            Caption = 'Job Title Code';
            TableRelation = "Job Title ERK";
            ToolTip = 'Specifies the value of the Job Title Code field.';
            trigger OnValidate()
            var
                JobTitle: Record "Job Title ERK";
            begin
                JobTitle.Get(Rec."Job Title Code ERK");
                Rec.Validate("Job Title", JobTitle.Description);
            end;
        }
        field(60001; "Position Level Code ERK"; Code[10])
        {
            Caption = 'Position Level Code';
            TableRelation = "Position Level ERK";
            ToolTip = 'Specifies the value of the Position Level Code field.';
            trigger OnValidate()
            var
                PositionLevel: Record "Position Level ERK";
            begin
                PositionLevel.Get("Position Level Code ERK");
                Rec.Validate("Posititon Level Desc. ERK", PositionLevel.Description);
            end;
        }
        field(60002; "Posititon Level Desc. ERK"; Text[100])
        {
            Caption = 'Posititon Level Description';
            ToolTip = 'Specifies the value of the Posititon Level Description field.';
        }
        field(60003; "Department Code Desc. ERK"; Text[50])
        {
            Caption = 'Department Code Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Dimension Value".Name where(Code = field("Global Dimension 1 Code")));
            ToolTip = 'Specifies the value of the Department Code Description field.';
        }
        field(60004; "Manager Full Name ERK"; Text[100])
        {
            Caption = 'Manager Full Name';
            ToolTip = 'Specifies the value of the Manager Full Name field.';
        }
        modify("Manager No.")
        {
            trigger OnAfterValidate()
            var
                Employee: Record Employee;
            begin
                Employee.Get("Manager No.");
                Validate("Manager Full Name ERK", Employee.FullName());
            end;
        }
    }
}
