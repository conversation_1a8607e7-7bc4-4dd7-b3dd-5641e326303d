table 60005 "Export Ledger Entry ERK"
{
    Caption = 'Export Ledger Entry';
    DataClassification = CustomerContent;
    LookupPageId = "Export Ledger Entries ERK";
    DrillDownPageId = "Export Ledger Entries ERK";

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Entry No. field.';
        }
        field(2; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            Editable = false;
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(3; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(4; "Document Type"; Enum "Gen. Journal Document Type")
        {
            Caption = 'Document Type';
            Editable = false;
            ToolTip = 'Specifies the value of the Document Type field.';
        }
        field(5; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer No. field.';
        }
        field(6; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(7; Amount; Decimal)
        {
            Caption = 'Amount';
            Editable = false;
            ToolTip = 'Specifies the value of the Amount field.';
        }
        field(8; "Amount (LCY)"; Decimal)
        {
            Caption = 'Amount (LCY)';
            Editable = false;
            ToolTip = 'Specifies the value of the Amount (LCY) field.';
        }
        field(9; "Amount (ACY)"; Decimal)
        {
            Caption = 'Amount (ACY)';
            Editable = false;
            ToolTip = 'Specifies the value of the Amount (ACY) field.';
        }
        field(10; "Remaining Amout"; Decimal)
        {
            Caption = 'Remaining Amout';
            Editable = false;
            ToolTip = 'Specifies the value of the Remaining Amout field.';
        }
        field(11; "Remaining Amout (LCY)"; Decimal)
        {
            Caption = 'Remaining Amout (LCY)';
            Editable = false;
            ToolTip = 'Specifies the value of the Remaining Amout (LCY) field.';
        }
        field(12; "Remaining Amout (ACY)"; Decimal)
        {
            Caption = 'Remaining Amout (ACY)';
            Editable = false;
            ToolTip = 'Specifies the value of the Remaining Amout (ACY) field.';
        }
        field(13; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        field(14; "Currency Exchange Rate"; Decimal)
        {
            Caption = 'Currency Exchange Rate';
            Editable = false;
            DecimalPlaces = 4 : 4;
            ToolTip = 'Specifies the value of the Currency Exchange Rate field.';
        }
        field(15; "Source Cust. Ledger Entry No."; Integer)
        {
            Caption = 'Source Customer Ledger Entry No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Customer Ledger Entry No. field.';
        }
        field(16; "Amount to Assign"; Decimal)
        {
            Caption = 'Amount to Assign';
            ToolTip = 'Specifies the value of the Amount to Assign field.';

            trigger OnValidate()
            var
                MustBePositiveErr: Label 'Assignable Amount can not be positive.';
            begin
                if Rec."Document Type" = Rec."Document Type"::Payment then begin
                    "Assignable Amount" := "Remaining Amout" + "Amount to Assign";
                    if "Assignable Amount" > 0 then
                        Error(MustBePositiveErr);
                end;
            end;
        }
        field(17; "Assignable Amount"; Decimal)
        {
            Caption = 'Assignable Amount';
            Editable = false;
            ToolTip = 'Specifies the value of the Assignable Amount field.';
        }
        field(18; "Source Export Ledg. Entry No."; Integer)
        {
            Caption = 'Source Export Ledg. Entry No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Export Ledg. Entry No. field.';
        }
        field(19; "Blanket Sales Order No."; Code[20])
        {
            Caption = 'Blanket Sales Order No.';
            TableRelation = "Sales Header"."No." where("Document Type" = const("Blanket Order"), "Sell-to Customer No." = field("Customer No."));
            ToolTip = 'Specifies the value of the Blanket Sales Order No. field.';
        }
        field(20; "Assigned Amount"; Decimal)
        {
            Caption = 'Assigned Amount';
            Editable = false;
            ToolTip = 'Specifies the value of the Assigned Amount field.';
        }
        field(21; "External Document No."; Code[35])
        {
            Caption = 'External Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the External Document No. field.';
        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ExportLedgerEntry: Record "Export Ledger Entry ERK";
    begin
        Rec."Entry No." := 1;
        if ExportLedgerEntry.FindLast() then
            Rec."Entry No." := ExportLedgerEntry."Entry No." + 1
    end;
}
