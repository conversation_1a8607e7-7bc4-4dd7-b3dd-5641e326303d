page 60144 "Port Service List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Port Service List';
    PageType = List;
    SourceTable = "Port Service Header ERK";
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Port Service Card ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Starting Date"; Rec."Starting Date")
                {
                }
                field("Ending Date"; Rec."Ending Date")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Bill-to Customer No."; Rec."Bill-to Customer No.")
                {
                }
                field("Bill-to Customer Name"; Rec."Bill-to Customer Name")
                {
                }
                field("Load Owner Name"; Rec."Load Owner Name")
                {
                }
                field("Load Type"; Rec."Load Type")
                {
                }
                field("Parent Load Type"; Rec."Parent Load Type")
                {
                }
                field("Port Name"; Rec."Port Name")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field("Declared Quantity (Tonnage)"; Rec."Declared Quantity (Tonnage)")
                {
                }
                field("Actual Quantity (Tonnage)"; Rec."Actual Quantity (Tonnage)")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies when the record was created.';
                }
            }
        }
    }
}