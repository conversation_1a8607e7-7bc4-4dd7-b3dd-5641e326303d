pageextension 60007 "Item Ledger Entries ERK" extends "Item Ledger Entries"
{
    layout
    {
        modify("Variant Code")
        {
            Visible = true;
        }
        addafter(Description)
        {
            field("Item Category Code ERK"; Rec."Item Category Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Item Category Code field.';
            }
        }
        addafter("Invoiced Quantity")
        {
            field("Invoiced Qty. By Date ERK"; Rec."Invoiced Qty. By Date ERK")
            {
                ApplicationArea = All;
            }
        }
        addafter("Document No.")
        {
            field("E-Export Invoice No. ERK"; Rec."E-Export Invoice No. ERK")
            {
                ApplicationArea = All;
            }
        }
    }
}
