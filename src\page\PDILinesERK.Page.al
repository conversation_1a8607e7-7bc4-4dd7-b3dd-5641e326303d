page 60150 "PDI Lines ERK"
{
    ApplicationArea = All;
    Caption = 'PDI Lines';
    PageType = List;
    SourceTable = "PDI Line ERK";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field(Code; Rec.Code)
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Indent; Rec.Indent)
                {
                }
                field(Result; Rec.Result)
                {
                }
                field(Comment; Rec.Comment)
                {
                }
                field("Parent Code"; Rec."Parent Code")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Operation Starting Date-Time"; Rec."Operation Starting Date-Time")
                {
                }
                field("Operation Ending Date-Time"; Rec."Operation Ending Date-Time")
                {
                }
                field("Responsible Name"; Rec."Responsible Name")
                {
                }
                field("Brand Code"; Rec."Brand Code")
                {
                }
                field("Model Code"; Rec."Model Code")
                {
                }
                field("Color Name"; Rec."Color Name")
                {
                }
                field("Fuel Type"; Rec."Fuel Type")
                {
                }
                field("Header Notes"; Rec."Header Notes")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                }
                field(Saved; Rec.Saved)
                {
                }
                field("Saved Atleast Once"; Rec."Saved Atleast Once")
                {
                }
            }
        }
    }
}