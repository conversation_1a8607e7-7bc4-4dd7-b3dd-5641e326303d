table 60053 "Report Sending Setup ERK"
{
    Caption = 'Report Sending Setup';
    DataClassification = CustomerContent;
    DataPerCompany = false;
    DrillDownPageId = "Report Sending Setup ERK";
    LookupPageId = "Report Sending Setup ERK";

    fields
    {
        field(1; "Report ID"; Integer)
        {
            Caption = 'Report ID';
            TableRelation = AllObjWithCaption."Object ID" where("Object Type" = const(Report));
            ToolTip = 'Specifies the value of the Report ID field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Report Name")
            end;
        }
        field(2; "E-Mail Address"; Text[2048])
        {
            Caption = 'E-Mail Address';
            ToolTip = 'Specifies the value of the E-Mail Address field.';
        }
        field(3; "Company Name"; Text[30])
        {
            Caption = 'Company Name';
            TableRelation = Company.Name;
            ToolTip = 'Specifies the value of the Company Name field.';
        }
        field(4; "Report Name"; Text[249])
        {
            Caption = 'Report Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(AllObjWithCaption."Object Caption" where("Object ID" = field("Report ID"), "Object Type" = const(Report)));
            ToolTip = 'Specifies the value of the Report Name field.';
        }
        field(5; Parameters; Text[2048])
        {
            Caption = 'Parameters';
            ToolTip = 'Specifies the value of the Parameters field.';
        }
        field(6; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            AllowInCustomizations = Always;
        }
        field(7; "Report Export Type"; Enum "Report Inbox Output Type")
        {
            Caption = 'Report Export Type';
            ToolTip = 'Specifies the value of the Report Export Type field.';
            ValuesAllowed = 0, 1, 2;

        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ReportSendingSetup: Record "Report Sending Setup ERK";
    begin
        if ReportSendingSetup.FindLast() then
            Rec."Entry No." := ReportSendingSetup."Entry No." + 1
        else
            Rec."Entry No." := 1;
    end;
}
