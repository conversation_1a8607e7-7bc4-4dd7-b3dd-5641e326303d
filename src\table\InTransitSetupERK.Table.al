table 60073 "In-Transit Setup ERK"
{
    Caption = 'In-Transit Setup';
    DataClassification = CustomerContent;
    LookupPageId = "In-Transit Setup ERK";
    DrillDownPageId = "In-Transit Setup ERK";

    fields
    {
        field(1; "Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Operation Type';
            ToolTip = 'Specifies the value of the Operation Type field.';
        }
        field(2; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(3; "In-Transit Bin Code"; Code[20])
        {
            Caption = 'In-Transit Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Location Code"), "In-Transit Bin ERK" = const(true));
            ToolTip = 'Specifies the value of the In-Transit Bin Code field.';
        }
    }
    keys
    {
        key(PK; "Operation Type", "In-Transit Bin Code", "Location Code")
        {
            Clustered = true;
        }
    }
}