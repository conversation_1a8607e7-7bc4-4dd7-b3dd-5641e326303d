page 60037 "Port Operation Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Port Operation Subpage';
    PageType = ListPart;
    SourceTable = "Port Operation Line ERK";
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Customer No."; Rec."Bill-to Customer No.")
                {
                    ShowMandatory = true;
                }
                field("Customer Name"; Rec."Bill-to Customer Name")
                {
                }
                field("Load Owner No."; Rec."Load Owner No.")
                {
                }
                field("Load Owner Name"; Rec."Load Owner Name")
                {
                }
                field("Contract No."; Rec."Contract No.")
                {
                }
                field("Parent Load Type"; Rec."Parent Load Type")
                {
                    ShowMandatory = true;
                }
                field("Load Type"; Rec."Sub Load Type")
                {
                    ShowMandatory = true;
                }
                field("Sub Load Type"; Rec."Variant Code")
                {
                    ShowMandatory = true;
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                    ShowMandatory = true;
                }
                field("Department Name"; Rec."Department Name")
                {
                }
                field("Total Entry Quantity"; Rec."Total Entry Quantity")
                {
                }
                field("Total Transfer Quantity"; Rec."Total Transfer Quantity")
                {
                }
                field("Total Exit Quantity"; Rec."Total Exit Quantity")
                {
                }
                field("Total Scrap Quantity"; Rec."Total Scrap Quantity")
                {
                }
                field("Remaining Quantity"; Rec."Remaining Quantity")
                {
                }
                // field(Tonnage; Rec.Tonnage)
                // {
                //     ToolTip = 'Specifies the value of the Tonnage field.';
                // }
                // field("Entry Quantity"; PortOperationManagement.CalculateEntryQuantityFromPortOperationLine(Rec))
                // {
                //     Caption = 'Entry Quantity';
                //     ToolTip = 'Specifies the value of the Entry Quantity field.';
                // }
                field("Revenue Amount (ACY)"; Rec."Revenue Amount (ACY)")
                {
                }
                field("Expense Amount (ACY)"; Rec."Expense Amount (ACY)")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(PortOperationLineDetails)
            {
                Caption = 'Port Operation Line Details';
                Image = GetLines;
                RunObject = page "Port Operation Line DetailsERK";
                RunPageLink = "Document No." = field("Document No."), "Document Line No." = field("Line No.");
                ToolTip = 'Executes the Port Operation Line Details action.';
            }
        }
    }
    var
    //PortOperationManagement: Codeunit "Port Operation Management ERK";
}
