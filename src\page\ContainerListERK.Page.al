page 60008 "Container List ERK"
{
    ApplicationArea = ExportManagementERK;
    Caption = 'Container List';
    PageType = List;
    SourceTable = "Container Header ERK";
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Container Card ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Export No."; Rec."Export No.")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field("E-Export No."; Rec."E-Export No.")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Total Box Quantity"; Rec."Total Box Quantity")
                {
                }
                field("Total Gross Weight (KG)"; Rec."Total Gross Weight (KG)")
                {
                }
                field("Total Net Weight (KG)"; Rec."Total Net Weight (KG)")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(ExportCard)
            {
                ApplicationArea = All;
                Caption = 'Export Card';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Card;
                PromotedOnly = true;
                ToolTip = 'Executes the Export Card action.';

                trigger OnAction()
                var
                    ExportHeader: Record "Export Header ERK";
                begin
                    ExportHeader.Get(Rec."Export No.");
                    Page.Run(Page::"Export Card ERK", ExportHeader)
                end;
            }
        }
        area(Reporting)
        {
            action(PackingList)
            {
                ApplicationArea = All;
                Caption = 'Packing List';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PrintReport;
                PromotedOnly = true;
                ToolTip = 'Executes the Packing List action.';

                trigger OnAction()
                begin
                    Rec.SetRecFilter();
                    Report.Run(Report::"Packing List ERK", true, true, Rec);
                end;
            }
        }
    }
}
