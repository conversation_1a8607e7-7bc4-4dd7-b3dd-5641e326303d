page 60065 "Vehicle Transfer Lines - Edit"
{
    ApplicationArea = All;
    Caption = 'Vehicle Transfer Lines - Edit';
    PageType = List;
    SourceTable = "Vehicle Transfer Line ERK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("From Location Code"; Rec."From Location Code")
                {
                }
                field("From Bin Code"; Rec."From Bin Code")
                {
                }
                field("To Location Code"; Rec."To Location Code")
                {
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                }
                field(Processed; Rec.Processed)
                {
                }
                field("Processed By"; Rec."Processed By")
                {
                }
                field("Processed At"; Rec."Processed At")
                {
                }
                field("Vehicle Trnsfr Ledg. Entry No."; Rec."Vehicle Trnsfr Ledg. Entry No.")
                {
                }
                field("In-Transit"; Rec."In-Transit")
                {
                }
                field("Operation Type"; Rec."Operation Type")
                {
                }
                field(TSE; Rec.TSE)
                {
                }
                field(Comment; Rec.Comment)
                {
                }
                field("Header To Location Code"; Rec."Header To Location Code")
                {
                }
                field("Header To Bin Code"; Rec."Header To Bin Code")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
            }
        }
    }
}
