table 60001 "Export Header ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Export Header';
    DrillDownPageId = "Export List ERK";
    LookupPageId = "Export List ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the Export No. field.';

            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                //NoSeries: Codeunit NoSeriesManagement;
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Export No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Blanket Sales Order No."; Code[20])
        {
            Caption = 'Blanket Sales Order No.';
            TableRelation = "Sales Header"."No." where("Document Type" = const("Blanket Order"));
            ToolTip = 'Specifies the value of the Blanket Sales Order No. field.';

            trigger OnLookup()
            var
                SalesHeader: Record "Sales Header";
            begin
                SalesHeader.Get(SalesHeader."Document Type"::"Blanket Order", "Blanket Sales Order No.");
                //Page.Run(Page::"Blanket Sales Order", SalesHeader);
                PageManagement.PageRun(SalesHeader);
            end;
        }
        field(3; "Currency Exchange Rate (ACY)"; Decimal)
        {
            Caption = 'Currency Exchange Rate (ACY)';
            DecimalPlaces = 4 : 4;
            ToolTip = 'Specifies the value of the Currency Exchange Rate (ACY) field.';
        }
        field(4; "Sales Order No."; Code[20])
        {
            Caption = 'Sales Order No.';
            TableRelation = "Sales Header"."No." where("Document Type" = const(Order));
            ToolTip = 'Specifies the value of the Sales Order No. field.';

            trigger OnLookup()
            var
                SalesHeader: Record "Sales Header";
            begin
                SalesHeader.Get(SalesHeader."Document Type"::Order, "Sales Order No.");
                //Page.Run(Page::"Sales Order", SalesHeader);
                PageManagement.PageRun(SalesHeader);
            end;
        }
        field(5; "Curr. Exchange Rate Date (ACY)"; Date)
        {
            Caption = 'Currency Exchange Rate Date (ACY)';
            ToolTip = 'Specifies the value of the Currency Exchange Rate Date (ACY) field.';
        }
        field(6; Completed; Boolean)
        {
            Caption = 'Completed';
            ToolTip = 'Specifies the value of the Completed field.';
        }
        field(7; "Loading Date"; Date)
        {
            Caption = 'Loading Date';
            ToolTip = 'Specifies the value of the Loading Date field.';
        }
        field(8; "Total Sales Amount"; Decimal)
        {
            Caption = 'Total Sales Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Export Line ERK"."Sales Line Amount" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Export Amount field.';
        }
        field(9; "Document Date"; Date)
        {
            Caption = 'Document Date';
            Editable = false;
            ToolTip = 'Specifies the value of the Document Date field.';
        }
        field(10; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Customer No. field.';
        }
        field(11; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Customer No.")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(12; "Blanket Order Date"; Date)
        {
            Caption = 'Blanket Order Date';
            ToolTip = 'Specifies the value of the Blanket Order Date field.';
        }
        field(13; "Sales Due Date"; Date)
        {
            Caption = 'Sales Due Date';
            ToolTip = 'Specifies the value of the Sales Due Date field.';
        }
        field(14; "Sales Shipment Method Code"; Code[10])
        {
            Caption = 'Sales Shipment Method Code';
            TableRelation = "Shipment Method".Code;
            ToolTip = 'Specifies the value of the Sales Shipment Method Code field.';
        }
        field(15; "Sales Payment Method Code"; Code[10])
        {
            Caption = 'Sales Payment Method Code';
            TableRelation = "Payment Method".Code;
            ToolTip = 'Specifies the value of the Sales Payment Method Code field.';
        }
        field(16; "Sales Currency Code"; Code[10])
        {
            Caption = 'Sales Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        field(22; "Notify Ship-to Code"; Code[10])
        {
            Caption = 'Notify Ship-to Code';
            TableRelation = "Ship-to Address".Code where("Customer No." = field("Customer No."));
            ToolTip = 'Specifies the value of the Notify Ship-to Code field.';

            trigger OnValidate()
            var
                ShiptoAddress: Record "Ship-to Address";
            begin
                if not ShiptoAddress.Get("Customer No.", "Notify Ship-to Code") then begin
                    "Notify Ship-to Name" := '';
                    "Notify Ship-to Name 2" := '';
                    "Notify Ship-to Address" := '';
                    "Notify Ship-to Address 2" := '';
                    "Notify Ship-to County" := '';
                    "Notify Ship-to City" := '';
                    "Notify Ship-to Country/Region" := '';
                end
                else begin
                    "Notify Ship-to Name" := ShiptoAddress.Name;
                    "Notify Ship-to Name 2" := ShiptoAddress."Name 2";
                    "Notify Ship-to Address" := ShiptoAddress.Address;
                    "Notify Ship-to Address 2" := ShiptoAddress."Address 2";
                    "Notify Ship-to County" := ShiptoAddress.County;
                    "Notify Ship-to City" := ShiptoAddress.City;
                    "Notify Ship-to Country/Region" := ShiptoAddress."Country/Region Code";
                end;
            end;
        }
        field(23; "Consignee Ship-to Code"; Code[10])
        {
            Caption = 'Consignee Ship-to Code';
            TableRelation = "Ship-to Address".Code where("Customer No." = field("Customer No."));
            ToolTip = 'Specifies the value of the Consignee Ship-to Code field.';

            trigger OnValidate()
            var
                ShiptoAddress: Record "Ship-to Address";
            begin
                if not ShiptoAddress.Get("Customer No.", "Consignee Ship-to Code") then begin
                    "Consignee Ship-to Name" := '';
                    "Consignee Ship-to Name 2" := '';
                    "Consignee Ship-to Address" := '';
                    "Consignee Ship-to Address 2" := '';
                    "Consignee Ship-to County" := '';
                    "Consignee Ship-to City" := '';
                    "Consignee Ship-to Country" := '';
                end
                else begin
                    "Consignee Ship-to Name" := ShiptoAddress.Name;
                    "Consignee Ship-to Name 2" := ShiptoAddress."Name 2";
                    "Consignee Ship-to Address" := ShiptoAddress.Address;
                    "Consignee Ship-to Address 2" := ShiptoAddress."Address 2";
                    "Consignee Ship-to County" := ShiptoAddress.County;
                    "Consignee Ship-to City" := ShiptoAddress.City;
                    "Consignee Ship-to Country" := ShiptoAddress."Country/Region Code";
                end;
            end;
        }
        field(24; "Notify Ship-to Name"; Text[100])
        {
            Caption = 'Notify Ship-to Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Notify Ship-to Name field.';
        }
        field(25; "Consignee Ship-to Name"; Text[100])
        {
            Caption = 'Consignee Ship-to Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Consignee Ship-to Name field.';
        }
        field(26; "Notify Ship-to Name 2"; Text[100])
        {
            Caption = 'Notify Ship-to Name 2';
            Editable = false;
            ToolTip = 'Specifies the value of the Notify Ship-to Name 2 field.';
        }
        field(27; "Consignee Ship-to Name 2"; Text[100])
        {
            Caption = 'Consignee Ship-to Name 2';
            Editable = false;
            ToolTip = 'Specifies the value of the Consignee Ship-to Name 2 field.';
        }
        field(28; "Notify Ship-to Address"; Text[100])
        {
            Caption = 'Notify Ship-to Address';
            Editable = false;
            ToolTip = 'Specifies the value of the Notify Ship-to Address field.';
        }
        field(29; "Consignee Ship-to Address"; Text[100])
        {
            Caption = 'Consignee Ship-to Address';
            Editable = false;
            ToolTip = 'Specifies the value of the Consignee Ship-to Address field.';
        }
        field(30; "Notify Ship-to Address 2"; Text[50])
        {
            Caption = 'Notify Ship-to Address 2';
            Editable = false;
            ToolTip = 'Specifies the value of the Notify Ship-to Address 2 field.';
        }
        field(31; "Consignee Ship-to Address 2"; Text[50])
        {
            Caption = 'Consignee Ship-to Address 2';
            Editable = false;
            ToolTip = 'Specifies the value of the Consignee Ship-to Address 2 field.';
        }
        field(32; "Notify Ship-to City"; Text[30])
        {
            Caption = 'Notify Ship-to City';
            Editable = false;
            ToolTip = 'Specifies the value of the Notify Ship-to City field.';
        }
        field(33; "Consignee Ship-to City"; Text[30])
        {
            Caption = 'Consignee Ship-to City';
            Editable = false;
            ToolTip = 'Specifies the value of the Consignee Ship-to City field.';
        }
        field(34; "Notify Ship-to County"; Text[30])
        {
            Caption = 'Notify Ship-to County';
            Editable = false;
            ToolTip = 'Specifies the value of the Notify Ship-to County field.';
        }
        field(35; "Consignee Ship-to County"; Text[30])
        {
            Caption = 'Consignee Ship-to County';
            Editable = false;
            ToolTip = 'Specifies the value of the Consignee Ship-to County field.';
        }
        field(36; "Notify Ship-to Country/Region"; Code[10])
        {
            Caption = 'Notify Ship-to Country/Region Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Notify Ship-to Country/Region Code field.';
        }
        field(37; "Consignee Ship-to Country"; Code[10])
        {
            Caption = 'Consignee Ship-to Country/Region Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Consignee Ship-to Country/Region Code field.';
        }
        field(38; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. Series field.';
        }
        field(39; "Country of Departure"; Code[10])
        {
            Caption = 'Country of Departure';
            TableRelation = "Country/Region".Code;
            ToolTip = 'Specifies the value of the Country of Departure field.';
        }
        field(40; "Port of Departure"; Code[10])
        {
            Caption = 'Port of Departure';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Port of Departure field.';
        }
        field(41; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(43; "Shipping Agent"; Text[100])
        {
            Caption = 'Shipping Agent';
            ToolTip = 'Specifies the value of the Shipping Agent field.';
        }
        field(44; "Country of Arrival"; Code[10])
        {
            Caption = 'Country of Arrival';
            TableRelation = "Country/Region".Code;
            ToolTip = 'Specifies the value of the Country of Arrival field.';
        }
        field(45; "Port of Arrival"; Code[10])
        {
            Caption = 'Port of Arrival';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Port of Arrival field.';
        }
        field(46; "Transport Method"; Code[10])
        {
            Caption = 'Transport Method';
            ToolTip = 'Specifies the value of the Transport Method field.';
        }
        field(47; "Estimated Time of Delivery"; Date)
        {
            Caption = 'Estimated Time of Delivery';
            ToolTip = 'Specifies the value of the Estimated Time of Delivery field.';
        }
        field(48; "Estimated Time of Departure"; Date)
        {
            Caption = 'Estimated Time of Departure';
            ToolTip = 'Specifies the value of the Estimated Time of Departure field.';
        }
        field(49; "Bill of Lading No."; Code[20])
        {
            Caption = 'Bill of Lading No.';
            ToolTip = 'Specifies the value of the Bill of Lading No. field.';
        }
        field(50; "Booking No."; Code[20])
        {
            Caption = 'Booking No.';
            ToolTip = 'Specifies the value of the Booking No. field.';
        }
        field(51; "Load Description"; Text[250])
        {
            Caption = 'Load Description';
            ToolTip = 'Specifies the value of the Load Description field.';
        }
        field(52; "Posted Sales Invoice No."; Code[20])
        {
            Caption = 'Posted Sales Invoice No.';
            ToolTip = 'Specifies the value of the Posted Sales Invoice No. field.';

            trigger OnLookup()
            var
                SalesInvoiceHeader: Record "Sales Invoice Header";
            begin
                SalesInvoiceHeader.Get("Posted Sales Invoice No.");
                //Page.Run(Page::"Posted Sales Invoice", SalesInvoiceHeader)
                PageManagement.PageRun(SalesInvoiceHeader);
            end;
        }
        field(53; "Export Invoice Amount"; Decimal)
        {
            Caption = 'Export Invoice Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Export Line ERK"."Sales Line Amount" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Export Invoice Amount field.';
        }
        field(54; "Blanket Sales Order Amount"; Decimal)
        {
            Caption = 'Blanket Sales Order Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Sales Line"."Amount Including VAT" where("Document Type" = const("Blanket Order"), "Document No." = field("Blanket Sales Order No.")));
            ToolTip = 'Specifies the value of the Blanket Sales Order Amount field.';
        }
        field(55; "Creation Order"; Integer)
        {
            Caption = 'Creation Order';
            ToolTip = 'Specifies the value of the Creation Order field.';
        }
        field(56; "Container Quantity"; Integer)
        {
            Caption = 'Container Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Container Header ERK" where("Export No." = field("No.")));
            ToolTip = 'Specifies the value of the Container Quantity field.';
        }
        field(57; "Total Assigned Amount"; Decimal)
        {
            Caption = 'Total Assigned Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Export Ledger Entry ERK"."Assigned Amount" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Assigned Amount field.';
        }
        field(58; "Bank Account Name"; Text[100])
        {
            Caption = 'Bank Account Name';
            ToolTip = 'Specifies the value of the Bank Account Name field.';
        }
        field(59; "Branch Name"; Text[100])
        {
            Caption = 'Branch Name';
            ToolTip = 'Specifies the value of the Branch Name field.';
        }
        field(60; IBAN; Code[50])
        {
            Caption = 'IBAN';
            ToolTip = 'Specifies the value of the IBAN field.';
        }
        field(61; "SWIFT Code"; Code[20])
        {
            Caption = 'SWIFT Code';
            ToolTip = 'Specifies the value of the SWIFT Code field.';
        }
        field(62; "Sales Payment Terms Code"; Code[10])
        {
            Caption = 'Sales Payment Terms Code';
            ToolTip = 'Specifies the value of the Sales Payment Terms Code field.';
        }
        field(63; "E-Export No."; Code[20])
        {
            Caption = 'E-Export No.';
            ToolTip = 'Specifies the value of the E-Export No. field.';
        }
        field(17; "MCT Actual Export Date"; Date)
        {
            Caption = 'MCT Actual Export Date';
            AllowInCustomizations = Never;
        }
        field(18; "Customs Declaration No."; Code[30])
        {
            Caption = 'Customs Declaration No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Customs Declaration No. field.';
        }
        field(65; "Actual Export Date FlowField"; Date)
        {
            Caption = 'Actual Export Date';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("E-Invoice Header INF"."Export Date" where("E-Invoice No." = field("E-Export No.")));
            ToolTip = 'Specifies the value of the Actual Export Date field.';
        }
        field(66; "Customs Declaration No. FF"; Code[30])
        {
            Caption = 'Customs Declaration No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("E-Invoice Header INF"."MCT Registration No." where("E-Invoice No." = field("E-Export No.")));
            ToolTip = 'Specifies the value of the Customs Declaration No. field.';
        }
        field(19; "Payment Amount"; Decimal)
        {
            Caption = 'Payment Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Export Ledger Entry ERK"."Assigned Amount" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Payment Amount field.';
        }
        field(20; "Port of Departure Description"; Text[100])
        {
            Caption = 'Port of Departure Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Port of Departure")));
            ToolTip = 'Specifies the value of the Port of Departure Description field.';
        }
        field(21; "Port of Arrival Description"; Text[100])
        {
            Caption = 'Port of Arrival Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Port of Arrival")));
            ToolTip = 'Specifies the value of the Port of Arrival Description field.';
        }
        field(42; "Vendor No."; Code[20])
        {
            Caption = 'Vendor No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Export Line ERK"."Vendor No." where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Vendor No. field.';
        }
        field(64; "Vendor Name"; Text[100])
        {
            Caption = 'Vendor Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Vendor No.")));
            ToolTip = 'Specifies the value of the Vendor Name field.';
        }
        field(67; "Vendor Invoice No."; Code[35])
        {
            Caption = 'Vendor Invoice No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Vendor Invoice No. field.';
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        //NoSeriesManagement: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Export No. Series");
            //NoSeriesManagement.InitSeries(ErkHoldingSetup."Export No. Series", xRec."No. Series", 0D, "No.", "No. Series");
            "No. Series" := ErkHoldingSetup."Export No. Series";
            if NoSeries.AreRelated(ErkHoldingSetup."Export No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;
        Rec.Validate("Document Date", WorkDate());
        Rec.Validate("Loading Date", WorkDate());
    end;

    trigger OnDelete()
    var
        ExportLine: Record "Export Line ERK";
        ContainerHeader: Record "Container Header ERK";
    begin
        Rec.TestField(Completed, false);
        ExportLine.SetRange("Document No.", Rec."No.");
        ExportLine.DeleteAll(true);
        ContainerHeader.SetRange("Export No.", Rec."No.");
        ContainerHeader.DeleteAll(true);
    end;

    var
        PageManagement: Codeunit "Page Management";
}
