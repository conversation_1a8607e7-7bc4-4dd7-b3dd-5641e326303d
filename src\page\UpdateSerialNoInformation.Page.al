page 60016 "Update Serial No. Information"
{
    ApplicationArea = All;
    Caption = 'Update Serial No. Information';
    PageType = List;
    SourceTable = "Serial No. Information";
    UsageCategory = Tasks;
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Serial No."; Rec."Serial No.")
                {
                    ToolTip = 'Specifies this number from the Tracking Specification table when a serial number information record is created.';
                }
                field("Manufacturer Code ERK"; Rec."Brand Code ERK")
                {
                    ToolTip = 'Specifies the value of the Manufacturer Code field.';
                }
                field("Model Version ERK"; Rec."Model Version ERK")
                {
                }
                field("Carline Code ERK"; Rec."Model Code ERK")
                {
                    ToolTip = 'Specifies the value of the Model Group field.';
                }
                field("Colour Name ERK"; Rec."Colour Name ERK")
                {
                }
                field("Fuel Type ERK"; Rec."Fuel Type ERK")
                {
                }
                field("Engine ID ERK"; Rec."Engine ID ERK")
                {
                }
                field("Gross Weight (KG) ERK"; Rec."Gross Weight (KG) ERK")
                {
                }
                field("Footprint (m2) ERK"; Rec."Footprint (m2) ERK")
                {
                }
                field("Customs Declaration No. ERK"; Rec."Customs Declaration No. ERK")
                {
                }
                field("Customs Dec. Line No. ERK"; Rec."Customs Dec. Line No. ERK")
                {
                }
                field("Customs Registration Date ERK"; Rec."Customs Registration Date ERK")
                {
                }
                field("Summary Declaration No. ERK"; Rec."Summary Declaration No. ERK")
                {
                }
                field("Bill of Lading No. ERK"; Rec."Bill of Lading No. ERK")
                {
                }
                field("Truck Plate ERK"; Rec."Truck Plate ERK")
                {
                    ToolTip = 'Specifies the truck plate number associated with this serial number.';
                }
                field(Blocked; Rec.Blocked)
                {
                    ToolTip = 'Specifies whether the serial number information is blocked.';
                    Visible = false;
                }
                field("Grupage No. ERK"; Rec."Grupage No. ERK")
                {
                }
                field("Grupage Date ERK"; Rec."Grupage Date ERK")
                {
                }
                field("Grupage Location Code ERK"; Rec."Grupage Location Code ERK")
                {
                }
                field("Grupage Bin Code ERK"; Rec."Grupage Bin Code ERK")
                {
                }
                field("Grupage Ship-to Name ERK"; Rec."Grupage Ship-to Name ERK")
                {
                }
                field("Grupage Ship-to Address ERK"; Rec."Grupage Ship-to Address ERK")
                {
                }
                field("Grupage Ship-to City ERK"; Rec."Grupage Ship-to City ERK")
                {
                }
                field("Print Grupage Label ERK"; Rec."Print Grupage Label ERK")
                {
                    ToolTip = 'Specifies whether the Grupage Label is printed.';
                }
                field("Note to Add ERK"; Rec."Note to Add ERK")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Update)
            {
                Caption = 'Update';
                Promoted = true;
                PromotedCategory = Process;
                Image = UpdateDescription;
                ToolTip = 'Update the serial number information.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    SerialNoManagement.UpdateSerialNoInformations(Rec);
                    CurrPage.Close();
                end;
            }
            action(MarkTSE)
            {
                Caption = 'Mark TSE';
                Promoted = true;
                PromotedCategory = Process;
                Image = CheckList;
                ToolTip = 'Mark the serial number information as TSE.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    SerialNoManagement.MarkTSE(Rec);
                    CurrPage.Close();
                end;
            }
            action(MarkNavigation)
            {
                Caption = 'Mark Navigation';
                Promoted = true;
                PromotedCategory = Process;
                Image = CheckList;
                ToolTip = 'Mark the serial number information as Navigation.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    SerialNoManagement.MarkNavigation(Rec);
                    CurrPage.Close();
                end;
            }
            action(MarkCommercialBlockage)
            {
                Caption = 'Mark Commercial Blockage';
                Promoted = true;
                PromotedCategory = Process;
                Image = CheckList;
                ToolTip = 'Mark the serial number information as Commercial Blockage.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    SerialNoManagement.MarkCommercialBlockage(Rec);
                    CurrPage.Close();
                end;
            }
            // action(MarkChargeCable)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Mark Charge Cable Should Exist';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     Image = CheckList;
            //     ToolTip = 'Mark the serial number information as Charge Cable Should Exist.';
            //     PromotedOnly = true;
            //     trigger OnAction()
            //     begin
            //         SerialNoManagement.MarkChargeCable(Rec);
            //         CurrPage.Close();
            //     end;
            // }
        }
    }
    trigger OnNewRecord(BelowxRec: Boolean)
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
    begin
        ErkHoldingSetup.GetRecordOnce();
        Rec."Item No." := ErkHoldingSetup."Item No. for Vehicles";
    end;

    var
        SerialNoManagement: Codeunit "Serial No. Management ERK";
}
