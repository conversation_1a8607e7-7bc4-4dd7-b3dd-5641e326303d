page 60097 "Contract Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Contract Subpage';
    PageType = ListPart;
    SourceTable = "Contract ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Version No."; Rec."Version No.")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Starting Date-Time"; Rec."Starting Date-Time")
                {
                }
                field("Ending Date-Time"; Rec."Ending Date-Time")
                {
                }
                field("Daily Hire"; Rec."Daily Hire")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Pay-to Vendor No."; Rec."Pay-to Vendor No.")
                {
                }
                field("Owner No."; Rec."Owner No.")
                {
                }
                field(Active; Rec.Active)
                {
                }
                field("Weather Limit (BF/DG)"; Rec."Weather Limit (BF/DG)")
                {
                }
                field("Hire Period"; Rec."Hire Period")
                {
                }
                field("Invoice Period"; Rec."Invoice Period")
                {
                }
                field("Contract Ending Date-Time"; Rec."Contract Ending Date-Time")
                {
                }
                field("Extend Option"; Rec."Extend Option")
                {
                }
                field("Description S. Consumption"; Rec."Description S. Consumption")
                {
                }
                field("Description Speed"; Rec."Description Speed")
                {
                }
                field("Hourly Hire"; Rec."Hourly Hire")
                {
                }
                field("Owner Name"; Rec."Owner Name")
                {
                }
                field("Pay-to Vendor Name"; Rec."Pay-to Vendor Name")
                {
                }
            }
        }
    }
}
