page 60080 "Car Carrier Order ERK"
{
    ApplicationArea = All;
    Caption = 'Car Carrier Order';
    PageType = Document;
    SourceTable = "Car Carrier Order Header ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                field(Status; Rec.Status)
                {
                    //Editable = false;
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ShowMandatory = true;
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Order Date"; Rec."Order Date")
                {
                    ShowMandatory = true;
                }
                field(Type; Rec."Type")
                {
                    ShowMandatory = true;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Contract No."; Rec."Contract No.")
                {
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                field("Your Reference"; Rec."Your Reference")
                {
                }
                field("Loading Port Code"; Rec."Loading Port Code")
                {
                    ShowMandatory = true;
                }
                field("Loading Port Description"; Rec."Loading Port Description")
                {
                }
                field("Loading Port Shipment Method"; Rec."Loading Port Shipment Method")
                {
                    ShowMandatory = true;
                }
                field("Discharge Port Code"; Rec."Discharge Port Code")
                {
                    ShowMandatory = true;
                }
                field("Discharge Port Description"; Rec."Discharge Port Description")
                {
                }
                field("Discharge Port Shipment Method"; Rec."Discharge Port Shipment Method")
                {
                    ShowMandatory = true;
                }
                field("Trans Shipment Allowed"; Rec."Trans Shipment Allowed")
                {
                }
                field("Transshipment Port"; Rec."Transshipment Port")
                {
                }
                field("BoL Type"; Rec."BoL Type")
                {
                }
                field("Ready-to Load Date"; Rec."Ready-to Load Date")
                {
                    ShowMandatory = true;
                }
                field("Shipment Method Code"; Rec."Shipment Method Code")
                {
                }
            }
            part(Lines; "Car Carrier Order Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
            // part(LoadDetails; "Car Carrier Order Line Dtl ERK")
            // {
            //     Caption = 'Load Details';
            //     SubPageLink = "Document No." = field("No.");
            //     UpdatePropagation = Both;
            // }
        }
    }
    actions
    {
        area(Processing)
        {
            action(DocAttach)
            {
                ApplicationArea = Basic, Suite;
                Caption = 'Attachments';
                Image = Attach;
                ToolTip = 'Add a file as an attachment. You can attach images as well as documents.';

                trigger OnAction()
                var
                    DocumentAttachmentDetails: Page "Document Attachment Details";
                    RecRef: RecordRef;
                begin
                    RecRef.GetTable(Rec);
                    DocumentAttachmentDetails.OpenForRecRef(RecRef);
                    DocumentAttachmentDetails.RunModal();
                end;
            }
        }
    }
    trigger OnClosePage()
    // var
    //     CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
    //     CompleteHeader: Boolean;
    begin
        // CarCarrierOrderLine.SetRange("Document No.", Rec."No.");
        // if CarCarrierOrderLine.IsEmpty then begin
        //     Rec.Validate(Status, Rec.Status::" ");
        //     Rec.Modify(true);
        // end
        // else begin
        //     CompleteHeader := true;
        //     CarCarrierOrderLine.FindSet();
        //     repeat
        //         if CarCarrierOrderLine.Status <> CarCarrierOrderLine.Status::Discharged then
        //             CompleteHeader := false;
        //     until CarCarrierOrderLine.Next() = 0;
        //     if CompleteHeader then begin
        //         Rec.Validate(Status, Rec.Status::Completed);
        //         Rec.Modify(true);
        //     end;
        // end;
        CarCarrierOrderMngt.SetStatusCompleted(Rec);
    end;

    var
        CarCarrierOrderMngt: Codeunit "Car Carrier Order Mngt. ERK";
}
