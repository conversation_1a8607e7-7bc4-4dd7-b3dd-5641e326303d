table 60008 "Vehicle Transfer Header ERK"
{
    DataClassification = SystemMetadata;
    Caption = 'Vehicle Transfer Header';
    DrillDownPageId = "Vehicle Transfers ERK";
    LookupPageId = "Vehicle Transfers ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                ErkHoldingSetup: Record "Erk Holding Setup ERK";
                //NoSeries: Codeunit NoSeriesManagement;
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    ErkHoldingSetup.Get();
                    NoSeries.TestManual(ErkHoldingSetup."Vehicle Transfer No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Operation Type';
            ToolTip = 'Specifies the value of the Operation Type field.';
            trigger OnValidate()
            begin
                VehicleTransferManagement.OnAfterValidateOperationType(Rec);
            end;
        }
        field(3; "Car Carrier No."; Code[20])
        {
            Caption = 'Car Carrier No.';
            TableRelation = "Car Carrier Header ERK"."No.";
            ToolTip = 'Specifies the value of the Car Carrier No. field.';
            trigger OnValidate()
            var
                CarCarrierHeader: Record "Car Carrier Header ERK";
            begin
                if not CarCarrierHeader.Get(Rec."Car Carrier No.") then begin
                    Rec.Validate("Ship No.", '');
                    Rec.Validate("Ship Name", '');
                end
                else begin
                    Rec.Validate("Ship No.", CarCarrierHeader."Ship No.");
                    Rec.Validate("Ship Name", CarCarrierHeader."Ship Name");
                end;
            end;
        }
        field(4; "To Location Code"; Code[10])
        {
            Caption = 'To Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the To Location Code field.';
            trigger OnValidate()
            begin
                VehicleTransferManagement.OnAfterValidateToLocationCodeOnTransferHeader(Rec);
                UpdateLocationFilter();
            end;
        }
        field(5; "To Bin Code"; Code[20])
        {
            Caption = 'To Bin Code';
            TableRelation = if ("Operation Type" = const(Wash)) Bin.Code where("Location Code" = field("To Location Code"), "Visible for Wash ERK" = const(true))
            else if ("Operation Type" = const("Nav Exit"), "Set Addressing Filter" = const(true)) Bin.Code where("Location Code" = field("To Location Code"), "Hide in Addressing ERK" = const(false))
            else if ("Operation Type" = const(Discharge)) Bin.Code where("Location Code" = field("To Location Code"), "Default Discharge ERK" = const(true))
            else if ("Operation Type" = const(Loading)) Bin.Code where("Location Code" = field("To Location Code"), "Default Loading ERK" = const(true))
            else if ("Operation Type" = const(Transfer)) Bin.Code where("Location Code" = field("To Location Code"), "Transfer ERK" = const(true))
            else if ("Operation Type" = const(Addressing)) Bin.Code where("Location Code" = field("To Location Code"), "Hide in Addressing ERK" = const(false))
            else if ("Operation Type" = const("Customs Exit")) Bin.Code where("Location Code" = field("To Location Code"), "Customs Exit ERK" = const(true))
            else if ("Operation Type" = const("PDI Exit")) Bin.Code where("Location Code" = field("To Location Code"), "PDI Area ERK" = const(true))
            else if ("Operation Type" = const("Damage Exit")) Bin.Code where("Location Code" = field("To Location Code"), "Visible for Damage Exit ERK" = const(true))
            else if ("Operation Type" = const("Nav Exit")) Bin.Code where("Location Code" = field("To Location Code"), "Nav Area ERK" = const(true))
            else if ("Operation Type" = const("PDI Entry")) Bin.Code where("Location Code" = field("To Location Code"), "Visible for PDI Entry ERK" = const(true))
            else if ("Operation Type" = const("Vehicle Entry")) Bin.Code where("Location Code" = field("To Location Code"), "Vehicle Entry ERK" = const(true))
            else if ("Operation Type" = const("Dispatch Preparation")) Bin.Code where("Location Code" = field("To Location Code"), "Visible for Dispatch Prep. ERK" = const(true))
            else if ("Operation Type" = const("Nav Entry")) Bin.Code where("Location Code" = field("To Location Code"), "Visible for Nav Entry ERK" = const(true))
            else if ("Operation Type" = filter("Dealer Dispatch" | "Grupage Dealer Dispatch")) Bin.Code where("Location Code" = field("To Location Code"), "Vis. for Dealer Dispatch ERK" = const(true));
            ToolTip = 'Specifies the value of the To Bin Code field.';
        }
        field(6; Completed; Boolean)
        {
            Caption = 'Completed';
            ToolTip = 'Specifies the value of the Completed field.';
        }
        field(7; "Target Address Finder"; Code[50])
        {
            Caption = 'Target Address Finder';
            ToolTip = 'Specifies the value of the Target Address Finder field.';
            trigger OnValidate()
            var
                SerialNoInformation: Record "Serial No. Information";
                CharacterLenghtErr: Label 'Serial No. must be at least 8 characters long.';
                SerialNoFilter: Text;
            begin
                if "Target Address Finder" = '' then
                    exit;
                if StrLen("Target Address Finder") < 8 then
                    Error(CharacterLenghtErr);
                SerialNoFilter := '*' + "Target Address Finder";
                SerialNoInformation.SetFilter("Serial No.", SerialNoFilter);
                SerialNoInformation.FindFirst();
                Rec.Validate("To Location Code", SerialNoInformation."Current Location Code ERK");
                Rec.Validate("To Bin Code", SerialNoInformation."Current Bin Code ERK");
                "Target Address Finder" := '';
            end;
        }
        field(8; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
            trigger OnValidate()
            begin
                VehicleTransferManagement.OnAfterValidateSerialNoOnVehicleTransferHeader(Rec);
            end;
        }
        field(9; "Total Quantity"; Integer)
        {
            Caption = 'Total Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Line ERK" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Quantity field.';
        }
        field(10; "Total Processed Quantity"; Integer)
        {
            Caption = 'Total Processed Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Line ERK" where("Document No." = field("No."), Processed = const(true)));
            ToolTip = 'Specifies the value of the Total Processed Quantity field.';
        }
        field(11; "From Vehicle Transfer No."; Code[20])
        {
            Caption = 'From Vehicle Transfer No.';
            AllowInCustomizations = Always;
        }
        field(12; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            TableRelation = "Ship ERK"."No.";
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Ship No. field.';
        }
        field(13; "Ship Name"; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(14; "Last Successful Serial No."; Code[50])
        {
            Caption = 'Last Successful Serial No.';
            ToolTip = 'Specifies the value of the Last Succesfull Serial No. field.';
        }
        field(15; "Lines Locked"; Boolean)
        {
            Caption = 'Lines Locked';
            ToolTip = 'Specifies the value of the Lines Locked field.';
            trigger OnValidate()
            var
                VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
            begin
                VehicleTransferHeader.SetRange("Source Document No.", Rec."No.");
                VehicleTransferHeader.ModifyAll("Lines Locked", Rec."Lines Locked", true);
            end;
        }
        field(16; Released; Boolean)
        {
            Caption = 'Released';
            ToolTip = 'Specifies the value of the Released field.';
            trigger OnValidate()
            var
                VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
            begin
                VehicleTransferHeader.SetRange("Source Document No.", Rec."No.");
                VehicleTransferHeader.ModifyAll(Released, Rec.Released, true);
            end;
        }
        field(17; "Shipping Agent Code"; Code[10])
        {
            Caption = 'Shipping Agent Code';
            TableRelation = "Shipping Agent".Code;
            ToolTip = 'Specifies the value of the Shipping Agent Code field.';
        }
        field(18; "From Location Code"; Code[10])
        {
            Caption = 'From Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the From Location Code field.';
            trigger OnValidate()
            begin
                UpdateLocationFilter();
            end;
        }
        field(19; "From Bin Code"; Code[20])
        {
            Caption = 'From Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("From Location Code"));
            ToolTip = 'Specifies the value of the From Bin Code field.';
        }
        field(20; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            ToolTip = 'Specifies the value of the Source Document No. field.';
            AllowInCustomizations = Always;
        }
        field(21; "Total In-Transit Quantity"; Integer)
        {
            Caption = 'Total In-Transit Quantity';
            ToolTip = 'Specifies the value of the Total In-Transit Quantity field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Vehicle Transfer Line ERK" where("Document No." = field("No."), "In-Transit" = const(true), Processed = const(false)));
        }
        field(22; "Set Addressing Filter"; Boolean)
        {
            Caption = 'Set Addressing Filter';
            ToolTip = 'Specifies the value of the Set Addressing Filter field.';
            AllowInCustomizations = Always;
        }
        field(23; T1; Boolean)
        {
            Caption = 'T1';
            ToolTip = 'Specifies the value of the T1 field.';
        }

        field(24; "Created From PDI Document"; Boolean)
        {
            Caption = 'Created From PDI Document';
            AllowInCustomizations = Always;
        }

        field(25; "Location Filter"; Text[21])
        {
            Caption = 'Location Filter';
            ToolTip = 'Specifies a filter that combines the From and To locations.';
            Editable = false;
            AllowInCustomizations = Always;
        }

        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        WarehouseEmployee: Record "Warehouse Employee";
        //NoSeriesManagement: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Vehicle Transfer No. Series");
            //NoSeriesManagement.InitSeries(ErkHoldingSetup."Vehicle Transfer No. Series", xRec."No. Series", 0D, "No.", "No. Series");
            "No. Series" := ErkHoldingSetup."Vehicle Transfer No. Series";
            if NoSeries.AreRelated(ErkHoldingSetup."Vehicle Transfer No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;

        WarehouseEmployee.SetRange("User ID", UserId());
        if WarehouseEmployee.FindFirst() then
            Rec.Validate("To Location Code", WarehouseEmployee."Location Code");
    end;

    trigger OnDelete()
    var
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    begin
        VehicleTransferLine.SetRange("Document No.", "No.");
        VehicleTransferLine.DeleteAll(true);
    end;

    var
        VehicleTransferManagement: Codeunit "Vehicle Transfer Management";

    procedure UpdateLocationFilter()
    begin
        "Location Filter" := StrSubstNo('%1|%2', "From Location Code", "To Location Code");
    end;
}
