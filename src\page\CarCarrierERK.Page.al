page 60025 "Car Carrier ERK"
{
    ApplicationArea = All;
    Caption = 'Car Carrier';
    PageType = Document;
    SourceTable = "Car Carrier Header ERK";
    UsageCategory = None;
    RefreshOnActivate = true;
    DelayedInsert = true;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = (Rec.Status <> Rec.Status::Completed);
                field("Voyage Type"; Rec."Voyage Type")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("No."; Rec."No.")
                {
                    Editable = false;
                }
                field("Voyage No."; Rec."Voyage No.")
                {
                }
                field("Voyage Category"; Rec."Voyage Category")
                {
                    ShowMandatory = true;
                }
                field(Status; Rec.Status)
                {
                }
                field("Sequence Order No."; Rec."Sequence Order No.")
                {
                }
                field("Sequence Order Text"; Rec."Sequence Order Text")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Ship Type"; Rec."Ship Type")
                {
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                }
                field("Starting Port"; Rec."Starting Port")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Starting Port Description"; Rec."Starting Port Description")
                {
                }
                field("Starting Port Cluster Desc."; Rec."Starting Port Cluster Desc.")
                {
                }
                field("Planned Starting Date"; Rec."Planned Starting Date")
                {
                }
                field("Starting Date"; Rec."Starting Date-Time")
                {
                    // trigger OnValidate()
                    // var
                    //     VoyageTypeErr: Label 'You can not change Starting Date-Time for Normal voyages.';
                    // begin
                    //     if (Rec."Voyage Type" = Rec."Voyage Type"::"Normal - Ferry") or (Rec."Voyage Type" = Rec."Voyage Type"::"Normal - Ro-Ro") then
                    //         Error(VoyageTypeErr);
                    // end;
                }
                field("Ending Port"; Rec."Ending Port")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Ending Port Description"; Rec."Ending Port Description")
                {
                }
                field("Ending Port Cluster Desc."; Rec."Ending Port Cluster Desc.")
                {
                }
                field("Planned Ending Date"; Rec."Planned Ending Date")
                {
                }
                field("Ending Date"; Rec."Ending Date-Time")
                {
                }
                field("Total Loaded Quantity"; Rec."Total Loaded Quantity")
                {
                }
                field("Total Discharged Quantity"; Rec."Total Discharged Quantity")
                {
                }
                field(RemainingQty; CarCarrierManagement.CalcualteRemainingQuantityFromLoadedAndDischargedQuantity(Rec."Total Loaded Quantity", Rec."Total Discharged Quantity"))
                {
                    Caption = 'Remaining Quantity';
                    ToolTip = 'Specifies the value of the Remaining Quantity field.';
                    trigger OnDrillDown()
                    var
                        VehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK";
                    begin
                        VehicleLedgerEntry.SetRange("Document No.", Rec."No.");
                        VehicleLedgerEntry.SetRange("Discharge DateTime", 0DT);

                        Page.Run(Page::"Car Carrier Ledger Entries ERK", VehicleLedgerEntry);
                    end;
                }
                // field("Starting Fuel (IFO)"; Rec."Starting Fuel (IFO)")
                // {
                //     ToolTip = 'Specifies the value of the Starting Fuel (IFO) field.';
                // }
                // field("Starting Fuel (MGO)"; Rec."Starting Fuel (MGO)")
                // {
                //     ToolTip = 'Specifies the value of the Starting Fuel (MGO) field.';
                // }
                // field("Starting Fuel (HSFO)"; Rec."Starting Fuel (HSFO)")
                // {
                //     ToolTip = 'Specifies the value of the Starting Fuel (HSFO) field.';
                // }
                field("Fuel Consumption Calculated"; Rec."Fuel Consumption Calculated")
                {
                }
                field(TotalFuelConsumptionIFO; CarCarrierManagement.CalculateTotalIFOConsumption(Rec))
                {
                    Caption = 'Total Fuel Consumption (IFO)';
                    ToolTip = 'Specifies the value of the Total Fuel Consumption (IFO) field.';
                }
                field(TotalFuelConsumptionMGO; CarCarrierManagement.CalculateTotalMGOConsumption(Rec))
                {
                    Caption = 'Total Fuel Consumption (MGO)';
                    ToolTip = 'Specifies the value of the Total Fuel Consumption (MGO) field.';
                }
                field(TotalHSFOConsumption; CarCarrierManagement.CalculateTotalHSFOConsumption(Rec))
                {
                    Caption = 'Total Fuel Consumption (HSFO)';
                    ToolTip = 'Specifies the value of the Total Fuel Consumption (HSFO) field.';
                }
                field(TotalLNGConsumption; CarCarrierManagement.CalculateTotalLNGConsumption(Rec))
                {
                    Caption = 'Total Fuel Consumption (LNG)';
                    ToolTip = 'Specifies the value of the Total Fuel Consumption (LNG) field.';
                }
                field("Previous Car Carrier No."; Rec."Previous Car Carrier No.")
                {
                    ToolTip = 'Specifies the value of the Previous Car Carrier No. field.';
                    //Editable = false;
                    trigger OnDrillDown()
                    var
                        CarCarrierHeader: Record "Car Carrier Header ERK";
                    begin
                        if CarCarrierHeader.Get(Rec."Previous Car Carrier No.") then
                            PageManagement.PageRun(CarCarrierHeader);
                    end;
                }
                field("Next Car Carrier No."; Rec."Next Car Carrier No.")
                {
                    ToolTip = 'Specifies the value of the Next Car Carrier No. field.';
                    //Editable = false;
                    trigger OnDrillDown()
                    var
                        CarCarrierHeader: Record "Car Carrier Header ERK";
                    begin
                        if CarCarrierHeader.Get(Rec."Next Car Carrier No.") then
                            PageManagement.PageRun(CarCarrierHeader);
                    end;
                }
            }
            group(StartingFuelInformation)
            {
                Caption = 'Starting Fuel Information';
                //Visible = Rec.Status = Rec.Status::Completed;
                field("Starting Fuel (IFO) 1"; Rec."Starting Fuel (IFO)")
                {
                }
                field("Starting Fuel (MGO) 1"; Rec."Starting Fuel (MGO)")
                {
                }
                field("Starting Fuel (HSFO) 1"; Rec."Starting Fuel (HSFO)")
                {
                }
                field("Starting Fuel (LNG) 1"; Rec."Starting Fuel (LNG)")
                {
                    ToolTip = 'Specifies the value of the Starting Fuel (LNG) field.';
                }
            }
            part(Lines; "Car Carrier Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                //Editable = (Rec.Status <> Rec.Status::Completed);
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                Caption = 'Attachments';
                SubPageLink = "Table ID" = const(Database::"Car Carrier Header ERK"),
                              "No." = field("No.");
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(RoundDownDataTime)
            {
                Caption = 'RoundDownDataTime';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Download;
                ToolTip = 'Executes the RoundDownDataTime action.';
                Visible = false;
                trigger OnAction()
                begin
                    Message('Rounded Down Starting Date-Time: %1\\Rounded Down Ending Date-Time: %2', RoundDateTime(Rec."Starting Date-Time", 3600000, '<'), RoundDateTime(Rec."Ending Date-Time", 3600000, '<'));
                end;
            }
            action(RevenueExpenses)
            {
                Caption = 'Revenue and Expenses';
                Image = TaxDetail;
                Promoted = true;
                PromotedOnly = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                RunObject = page "Car Carr. Revenue/Expenses ERK";
                RunPageLink = "Document No." = field("No.");//, "Document Line No." = field("Line No.");
                ToolTip = 'Executes the Revenue and Expenses action.';
            }
            action(CreateIFOFuelConsumptions)
            {
                Caption = 'Create IFO Consumption';
                Image = Create;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Create IFO Consumption action.';
                trigger OnAction()
                var
                    CarCarrierLine: Record "Car Carrier Line ERK";
                begin
                    CarCarrierLine.SetRange("Document No.", Rec."No.");
                    CarCarrierLine.FindFirst();

                    CarCarrierManagement.CalculateConsumptionValuesFromCarCarrierLineLoop(CarCarrierLine);
                    CarCarrierManagement.CreateIFOConsumptionLineFromCarCarrierHeader(Rec);
                end;
            }
            action(CreateMGOFuelConsumptions)
            {
                Caption = 'Create MGO Consumption';
                Image = Create;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Create MGO Consumption action.';
                trigger OnAction()
                var
                    CarCarrierLine: Record "Car Carrier Line ERK";
                begin
                    CarCarrierLine.SetRange("Document No.", Rec."No.");
                    CarCarrierLine.FindFirst();

                    CarCarrierManagement.CalculateConsumptionValuesFromCarCarrierLineLoop(CarCarrierLine);
                    CarCarrierManagement.CreateMGOConsumptionLineFromCarCarrierHeader(Rec);
                end;
            }
            action(CreateHSFOFuelConsumptions)
            {
                Caption = 'Create HSFO Consumption';
                Image = Create;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Create HSFO Consumption action.';
                trigger OnAction()
                var
                    CarCarrierLine: Record "Car Carrier Line ERK";
                begin
                    CarCarrierLine.SetRange("Document No.", Rec."No.");
                    CarCarrierLine.FindFirst();

                    CarCarrierManagement.CalculateConsumptionValuesFromCarCarrierLineLoop(CarCarrierLine);
                    CarCarrierManagement.CreateHSFOConsumptionLineFromCarCarrierHeader(Rec);
                end;
            }
            action(CreateLNGFuelConsumptions)
            {
                Caption = 'Create LNG Consumption';
                Image = Create;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Create LNG Consumption action.';
                trigger OnAction()
                var
                    CarCarrierLine: Record "Car Carrier Line ERK";
                begin
                    CarCarrierLine.SetRange("Document No.", Rec."No.");
                    CarCarrierLine.FindFirst();

                    CarCarrierManagement.CalculateConsumptionValuesFromCarCarrierLineLoop(CarCarrierLine);
                    CarCarrierManagement.CreateLNGConsumptionLineFromCarCarrierHeader(Rec);
                end;
            }

            action(CreateHireExpense)
            {
                Caption = 'Create Hire Expense';
                Image = Create;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Create Hire Expense action.';
                trigger OnAction()
                begin
                    CarCarrierManagement.CreateShipHireExpenseLinesFromCarCarrierHeader(Rec);
                end;
            }
            action(CarCarrierLedgerEntries)
            {
                Caption = 'Car Carrier Ledger Entries';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = LedgerEntries;
                RunObject = page "Car Carrier Ledger Entries ERK";
                RunPageLink = "Document No." = field("No.");
                ToolTip = 'Executes the Car Carrier Ledger Entries action.';
            }
            action(CarCarrierLinesDetails)
            {
                Caption = 'Car Carrier Line Details';
                Image = TaxDetail;
                PromotedCategory = Report;
                Promoted = true;
                PromotedOnly = true;
                PromotedIsBig = true;
                RunObject = page "Car Carrier Line Details ERK";
                RunPageLink = "Document No." = field("No.");//, "Document Line No." = field("Line No.");
                ToolTip = 'Executes the Car Carrier Line Details action.';
            }
            action(CalculateFuelConsuptions)
            {
                Caption = 'Calculate Fuel Consumptions';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = CalculateSalesTax;
                ToolTip = 'Executes the Calculate Fuel Consumptions action.';
                trigger OnAction()
                var
                    CarCarrierLine: Record "Car Carrier Line ERK";
                begin
                    CarCarrierLine.SetRange("Document No.", Rec."No.");
                    CarCarrierLine.FindFirst();

                    CarCarrierManagement.CalculateConsumptionValuesFromCarCarrierLineLoop(CarCarrierLine);
                end;
            }
            action(CreateExpectedHireCost)
            {
                Caption = 'Create Expected Hire Cost';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Create;
                ToolTip = 'Executes the Create Expected Hire Cost action.';
                trigger OnAction()
                begin
                    CarCarrierManagement.CreateEstimatedandExpectedHireLine(Rec);
                end;
            }

            action(UpdateConsumptionQuantitiesFromStormGeoData)
            {
                Caption = 'Update Consumption Quantities From StormGeo Data';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = UpdateXML;
                ToolTip = 'Executes the Update Consumption Quantities From StormGeo Data action.';
                trigger OnAction()
                begin
                    StormGeoManagement.UpdateROBQuantitiesFromCarCarrierHeader(Rec);
                end;
            }
            action(TransferVehiclesToNextVoyage)
            {
                Caption = 'Transfer Vehicles To Next Voyage';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = TransferOrder;
                ToolTip = 'Executes the Transfer Vehicles To Next Voyage action.';
                trigger OnAction()
                begin
                    CarCarrierManagement.TransferRemaningVehicles(Rec);
                end;
            }
            action(UpdateDimensionCode)
            {
                Caption = 'Update Dimension Code', Comment = 'TRK="Boyut Kodunu Güncelle"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = UpdateDescription;
                RunPageOnRec = true;
                RunObject = page "Update Dimension Code ERK";
                ToolTip = 'Executes the Update Dimension Code action.';

                // trigger OnAction()
                // begin

                // end;
            }
        }
    }
    var
        CarCarrierManagement: Codeunit "Car Carrier Management ERK";
        StormGeoManagement: Codeunit "StormGeo Management ERK";
        PageManagement: Codeunit "Page Management";
}