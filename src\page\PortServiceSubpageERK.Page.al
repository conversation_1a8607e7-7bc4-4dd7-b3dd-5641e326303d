page 60147 "Port Service Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Port Service Lines';
    PageType = ListPart;
    SourceTable = "Port Service Line ERK";
    AutoSplitKey = true;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Type; Rec.Type)
                {
                }
                field("Source No."; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Unit Price"; Rec."Unit Price")
                {
                }
                field("VAT Product Posting Group"; Rec."VAT Product Posting Group")
                {
                }
                field("Line Amount"; Rec."Line Amount")
                {
                }
                field("Line Amount (ACY)"; Rec."Line Amount (ACY)")
                {
                }
                field("Invoice Currency Code"; Rec."Invoice Currency Code")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Invoice No."; Rec."Invoice No.")
                {
                }
                field("Posted Invoice No."; Rec."Posted Invoice No.")
                {
                }
                field("External Document No."; Rec."External Document No.")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(InvoiceDetails)
            {
                Caption = 'Invoice Details';
                Image = Invoice;
                ToolTip = 'View the details of the invoice.';

                trigger OnAction()
                begin
                    // Implementation will be added later
                    Message('Invoice Details functionality will be implemented.');
                end;
            }
        }
    }
}