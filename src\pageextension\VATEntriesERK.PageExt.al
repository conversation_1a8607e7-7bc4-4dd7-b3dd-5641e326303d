pageextension 60051 "VAT Entries ERK" extends "VAT Entries"
{
    layout
    {
        addafter("Bill-to/Pay-to No.")
        {
            field("Bill-to/Pay-to Name ERK"; ErkHoldingBasicFunctions.GetBilltoPaytoNameFromVATEntry(Rec))
            {
                ApplicationArea = All;
                Caption = 'Bill-to/Pay-to Name';
                ToolTip = 'Specifies the value of the Bill-to/Pay-to Name field.';
            }
        }
        addafter("Document No.")
        {
            field("External Document No. ERK"; Rec."External Document No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the External Document No. field.';
            }
        }
        addafter("VAT Prod. Posting Group")
        {
            field("E-Doc. Exemption Type Code ERK"; Rec."E-Doc. Exemption Type Code ERK")
            {
                ApplicationArea = All;
            }
            field("E-Document Exemption Code ERK"; Rec."E-Document Exemption Code ERK")
            {
                ApplicationArea = All;
            }
        }
    }
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
