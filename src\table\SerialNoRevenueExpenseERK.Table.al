table 60039 "Serial No. Revenue/Expense ERK"
{
    Caption = 'Serial No. Revenue/Expense ERK';
    DataClassification = CustomerContent;
    LookupPageId = "Serial No. Revenue/Expenses";
    DrillDownPageId = "Serial No. Revenue/Expenses";

    fields
    {
        field(1; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            TableRelation = "Serial No. Information"."Serial No.";
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Never;
        }
        field(3; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(4; Type; Enum "Voyage Line Detail Type ERK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(5; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the value of the No. field.';
        }
        field(6; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(7; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(8; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            ToolTip = 'Specifies the value of the Source No. field.';
        }
        field(9; "Source Name"; Text[100])
        {
            Caption = 'Source Name';
            ToolTip = 'Specifies the value of the Source Name field.';
        }
        field(11; "Amount (ACY)"; Decimal)
        {
            Caption = 'Amount (ACY)';
            DecimalPlaces = 4 : 4;
            ToolTip = 'Specifies the value of the Amount (ACY) field.';
        }
        field(13; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency;
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        field(16; "Car Carrier Rev/Exp Line No."; Integer)
        {
            Caption = 'Car Carrier Rev/Exp Line No.';
            ToolTip = 'Specifies the value of the Car Carrier Rev/Exp Line No. field.';
            AllowInCustomizations = Always;
        }
        field(17; Amount; Decimal)
        {
            Caption = 'Amount';
            ToolTip = 'Specifies the value of the Amount field.';
            trigger OnValidate()
            var
                CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
            begin
                if "Created From Invoice" then
                    "Amount (ACY)" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", Amount)
                else begin
                    CarCarrRevenueExpense.Get(Rec."Document No.", Rec."Car Carrier Rev/Exp Line No.");
                    "Amount (ACY)" := ExportManagement.ConvertAmountToACY(CarCarrRevenueExpense."Posting Date", "Currency Code", Amount);
                end;
            end;
        }
        field(10; "Created From Invoice"; Boolean)
        {
            Caption = 'Created From Invoice';
            ToolTip = 'Specifies whether the line was created from an invoice.';
            AllowInCustomizations = Always;
        }
        field(12; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
            trigger OnValidate()
            var
                CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
            begin
                if "Created From Invoice" then
                    "Amount (ACY)" := ExportManagement.ConvertAmountToACY("Posting Date", "Currency Code", Amount)
                else begin
                    CarCarrRevenueExpense.Get(Rec."Document No.", Rec."Car Carrier Rev/Exp Line No.");
                    "Amount (ACY)" := ExportManagement.ConvertAmountToACY(CarCarrRevenueExpense."Posting Date", "Currency Code", Amount);
                end;
            end;
        }
        field(14; "Unposted Invoice No."; Code[20])
        {
            Caption = 'Unposted Invoice No.';
            ToolTip = 'Specifies the value of the Unposted Invoice No. field.';
        }
        field(15; "Posted Invoice No."; Code[20])
        {
            Caption = 'Posted Invoice No.';
            ToolTip = 'Specifies the value of the Posted Invoice No. field.';
        }
        field(18; "Invoice Line No."; Integer)
        {
            Caption = 'Invoice Line No.';
            ToolTip = 'Specifies the value of the Invoice Line No. field.';
        }
        field(19; "Department Code"; Code[20])
        {
            Caption = 'Department Code';
            AllowInCustomizations = Always;
        }
        // field(20; "External Document No."; Text[35])
        // {
        //     Caption = 'External Document No.';
        // }
    }
    keys
    {
        key(PK; "Serial No.", "Line No.")
        {
            Clustered = true;
        }
        key(SK; "Serial No.", "Document No.", Type)
        {
        }

        key(TK; "Document No.", "Car Carrier Rev/Exp Line No.")
        {

        }
    }
    trigger OnInsert()
    var
        SerialNoRevenueExpense: Record "Serial No. Revenue/Expense ERK";
    begin
        SerialNoRevenueExpense.SetRange("Serial No.", Rec."Serial No.");
        if SerialNoRevenueExpense.FindLast() then
            Rec."Line No." := SerialNoRevenueExpense."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    var
        ExportManagement: Codeunit "Export Management ERK";
}
