page 60024 "Car Carrier List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Car Carrier List';
    PageType = List;
    SourceTable = "Car Carrier Header ERK";
    UsageCategory = Documents;
    CardPageId = "Car Carrier ERK";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Previous Car Carrier No."; Rec."Previous Car Carrier No.")
                {
                }
                field("Next Car Carrier No."; Rec."Next Car Carrier No.")
                {
                }
                field("Voyage No."; Rec."Voyage No.")
                {
                }
                field("Voyage Type"; Rec."Voyage Type")
                {
                }
                field("Voyage Category"; Rec."Voyage Category")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Sequence Order No."; Rec."Sequence Order No.")
                {
                }
                field("Sequence Order Text"; Rec."Sequence Order Text")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                    //ExtendedDatatype = Masked;
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Starting Port"; Rec."Starting Port")
                {
                }
                field("Starting Port Description"; Rec."Starting Port Description")
                {
                }
                field("Starting Port Cluster Desc."; Rec."Starting Port Cluster Desc.")
                {
                }
                field("Planned Starting Date"; Rec."Planned Starting Date")
                {
                }
                field("Starting Date"; Rec."Starting Date-Time")
                {
                }
                field("Ending Port"; Rec."Ending Port")
                {
                }
                field("Ending Port Description"; Rec."Ending Port Description")
                {
                }
                field("Ending Port Cluster Desc."; Rec."Ending Port Cluster Desc.")
                {
                }
                field("Planned Ending Date"; Rec."Planned Ending Date")
                {
                }
                field("Ending Date"; Rec."Ending Date-Time")
                {
                }
                field("Voyage Duration (Hour)"; Rec."Voyage Duration (Hour)")
                {
                }
                field(CurrentLocation; CarCarrierManagement.PopulateCurrentLocationOfShipFromCarCarrierHeader(Rec))
                {
                    Caption = 'Current Location';
                    ToolTip = 'Specifies the value of the Current Location field.';
                    Visible = false;
                }
                field("Total Loaded Quantity"; Rec."Total Loaded Quantity")
                {
                }
                field("Total Discharged Quantity"; Rec."Total Discharged Quantity")
                {
                }
                field(RemainingQty; CarCarrierManagement.CalcualteRemainingQuantityFromLoadedAndDischargedQuantity(Rec."Total Loaded Quantity", Rec."Total Discharged Quantity"))
                {
                    Caption = 'Remaning Quantity';
                    ToolTip = 'Specifies the value of the Remaning Quantity field.';

                    trigger OnDrillDown()
                    var
                        VehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK";
                    begin
                        VehicleLedgerEntry.SetRange("Document No.", Rec."No.");
                        VehicleLedgerEntry.SetRange("Discharge Port", '');
                        Page.Run(Page::"Car Carrier Ledger Entries ERK", VehicleLedgerEntry);
                    end;
                }
                field("Expected Revenue (ACY)"; Rec."Expected Revenue (ACY)")
                {
                }
                field("Expected Expense (ACY)"; Rec."Expected Expense (ACY)")
                {
                }
                field("Actual Revenue (ACY)"; Rec."Actual Revenue (ACY)")
                {
                }
                field("Actual Expense (ACY)"; Rec."Actual Expense (ACY)")
                {
                }
                field(ExpectedProfitACY; Rec."Expected Revenue (ACY)" - Rec."Expected Expense (ACY)")
                {
                    Caption = 'Expected Profit (ACY)';
                    ToolTip = 'Specifies the value of the Expected Profit (ACY) field.';
                }
                field(ActualProfitACY; Rec."Actual Revenue (ACY)" - Rec."Actual Expense (ACY)")
                {
                    Caption = 'Actual Profit (ACY)';
                    ToolTip = 'Specifies the value of the Actual Profit (ACY) field.';
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                Caption = 'Attachments';
                SubPageLink = "Table ID" = const(Database::"Car Carrier Header ERK"), "No." = field("No.");
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateBallastVoyages)
            {
                ApplicationArea = All;
                Caption = 'Create Ballast Voyages';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = CreateBinContent;
                ToolTip = 'Executes the Create Ballast Voyages action.';

                trigger OnAction()
                begin
                    CarCarrierManagement.CreateBalastTrips();
                end;
            }
            action(CalculateComplatedVoyagesDuration)
            {
                ApplicationArea = All;
                Caption = 'Calculate Complated Voyages Duration';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Calculate;
                ToolTip = 'Executes the Calculate Complated Voyages Duration action.';

                trigger OnAction()
                var
                    CarCarrierHeader: Record "Car Carrier Header ERK";
                    NoCompletedErr: Label 'No Completed Voyages Found';
                    SuccesMsg: Label '%1 Completed Voyages Duration Calculated', Comment = '%1="Car Carrier Header ERK".Count()';
                begin
                    CarCarrierHeader.SetRange(Status, CarCarrierHeader.Status::Completed);
                    CarCarrierHeader.SetFilter("Starting Date-Time", '<>%1', 0DT);
                    CarCarrierHeader.SetFilter("Ending Date-Time", '<>%1', 0DT);
                    if not CarCarrierHeader.FindSet() then
                        Error(NoCompletedErr);
                    repeat
                        CarCarrierHeader."Voyage Duration (Hour)" := (CarCarrierHeader."Ending Date-Time" - CarCarrierHeader."Starting Date-Time") / 3600000;
                        CarCarrierHeader.Modify(false);
                    until CarCarrierHeader.Next() = 0;
                    Message(SuccesMsg, CarCarrierHeader.Count());
                end;
            }
            action(SetVoyageStatusToPlanning)
            {
                ApplicationArea = All;
                Caption = 'Set Voyage Status To Planned';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Set Voyage Status To Planned action.';
                Image = Status;
                Visible = false;

                trigger OnAction()
                begin
                    Rec.Status := Rec.Status::Planned;
                    Rec.Modify(false);
                end;
            }
            action(SetVoyageStatusToCompleted)
            {
                ApplicationArea = All;
                Caption = 'Set Voyage Status To Completed';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Set Voyage Status To Completed action.';
                Image = Status;
                Visible = false;

                trigger OnAction()
                begin
                    Rec.Status := Rec.Status::Completed;
                    Rec.Modify(false);
                end;
            }
            // action(CalculateSequenceOrderNos)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Calculate Sequence Order Nos';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     ToolTip = 'Executes the Calculate Sequence Order Nos action.';
            //     Image = Calculate;

            //     trigger OnAction()
            //     begin
            //         CarCarrierManagement.CalculateCarCarrierOrderSequenceNo();
            //     end;
            // }
        }
        area(Navigation)
        {
            action(VehicleLedgerEntries)
            {
                ApplicationArea = All;
                Caption = 'Car Carrier Ledger Entries';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = LedgerEntries;
                RunObject = page "Car Carrier Ledger Entries ERK";
                RunPageLink = "Document No." = field("No.");
                ToolTip = 'Executes the Car Carrier Ledger Entries action.';
            }
        }
    }
    var
        CarCarrierManagement: Codeunit "Car Carrier Management ERK";
}
