codeunit 60006 "Vehicle Transfer Management"
{
    // procedure CreateTransferLedgerEntryFromVehicleLedgerEntry(VehicleLedgerEntry: Record "Vehicle Ledger Entry ERK")
    // var
    //     EntryExitPoint: Record "Entry/Exit Point";
    //     Bin: Record Bin;
    //     VehicleTransferLedgerEntry: Record "Vehicle Transfer Ledger Entry";
    //     CarCarrierHeader: Record "Car Carrier Header ERK";
    //     SerialNoInformation: Record "Serial No. Information";
    // begin
    //     EntryExitPoint.Get(VehicleLedgerEntry."Discharge Port");
    //     if EntryExitPoint."Location Code ERK" = '' then
    //         exit;
    //     Bin.SetRange("Location Code", EntryExitPoint."Location Code ERK");
    //     Bin.SetRange("Default Loading/Discharge ERK", true);
    //     Bin.FindFirst();
    //     CarCarrierHeader.Get(VehicleLedgerEntry."Document No.");
    //     VehicleTransferLedgerEntry.Init();
    //     VehicleTransferLedgerEntry."From Location Code" := VehicleLedgerEntry."Loading Port";
    //     VehicleTransferLedgerEntry."From Bin Code" := VehicleLedgerEntry."Loading Port";
    //     VehicleTransferLedgerEntry."To Location Code" := EntryExitPoint."Location Code ERK";
    //     VehicleTransferLedgerEntry."To Bin Code" := Bin.Code;
    //     VehicleTransferLedgerEntry."Operation Date-Time" := CurrentDateTime();
    //     VehicleTransferLedgerEntry."Car Carrier No." := CarCarrierHeader."No.";
    //     VehicleTransferLedgerEntry."Serial No." := VehicleLedgerEntry."Serial No.";
    //     VehicleTransferLedgerEntry."Ship No." := CarCarrierHeader."Ship No.";
    //     VehicleTransferLedgerEntry."Ship Name" := CarCarrierHeader."Ship Name";
    //     VehicleTransferLedgerEntry."Vehicle Ledger Entry No." := VehicleLedgerEntry."Entry No.";
    //     VehicleTransferLedgerEntry.Insert(true);
    //     SerialNoInformation.SetRange("Serial No.", VehicleTransferLedgerEntry."Serial No.");
    //     SerialNoInformation.FindFirst();
    //     SerialNoInformation.Validate("Current Location Code ERK", VehicleTransferLedgerEntry."To Location Code");
    //     SerialNoInformation.Validate("Current Bin Code ERK", VehicleTransferLedgerEntry."To Bin Code");
    //     SerialNoInformation.Modify(true);
    // end;
    procedure OnAfterValidateOperationType(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    begin
        VehicleTransferHeader.Validate("To Bin Code", '');
        case VehicleTransferHeader."Operation Type" of
            VehicleTransferHeader."Operation Type"::" ":
                ; //Nothing to do here yet.
            VehicleTransferHeader."Operation Type"::Addressing:
                ;//Nothing to do here yet.
            VehicleTransferHeader."Operation Type"::Discharge:
                OperationTypeDischarge(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::Loading:
                ;//Nothing to do here yet.
            VehicleTransferHeader."Operation Type"::"Customs Exit":
                ; //OperationTypePDI(VehicleTransferHeader);//Nothing to do here yet.
        end;
    end;
    // local procedure OperationTypeChangeAddress(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    // begin
    //     Message(VehicleTransferHeader."No.");
    // end;
    local procedure OperationTypeDischarge(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        Bin: Record Bin;
    begin
        Bin.SetRange("Default Discharge ERK", true);
        Bin.FindFirst();
        VehicleTransferHeader.Validate("To Location Code", Bin."Location Code");
        VehicleTransferHeader.Validate("To Bin Code", Bin.Code);
    end;

    procedure OnAfterValidateToLocationCodeOnTransferHeader(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    begin
        case VehicleTransferHeader."Operation Type" of
            "Vehicle Transfer Opr. Type ERK"::"Customs Exit":
                OnAfterValidateToLocationCodeOnTransferHeader_OperationTypeCustomsExit(VehicleTransferHeader);
            "Vehicle Transfer Opr. Type ERK"::Wash:
                OnAfterValidateToLocationCodeOnTransferHeader_OperationTypeWash(VehicleTransferHeader);
            "Vehicle Transfer Opr. Type ERK"::"PDI Entry":
                OnAfterValidateToLocationCodeOnTransferHeader_OperationTypePDIEntry(VehicleTransferHeader);
        end;
    end;

    procedure OnAfterValidateToLocationCodeOnTransferHeader_OperationTypePDIEntry(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        Bin: Record Bin;
    begin
        Bin.SetRange("Location Code", VehicleTransferHeader."To Location Code");
        Bin.SetRange("Visible for PDI Entry ERK", true);
        if Bin.FindFirst() then
            VehicleTransferHeader.Validate("To Bin Code", Bin.Code)
    end;

    procedure OnAfterValidateToLocationCodeOnTransferHeader_OperationTypeWash(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        Bin: Record Bin;
    begin
        Bin.SetRange("Location Code", VehicleTransferHeader."To Location Code");
        Bin.SetRange("Visible for Wash ERK", true);
        if Bin.FindFirst() then
            VehicleTransferHeader.Validate("To Bin Code", Bin.Code)
    end;

    local procedure VehicleTransferLineMustExist(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    //SerialNoFilter: Text;
    begin
        SerialNoFilter := '*' + VehicleTransferHeader."Serial No.";
        VehicleTransferLine.SetRange("Document No.", VehicleTransferHeader."No.");
        VehicleTransferLine.SetFilter("Serial No.", SerialNoFilter);
        VehicleTransferLine.FindFirst();
        TSECheck(VehicleTransferHeader, VehicleTransferLine);
        VehicleTransferLine.TestField(Processed, false);
        VehicleTransferLine.Validate(Processed, true);
        VehicleTransferLine.Modify(false);
    end;

    procedure OnAfterValidateSerialNoOnVehicleTransferHeader(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        SerialNoInformation: Record "Serial No. Information";
    begin
        if VehicleTransferHeader."Serial No." = '' then
            exit;

        IsUserAllowedToOperate(VehicleTransferHeader);

        case VehicleTransferHeader."Operation Type" of
            VehicleTransferHeader."Operation Type"::Addressing,
            VehicleTransferHeader."Operation Type"::"Vehicle Entry":
                begin
                    CreateNewVehicleTransferLineFromBarcodeReading(VehicleTransferHeader);
                    SetNextAvailableBinCode(VehicleTransferHeader, false);
                end;
            VehicleTransferHeader."Operation Type"::"Dealer Dispatch":
                ProcessOperationTypeForDealerDispatch(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::Discharge:
                VehicleTransferLineMustExist(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::Loading:
                ProcessOperationTypeLoading(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::Transfer:
                ProcessSerialNoForPDI(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"Customs Exit":
                ProcessSerialNoForPDI(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"PDI Entry":
                ProcessOperationTypePDIEntry(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"PDI Exit":
                ProcessOperationTypeForPDIExit(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"Nav Exit":
                ProcessOperationTypeForNavExit(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"Damage Exit":
                ProcessOperationTypeForDamageExit(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::Wash:
                ProcessOperationTypeForWash(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"Stock-Taking":
                ProcessOperationTypeForStockTaking(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"Grupage Dealer Dispatch":
                ProcessOperationTypeForGruppageDealerDispatch(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"Dispatch Preparation":
                ProcessOperationTypeForDispatchPreparation(VehicleTransferHeader);
            VehicleTransferHeader."Operation Type"::"Nav Entry":
                ProcessOperationTypeForNavEntry(VehicleTransferHeader);
        end;
        //ShowSuccesMsgAfterReadingSerialNoOnVehicleTransferHeader(VehicleTransferHeader);

        GetSerialNoInformationFromShortSerialNo(VehicleTransferHeader, SerialNoInformation);
        VehicleTransferHeader."Last Successful Serial No." := SerialNoInformation."Serial No.";
        VehicleTransferHeader."Serial No." := '';
    end;

    local procedure ProcessOperationTypeForNavEntry(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    begin
        ProcessSerialNoForPDI(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeForDispatchPreparation(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    begin
        VehicleTransferLineMustExist(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeForGruppageDealerDispatch(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    // var
    //     VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    begin
        VehicleTransferLineMustExist(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeForDealerDispatch(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
        AlreadyInGrupageDiscpatchErr: Label 'You can not process the same vehicle for Dealer Discpatch if it is already in Grupage Dealer Dispatch Document.';
    begin
        SerialNoFilter := '*' + VehicleTransferHeader."Serial No.";
        VehicleTransferLine.SetFilter("Serial No.", SerialNoFilter);
        VehicleTransferLine.SetRange("Operation Type", VehicleTransferLine."Operation Type"::"Grupage Dealer Dispatch");
        if VehicleTransferLine.FindFirst() then
            Error(AlreadyInGrupageDiscpatchErr);
        VehicleTransferLine.SetRange("Operation Type");
        VehicleTransferLine.SetRange("Document No.", VehicleTransferHeader."No.");
        if VehicleTransferLine.IsEmpty() then
            CreateNewVehicleTransferLineFromBarcodeReading(VehicleTransferHeader)
        else
            VehicleTransferLineMustExist(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeForStockTaking(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        SerialNoInformation: Record "Serial No. Information";
    begin
        GetSerialNoInformationFromShortSerialNo(VehicleTransferHeader, SerialNoInformation);
        CreateNewVehicleTransferLineFromBarcodeReading(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeForWash(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    begin
        VehicleTransferLineMustExist(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeForDamageExit(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        SerialNoInformation: Record "Serial No. Information";
        FromBin: Record Bin;
    begin
        GetSerialNoInformationFromShortSerialNo(VehicleTransferHeader, SerialNoInformation);
        FromBin.Get(SerialNoInformation."Current Location Code ERK", SerialNoInformation."Current Bin Code ERK");
        FromBin.TestField("Man. From Bin Dmg. Exit ERK");
        CreateNewVehicleTransferLineFromBarcodeReading(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeForNavExit(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        SerialNoInformation: Record "Serial No. Information";
        FromBin: Record Bin;
        ConfirmLbl: Label 'Do you want to process vehicle to %1', Comment = '%1="Vehicle Transfer Header ERK"."To Bin Code"';
        ProcessAbortedLbl: Label 'Operation cancelled by user.';
        NavUploadSuccesfulQst: Label 'Is Nav upload successful?';
        COnfirmQst: Text;

    begin
        GetSerialNoInformationFromShortSerialNo(VehicleTransferHeader, SerialNoInformation);
        FromBin.Get(SerialNoInformation."Current Location Code ERK", SerialNoInformation."Current Bin Code ERK");

        if FromBin."Civil Area ERK" then begin
            COnfirmQst := StrSubstNo(ConfirmLbl, VehicleTransferHeader."To Bin Code");
            if not ConfirmManagement.GetResponseOrDefault(COnfirmQst, true) then
                Error(ProcessAbortedLbl);

            SerialNoInformation."Nav Upload Succesful ERK" := true;
            SerialNoInformation.Modify(true);
        end
        else
            if ConfirmManagement.GetResponseOrDefault(NavUploadSuccesfulQst, true) then begin
                SerialNoInformation."Nav Upload Succesful ERK" := true;
                SerialNoInformation.Modify(true);
            end;

        FromBin.TestField("Man. From Bin For Nav Exit ERK");
        CreateNewVehicleTransferLineFromBarcodeReading(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeForPDIExit(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        SerialNoInformation: Record "Serial No. Information";
        FromBin: Record Bin;
        PrintQst: Label 'Do you want to print Grupage Label?';
    begin
        GetSerialNoInformationFromShortSerialNo(VehicleTransferHeader, SerialNoInformation);

        if SerialNoInformation."Grupage Bin Code ERK" <> '' then begin
            VehicleTransferHeader.Validate("To Bin Code", SerialNoInformation."Grupage Bin Code ERK");
            VehicleTransferHeader.Modify(true);
        end;

        OpenAndCheckPDIDocument(SerialNoInformation, VehicleTransferHeader);

        FromBin.Get(SerialNoInformation."Current Location Code ERK", SerialNoInformation."Current Bin Code ERK");
        FromBin.TestField("PDI Bin ERK");
        CreateNewVehicleTransferLineFromBarcodeReading(VehicleTransferHeader);
        if SerialNoInformation."Print Grupage Label ERK" then
            if ConfirmManagement.GetResponseOrDefault(PrintQst, true) then
                Report.RunModal(Report::"SN Label", false, true, SerialNoInformation);
    end;

    procedure OpenAndCheckPDIDocument(SerialNoInformation: Record "Serial No. Information"; VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        PDIHeader: Record "PDI Header ERK";
        ProcessFromPDIDocumentErr: Label 'You have to do transfer process from PDI Document: %1', Comment = '%1="PDI Header ERK"."No."';
    begin
        if VehicleTransferHeader."Created From PDI Document" then
            exit;

        PDIHeader.SetRange("Serial No.", SerialNoInformation."Serial No.");
        if PDIHeader.FindFirst() then
            Error(ProcessFromPDIDocumentErr, PDIHeader."No.");

        // No need to check if Saved anymore since we can reopen them
    end;

    local procedure ProcessOperationTypePDIEntry(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        SerialNoInformation: Record "Serial No. Information";
        FromBin: Record Bin;
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
        CommercialBlockageMsg: Label 'This vehicle has commercial blockage.';
    //ChargeCableShouldExistMsg: Label 'Charge Cable should exist';
    begin
        GetSerialNoInformationFromShortSerialNo(VehicleTransferHeader, SerialNoInformation);
        FromBin.Get(SerialNoInformation."Current Location Code ERK", SerialNoInformation."Current Bin Code ERK");
        FromBin.TestField("Mandatory From Bin For PDI ERK");
        if SerialNoInformation."Commercial Blockage ERK" then
            Message(CommercialBlockageMsg);
        SerialNoFilter := '*' + VehicleTransferHeader."Serial No.";
        VehicleTransferLine.SetRange("Document No.", VehicleTransferHeader."No.");
        VehicleTransferLine.SetFilter("Serial No.", SerialNoFilter);
        if VehicleTransferLine.IsEmpty() then
            CreateNewVehicleTransferLineFromBarcodeReading(VehicleTransferHeader)
        else
            VehicleTransferLineMustExist(VehicleTransferHeader);
    end;

    local procedure ProcessOperationTypeLoading(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
        ProcessDischargeDocumentFirstErr: Label 'You must process the discharge operation first for the Serial No. %1 in Vehicle Discharge Document %2.', Comment = '%1="Vehicle Transfer Line ERK"."Serial No."; %2="Vehicle Transfer Line ERK"."Document No."';
    begin
        //VehicleTransferLine.SetRange("Document No.", VehicleTransferHeader."No.");
        VehicleTransferLine.SetRange("Operation Type", VehicleTransferLine."Operation Type"::Discharge);
        VehicleTransferLine.SetRange("Serial No.", VehicleTransferHeader."Serial No.");
        VehicleTransferLine.SetRange(Processed, false);
        if VehicleTransferLine.FindFirst() then
            Error(ProcessDischargeDocumentFirstErr, VehicleTransferLine."Serial No.", VehicleTransferLine."Document No.");
        VehicleTransferLineMustExist(VehicleTransferHeader);
    end;

    local procedure ProcessSerialNoForPDI(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    //SerialNoFilter: Text;
    begin
        SerialNoFilter := '*' + VehicleTransferHeader."Serial No.";
        VehicleTransferLine.SetRange("Document No.", VehicleTransferHeader."No.");
        VehicleTransferLine.SetFilter("Serial No.", SerialNoFilter);
        VehicleTransferLine.FindFirst();
        if VehicleTransferLine."In-Transit" then
            VehicleTransferLineMustExist(VehicleTransferHeader)
        else begin
            VehicleTransferLine.Validate("In-Transit", true);
            VehicleTransferLine.Modify(true);
        end;
    end;

    procedure OnAfterValidaInTransitOnVehicleTransferLine(var VehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        // InTransitSetup: Record "In-Transit Setup ERK";
        //Bin: Record Bin;
        InTransitBinCode: Code[20];
    begin
        VehicleTransferHeader.Get(VehicleTransferLine."Document No.");

        InTransitBinCode := UpdateFromBinCodeForInTransitIfNeccesary(VehicleTransferHeader, VehicleTransferLine, InTransitBinCode);
        // Bin.SetRange("Location Code", VehicleTransferLine."From Location Code");
        // Bin.SetRange("In-Transit Bin ERK", true);
        // Bin.FindFirst();
        //if VehicleTransferHeader."Operation Type" = VehicleTransferHeader."Operation Type"::"Customs Exit" then begin
        //InTransitHardcoded := 'KAPI'

        // end
        // else
        //     if VehicleTransferHeader."Operation Type" = VehicleTransferHeader."Operation Type"::Transfer then
        //         InTransitHardcoded := 'IN-TRANSIT';
        CreateVehicleTransferLedgerEntryFromVehicleTransferLine(VehicleTransferLine, InTransitBinCode, true);
        VehicleTransferLine.Validate("In-Transit By", UserId());
        VehicleTransferLine.Validate("In-Transit At", CurrentDateTime());
    end;

    procedure OnAfterValideSerialNoOnVehicleTransferLine(var VehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        SerialNoInformation: Record "Serial No. Information";
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        VehicleTransferLine2: Record "Vehicle Transfer Line ERK";
        Bin: Record Bin;
        MultipleSerialNoErr: Label 'You can not insert Serial No.: %1 twice.', Comment = '%1="Vehicle Transfer Line ERK"."Serial No."';
        SerialNoLenghtErr: Label 'Serial No. must be at least 8 characters long.';
        SameLocationErr: Label 'Current location can not be same with target location.';
        FilterText: Text;
    begin
        if VehicleTransferLine."Serial No." = '' then
            exit;

        VehicleTransferHeader.Get(VehicleTransferLine."Document No.");
        if (VehicleTransferHeader."Operation Type" in [VehicleTransferHeader."Operation Type"::Discharge,
                                                        VehicleTransferHeader."Operation Type"::Loading,
                                                        VehicleTransferHeader."Operation Type"::"Customs Exit"]) then begin
            VehicleTransferLine2.SetRange("Document No.", VehicleTransferLine."Document No.");
            VehicleTransferLine2.SetRange("Serial No.", VehicleTransferLine."Serial No.");
            if not VehicleTransferLine2.IsEmpty() then
                Error(MultipleSerialNoErr, VehicleTransferLine."Serial No.");
        end;
        if StrLen(VehicleTransferLine."Serial No.") < 8 then
            Error(SerialNoLenghtErr);

        FilterText := '*' + VehicleTransferLine."Serial No.";
        SerialNoInformation.SetFilter("Serial No.", FilterText);
        if not SerialNoInformation.FindFirst() then begin
            ErkHoldingSetup.GetRecordOnce();
            ErkHoldingSetup.TestField("Item No. for Vehicles");
            VehicleTransferHeader.TestField("From Location Code");
            VehicleTransferHeader.TestField("From Bin Code");
            SerialNoInformation.Init();
            SerialNoInformation."Item No." := ErkHoldingSetup."Item No. for Vehicles";
            SerialNoInformation."Serial No." := VehicleTransferLine."Serial No.";
            SerialNoInformation."Current Location Code ERK" := VehicleTransferHeader."From Location Code";
            SerialNoInformation."Current Bin Code ERK" := VehicleTransferHeader."From Bin Code";
            SerialNoInformation.Insert(true);
        end;


        if Bin.Get(SerialNoInformation."Current Location Code ERK", SerialNoInformation."Current Bin Code ERK") then
            if VehicleTransferHeader."Operation Type" = VehicleTransferHeader."Operation Type"::"PDI Exit" then
                Bin.TestField("PDI Bin ERK", true);

        if (VehicleTransferHeader."To Location Code" = SerialNoInformation."Current Location Code ERK") and (VehicleTransferHeader."To Bin Code" = SerialNoInformation."Current Bin Code ERK") then
            Error(SameLocationErr);

        VehicleTransferLine."Serial No." := SerialNoInformation."Serial No.";
        VehicleTransferLine.Validate("From Location Code", SerialNoInformation."Current Location Code ERK");
        VehicleTransferLine.Validate("From Bin Code", SerialNoInformation."Current Bin Code ERK");

        IsFromLocationAndBinAllowedForOperationType(VehicleTransferLine);

        CreateAutomaticTransferDocumentForOperationTypeTransfer(VehicleTransferHeader, VehicleTransferLine);
    end;

    local procedure CreateNewVehicleTransferLineFromBarcodeReading(VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    begin
        VehicleTransferLine.Init();
        VehicleTransferLine."Document No." := VehicleTransferHeader."No.";
        VehicleTransferLine.Insert(true);
        VehicleTransferLine.Validate("Serial No.", VehicleTransferHeader."Serial No.");
        VehicleTransferLine.Validate("To Location Code", VehicleTransferHeader."To Location Code");
        VehicleTransferLine.Validate("To Bin Code", VehicleTransferHeader."To Bin Code");
        VehicleTransferLine.Validate(Processed, true);
        VehicleTransferLine.Modify(false);
    end;

    local procedure OnAfterValidateToLocationCodeOnTransferHeader_OperationTypeCustomsExit(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        Bin: Record Bin;
    begin
        Bin.SetRange("Location Code", VehicleTransferHeader."To Location Code");
        Bin.SetRange("Customs Exit ERK", true);
        if Bin.FindFirst() then
            VehicleTransferHeader.Validate("To Bin Code", Bin.Code);
    end;

    local procedure FindNextAvailableBinCode(var Bin: Record Bin)
    var
        InitialBin: Record Bin;
        EmptyBinFound: Boolean;
    begin
        InitialBin := Bin;
        while not EmptyBinFound do begin
            if Bin."Vehicle Quantity ERK" < Bin."Vehicle Capacity ERK" then
                EmptyBinFound := true;
            if not EmptyBinFound then
                if Bin.Next() = 0 then begin
                    Bin := InitialBin;
                    exit;
                end;
        end;
    end;

    local procedure TSECheck(VehicleTransferHeader: Record "Vehicle Transfer Header ERK"; VehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        TSEMsg: Label 'This vehicle requires TSE check.';
    begin
        if VehicleTransferHeader."Operation Type" <> VehicleTransferHeader."Operation Type"::Discharge then
            exit;
        VehicleTransferLine.CalcFields(TSE);
        if VehicleTransferLine.TSE then
            Message(TSEMsg);
    end;

    local procedure CreateAutomaticTransferDocumentForOperationTypeTransfer(var FromVehicleTransferHeader: Record "Vehicle Transfer Header ERK"; FromVehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        NewVehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        NewVehicleTransferLine: Record "Vehicle Transfer Line ERK";
        AutoVehicleTransferSetup: Record "Auto.Vehicle Transfer Setup";
        DocumentCreatedMsg: Label 'New vehicle transfer document has been created.';
    begin
        AutoVehicleTransferSetup.SetRange("Valid for Operation Type", FromVehicleTransferHeader."Operation Type");
        AutoVehicleTransferSetup.SetRange("To Location Code", FromVehicleTransferHeader."To Location Code");
        AutoVehicleTransferSetup.SetRange("To Bin Code", FromVehicleTransferHeader."To Bin Code");
        if not AutoVehicleTransferSetup.FindFirst() then
            exit;

        NewVehicleTransferHeader.SetRange("From Vehicle Transfer No.", FromVehicleTransferHeader."No.");
        NewVehicleTransferHeader.SetRange("Operation Type", AutoVehicleTransferSetup."New Operation Type");
        NewVehicleTransferHeader.SetRange(Completed, false);
        if not NewVehicleTransferHeader.FindFirst() then begin
            NewVehicleTransferHeader.Init();
            NewVehicleTransferHeader.Insert(true);
            NewVehicleTransferHeader.Validate("Operation Type", AutoVehicleTransferSetup."New Operation Type");
            NewVehicleTransferHeader.Validate("To Location Code", AutoVehicleTransferSetup."New To Location Code");
            NewVehicleTransferHeader.Validate("To Bin Code", AutoVehicleTransferSetup."New To Bin Code");
            NewVehicleTransferHeader.Validate("From Vehicle Transfer No.", FromVehicleTransferHeader."No.");
            NewVehicleTransferHeader.Validate("Ship Name", FromVehicleTransferHeader."Ship Name");
            NewVehicleTransferHeader.Validate("Source Document No.", FromVehicleTransferHeader."No.");
            NewVehicleTransferHeader.Modify(true);

            Message(DocumentCreatedMsg);
        end;

        NewVehicleTransferLine.Init();
        NewVehicleTransferLine."Document No." := NewVehicleTransferHeader."No.";
        NewVehicleTransferLine.Insert(true);
        NewVehicleTransferLine.Validate("Serial No.", FromVehicleTransferLine."Serial No.");
        NewVehicleTransferLine.Modify(true);
    end;

    local procedure UpdateFromLocationAndBinCode(var FromVehicleTransferHeader: Record "Vehicle Transfer Header ERK"; SerialNoInformation: Record "Serial No. Information")
    var
        NewVehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        NewVehicleTransferLine: Record "Vehicle Transfer Line ERK";
        AutoVehicleTransferSetup: Record "Auto.Vehicle Transfer Setup";
    begin
        if FromVehicleTransferHeader."Operation Type" <> FromVehicleTransferHeader."Operation Type"::"Customs Exit" then
            exit;
        AutoVehicleTransferSetup.SetRange("To Location Code", FromVehicleTransferHeader."To Location Code");
        AutoVehicleTransferSetup.SetRange("To Bin Code", FromVehicleTransferHeader."To Bin Code");
        if AutoVehicleTransferSetup.IsEmpty() then
            exit;
        NewVehicleTransferHeader.SetRange("From Vehicle Transfer No.", FromVehicleTransferHeader."No.");
        NewVehicleTransferHeader.SetRange("Operation Type", NewVehicleTransferHeader."Operation Type"::Transfer);
        NewVehicleTransferHeader.SetRange(Completed, false);
        if not NewVehicleTransferHeader.FindFirst() then
            exit;
        NewVehicleTransferLine.SetRange("Document No.", NewVehicleTransferHeader."No.");
        NewVehicleTransferLine.SetRange("Serial No.", SerialNoInformation."Serial No.");
        if not NewVehicleTransferLine.FindFirst() then
            exit;
        NewVehicleTransferLine.Validate("From Location Code", SerialNoInformation."Current Location Code ERK");
        NewVehicleTransferLine.Validate("From Bin Code", SerialNoInformation."Current Bin Code ERK");
        NewVehicleTransferLine.Modify(true);
    end;

    procedure CreateVehicleTransferLedgerEntryFromVehicleTransferLine(var VehicleTransferLine: Record "Vehicle Transfer Line ERK"; ToBinCode: Code[20]; IsInTransit: Boolean)
    var
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        VehicleTransferLedgerEntry: Record "Vehicle Transfer Ledger Entry";
        SerialNoInformation: Record "Serial No. Information";
        FromBin: Record Bin;
        ToBin: Record Bin;
    begin
        VehicleTransferHeader.Get(VehicleTransferLine."Document No.");
        if VehicleTransferHeader."Operation Type" = VehicleTransferHeader."Operation Type"::"Stock-Taking" then
            exit;
        FromBin.Get(VehicleTransferLine."From Location Code", VehicleTransferLine."From Bin Code");
        ToBin.Get(VehicleTransferHeader."To Location Code", VehicleTransferHeader."To Bin Code");
        if not (VehicleTransferHeader."Operation Type" in [VehicleTransferHeader."Operation Type"::"Customs Exit", VehicleTransferHeader."Operation Type"::Discharge, VehicleTransferHeader."Operation Type"::Loading]) then
            ToBin.TestField("Civil Area ERK", FromBin."Civil Area ERK");

        if VehicleTransferHeader."Operation Type" = VehicleTransferHeader."Operation Type"::Addressing then
            ToBin.TestField("Location Code", FromBin."Location Code");

        if VehicleTransferLine."To Location Code" = '' then
            VehicleTransferLine.Validate("To Location Code", VehicleTransferHeader."To Location Code");

        if VehicleTransferLine."To Bin Code" = '' then
            VehicleTransferLine.Validate("To Bin Code", VehicleTransferHeader."To Bin Code");

        VehicleTransferLedgerEntry.Init();
        VehicleTransferLedgerEntry."From Location Code" := VehicleTransferLine."From Location Code";
        if not IsInTransit then
            VehicleTransferLedgerEntry."From Bin Code" := UpdateFromBinCodeForInTransitIfNeccesary(VehicleTransferHeader, VehicleTransferLine, VehicleTransferLine."From Bin Code")
        else
            VehicleTransferLedgerEntry."From Bin Code" := VehicleTransferLine."From Bin Code";
        if IsInTransit then
            VehicleTransferLedgerEntry."To Location Code" := VehicleTransferLine."From Location Code"
        else
            VehicleTransferLedgerEntry."To Location Code" := VehicleTransferLine."To Location Code";

        VehicleTransferLedgerEntry."To Bin Code" := ToBinCode;
        VehicleTransferLedgerEntry."Operation Date-Time" := CurrentDateTime();
        VehicleTransferLedgerEntry."Serial No." := VehicleTransferLine."Serial No.";
        VehicleTransferLedgerEntry."Operation Type" := VehicleTransferHeader."Operation Type";
        VehicleTransferLedgerEntry."Document No." := VehicleTransferLine."Document No.";
        VehicleTransferLedgerEntry."Document Line No." := VehicleTransferLine."Line No.";
        VehicleTransferLedgerEntry."Shipping Agent Code" := VehicleTransferHeader."Shipping Agent Code";
        VehicleTransferLedgerEntry.T1 := VehicleTransferHeader.T1;
        VehicleTransferLedgerEntry.Insert(true);

        SerialNoFilter := '*' + VehicleTransferLedgerEntry."Serial No.";
        SerialNoInformation.SetFilter("Serial No.", SerialNoFilter);
        SerialNoInformation.FindFirst();
        SerialNoInformation.Validate("Current Location Code ERK", VehicleTransferLedgerEntry."To Location Code");
        SerialNoInformation.Validate("Current Bin Code ERK", VehicleTransferLedgerEntry."To Bin Code");
        SerialNoInformation.Modify(true);

        UpdateFromLocationAndBinCode(VehicleTransferHeader, SerialNoInformation);
        UpdateOtherVehicleTransferLineFromLocationAndBinCodes(SerialNoInformation, VehicleTransferLine);
        VehicleTransferLine.Validate("Vehicle Trnsfr Ledg. Entry No.", VehicleTransferLedgerEntry."Entry No.");
    end;

    local procedure UpdateOtherVehicleTransferLineFromLocationAndBinCodes(SerialNoInformation: Record "Serial No. Information"; ParamVehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        VehicleTransferLine: Record "Vehicle Transfer Line ERK";
    begin
        VehicleTransferLine.SetRange("Serial No.", SerialNoInformation."Serial No.");
        VehicleTransferLine.SetRange(Processed, false);
        VehicleTransferLine.SetRange("In-Transit", false);
        VehicleTransferLine.SetFilter("Document No.", '<>%1', ParamVehicleTransferLine."Document No.");
        VehicleTransferLine.ModifyAll("From Location Code", SerialNoInformation."Current Location Code ERK", false);
        VehicleTransferLine.ModifyAll("From Bin Code", SerialNoInformation."Current Bin Code ERK", false);
    end;

    local procedure UpdateFromBinCodeForInTransitIfNeccesary(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK"; VehicleTransferLine: Record "Vehicle Transfer Line ERK"; var BinCodeToUpdate: Code[20]): Code[20]
    var
        InTransitSetup: Record "In-Transit Setup ERK";
    begin
        case VehicleTransferHeader."Operation Type" of
            "Vehicle Transfer Opr. Type ERK"::"Customs Exit",
            "Vehicle Transfer Opr. Type ERK"::Transfer,
            "Vehicle Transfer Opr. Type ERK"::"Nav Entry":
                begin
                    InTransitSetup.SetRange("Operation Type", VehicleTransferHeader."Operation Type");
                    InTransitSetup.SetRange("Location Code", VehicleTransferLine."From Location Code");
                    InTransitSetup.FindFirst();
                    BinCodeToUpdate := InTransitSetup."In-Transit Bin Code";
                end;
        end;

        exit(BinCodeToUpdate);
    end;

    procedure GetSerialNoInformationFromShortSerialNo(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK"; var SerialNoInformation: Record "Serial No. Information")
    var
    begin
        SerialNoFilter := '*' + VehicleTransferHeader."Serial No.";
        SerialNoInformation.SetFilter("Serial No.", SerialNoFilter);
        SerialNoInformation.FindFirst();
    end;

    procedure SetNextAvailableBinCode(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK"; ManualSkip: Boolean)
    var
        SourceBin: Record Bin;
        Bin: Record Bin;
    begin
        SourceBin.Get(VehicleTransferHeader."To Location Code", VehicleTransferHeader."To Bin Code");
        if (SourceBin."Default Discharge ERK") or (SourceBin."Vehicle Entry ERK") then
            exit;

        Bin.SetAutoCalcFields("Vehicle Quantity ERK");
        Bin.SetRange("Location Code", VehicleTransferHeader."To Location Code");
        Bin.SetRange("Zone Code", SourceBin."Zone Code");
        if ManualSkip then
            Bin.SetFilter("Bin Ranking", '>%1', SourceBin."Bin Ranking")
        else
            Bin.SetFilter("Bin Ranking", '>=%1', SourceBin."Bin Ranking");

        if Bin.FindSet() then
            FindNextAvailableBinCode(Bin);

        if VehicleTransferHeader."To Bin Code" <> Bin.Code then
            VehicleTransferHeader.Validate("To Bin Code", Bin.Code);
    end;

    procedure SetPreviousBinCode(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        SourceBin: Record Bin;
        Bin: Record Bin;
    begin
        SourceBin.Get(VehicleTransferHeader."To Location Code", VehicleTransferHeader."To Bin Code");
        Bin.SetCurrentKey("Bin Ranking");
        Bin.SetRange("Location Code", VehicleTransferHeader."To Location Code");
        Bin.SetRange("Zone Code", SourceBin."Zone Code");
        Bin.SetFilter("Bin Ranking", '<%1', SourceBin."Bin Ranking");
        Bin.FindLast();
        VehicleTransferHeader.Validate("To Bin Code", Bin.Code);
    end;

    procedure GetBinCapacityFromLocationAndBinCode(LocationCode: Code[10]; BinCode: Code[20]): Integer
    var
        Bin: Record Bin;
    begin
        if not Bin.Get(LocationCode, BinCode) then
            exit(0);

        exit(Bin."Vehicle Capacity ERK");
    end;

    procedure GetBinVehicleQtyFromLocationAndBinCode(LocationCode: Code[10]; BinCode: Code[20]): Integer
    var
        Bin: Record Bin;
    begin
        if not Bin.Get(LocationCode, BinCode) then
            exit(0);

        Bin.CalcFields("Vehicle Quantity ERK");
        exit(Bin."Vehicle Quantity ERK");
    end;

    procedure InsertVehicleQueryLedgerEntry(SerialNoInformation: Record "Serial No. Information")
    var
        VehicleQueryLedgerEntry: Record "Vehicle Query Ledger Entry ERK";

        Location: Record Location;
        SuccesMsg: Label '%1: %2\%3: %4\%5: %6\%7: %8', Comment = '%1=FieldCaption("Serial No."); %2="Serial No. Information"."Serial No."; %3=FieldCaption("Customs Declaration No. ERK"); %4="Serial No. Information"."Customs Declaration No. ERK"; %5=FieldCaption("Customs Dec. Line No. ERK"); %6="Serial No. Information"."Customs Dec. Line No. ERK", %7 = Fiedl Summar Dec. No., %8 = Summar Dec. No. ERK';
    begin
        Location.Get(SerialNoInformation."Current Location Code ERK");
        VehicleQueryLedgerEntry.Init();
        VehicleQueryLedgerEntry."Serial No." := SerialNoInformation."Serial No.";
        VehicleQueryLedgerEntry."Customs Declaration No." := SerialNoInformation."Customs Declaration No. ERK";
        VehicleQueryLedgerEntry."Customs Declaration Line No." := SerialNoInformation."Customs Dec. Line No. ERK";
        VehicleQueryLedgerEntry."Location Code" := SerialNoInformation."Current Location Code ERK";
        VehicleQueryLedgerEntry."Summary Declaration No." := SerialNoInformation."Summary Declaration No. ERK";
        if Location."Declaration Approval Req. ERK" then
            VehicleQueryLedgerEntry.Status := VehicleQueryLedgerEntry.Status::"Approval Pending"
        else
            VehicleQueryLedgerEntry.Status := VehicleQueryLedgerEntry.Status::Approved;
        VehicleQueryLedgerEntry.Insert(true);

        Message(SuccesMsg, SerialNoInformation.FieldCaption("Serial No."), SerialNoInformation."Serial No.", SerialNoInformation.FieldCaption("Customs Declaration No. ERK"), SerialNoInformation."Customs Declaration No. ERK", SerialNoInformation.FieldCaption("Customs Dec. Line No. ERK"), SerialNoInformation."Customs Dec. Line No. ERK", SerialNoInformation.FieldCaption("Summary Declaration No. ERK"), SerialNoInformation."Summary Declaration No. ERK");
    end;

    procedure ShowSuccesMsgAfterReadingSerialNoOnVehicleTransferHeader(VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        SuccesMsg: Label 'To Location Code: %1\To Bin Code: %2\Vehicle succesfully read.', Comment = '%1="Vehicle Transfer Header ERK"."To Location Code"; %2="Vehicle Transfer Header ERK"."To Bin Code"';
    begin
        if VehicleTransferHeader."Operation Type" <> VehicleTransferHeader."Operation Type"::"Customs Exit" then
            exit;
        Message(SuccesMsg, VehicleTransferHeader."To Location Code", VehicleTransferHeader."To Bin Code");
    end;

    procedure InsertVehicleFuelLedgerEntry(var VehicleFuelOperation: Record "Vehicle Add. Operation ERK")
    var
        VehicleFuelLedgerEntry: Record "Vehicle Fuel Ledger Entry ERK";
        SuccesMsg: Label 'Additional operation succesfully recorded.';
    begin
        VehicleFuelOperation.TestField("Serial No.");

        case VehicleFuelOperation."Operation Type" of
            "Vehicle Add. Operation Type"::Fueling:
                VehicleFuelOperation.TestField("Fuel Type");
            "Vehicle Add. Operation Type"::Tire:
                VehicleFuelOperation.TestField("Tire Location");
        end;

        Clear(VehicleFuelLedgerEntry);
        VehicleFuelLedgerEntry.Init();
        VehicleFuelLedgerEntry.Insert(true);
        VehicleFuelLedgerEntry."Serial No." := VehicleFuelOperation."Serial No.";
        VehicleFuelLedgerEntry."Quantity (LT)" := VehicleFuelOperation."Quantity (LT)";
        VehicleFuelLedgerEntry."Posting Date" := WorkDate();
        VehicleFuelLedgerEntry."Fuel Type" := VehicleFuelOperation."Fuel Type";
        VehicleFuelLedgerEntry."Card ID" := VehicleFuelOperation."Card ID";
        VehicleFuelLedgerEntry."Charge Station ID" := VehicleFuelOperation."Charge Station ID";
        VehicleFuelLedgerEntry.kWh := VehicleFuelOperation.kWh;
        VehicleFuelLedgerEntry."Operation Type" := VehicleFuelOperation."Operation Type";
        VehicleFuelLedgerEntry."Tire Location" := VehicleFuelOperation."Tire Location";
        VehicleFuelLedgerEntry.Modify(true);
        Message(SuccesMsg);

        VehicleFuelOperation."Serial No." := '';
        VehicleFuelOperation."Quantity (LT)" := 0;
        VehicleFuelOperation."Fuel Type" := VehicleFuelOperation."Fuel Type"::" ";
        VehicleFuelOperation."Card ID" := '';
        VehicleFuelOperation."Charge Station ID" := '';
        VehicleFuelOperation.kWh := 0;
    end;

    procedure TransferSelectedBinsToXXBin(var Bin: Record Bin)
    var
        SerialNoInformation: Record "Serial No. Information";
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        ConfirmQst: Label 'Do you want to transfer all vehicles to XX Bin?';
        VehicleQuantity: Integer;
    begin
        if not ConfirmManagement.GetResponseOrDefault(ConfirmQst, false) then
            exit;
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Item No. for Vehicles");
        Bin.SetAutoCalcFields("Vehicle Quantity ERK");
        Bin.FindSet();
        VehicleTransferHeader.Init();
        VehicleTransferHeader.Insert(true);
        VehicleTransferHeader.Validate("Operation Type", VehicleTransferHeader."Operation Type"::Addressing);
        VehicleTransferHeader.Validate("To Location Code", Bin."Location Code");
        VehicleTransferHeader.Validate("To Bin Code", 'XX');
        VehicleTransferHeader.Modify(true);
        repeat
            SerialNoInformation.SetRange("Item No.", ErkHoldingSetup."Item No. for Vehicles");
            SerialNoInformation.SetRange("Current Location Code ERK", Bin."Location Code");
            SerialNoInformation.SetRange("Current Bin Code ERK", Bin.Code);
            if SerialNoInformation.FindSet(true) then
                repeat
                    VehicleTransferHeader.Validate("Serial No.", SerialNoInformation."Serial No.");
                    VehicleQuantity += 1;
                until SerialNoInformation.Next() = 0;
        until Bin.Next() = 0;
        Message('Total %1 Vehicles addressed to XX Bin.', VehicleQuantity);
    end;

    procedure CheckUserVehicleLocation(VehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        WarehouseEmployee: Record "Warehouse Employee";
        ErrorErr: Label 'You are not authorized to transfer vehicles from this location.';
    begin
        VehicleTransferLine.CalcFields("Operation Type");
        case VehicleTransferLine."Operation Type" of
            "Vehicle Transfer Opr. Type ERK"::Discharge,
            "Vehicle Transfer Opr. Type ERK"::"Vehicle Entry":
                begin
                    WarehouseEmployee.SetRange("User ID", UserId());
                    WarehouseEmployee.SetRange("Location Code", VehicleTransferLine."To Location Code");
                    if WarehouseEmployee.IsEmpty() then
                        Error(ErrorErr);
                end;
            "Vehicle Transfer Opr. Type ERK"::Loading:
                begin
                    WarehouseEmployee.SetRange("User ID", UserId());
                    WarehouseEmployee.SetRange("Location Code", VehicleTransferLine."From Location Code");
                    if WarehouseEmployee.IsEmpty() then
                        Error(ErrorErr);
                end;

        // "Vehicle Transfer Opr. Type ERK"::Addressing,
        // "Vehicle Transfer Opr. Type ERK"::"PDI Entry",
        // "Vehicle Transfer Opr. Type ERK"::"PDI Exit",
        // "Vehicle Transfer Opr. Type ERK"::"Nav Exit",
        // "Vehicle Transfer Opr. Type ERK"::"Damage Exit",
        // "Vehicle Transfer Opr. Type ERK"::Wash,
        // "Vehicle Transfer Opr. Type ERK"::"Customs Exit",
        // "Vehicle Transfer Opr. Type ERK"::"Dealer Dispatch":
        //     VehicleTransferLine.TestField("From Location Code", VehicleTransferLine."To Location Code");
        end;
    end;

    procedure VehicleTransferMessageCheck(VehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        SerialNoMessageSetup: Record "Serial No. - Message Setup ERK";
    begin
        VehicleTransferLine.CalcFields("Operation Type");
        SerialNoMessageSetup.SetRange("Serial No.", VehicleTransferLine."Serial No.");
        SerialNoMessageSetup.SetRange("Operation Type", VehicleTransferLine."Operation Type");
        if SerialNoMessageSetup.FindSet(false) then
            repeat
                Message(SerialNoMessageSetup."Message Text");
                SerialNoMessageSetup."Message Shown" := true;
                SerialNoMessageSetup.Modify(true);
            until SerialNoMessageSetup.Next() = 0;
    end;



    procedure DeleteMultipleVehicleTransferDocuments(var VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        ConfirmTxt: Label 'Do you want to delete selected documents?';
        SuccesMsg: Label 'Selected documents deleted.';
    begin
        if not ConfirmManagement.GetResponseOrDefault(ConfirmTxt, true) then
            exit;

        VehicleTransferHeader.DeleteAll(true);

        Message(SuccesMsg);
    end;

    procedure GetPDIExitDateFromSerialNo(SerialNo: Code[50]): DateTime
    var
        VehicleTransferLedgerEntry: Record "Vehicle Transfer Ledger Entry";
    begin
        VehicleTransferLedgerEntry.SetRange("Serial No.", SerialNo);
        VehicleTransferLedgerEntry.SetRange("Operation Type", VehicleTransferLedgerEntry."Operation Type"::"PDI Exit");
        if VehicleTransferLedgerEntry.FindLast() then
            exit(VehicleTransferLedgerEntry."Operation Date-Time");
    end;

    procedure IsUserAllowedToOperate(VehicleTransferHeader: Record "Vehicle Transfer Header ERK")
    var
        WarehouseEmployee: Record "Warehouse Employee";
        ErrorErr: Label 'You are not authorized to operate on this document.';
    begin

        WarehouseEmployee.SetRange("User ID", UserId());
        WarehouseEmployee.SetRange("Location Code", VehicleTransferHeader."To Location Code");

        case VehicleTransferHeader."Operation Type" of
            "Vehicle Transfer Opr. Type ERK"::Addressing:
                WarehouseEmployee.SetRange("Addressing ERK", true);
            "Vehicle Transfer Opr. Type ERK"::"PDI Entry":
                WarehouseEmployee.SetRange("PDI Entry ERK", true);
            "Vehicle Transfer Opr. Type ERK"::"PDI Exit":
                WarehouseEmployee.SetRange("PDI Exit ERK", true);
            "Vehicle Transfer Opr. Type ERK"::"Nav Exit":
                WarehouseEmployee.SetRange("Nav Exit ERK", true);
            "Vehicle Transfer Opr. Type ERK"::"Damage Exit":
                WarehouseEmployee.SetRange("Damage Exit ERK", true);
            "Vehicle Transfer Opr. Type ERK"::Wash:
                WarehouseEmployee.SetRange("Wash ERK", true);
            "Vehicle Transfer Opr. Type ERK"::"Customs Exit":
                WarehouseEmployee.SetRange("Customs Exit ERK", true);
            "Vehicle Transfer Opr. Type ERK"::"Dealer Dispatch":
                WarehouseEmployee.SetRange("Dealer Dispatch ERK", true);
            "Vehicle Transfer Opr. Type ERK"::Transfer:
                WarehouseEmployee.SetRange("Transfer ERK", true);
            "Vehicle Transfer Opr. Type ERK"::Loading:
                //WarehouseEmployee.SetRange("Loading ERK", true);
                exit;
            "Vehicle Transfer Opr. Type ERK"::Discharge:
                //WarehouseEmployee.SetRange("Discharge ERK", true);
                exit;
            "Vehicle Transfer Opr. Type ERK"::"Vehicle Entry":
                WarehouseEmployee.SetRange("Vehicle Entry ERK", true);
            "Vehicle Transfer Opr. Type ERK"::"Stock-Taking":
                exit;
            "Vehicle Transfer Opr. Type ERK"::"Dispatch Preparation":
                WarehouseEmployee.SetRange("Dispatch Preparation ERK", true);
        end;

        if WarehouseEmployee.IsEmpty() then
            Error(ErrorErr);
    end;

    procedure IsFromLocationAndBinAllowedForOperationType(var VehicleTransferLine: Record "Vehicle Transfer Line ERK")
    var
        RestrictedLocationOperType: Record "Restricted Location-Oper. Type";
        NotAllowedErr: Label 'You are not allowed to process vehicles from Location Code: %1 Bin Code: %2 with Operation Type: %3.', Comment = '%1="Vehicle Transfer Line ERK"."From Location Code"; %2="Vehicle Transfer Line ERK"."From Bin Code"; %3="Vehicle Transfer Line ERK"."Operation Type"';
    begin
        VehicleTransferLine.CalcFields("Operation Type");

        if RestrictedLocationOperType.Get(VehicleTransferLine."From Location Code", VehicleTransferLine."From Bin Code", VehicleTransferLine."Operation Type") then
            Error(NotAllowedErr, VehicleTransferLine."From Location Code", VehicleTransferLine."From Bin Code", VehicleTransferLine."Operation Type");
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        ConfirmManagement: Codeunit "Confirm Management";
        SerialNoFilter: Text;
}
