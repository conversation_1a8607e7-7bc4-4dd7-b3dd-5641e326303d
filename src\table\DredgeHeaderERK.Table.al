table 60100 "Dredge Header ERK"
{
    DataClassification = CustomerContent;
    Caption = 'Dredge Header';
    DrillDownPageId = "Dredge Document List ERK";
    LookupPageId = "Dredge Document List ERK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                LocalErkHoldingSetup: Record "Erk Holding Setup ERK";
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    LocalErkHoldingSetup.GetRecordOnce();
                    NoSeries.TestManual(LocalErkHoldingSetup."Dredge Header Nos.");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            TableRelation = "Ship ERK";
            ToolTip = 'Specifies the value of the Ship No. field.';
            trigger OnValidate()
            var
                Ship: Record "Ship ERK";
            begin
                if "Ship No." <> xRec."Ship No." then
                    if Ship.Get("Ship No.") then
                        "Ship Name" := Ship.Name
                    else
                        "Ship Name" := '';
            end;
        }
        field(3; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(4; "Operation Starting Date"; Date)
        {
            Caption = 'Operation Starting Date';
            ToolTip = 'Specifies the value of the Operation Starting Date field.';
        }
        field(5; "Operation Ending Date"; Date)
        {
            Caption = 'Operation Ending Date';
            ToolTip = 'Specifies the value of the Operation Ending Date field.';
        }
        field(6; "Department Code"; Code[20])
        {
            Caption = 'Department Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1));
            ToolTip = 'Specifies the value of the Department Code field.';
            trigger OnValidate()
            var
                DimensionValue: Record "Dimension Value";
            begin
                if "Department Code" <> xRec."Department Code" then
                    if DimensionValue.Get('DEPARTMENT', "Department Code") then
                        "Department Name" := DimensionValue.Name
                    else
                        "Department Name" := '';
            end;
        }
        field(7; "Department Name"; Text[100])
        {
            Caption = 'Department Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Department Name field.';
        }
        field(8; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(9; "Port Code"; Code[10])
        {
            Caption = 'Port Code';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Port Code field.';
            trigger OnValidate()
            var
                Port: Record "Entry/Exit Point";
            begin
                if "Port Code" <> xRec."Port Code" then
                    if Port.Get("Port Code") then
                        "Port Name" := Port.Description
                    else
                        "Port Name" := '';
            end;
        }
        field(10; "Port Name"; Text[100])
        {
            Caption = 'Port Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Port Name field.';
        }
        field(11; Status; Enum "Dredge Status ERK")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
            trigger OnValidate()
            begin
                OnAfterValidate_Status_DredgeHeader();
            end;
        }
        field(12; "Expected Revenue (ACY)"; Decimal)
        {
            Caption = 'Expected Revenue (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Dredge Line ERK"."Amount ACY" where("Document No." = field("No."),
                                                                     Type = filter(Revenue | "Expected Revenue")));
            ToolTip = 'Specifies the value of the Expected Revenue (ACY) field.';
        }
        field(13; "Actual Revenue (ACY)"; Decimal)
        {
            Caption = 'Actual Revenue (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Dredge Line ERK"."Amount ACY" where("Document No." = field("No."),
                                                                     Type = filter(Revenue)));
            ToolTip = 'Specifies the value of the Actual Revenue (ACY) field.';
        }
        field(14; "Expected Expense (ACY)"; Decimal)
        {
            Caption = 'Expected Expense (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Dredge Line ERK"."Amount ACY" where("Document No." = field("No."),
                                                                     Type = filter(Expense | "Expected Expense")));
            ToolTip = 'Specifies the value of the Expected Expense (ACY) field.';
        }
        field(15; "Actual Expense (ACY)"; Decimal)
        {
            Caption = 'Actual Expense (ACY)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Dredge Line ERK"."Amount ACY" where("Document No." = field("No."),
                                                                     Type = filter(Expense)));
            ToolTip = 'Specifies the value of the Actual Expense (ACY) field.';
        }
        field(16; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            Editable = false;
            TableRelation = "No. Series";
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
        key(SK; "Ship No.", "Operation Starting Date", Status)
        {
        }
    }

    trigger OnInsert()
    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            ErkHoldingSetup.Get();
            ErkHoldingSetup.TestField("Dredge Header Nos.");
            "No. Series" := ErkHoldingSetup."Dredge Header Nos.";
            if xRec."No. Series" <> '' then
                if NoSeries.AreRelated(ErkHoldingSetup."Dredge Header Nos.", xRec."No. Series") then
                    "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;
    end;

    trigger OnDelete()
    var
        DredgeLine: Record "Dredge Line ERK";
        StatusErr: Label 'Completed Dredge documents cannot be deleted.';
    begin
        if Status = Status::Completed then
            Error(StatusErr);

        DredgeLine.SetRange("Document No.", "No.");
        DredgeLine.DeleteAll(true);
    end;

    local procedure OnAfterValidate_Status_DredgeHeader()
    begin
        case Status of
            Status::Planned:
                begin
                    Rec.TestField("No.");
                    Rec.TestField("Department Code");
                    Rec.TestField("Port Code");
                end;
            Status::Active:
                begin
                    Rec.TestField("No.");
                    Rec.TestField("Ship No.");
                    Rec.TestField("Department Code");
                    Rec.TestField("Port Code");
                    Rec.TestField("Operation Starting Date");
                end;
            Status::Completed:
                begin
                    Rec.TestField("No.");
                    Rec.TestField("Ship No.");
                    Rec.TestField("Department Code");
                    Rec.TestField("Port Code");
                    Rec.TestField("Operation Starting Date");
                    Rec.TestField("Operation Ending Date");
                end;
        end;
    end;
}