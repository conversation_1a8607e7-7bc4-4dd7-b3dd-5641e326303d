table 60047 "Serial No. - Message Setup ERK"
{
    Caption = 'Serial No. - Message Setup';
    DataClassification = CustomerContent;
    DrillDownPageId = "Serial No. - Message Setup ERK";
    LookupPageId = "Serial No. - Message Setup ERK";

    fields
    {
        field(1; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            TableRelation = "Serial No. Information"."Serial No.";
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(2; "Operation Type"; Enum "Vehicle Transfer Opr. Type ERK")
        {
            Caption = 'Operation Type';
            ToolTip = 'Specifies the value of the Operation Type field.';
        }
        field(3; "Message Text"; Text[1024])
        {
            Caption = 'Message Text';
            ToolTip = 'Specifies the value of the Message Text field.';
        }
        field(4; "Message Shown"; Boolean)
        {
            Caption = 'Message Shown';
            ToolTip = 'Specifies the value of the Message Shown field.';
        }
    }
    keys
    {
        key(PK; "Serial No.", "Operation Type", "Message Text")
        {
            Clustered = true;
        }
    }
}
