page 60010 "Container Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Container Subpage';
    PageType = ListPart;
    SourceTable = "Container Line ERK";
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                    trigger OnAssistEdit()
                    var
                        ExportLine: Record "Export Line ERK";
                        SelectedExportLine: Record "Export Line ERK";
                        ItemVariant: Record "Item Variant";
                    begin
                        ExportLine.SetRange("Document No.", ExportNo);
                        if Page.RunModal(Page::"Item Selection ERK", ExportLine) = Action::LookupOK then begin
                            SelectedExportLine.Get(ExportLine."Document No.", ExportLine."Line No.");
                            if ItemVariant.Get(SelectedExportLine."Item No.", SelectedExportLine."Variant Code") then
                                Rec.Validate("Package Type", ItemVariant."Packaging Type ERK");
                            Rec.Validate("Item No.", SelectedExportLine."Item No.");
                            Rec.Validate("Variant Code", SelectedExportLine."Variant Code");
                            Rec.Validate(Description, SelectedExportLine."Item Description");
                        end;
                    end;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Specification; Rec.Specification)
                {
                    ShowMandatory = true;
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field("Box Quantity"; Rec."Box Quantity")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Packaging Type Code"; Rec."Packaging Type Code")
                {
                    ShowMandatory = true;
                }
                field("Net Weight (KG)"; Rec."Net Weight (KG)")
                {
                }
                field("Gross Weight (KG)"; Rec."Gross Weight (KG)")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Palette Quantity"; Rec."Palette Quantity")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
            }
        }
    }
    procedure SetExportNo(ParamExportNo: Code[20])
    begin
        ExportNo := ParamExportNo;
    end;

    var
        ExportNo: Code[20];
}
