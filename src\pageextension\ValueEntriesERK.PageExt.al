pageextension 60023 "Value Entries ERK" extends "Value Entries"
{
    layout
    {
        addafter("Item Ledger Entry Type")
        {
            field("ILE Entry Type ERK"; Rec."ILE Entry Type ERK")
            {
                ApplicationArea = All;
            }
        }
        modify("Location Code")
        {
            Visible = true;
        }
        modify("Cost Posted to G/L (ACY)")
        {
            Visible = true;
        }
        modify("Cost Amount (Actual) (ACY)")
        {
            Visible = true;
        }
        modify("Cost Amount (Expected) (ACY)")
        {
            Visible = true;
        }
        modify("Cost per Unit (ACY)")
        {
            Visible = true;
        }
        modify("Cost Amount (Non-Invtbl.)(ACY)")
        {
            Visible = true;
        }
    }
}
