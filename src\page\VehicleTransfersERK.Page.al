page 60045 "Vehicle Transfers ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Vehicle Transfers';
    PageType = List;
    SourceTable = "Vehicle Transfer Header ERK";
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Vehicle Transfer ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Operation Type"; Rec."Operation Type")
                {
                }
                field("Car Carrier No."; Rec."Car Carrier No.")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("To Location Code"; Rec."To Location Code")
                {
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field("Target Address Finder"; Rec."Target Address Finder")
                {
                }
                field("Total Quantity"; Rec."Total Quantity")
                {
                }
                field("Total In-Transit Quantity"; Rec."Total In-Transit Quantity")
                {
                }
                field("Total Processed Quantity"; Rec."Total Processed Quantity")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(DeleteSelectedDocuments)
            {
                ApplicationArea = All;
                Caption = 'Delete Documents';
                Image = DeleteAllBreakpoints;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Delete Documents action.';

                trigger OnAction()
                var
                    VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                begin
                    CurrPage.SetSelectionFilter(VehicleTransferHeader);
                    VehicleTransferManagement.DeleteMultipleVehicleTransferDocuments(VehicleTransferHeader);
                end;
            }
            action(VehicleTransferLedgerEntries)
            {
                ApplicationArea = All;
                Caption = 'Vehicle Transfer Ledger Entries';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = EntriesList;
                RunObject = page "Vehicle Transfer Ledg. Entries";
                RunPageLink = "Document No." = field("No.");
                ToolTip = 'Executes the Vehicle Ledger Entries action.';
            }
        }
        area(Reporting)
        {
            action(GrupageDocumentERK)
            {
                ApplicationArea = All;
                Caption = 'Print Grupage Document';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Report;
                ToolTip = 'Executes the Grupage Document action.';

                trigger OnAction()
                var
                    VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                begin
                    CurrPage.SetSelectionFilter(VehicleTransferHeader);
                    Report.Run(Report::"Grupage Document ERK", true, true, VehicleTransferHeader);
                end;
            }
        }
    }
    var
        VehicleTransferManagement: Codeunit "Vehicle Transfer Management";
}
