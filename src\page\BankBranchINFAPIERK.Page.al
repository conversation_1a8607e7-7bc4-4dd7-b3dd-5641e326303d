#pragma warning disable LC0062
page 60115 "Bank Branch INF API ERK"
#pragma warning restore LC0062
{
    PageType = API;
    Caption = 'Swift Code API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'bankBranchsINF';
    EntitySetName = 'bankBranchsINF';
    SourceTable = "Bank Branch INF";
    DelayedInsert = true;
    ODataKeyFields = SystemId;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(id; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(bankCode; Rec."Bank Code")
                {
                    Caption = 'Bank Code';
                }
                field(branchCode; Rec."Branch Code")
                {
                    Caption = 'Branch Code';
                }
                field(branchName; Rec."Branch Name")
                {
                    Caption = 'Branch Name';
                }
            }
        }
    }
}
