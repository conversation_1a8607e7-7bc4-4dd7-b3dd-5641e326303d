page 60074 "Car Carrier Lines ERK"
{
    ApplicationArea = All;
    Caption = 'Car Carrier Lines';
    PageType = List;
    SourceTable = "Car Carrier Line ERK";
    UsageCategory = History;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Fuel Ship Cons. Qty. (IFO)"; Rec."Fuel Ship Cons. Qty. (IFO)")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Departure Port"; Rec."Departure Port")
                {
                }
                field("Starting Port Cluster Desc."; Rec."Starting Port Cluster Desc.")
                {
                }
                field("Departure Date"; Rec."Departure Date-Time")
                {
                }
                field("Arrival Port"; Rec."Arrival Port")
                {
                }
                field("Ending Port Cluster Desc."; Rec."Ending Port Cluster Desc.")
                {
                }
                field("Arrival Date"; Rec."Arrival Date-Time")
                {
                }
                field("Loaded Quantity"; Rec."Loaded Quantity")
                {
                }
                field("Discharged Quantity"; Rec."Discharged Quantity")
                {
                }
                field("Fuel Ship Cons. Qty. (MGO)"; Rec."Fuel Ship Cons. Qty. (MGO)")
                {
                }
                field("Departure Port Description"; Rec."Departure Port Description")
                {
                }
                field("Arrival Port Description"; Rec."Arrival Port Description")
                {
                }
                field("Departure Port Fuel Qty. (IFO)"; Rec."Departure Port Fuel Qty. (IFO)")
                {
                }
                field("Departure Port Fuel Qty. (MGO)"; Rec."Departure Port Fuel Qty. (MGO)")
                {
                }
                field("COSP ROB (IFO)"; Rec."COSP ROB (IFO)")
                {
                }
                field("COSP ROB (MGO)"; Rec."COSP ROB (MGO)")
                {
                }
                field("EOSP ROB (IFO)"; Rec."EOSP ROB (IFO)")
                {
                }
                field("EOSP ROB (MGO)"; Rec."EOSP ROB (MGO)")
                {
                }
                field("All Fast ROB (IFO)"; Rec."All Fast ROB (IFO)")
                {
                }
                field("All Fast ROB (MGO)"; Rec."All Fast ROB (MGO)")
                {
                }
                field("Fueling (IFO)"; Rec."Fueling (IFO)")
                {
                }
                field("Fueling (MGO)"; Rec."Fueling (MGO)")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}
