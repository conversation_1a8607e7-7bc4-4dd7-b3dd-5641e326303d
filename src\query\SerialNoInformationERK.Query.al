query 60011 "Serial No. Information ERK"
{
    Caption = 'Serial No. Information';
    QueryType = Normal;

    elements
    {
        dataitem(SerialNoInformation; "Serial No. Information")
        {
            column(ItemNo; "Item No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(SerialNo; "Serial No.")
            {
            }
            column(Description; Description)
            {
            }
            column(Blocked; Blocked)
            {
            }
            column(Comment; Comment)
            {
            }
            column(Inventory; Inventory)
            {
            }
            column(ExpiredInventory; "Expired Inventory")
            {
            }
            column(BrandCodeERK; "Brand Code ERK")
            {
            }
            column(CommercialBlockageERK; "Commercial Blockage ERK")
            {
            }
            column(ModelVersionERK; "Model Version ERK")
            {
            }
            column(ModelCodeERK; "Model Code ERK")
            {
            }
            column(ColourNameERK; "Colour Name ERK")
            {
            }
            column(FuelTypeERK; "Fuel Type ERK")
            {
            }
            column(EngineIDERK; "Engine ID ERK")
            {
            }
            column(GrossWeightKGERK; "Gross Weight (KG) ERK")
            {
            }
            column(Footprintm2ERK; "Footprint (m2) ERK")
            {
            }
            column(TSEERK; "TSE ERK")
            {
            }
            column(CustomsDeclarationNoERK; "Customs Declaration No. ERK")
            {
            }
            column(CustomsDecLineNoERK; "Customs Dec. Line No. ERK")
            {
            }
            column(CurrentLocationCodeERK; "Current Location Code ERK")
            {
            }
            column(CurrentBinCodeERK; "Current Bin Code ERK")
            {
            }
            column(CivilAreaERK; "Civil Area ERK")
            {
            }
            column(CustomsRegistrationDateERK; "Customs Registration Date ERK")
            {
            }
            column(CarCarrierLedgerEntriesERK; "Car Carrier Ledger Entries ERK")
            {
            }
            column(VehicleTrnsLedgEntriesERK; "Vehicle Trns Ledg. Entries ERK")
            {
            }
            column(NavProcessRequiredERK; "Nav. Process Required ERK")
            {
            }
            column(Volumem3ERK; "Volume (m3) ERK")
            {
            }
            column(GrupageNoERK; "Grupage No. ERK")
            {
            }
            column(GrupageDateERK; "Grupage Date ERK")
            {
            }
            column(GrupageShiptoNameERK; "Grupage Ship-to Name ERK")
            {
            }
            column(GrupageShiptoCityERK; "Grupage Ship-to City ERK")
            {
            }
            column(GrupageBinCodeERK; "Grupage Bin Code ERK")
            {
            }
            column(GrupageShiptoAddressERK; "Grupage Ship-to Address ERK")
            {
            }
            column(GrupageLocationCodeERK; "Grupage Location Code ERK")
            {
            }
            column(PrintGrupageLabelERK; "Print Grupage Label ERK")
            {
            }
            column(TempTrafficDocumentNoERK; "Temp. Traffic Document No. ERK")
            {
            }
            column(TempLicensePlateNoERK; "Temp. License Plate No. ERK")
            {
            }
            column(TempDriverFullNameERK; "Temp. Driver Full Name ERK")
            {
            }
            column(SummaryDeclarationNoERK; "Summary Declaration No. ERK")
            {
            }
            column(NavUploadSuccesfulERK; "Nav Upload Succesful ERK")
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}