table 60014 "Car Carrier Line ERK"
{
    Caption = 'Car Carrier Line';
    DataClassification = CustomerContent;
    LookupPageId = "Car Carrier Lines ERK";
    DrillDownPageId = "Car Carrier Lines ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Never;
            TableRelation = "Car Carrier Header ERK"."No.";
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(4; "Departure Port"; Code[10])
        {
            Caption = 'Departure Port';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Departure Port field.';
            trigger OnValidate()
            var
                CarCarrierLine: Record "Car Carrier Line ERK";
                CarCarrierHeader: Record "Car Carrier Header ERK";
                EntryExitPoint: Record "Entry/Exit Point";
            begin
                CarCarrierLine.SetRange("Document No.", Rec."Document No.");
                //CarCarrierLine.SetFilter("Line No.", '<%1', Rec."Line No.");
                if CarCarrierLine.IsEmpty() then begin
                    CarCarrierHeader.Get(Rec."Document No.");
                    CarCarrierHeader.Validate("Starting Port", Rec."Departure Port");
                    CarCarrierHeader.Modify(true);
                end;
                Rec.CalcFields("Voyage Type");
                if not EntryExitPoint.Get(Rec."Departure Port") then
                    Rec.Validate("Starting Port Cluster Code", '')
                else begin
                    EntryExitPoint.TestField("Port Cluster Code ERK");
                    Rec.Validate("Starting Port Cluster Code", EntryExitPoint."Port Cluster Code ERK");
                end;
            end;
        }
        field(5; "Departure Date-Time"; DateTime)
        {
            Caption = 'Departure Date-Time';
            ToolTip = 'Specifies the value of the Departure Date-Time field.';
            trigger OnValidate()
            var
                CarCarrierHeader: Record "Car Carrier Header ERK";
            begin
                CarCarrierHeader.Get(Rec."Document No.");
                if CarCarrierHeader."Starting Date-Time" = 0DT then
                    exit;
                if CarCarrierHeader."Ending Date-Time" = 0DT then
                    exit;
                if TypeHelper.CompareDateTime("Departure Date-Time", CarCarrierHeader."Starting Date-Time") < 0 then
                    Error(LessThanErr, FieldCaption("Departure Date-Time"), CarCarrierHeader.FieldCaption("Starting Date-Time"));
                if TypeHelper.CompareDateTime("Departure Date-Time", CarCarrierHeader."Ending Date-Time") > 0 then
                    Error(GreaterThanErr, FieldCaption("Departure Date-Time"), CarCarrierHeader.FieldCaption("Ending Date-Time"));
            end;
        }
        field(6; "Arrival Port"; Code[10])
        {
            Caption = 'Arrival Port';
            TableRelation = "Entry/Exit Point";
            ToolTip = 'Specifies the value of the Arrival Port field.';
            trigger OnValidate()
            var
                CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
                EntryExitPoint: Record "Entry/Exit Point";
            begin
                CarCarrierLineDetail.SetRange("Document No.", Rec."Document No.");
                CarCarrierLineDetail.SetRange("Discharge Port Line No.", Rec."Line No.");
                if CarCarrierLineDetail.FindSet(true) then
                    repeat
                        CarCarrierLineDetail.Validate("Discharge Port Line No.", 0);
                        CarCarrierLineDetail.Modify(true);
                    until CarCarrierLineDetail.Next() = 0;
                if not EntryExitPoint.Get(Rec."Arrival Port") then
                    Rec.Validate("Ending Port Cluster Code", '')
                else begin
                    EntryExitPoint.TestField("Port Cluster Code ERK");
                    Rec.Validate("Ending Port Cluster Code", EntryExitPoint."Port Cluster Code ERK");
                end;
            end;
        }
        field(7; "Arrival Date-Time"; DateTime)
        {
            Caption = 'Arrival Date-Time';
            ToolTip = 'Specifies the value of the Arrival Date-Time field.';
            trigger OnValidate()
            var
                CarCarrierLine: Record "Car Carrier Line ERK";
                CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
                CarCarrierHeader: Record "Car Carrier Header ERK";
                //DateErr: Label 'Arrival Date can not be less than Departure Date.';
                Date2Err: Label 'Arrival Date can not be less than previous Depature Date.';
                CanNotChangeArrivalDateTimeErr: Label 'You can not change %1 if there is related Car Carrier Details.', Comment = '%1=FieldCaption("Arrival Date-Time")';
            begin
                if TypeHelper.CompareDateTime("Arrival Date-Time", "Departure Date-Time") < 0 then
                    Error(LessThanErr, FieldCaption("Arrival Date-Time"), FieldCaption("Departure Date-Time"));
                CarCarrierLine.SetRange("Document No.", Rec."Document No.");
                CarCarrierLine.SetFilter("Line No.", '<%1', Rec."Line No.");
                if CarCarrierLine.FindLast() then
                    if TypeHelper.CompareDateTime("Arrival Date-Time", CarCarrierLine."Departure Date-Time") < 0 then
                        Error(Date2Err);
                CarCarrierLineDetail.SetRange("Document No.", Rec."Document No.");
                CarCarrierLineDetail.SetRange("Document Line No.", Rec."Line No.");
                CarCarrierLineDetail.SetFilter("Discharge Start Date-Time", '<>%1', 0DT);
                if not CarCarrierLineDetail.IsEmpty() then
                    Error(CanNotChangeArrivalDateTimeErr, FieldCaption("Arrival Date-Time"));
                CarCarrierHeader.Get(Rec."Document No.");
                if CarCarrierHeader."Starting Date-Time" = 0DT then
                    exit;
                if CarCarrierHeader."Ending Date-Time" = 0DT then
                    exit;
                if TypeHelper.CompareDateTime("Arrival Date-Time", CarCarrierHeader."Starting Date-Time") < 0 then
                    Error(LessThanErr, FieldCaption("Arrival Date-Time"), CarCarrierHeader.FieldCaption("Starting Date-Time"));
                if TypeHelper.CompareDateTime("Arrival Date-Time", CarCarrierHeader."Ending Date-Time") > 0 then
                    Error(GreaterThanErr);
            end;
        }
        field(8; "Loaded Quantity"; Integer)
        {
            Caption = 'Loaded Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Document No." = field("Document No."), "Loading Port" = field("Departure Port"), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Loaded Quantity field.';
        }
        field(9; "Discharged Quantity"; Integer)
        {
            Caption = 'Discharged Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Car Carrier Ledger Entry ERK" where("Document No." = field("Document No."), "Discharge Port" = field("Arrival Port"), "Discharge DateTime" = filter(<> 0DT), "Discharge Port Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Discharged Quantity field.';
        }
        field(3; "Fuel Ship Cons. Qty. (IFO)"; Decimal)
        {
            Caption = 'Fuel Ship Consumption Qty. (IFO)';
            ToolTip = 'Specifies the value of the Fuel Ship Consumption Qty. (IFO) field.';
        }
        field(10; "Fuel Ship Cons. Qty. (MGO)"; Decimal)
        {
            Caption = 'Fuel Ship Consumption Qty. (MGO)';
            ToolTip = 'Specifies the value of the Fuel Ship Consumption Qty. (MGO) field.';
        }
        field(11; "Departure Port Description"; Text[100])
        {
            Caption = 'Departure Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Departure Port")));
            ToolTip = 'Specifies the value of the Departure Port Description field.';
        }
        field(12; "Arrival Port Description"; Text[100])
        {
            Caption = 'Arrival Port Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Entry/Exit Point".Description where(Code = field("Arrival Port")));
            ToolTip = 'Specifies the value of the Arrival Port Description field.';
        }
        field(13; "Departure Port Fuel Qty. (IFO)"; Decimal)
        {
            Caption = 'Departure Port Fuel Qty. (IFO)';
            ToolTip = 'Specifies the value of the Departure Port Fuel Qty. (IFO) field.';
        }
        field(14; "Departure Port Fuel Qty. (MGO)"; Decimal)
        {
            Caption = 'Departure Port Fuel Qty. (MGO)';
            ToolTip = 'Specifies the value of the Departure Port Fuel Qty. (MGO) field.';
        }
        field(15; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ship No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship No. field.';
        }
        field(16; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ship Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(17; "Fueling (IFO)"; Decimal)
        {
            Caption = 'Fueling (IFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Fueling (IFO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(18; "Fueling (MGO)"; Decimal)
        {
            Caption = 'Fueling (MGO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Fueling (MGO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(31; "Fueling (HSFO)"; Decimal)
        {
            Caption = 'Fueling (HSFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Fueling (HSFO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(19; "COSP ROB (IFO)"; Decimal)
        {
            Caption = 'COSP ROB (IFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the COSP ROB (IFO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(20; "COSP ROB (MGO)"; Decimal)
        {
            Caption = 'COSP ROB (MGO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the COSP ROB (MGO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(32; "COSP ROB (HSFO)"; Decimal)
        {
            Caption = 'COSP ROB (HSFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the COSP ROB (HSFO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(21; "EOSP ROB (IFO)"; Decimal)
        {
            Caption = 'EOSP ROB (IFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the EOSP ROB (IFO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(22; "EOSP ROB (MGO)"; Decimal)
        {
            Caption = 'EOSP ROB (MGO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the EOSP ROB (MGO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(33; "EOSP ROB (HSFO)"; Decimal)
        {
            Caption = 'EOSP ROB (HSFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the EOSP ROB (HSFO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(23; "All Fast ROB (IFO)"; Decimal)
        {
            Caption = 'All Fast ROB (IFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the All Fast ROB (IFO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(24; "All Fast ROB (MGO)"; Decimal)
        {
            Caption = 'All Fast ROB (MGO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the All Fast ROB (MGO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(34; "All Fast ROB (HSFO)"; Decimal)
        {
            Caption = 'All Fast ROB (HSFO)';
            MinValue = 0;
            ToolTip = 'Specifies the value of the All Fast ROB (HSFO) field.';
            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(25; "COSP Consumption (IFO)"; Decimal)
        {
            Caption = 'COSP Consumption (IFO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(26; "COSP Consumption (MGO)"; Decimal)
        {
            Caption = 'COSP Consumption (MGO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(35; "COSP Consumption (HSFO)"; Decimal)
        {
            Caption = 'COSP Consumption (HSFO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(27; "Sea Passage Consumption (IFO)"; Decimal)
        {
            Caption = 'Sea Passage Consumption (IFO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(28; "Sea Passage Consumption (MGO)"; Decimal)
        {
            Caption = 'Sea Passage Consumption (MGO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(36; "Sea Passage Consum. (HSFO)"; Decimal)
        {
            Caption = 'Sea Passage Consumption (HSFO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(29; "EOSP Consumption (IFO)"; Decimal)
        {
            Caption = 'EOSP Consumption (IFO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(30; "EOSP Consumption (MGO)"; Decimal)
        {
            Caption = 'EOSP Consumption (MGO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(37; "EOSP Consumption (HSFO)"; Decimal)
        {
            Caption = 'EOSP Consumption (HSFO)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(38; "Starting Port Cluster Code"; Code[10])
        {
            Caption = 'Starting Port Cluster Code';
            TableRelation = "Port Cluster ERK".Code;
            Editable = false;
            AllowInCustomizations = Always;

            trigger OnValidate()
            var
                PortCluster: Record "Port Cluster ERK";
            begin
                if PortCluster.Get(Rec."Starting Port Cluster Code") then
                    Rec.Validate("Starting Port Cluster Desc.", PortCluster.Description)
                else
                    Rec.Validate("Starting Port Cluster Desc.", '');
            end;
        }
        field(39; "Starting Port Cluster Desc."; Text[100])
        {
            Caption = 'Starting Port Cluster Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Starting Port Cluster Description field.';
        }
        field(40; "Ending Port Cluster Code"; Code[10])
        {
            Caption = 'Ending Port Cluster Code';
            TableRelation = "Port Cluster ERK".Code;
            Editable = false;
            AllowInCustomizations = Always;

            trigger OnValidate()
            var
                PortCluster: Record "Port Cluster ERK";
            begin
                if PortCluster.Get(Rec."Ending Port Cluster Code") then
                    Rec.Validate("Ending Port Cluster Desc.", PortCluster.Description)
                else
                    Rec.Validate("Ending Port Cluster Desc.", '');
            end;
        }
        field(41; "Ending Port Cluster Desc."; Text[100])
        {
            Caption = 'Ending Port Cluster Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Ending Port Cluster Description field.';
        }
        field(42; "Voyage Type"; Enum "Car Carrier Voyage Type ERK")
        {
            Caption = 'Voyage Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Voyage Type" where("No." = field("Document No.")));
            AllowInCustomizations = Always;
        }
        field(43; "Overwrite All Fast ROBs"; Boolean)
        {
            Caption = 'Overwrite All Fast ROBs';
            AllowInCustomizations = Never;
        }
        field(44; "Ending Date-Time"; DateTime)
        {
            Caption = 'Ending Date-Time';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Ending Date-Time" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ending Date-Time field.';
            AllowInCustomizations = Always;
        }
        field(45; "Starting Date-Time"; DateTime)
        {
            Caption = 'Starting Date-Time';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Car Carrier Header ERK"."Starting Date-Time" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Starting Date-Time field.';
            AllowInCustomizations = Always;
        }
        field(46; "Fueling (LNG)"; Decimal)
        {
            Caption = 'Fueling (LNG)';
            MinValue = 0;

            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(47; "COSP ROB (LNG)"; Decimal)
        {
            Caption = 'COSP ROB (LNG)';
            MinValue = 0;

            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(48; "EOSP ROB (LNG)"; Decimal)
        {
            Caption = 'EOSP ROB (LNG)';
            MinValue = 0;

            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(49; "All Fast ROB (LNG)"; Decimal)
        {
            Caption = 'All Fast ROB (LNG)';
            MinValue = 0;

            trigger OnValidate()
            begin
                CarCarrierManagement.ClearFuelConsumptionCalculatedField(Rec);
            end;
        }
        field(50; "COSP Consumption (LNG)"; Decimal)
        {
            Caption = 'COSP Consumption (LNG)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(51; "EOSP Consumption (LNG)"; Decimal)
        {
            Caption = 'EOSP Consumption (LNG)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
        field(52; "Sea Passage Consum. (LNG)"; Decimal)
        {
            Caption = 'Sea Passage Consumption (LNG)';
            MinValue = 0;
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(Key2; "Arrival Date-Time")
        {
        }
    }
    fieldgroups
    {
        fieldgroup(DropDown; "Line No.", "Arrival Port", "Arrival Port Description")
        {
        }
    }
    trigger OnInsert()
    var
        //ErkHoldingSetup: Record "Erk Holding Setup ERK";
        CarCarrierLine: Record "Car Carrier Line ERK";
    begin
        // ErkHoldingSetup.GetRecordOnce();
        // ErkHoldingSetup.TestField("Trip No. Series");
        CarCarrierLine.SetRange("Document No.", Rec."Document No.");
        if CarCarrierLine.FindLast() then
            Rec.Validate("Departure Port", CarCarrierLine."Arrival Port");
    end;

    trigger OnDelete()
    var
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
    begin
        CarCarrierLineDetail.SetRange("Document No.", Rec."Document No.");
        CarCarrierLineDetail.SetRange("Document Line No.", Rec."Line No.");
        CarCarrierLineDetail.DeleteAll(true);
    end;

    var
        TypeHelper: Codeunit "Type Helper";
        CarCarrierManagement: Codeunit "Car Carrier Management ERK";
        LessThanErr: Label '%1 can not be less than %2.', Comment = '%1=FieldCaption("Arrival Date-Time"), %2=FieldCaption("Starting Date-Time")';
        GreaterThanErr: Label '%1 can not be greater than %2.', Comment = '%1=FieldCaption("Arrival Date-Time"), %2=FieldCaption("Ending Date-Time")';
}
