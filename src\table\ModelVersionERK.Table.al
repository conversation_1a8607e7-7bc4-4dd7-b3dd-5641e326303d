table 60057 "Model Version ERK"
{
    Caption = 'Model Version';
    DataClassification = CustomerContent;
    DrillDownPageId = "Model Version List ERK";
    LookupPageId = "Model Version List ERK";

    fields
    {
        field(1; "Model Code"; Code[40])
        {
            Caption = 'Model Code';
            NotBlank = false;
            TableRelation = "Model ERK".Code;
            ToolTip = 'Specifies the value of the Model Code field.';
        }
        field(2; Code; Code[100])
        {
            Caption = 'Code';
            NotBlank = true;
            ToolTip = 'Specifies the value of the Code field.';
        }
    }
    keys
    {
        key(PK; "Model Code", Code)
        {
            Clustered = true;
        }
    }
}
