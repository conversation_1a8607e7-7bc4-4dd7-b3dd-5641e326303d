#pragma warning disable LC0062
page 60112 "EInvoiceProfile API ERK"
#pragma warning restore LC0062
{
    PageType = API;
    Caption = 'E-Invoice Profile API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'eInvoiceProfiles';
    EntitySetName = 'eInvoiceProfiles';
    SourceTable = "E-Invoice Profile INF";
    DelayedInsert = true;
    ODataKeyFields = SystemId;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
#pragma warning disable LC0063
                field(systemId; Rec.SystemId)
#pragma warning restore LC0063
                {
                    Caption = 'SystemId';
                }
                field(id; Rec.ID)
                {
                    Caption = 'ID';
                }
                field(description; Rec.Description)
                {
                    Caption = 'Description';
                }
            }
        }
    }
}
