page 60139 "Logistics Dept. Role Center"
{
    PageType = RoleCenter;
    Caption = 'Logistics Department';
    ApplicationArea = All;

    layout
    {
        area(RoleCenter)
        {
            part(Activities; "Logistics Operation Activities")
            {
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group("Port Operations")
            {
                Caption = 'Port Operations';
                action("Port Operation Documents")
                {
                    RunObject = page "Port Operation List ERK";
                    Caption = 'Port Operation Documents';
                    ToolTip = 'View port operation documents.';
                    Image = Document;
                }

                action("Port Operation Line Details")
                {
                    RunObject = page "Port Operation Line DetailsERK";
                    Caption = 'Port Operation Line Details';
                    ToolTip = 'View port operation line details.';
                    Image = Line;
                }
            }
        }

        area(Embedding)
        {
            action("Pending Port Operations")
            {
                RunObject = page "Port Operation Line DetailsERK";
                RunPageView = where("Next Invoice Date" = filter(<> 0D),
                                    "External Document No." = filter(''),
                                    "Invoice No." = filter(''),
                                    "Is Cancelled" = const(false));
                Caption = 'Pending Port Operations';
                ToolTip = 'View port operations that are pending invoice.';
                Image = Calendar;
            }
        }
    }
}