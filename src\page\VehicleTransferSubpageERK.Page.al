page 60047 "Vehicle Transfer Subpage ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle Transfer Subpage';
    PageType = ListPart;
    SourceTable = "Vehicle Transfer Line ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Serial No."; Rec."Serial No.")
                {
                    Editable = not Rec.Processed;
                    trigger OnValidate()
                    begin
                        VehicleTransferHeader.Get(Rec."Document No.");
                        VehicleTransferHeader.TestField("Lines Locked", false);
                    end;
                }
                field(TSE; Rec.TSE)
                {
                    Visible = false;
                }
                field("From Location Code"; Rec."From Location Code")
                {
                    Editable = false;
                }
                field("From Bin Code"; Rec."From Bin Code")
                {
                    Editable = false;
                }
                field("To Location Code"; Rec."To Location Code")
                {
                    trigger OnValidate()
                    begin
                        VehicleTransferHeader.Get(Rec."Document No.");
                        VehicleTransferHeader.TestField("Lines Locked", false);
                    end;
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                    trigger OnValidate()
                    begin
                        VehicleTransferHeader.Get(Rec."Document No.");
                        VehicleTransferHeader.TestField("Lines Locked", false);
                    end;
                }
                field(PDIExitDateTime; VehicleTransferManagement.GetPDIExitDateFromSerialNo(Rec."Serial No."))
                {
                    Caption = 'PDI Exit Date-Time';
                    ToolTip = 'Specifies the value of the PDI Exit Date Time field.';
                    Editable = false;
                }
                field(Processed; Rec.Processed)
                {
                }
                field("In-Transit"; Rec."In-Transit")
                {
                    //Visible = Rec."Operation Type" = Rec."Operation Type"::PDI;
                }
                field("Processed By"; Rec."Processed By")
                {
                }
                field("Processed At"; Rec."Processed At")
                {
                }
                field(Comment; Rec.Comment)
                {
                }
                field("Serial No. Reading"; Rec."Serial No. Reading")
                {

                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(SetInTransit)
            {
                Caption = 'Set In-Transit Filter';
                Image = FilterLines;
                ToolTip = 'Executes the Set In-Transit action.';

                trigger OnAction()
                begin
                    SetInTransitFilter();
                end;
            }
            action(SetProcessFilter)
            {
                Caption = 'Set Processed Filter';
                Image = FilterLines;
                ToolTip = 'Executes the Set Processed Filter action.';

                trigger OnAction()
                begin
                    SetProcessedFilter();
                end;
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(Rec."From Bin Code");
        Rec.SetRange("In-Transit", false);
        Rec.SetRange(Processed, false);
    end;

    procedure SetInTransitFilter()
    begin
        if Rec.GetFilter("In-Transit") <> '' then
            Rec.SetRange("In-Transit")
        else
            Rec.SetRange("In-Transit", false);
    end;

    procedure SetProcessedFilter()
    begin
        if Rec.GetFilter(Processed) <> '' then
            Rec.SetRange(Processed)
        else
            Rec.SetRange(Processed, false);
    end;

    // trigger OnModifyRecord(): Boolean
    // var
    // begin

    // end;

    var
        VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
        VehicleTransferManagement: Codeunit "Vehicle Transfer Management";
}
