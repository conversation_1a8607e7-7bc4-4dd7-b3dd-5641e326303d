page 60015 "Car Carr. Revenue/Expenses ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Car Carr. Revenue/Expenses';
    PageType = List;
    SourceTable = "Car Carr. Revenue/Expense ERK";
    SourceTableView = where("Document No." = filter(<> ''));
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                    Editable = false;
                }
                field(Status; Rec.Status)
                {
                    Editable = false;
                }
                field(Type; Rec."Type")
                {
                    ValuesAllowed = 0, 1, 2, 6, 8, 9, 10, 11, 12, 13;
                }
                field("Source No."; Rec."Source No.")
                {
                    Editable = not (Rec.Type = Rec.Type::Consumption);
                }
                field("Source Name"; Rec."Source Name")
                {
                    Editable = false;
                }
                field("No."; Rec."No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Port Code"; Rec."Port Code")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Port Description"; Rec."Port Description")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("UoM Code"; Rec."UoM Code")
                {
                }
                field("Unit Price/Cost"; Rec."Unit Price/Cost")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    Editable = Rec.Type <> Rec.Type::Consumption;
                }
                field("VAT Prod. Posting Group"; Rec."VAT Prod. Posting Group")
                {
                    ToolTip = 'Specifies the VAT product posting group for the item.';
                }
                field("Line Amount"; Rec."Line Amount")
                {
                    Editable = false;
                }
                field("Line Amount (ACY)"; Rec."Line Amount (ACY)")
                {
                    Editable = false;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Your Reference"; Rec."Your Reference")
                {
                }
                field("External Document No."; Rec."External Document No.")
                {
                }
                field("Unposted Invoice No."; Rec."Unposted Invoice No.")
                {
                    Editable = false;

                    trigger OnDrillDown()
                    var
                        SalesHeader: Record "Sales Header";
                        PurchaseHeader: Record "Purchase Header";
                    begin
                        Rec.TestField("Unposted Invoice No.");
                        if (Rec.Type = Rec.Type::Revenue) or (Rec.Type = Rec.Type::Consumption) then begin
                            if SalesHeader.Get(SalesHeader."Document Type"::Invoice, Rec."Unposted Invoice No.") then
                                PageManagement.PageRun(SalesHeader)
                        end
                        else
                            if Rec.Type = Rec.Type::Expense then
                                if PurchaseHeader.Get(PurchaseHeader."Document Type"::Invoice, Rec."Unposted Invoice No.") then
                                    PageManagement.PageRun(PurchaseHeader);
                    end;
                }
                field("Posted Invoice No."; Rec."Posted Invoice No.")
                {
                    Editable = false;

                    trigger OnDrillDown()
                    var
                        SalesInvoiceHeader: Record "Sales Invoice Header";
                        PurchInvHeader: Record "Purch. Inv. Header";
                        SalesCrMemoHeader: Record "Sales Cr.Memo Header";
                        PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr.";
                    begin
                        Rec.TestField("Unposted Invoice No.");
                        if (Rec.Type = Rec.Type::Revenue) or (Rec.Type = Rec.Type::Consumption) then begin
                            if SalesInvoiceHeader.Get(Rec."Posted Invoice No.") then
                                PageManagement.PageRun(SalesInvoiceHeader)
                            else
                                if SalesCrMemoHeader.Get(Rec."Posted Invoice No.") then
                                    PageManagement.PageRun(SalesCrMemoHeader)
                        end
                        else
                            if Rec.Type = Rec.Type::Expense then
                                if PurchInvHeader.Get(Rec."Posted Invoice No.") then
                                    PageManagement.PageRun(PurchInvHeader)
                                else
                                    if PurchCrMemoHdr.Get(Rec."Posted Invoice No.") then
                                        PageManagement.PageRun(PurchCrMemoHdr);
                    end;
                }
                field("Distrubuted Quantity"; Rec."Distrubuted Quantity")
                {
                }
                field("Consumption Amount Calculated"; Rec."Consumption Amount Calculated")
                {
                }
                field(Notes; Rec.Notes)
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Car Carrier Starting Date-Time"; Rec."Car Carrier Starting Date-Time")
                {
                }
                field("Car Carrier Ending Date-Time"; Rec."Car Carrier Ending Date-Time")
                {
                }
                field("Planned Starting Date"; Rec."Planned Starting Date")
                {
                }
                field("Planned Ending Date"; Rec."Planned Ending Date")
                {
                }
                field("Source Proforma Line No."; Rec."Source Proforma Line No.")
                {
                }
                field("Source Realized Line No."; Rec."Source Realized Line No.")
                {
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ApplicationArea = All;
                    Caption = 'Created By Security Id';
                    ToolTip = 'Specifies the value of the System Created By field.';
                }
                field(UserName; ErkHoldingBasicFunctions.GetUserNameFromSecurityId(Rec.SystemCreatedBy))
                {
                    ApplicationArea = All;
                    Caption = 'Created By';
                    ToolTip = 'Specifies the value of the User Name field.';
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ApplicationArea = All;
                    Caption = 'Created At';
                    ToolTip = 'Specifies the value of the System Created At field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(VehicleList)
            {
                ApplicationArea = All;
                Caption = 'Vehicle List';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Vehicle List action.';
                Image = AmountByPeriod;
                PromotedOnly = true;

                trigger OnAction()
                begin
                    CarCarrierManagement.OpenVehicleDistributionList(Rec);
                end;
            }
            action(CancelDistribution)
            {
                ApplicationArea = All;
                Caption = 'Cancel Distribution';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Cancel Distribution action.';
                Image = AmountByPeriod;
                PromotedOnly = true;

                trigger OnAction()
                begin
                    CarCarrierManagement.CancelDistribution(Rec);
                end;
            }
            action(CreatePuchaseInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Puchase Invoice';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Create Puchase Invoice action.';
                Image = AmountByPeriod;
                PromotedOnly = true;

                trigger OnAction()
                var
                    CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
                begin
                    if Rec."Posting Date" < 20240101D then
                        Error(DateErr);
                    CurrPage.SetSelectionFilter(CarCarrRevenueExpense);
                    CarCarrierManagement.CreatePurchaseInvoiceFromCarCarrierRevenueExpense(CarCarrRevenueExpense);
                end;
            }
            action(CreateSalesInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Sales Invoice';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Create Sales Invoice action.';
                Image = AmountByPeriod;
                PromotedOnly = true;

                trigger OnAction()
                var
                    CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
                begin
                    if Rec."Posting Date" < 20240101D then
                        Error(DateErr);
                    CurrPage.SetSelectionFilter(CarCarrRevenueExpense);
                    CarCarrierManagement.CreateSalesInvoiceFromCarCarrierRevenueExpense(CarCarrRevenueExpense);
                end;
            }
            action(CreateConsumptionInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Consumption Invoice';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Create Consumption Invoice action.';
                Image = AmountByPeriod;
                PromotedOnly = true;

                trigger OnAction()
                var
                    CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
                begin
                    if Rec."Posting Date" < 20240101D then
                        Error(DateErr);
                    CurrPage.SetSelectionFilter(CarCarrRevenueExpense);
                    CarCarrierManagement.CreateConsumptionInvoiceFromCarCarrierRevenueExpense(CarCarrRevenueExpense);
                end;
            }
            action(CalculateConsumtionCosts)
            {
                ApplicationArea = All;
                Caption = 'Calculate Consumption Costs';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Calculate Consumption Costs action.';
                Image = AmountByPeriod;
                PromotedOnly = true;

                trigger OnAction()
                var
                    CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
                begin
                    CarCarrRevenueExpense.SetRange("Document No.", Rec."Document No.");
                    if CarCarrRevenueExpense.FindSet(true) then
                        repeat
                            CalculateConsumptionCostFromCarCarrierRevExp(Rec);
                        until CarCarrRevenueExpense.Next() = 0;
                end;
            }
            action(CreateRealizedLines)
            {
                ApplicationArea = All;
                Caption = 'Create Realized Lines';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Create Realized Lines action.';
                Image = AmountByPeriod;
                PromotedOnly = true;

                trigger OnAction()
                var
                    CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
                begin
                    CurrPage.SetSelectionFilter(CarCarrRevenueExpense);
                    CarCarrierManagement.CreateRealizedLinesFromExpectedCarCarrierRevenueExpenseLines(CarCarrRevenueExpense);
                end;
            }
        }
    }
    trigger OnNewRecord(BelowxRec: Boolean)
    var
        CarCarrierHeader: Record "Car Carrier Header ERK";
    begin
        // If user is creating a new record, try to get Document No. from filters
        if Rec.GetFilter("Document No.") <> '' then
            if CarCarrierHeader.Get(Rec.GetRangeMin("Document No.")) then
                Rec.Validate("Document No.", CarCarrierHeader."No.");
    end;

    procedure CalculateConsumptionCostFromCarCarrierRevExp(var CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK")
    var
        ValueEntry: Record "Value Entry";
        SalesInvoiceLine: Record "Sales Invoice Line";
        QtyPerUnitOfMeasureCode: Decimal;
    begin
        if CarCarrRevenueExpense.Type <> CarCarrRevenueExpense.Type::Consumption then
            exit;
        if CarCarrRevenueExpense."Posted Invoice No." = '' then
            exit;
        ValueEntry.SetRange("Document No.", CarCarrRevenueExpense."Posted Invoice No.");
        ValueEntry.SetRange("Global Dimension 2 Code", CarCarrRevenueExpense."Document No.");
        ValueEntry.SetRange("Item No.", CarCarrRevenueExpense."No.");
        ValueEntry.SetRange("Variant Code", CarCarrRevenueExpense."Variant Code");
        ValueEntry.CalcSums("Cost per Unit (ACY)", "Cost per Unit");
        SalesInvoiceLine.SetRange("Document No.", CarCarrRevenueExpense."Posted Invoice No.");
        SalesInvoiceLine.SetRange("Shortcut Dimension 2 Code", CarCarrRevenueExpense."Document No.");
        SalesInvoiceLine.SetRange("No.", CarCarrRevenueExpense."No.");
        SalesInvoiceLine.SetRange("Variant Code", CarCarrRevenueExpense."Variant Code");
        if SalesInvoiceLine.FindFirst() then
            QtyPerUnitOfMeasureCode := SalesInvoiceLine."Qty. per Unit of Measure"
        else
            QtyPerUnitOfMeasureCode := 1;
        CarCarrRevenueExpense.Validate("Unit Price/Cost", ValueEntry."Cost per Unit" * QtyPerUnitOfMeasureCode);
        CarCarrRevenueExpense.Modify(false);
    end;

    var
        CarCarrierManagement: Codeunit "Car Carrier Management ERK";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        PageManagement: Codeunit "Page Management";
        DateErr: Label 'Posting date must be greater than or equal to 01.01.24.';
}
