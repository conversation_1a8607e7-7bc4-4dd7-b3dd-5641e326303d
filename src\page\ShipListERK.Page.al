page 60022 "Ship List ERK"
{
    ApplicationArea = All;
    Caption = 'Ship List';
    PageType = List;
    SourceTable = "Ship ERK";
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Ship Card ERK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field(Name; Rec.Name)
                {
                }
                field("Ship Type"; Rec."Ship Type")
                {
                }
                field(Flag; Rec.Flag)
                {
                }
                // field("Cargo Type"; Rec."Cargo Type")
                // {
                //     ToolTip = 'Specifies the value of the Cargo Type field.';
                // }
                field("Lenght (m)"; Rec."Lenght (m)")
                {
                }
                field("Width (m)"; Rec."Width (m)")
                {
                }
                field("Draft (m)"; Rec."Draft (m)")
                {
                }
                field("GRT (ton)"; Rec."GRT (ton)")
                {
                }
                field("DWT (ton)"; Rec."DWT (ton)")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field("Average Speed (Knot)"; Rec."Average Speed (Knot)")
                {
                }
                field("Call Sign"; Rec."Call Sign")
                {
                }
                field("IFO/HSFO Consump. (Ton/Hour)"; Rec."Eco IFO/HSFO Cons. (Ton/Day)")
                {
                }
                field("Max IFO/HSFO Cons. (Ton/Day)"; Rec."Max IFO/HSFO Cons. (Ton/Day)")
                {
                }
                field("MGO Consumption (Ton/Day)"; Rec."MGO Consumption (Ton/Day)")
                {
                }
                field("Fuel Quantity"; Rec."Fuel Quantity")
                {
                }
                field("Contract Unit Cost"; Rec."Contract Unit Cost")
                {
                }
                field("Contract Currency Code"; Rec."Contract Currency Code")
                {
                }
            }
        }
    }
    // trigger OnOpenPage()
    // var
    //     WorkCenter: Record "Work Center";
    //     MachineCenter: Record "Machine Center";
    // begin
    //     WorkCenter.DeleteAll(false);
    //     MachineCenter.DeleteAll(false);
    // end;
}
