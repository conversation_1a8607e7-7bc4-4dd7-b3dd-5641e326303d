tableextension 60017 "Location ERK" extends Location
{
    fields
    {
        field(60000; "Car Carrier Related ERK"; Boolean)
        {
            Caption = 'Car Carrier Related';
            ToolTip = 'Specifies the value of the Car Carrier Related field.';
        }
        field(60001; "Req.Temp. Traffic Doc. ERK"; Bo<PERSON>an)
        {
            Caption = 'Required for Temp. Traffic Doc.';
            ToolTip = 'This field indicates if the location is required for temporary traffic documents.';
        }
        field(60002; "Declaration Approval Req. ERK"; Boolean)
        {
            Caption = 'Declaration Approval Required';
            ToolTip = 'This field indicates if the location is Declaration Approval Required field.';
        }
        field(60003; "Total Vehicle Capacity ERK"; Integer)
        {
            Caption = 'Total Vehicle Capacity';
            ToolTip = 'This field indicates the total vehicle capacity of the location.';
        }
        field(60004; "Create PDI Document ERK"; Boolean)
        {
            Caption = 'Create PDI Document';
            ToolTip = 'Specifies the value of the Create PDI Document field.';
        }
    }
}
