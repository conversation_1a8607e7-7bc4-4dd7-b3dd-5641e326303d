table 60030 "Model ERK"
{
    Caption = 'Model';
    DataClassification = CustomerContent;
    DrillDownPageId = "Model List ERK";
    LookupPageId = "Model List ERK";

    fields
    {
        field(1; Code; Code[40])
        {
            Caption = 'Code';
            NotBlank = true;
            ToolTip = 'Specifies the value of the Code field.';
        }
        field(2; "Brand Code"; Code[30])
        {
            Caption = 'Brand Code';
            //TableRelation = Manufacturer.Code;
            NotBlank = true;
            ToolTip = 'Specifies the value of the Brand Code field.';
        }
        field(3; "Width (mm)"; Decimal)
        {
            Caption = 'Width (mm)';
            DecimalPlaces = 0 : 0;
            ToolTip = 'Specifies the value of the Width (mm) field.';
            trigger OnValidate()
            begin
                SerialNoManagement.CalculateAreaAndVolumeFromModel(Rec);
            end;
        }
        field(4; "Length (mm)"; Decimal)
        {
            Caption = 'Length (mm)';
            DecimalPlaces = 0 : 0;
            ToolTip = 'Specifies the value of the Width (mm) field.';
            trigger OnValidate()
            begin
                SerialNoManagement.CalculateAreaAndVolumeFromModel(Rec);
            end;
        }
        field(5; "Height (mm)"; Decimal)
        {
            Caption = 'Height (mm)';
            DecimalPlaces = 0 : 0;
            ToolTip = 'Specifies the value of the Width (mm) field.';
            trigger OnValidate()
            begin
                SerialNoManagement.CalculateAreaAndVolumeFromModel(Rec);
            end;
        }
        field(6; "Gross Weight (kg)"; Decimal)
        {
            Caption = 'Gross Weight (kg)';
            DecimalPlaces = 0 : 0;
            ToolTip = 'Specifies the value of the Gross Weight (kg) field.';
        }
        field(7; "Volume (m3)"; Decimal)
        {
            Caption = 'Volume (m3)';
            Editable = false;
            ToolTip = 'Specifies the value of the Volume (m3) field.';
        }
        field(8; "Area (m2)"; Decimal)
        {
            Caption = 'Area (m2)';
            Editable = false;
            ToolTip = 'Specifies the value of the Area (m2) field.';
        }
    }
    keys
    {
        key(PK; Code)
        {
            Clustered = true;
        }
    }
    var
        SerialNoManagement: Codeunit "Serial No. Management ERK";
}
