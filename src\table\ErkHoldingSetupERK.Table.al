table 60000 "Erk Holding Setup ERK"
{
    Caption = 'Erk Holding Setup';

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "Export No. Series"; Code[20])
        {
            Caption = 'Export No. Series';
            DataClassification = CustomerContent;
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Export No. Series field.';
        }
        field(3; "Dimension Code For Export"; Code[20])
        {
            Caption = 'Dimension Code For Export';
            TableRelation = Dimension.Code;
            ToolTip = 'Specifies the value of the Dimension Code For Export field.';
        }
        field(4; "Voyage No. Series"; Code[20])
        {
            Caption = 'Voyage No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Voyage No. Series field.';
        }
        field(5; "Car Carrier No. Series"; Code[20])
        {
            Caption = 'Car Carrier No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Car Carrier Car Carrier No. Series field.';
        }
        field(6; "Trip No. Series"; Code[20])
        {
            Caption = 'Trip No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Trip No. Series field.';
        }
        field(7; "Empty Trip No Series"; Code[20])
        {
            Caption = 'Empty Trip No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Empty Trip No Series field.';
        }
        field(8; "MGO Item No."; Code[20])
        {
            Caption = 'MGO Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the value of the MGO Item No. field.';
        }
        field(9; "IFO Item No."; Code[20])
        {
            Caption = 'IFO Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the value of the IFO Item No. field.';
        }
        field(10; "Consumption Customer No."; Code[20])
        {
            Caption = 'Consumption Customer No.';
            TableRelation = Customer;
            ToolTip = 'Specifies the value of the Fuel Consumption Customer No. field.';
        }
        field(11; "Department Dimension Code"; Code[20])
        {
            Caption = 'Department Dimension Code';
            TableRelation = Dimension;
            ToolTip = 'Specifies the value of the Department Dimension Code field.';
        }
        // field(12; "Ro-Ro Dimension Value Code"; Code[20])
        // {
        //     Caption = 'Ro-Ro Dimension Value Code';
        //     TableRelation = "Dimension Value".Code where("Dimension Code" = field("Department Dimension Code"));
        // }
        field(13; "Hire Item No."; Code[20])
        {
            Caption = 'Hire Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the value of the Hire Item No. field.';
        }
        field(14; "Port Operation No. Series"; Code[20])
        {
            Caption = 'Port Operation No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Port Operation No. Series field.';
        }
        field(15; "Load Process Jnl. Template"; Code[10])
        {
            Caption = 'Load Process Item Jnl. Template';
            TableRelation = "Item Journal Template";
            ToolTip = 'Specifies the value of the Load Process Item Jnl. Template field.';
        }
        field(16; "Load Process Jnl. Batch"; Code[10])
        {
            Caption = 'Load Process Jnl. Batch';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Load Process Jnl. Template"));
            ToolTip = 'Specifies the value of the Load Process Jnl. Batch field.';
        }
        field(17; "Load Item No."; Code[20])
        {
            Caption = 'Load Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the value of the Load Item No. field.';
        }
        field(18; "Load Transfer Template Name"; Code[10])
        {
            Caption = 'Load Transfer Template Name';
            TableRelation = "Item Journal Template";
            ToolTip = 'Specifies the value of the Load Transfer Template Name field.';
        }
        field(19; "Load Transfer Batch Name"; Code[10])
        {
            Caption = 'Load Transfer Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Load Transfer Template Name"));
            ToolTip = 'Specifies the value of the Load Transfer Batch Name field.';
        }
        field(20; "Item No. for Vehicles"; Code[20])
        {
            Caption = 'Item No. for Vehicles';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Item No. for Vehicles field.';
        }
        field(21; "Vehicle Transfer No. Series"; Code[20])
        {
            Caption = 'Vehicle Transfer No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Vehicle Transfer No. Series field.';
        }
        field(22; "Stamp Tax Item No."; Code[20])
        {
            Caption = 'Stamp Tax Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Stamp Tax Item No. field.';
        }
        field(23; "PDI No. Series"; Code[20])
        {
            Caption = 'PDI No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the PDI No. Series field.';
        }
        field(24; "Customs Operation No. Series"; Code[20])
        {
            Caption = 'Customs Operation No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Customs Operation No. Series field.';
        }
        // field(25; "Ship Location Code"; Code[10])
        // {
        //     Caption = 'Ship Location Code';
        //     TableRelation = Location.Code;
        // }
        field(26; "Ballast No. Series"; Code[20])
        {
            Caption = 'Car Carrier Ballast No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Ballast No. Series field.';
        }
        field(33; "Ferry Ballast No. Series"; Code[20])
        {
            Caption = 'Ferry Ballast No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Ferry Ballast No. Series field.';
        }
        field(27; "Voyage Account No. Series"; Code[20])
        {
            Caption = 'Voyage Account No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Voyage Account No. Series field.';
        }
        field(28; "Ferry Fuel Item No."; Code[20])
        {
            Caption = 'Ferry Fuel Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Ferry Fuel Item No. field.';
        }
        field(29; "Def. Cons. Loc. for Logistics"; Code[10])
        {
            Caption = 'Def. Consumption Loc. for Logistics';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Def. Consumption Loc. for Logistics field.';
        }
        field(30; "Def. Cons. Bin for Logistics"; Code[20])
        {
            Caption = 'Default Consumption Bin for Logistics';
            TableRelation = Bin.Code where("Location Code" = field("Def. Cons. Loc. for Logistics"));
            ToolTip = 'Specifies the value of the Default Consumption Bin for Logistics field.';
        }
        field(31; "Logistics Dept. Value Code"; Code[20])
        {
            Caption = 'Logistics Department Value Code';
            TableRelation = "Dimension Value".Code where("Dimension Code" = field("Department Dimension Code"));
            ToolTip = 'Specifies the value of the Logistics Department Value Code field.';
        }
        field(32; "Car Carrier Order Nos"; Code[20])
        {
            Caption = 'Car Carrier Order Nos';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Car Carrier Order Nos field.';
        }
        field(25; "HSFO Item No."; Code[20])
        {
            Caption = 'HSFO Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the HSFO Item No. field.';
        }
        field(47; "LNG Item No."; Code[20])
        {
            Caption = 'LNG Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the LNG Item No. field.';
        }
        field(34; "Ferry Car Carrier Nos"; Code[20])
        {
            Caption = 'Ferry Car Carrier Nos';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Ferry Car Carrier Nos field.';
        }
        field(35; "Est. Revenue Item No."; Code[20])
        {
            Caption = 'Est. Revenue Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Est. Revenue Item No. field.';
        }
        field(36; "Est. Fuel Item No."; Code[20])
        {
            Caption = 'Est. Fuel Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Est. Fuel Item No. field.';
        }
        field(37; "Est. Hire Item No."; Code[20])
        {
            Caption = 'Est. Hire Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Est. Hire Item No. field.';
        }
        field(38; "Est. Other Cost Item No."; Code[20])
        {
            Caption = 'Est. Other Cost Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Est. Other Cost Item No. field.';
        }
        field(39; "Temp. Traffic Doc. No. Series"; Code[20])
        {
            Caption = 'Temp. Traffic Document No. Series';
            ToolTip = 'Specifies the value of the Temp. Traffic Document No. Series field.';
            TableRelation = "No. Series".Code;
        }
        field(40; "Inb. E-Inv Vendor Filter"; Text[250])
        {
            Caption = 'Inbound E-Invoice Vendor Filter';
            ToolTip = 'Specifies the value of the Inbound E-Invoice Vendor Filter field.';
        }
        field(41; "Port Operation Contract Nos"; Code[20])
        {
            Caption = 'Port Operation Contract Nos';
            ToolTip = 'Specifies the value of the Port Operation Contract Nos field.';
            TableRelation = "No. Series".Code;
        }
        field(43; "Date Filter for Rev./Exp."; Date)
        {
            Caption = 'Date Filter for Revenue/Expense Creation';
            ToolTip = 'Specifies the value of the Date Filter for Revenue/Expense Creation field.';
            DataClassification = CustomerContent;
        }
        field(42; "Excluded VPG Code for EBA"; Text[250])
        {
            Caption = 'Excluded Vendor Postin Gr. for EBA';
            ToolTip = 'Specifies the value of the Excluded Vendor Postin Gr. for EBA field.';
        }
        field(44; "Road Transport Nos"; Code[20])
        {
            Caption = 'Road Transport Nos';
            ToolTip = 'Specifies the value of the Road Transport No. Series field.';
            TableRelation = "No. Series".Code;
        }
        field(45; "Port Service Nos"; Code[20])
        {
            Caption = 'Port Service Nos';
            ToolTip = 'Specifies the no. series used for Port Service documents.';
            TableRelation = "No. Series".Code;
        }
        field(46; "Dredge Header Nos."; Code[20])
        {
            Caption = 'Dredge Header Nos.';
            ToolTip = 'Specifies the no. series used for Dredge Header documents.';
            TableRelation = "No. Series";
        }
    }
    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }
    var
        RecordHasBeenRead: Boolean;

    procedure GetRecordOnce()
    begin
        if RecordHasBeenRead then
            exit;
        Get();
        RecordHasBeenRead := true;
    end;

    procedure InsertIfNotExists()
    begin
        Reset();
        if not Get() then begin
            Init();
            Insert(true);
        end;
    end;
}
