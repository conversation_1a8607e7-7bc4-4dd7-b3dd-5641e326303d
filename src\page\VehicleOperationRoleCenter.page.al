page 60050 "Vehicle Operation Role Center"
{
    PageType = RoleCenter;
    Caption = 'Vehicle Operation';
    ApplicationArea = All;

    layout
    {
        area(RoleCenter)
        {
            // part(Headline; "Headline RC Vehicle Operation")
            // {
            //     ApplicationArea = Basic, Suite;
            // }
            part(Activities; "Vehicle Operation Activities")
            {
            }
            // part("Help And Chart Wrapper"; "Help And Chart Wrapper")
            // {
            //     ApplicationArea = Basic, Suite;
            // }
            // part("Report Inbox Part"; "Report Inbox Part")
            // {
            //     ApplicationArea = Basic, Suite;
            // }
            // part("Power BI Report Spinner Part"; "Power BI Report Spinner Part")
            // {
            //     ApplicationArea = Basic, Suite;
            // }
        }
    }
    actions
    {
        // area(Creation)
        // {
        //     action(Addressing)
        //     {
        //         RunPageMode = Create;
        //         Caption = 'Addressing';
        //         ToolTip = 'Add some tooltip here';
        //         Image = New;
        //         RunObject = page "Addressing";
        //         ApplicationArea = Basic, Suite;
        //     }
        // }
        area(Processing)
        {
            // group(New)
            // {
            //     action(AppNameMasterData)
            //     {
            //         RunPageMode = Create;
            //         Caption = 'AppNameMasterData';
            //         ToolTip = 'Register new AppNameMasterData';
            //         RunObject = page "AppNameMasterData Card";
            //         Image = DataEntry;
            //         ApplicationArea = Basic, Suite;
            //     }
            // }
            group("Vehicle Operations Group")
            {
                Caption = 'Vehicle Operations Group';
                //     action(VehicleQuery)
                //     {
                //         Caption = 'Vehicle Query';
                //         ToolTip = 'Vehicle Query.';
                //         Image = Process;
                //         RunObject = Codeunit "Vehicle Query ERK";
                //         ApplicationArea = Basic, Suite;
                //     }
                // }
                // action(VehicleQuery2)
                // {
                //     Caption = 'Vehicle Query';
                //     ToolTip = 'Vehicle Query.';
                //     Image = Process;
                //     RunObject = Codeunit "Vehicle Query ERK";
                //     ApplicationArea = Basic, Suite;
            }
            // group("Vehicle Operation Reports")
            // {
            //     action(AppNameSomeReport)
            //     {
            //         Caption = 'AppNameSomeReport';
            //         ToolTip = 'AppNameSomeReport description';
            //         Image = Report;
            //         RunObject = report "AppNameSomeReport";
            //         ApplicationArea = Basic, Suite;
            //     }
            // }
        }
        // area(Reporting)
        // {
        //     action(AppNameSomeReport)
        //     {
        //         Caption = 'AppNameSomeReport';
        //         ToolTip = 'AppNameSomeReport description';
        //         Image = Report;
        //         RunObject = report "AppNameSomeReport";
        //         Promoted = true;
        //         PromotedCategory = Report;
        //         PromotedIsBig = true;
        //         ApplicationArea = Basic, Suite;
        //     }
        // }
        area(Embedding)
        {
            action("Vehicle List")
            {
                Caption = 'Vehicle List';
                RunObject = page "Serial No. Information List";
                ToolTip = 'Executes the Vehicle List action.';
            }
            action("Vehicle Ledger Entries")
            {
                Caption = 'Vehicle Ledger Entries';
                RunObject = page "Vehicle Transfer Ledg. Entries";
                ToolTip = 'Executes the Vehicle Ledger Entries action.';
            }
        }
        // area(Sections)
        // {
        //     group(Setup)
        //     {
        //         Caption = 'Setup';
        //         ToolTip = 'Overview and change system and application settings, and manage extensions and services';
        //         Image = Setup;
        //         action("Vehicle Operation Setup")
        //         {
        //             ToolTip = 'Setup Vehicle Operation';
        //             RunObject = Page "Vehicle Operation Setup";
        //             ApplicationArea = Basic, Suite;
        //         }
        //         action("Assisted Setup")
        //         {
        //             ToolTip = 'Set up core functionality such as sales tax, sending documents as email, and approval workflow by running through a few pages that guide you through the information.';
        //             RunObject = Page "Assisted Setup";
        //             ApplicationArea = Basic, Suite;
        //         }
        //         action("Manual Setup")
        //         {
        //             ToolTip = 'Define your company policies for business departments and for general activities by filling setup windows manually.';
        //             RunObject = Page "Business Setup";
        //             ApplicationArea = Basic, Suite;
        //         }
        //         action("Service Connections")
        //         {
        //             ToolTip = 'Enable and configure external services, such as exchange rate updates, Microsoft Social Engagement, and electronic bank integration.';
        //             RunObject = Page "Service Connections";
        //             ApplicationArea = Basic, Suite;
        //         }
        //     }
        // }
    }
}
