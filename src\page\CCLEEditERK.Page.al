page 60075 "CCLE - Edit ERK"
{
    ApplicationArea = All;
    Caption = 'CCLE - Edit';
    PageType = List;
    SourceTable = "Car Carrier Ledger Entry ERK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Document Line Detail No."; Rec."Document Line Detail No.")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Loading Port"; Rec."Loading Port")
                {
                }
                field("Discharge Port"; Rec."Discharge Port")
                {
                }
                field("Loading DateTime"; Rec."Loading DateTime")
                {
                }
                field("Discharge DateTime"; Rec."Discharge DateTime")
                {
                }
                field("From Document No."; Rec."From Document No.")
                {
                }
                field("Carline Code"; Rec."Model Code")
                {
                }
                field("Loading Port Description"; Rec."Loading Port Description")
                {
                }
                field("Discharge Port Description"; Rec."Discharge Port Description")
                {
                }
                field("Loading to Discharge Desc."; Rec."Loading to Discharge Desc.")
                {
                }
                field("Ship No."; Rec."Ship No.")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
            }
        }
    }
}
