report 60000 "Proforma Invoice ERK"
{
    Caption = 'Proforma Invoice';
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;

    dataset
    {
        dataitem(SalesHeader; "Sales Header")
        {
            column(WorkDescription; ErkHoldingBasicFunctions.GetWorkDescription(SalesHeader))
            {
            }
            column(PaymentTermsCode_SalesHeader; "Payment Terms Code")
            {
            }
            column(PortofArrivalERK_SalesHeader; VoyageMangement.GetEntryExitDescriptionFromEntryExitCode(SalesHeader."Port of Arrival ERK"))
            {
            }
            column(DocumentDate_SalesHeader; "Document Date")
            {
            }
            column(No_SalesHeader; "No.")
            {
            }
            column(BilltoAddress_SalesHeader; "Bill-to Address")
            {
            }
            column(BilltoAddress2_SalesHeader; "Bill-to Address 2")
            {
            }
            column(BilltoCity_SalesHeader; "Bill-to City")
            {
            }
            column(BilltoContact_SalesHeader; "Bill-to Contact")
            {
            }
            column(BilltoContactNo_SalesHeader; "Bill-to Contact No.")
            {
            }
            column(BilltoCountryRegionCode_SalesHeader; "Bill-to Country/Region Code")
            {
            }
            column(BilltoCounty_SalesHeader; "Bill-to County")
            {
            }
            column(BilltoCustomerNo_SalesHeader; "Bill-to Customer No.")
            {
            }
            column(BilltoCustomerTemplCode_SalesHeader; "Bill-to Customer Templ. Code")
            {
            }
            column(BilltoICPartnerCode_SalesHeader; "Bill-to IC Partner Code")
            {
            }
            column(BilltoName_SalesHeader; "Bill-to Name")
            {
            }
            column(BilltoName2_SalesHeader; "Bill-to Name 2")
            {
            }
            column(BilltoPostCode_SalesHeader; "Bill-to Post Code")
            {
            }
            column(SelltoAddress_SalesHeader; "Sell-to Address")
            {
            }
            column(SelltoAddress2_SalesHeader; "Sell-to Address 2")
            {
            }
            column(SelltoCity_SalesHeader; "Sell-to City")
            {
            }
            column(SelltoContact_SalesHeader; "Sell-to Contact")
            {
            }
            column(SelltoContactNo_SalesHeader; "Sell-to Contact No.")
            {
            }
            column(SelltoCountryRegionCode_SalesHeader; "Sell-to Country/Region Code")
            {
            }
            column(SelltoCounty_SalesHeader; "Sell-to County")
            {
            }
            column(SelltoCustomerName_SalesHeader; "Sell-to Customer Name")
            {
            }
            column(SelltoCustomerName2_SalesHeader; "Sell-to Customer Name 2")
            {
            }
            column(SelltoCustomerNo_SalesHeader; "Sell-to Customer No.")
            {
            }
            column(SelltoCustomerTemplCode_SalesHeader; "Sell-to Customer Templ. Code")
            {
            }
            column(SelltoEMail_SalesHeader; "Sell-to E-Mail")
            {
            }
            column(SelltoICPartnerCode_SalesHeader; "Sell-to IC Partner Code")
            {
            }
            column(SelltoPhoneNo_SalesHeader; "Sell-to Phone No.")
            {
            }
            column(ShiptoCity_SalesHeader; "Ship-to City")
            {
            }
            column(ShipmentMethodCode_SalesHeader; "Shipment Method Code")
            {
            }
            column(PaymentMethodCode_SalesHeader; "Payment Method Code")
            {
            }
            column(SelltoPostCode_SalesHeader; "Sell-to Post Code")
            {
            }
            column(CountryRegionName; ErkHoldingBasicFunctions.GetCountryRegionNameInCurrentLanguage("Ship-to Country/Region Code"))
            {
            }
            column(EstimatedTimeofDeliveryERK_SalesHeader; "Estimated Time of Delivery ERK")
            {
            }
            column(ShiptoAddress_SalesHeader; "Ship-to Address")
            {
            }
            column(ShiptoAddress2_SalesHeader; "Ship-to Address 2")
            {
            }
            column(ShiptoCode_SalesHeader; "Ship-to Code")
            {
            }
            column(ShiptoContact_SalesHeader; "Ship-to Contact")
            {
            }
            column(ShiptoCountryRegionCode_SalesHeader; "Ship-to Country/Region Code")
            {
            }
            column(ShiptoCounty_SalesHeader; "Ship-to County")
            {
            }
            column(ShiptoName_SalesHeader; "Ship-to Name")
            {
            }
            column(ShiptoName2_SalesHeader; "Ship-to Name 2")
            {
            }
            column(ShiptoPostCode_SalesHeader; "Ship-to Post Code")
            {
            }
            column(PaymentMethodDescription; ErkHoldingBasicFunctions.GetTranslatedPaymentTermsDescriptionFromCode("Payment Terms Code", 'ENU'))
            {
            }
            dataitem(CompanyInformation; "Company Information")
            {
                column(Name_CompanyInformation; Name)
                {
                }
                column(Picture_CompanyInformation; Picture)
                {
                }
                column(Name2_CompanyInformation; "Name 2")
                {
                }
                column(Address_CompanyInformation; Address)
                {
                }
                column(Address2_CompanyInformation; "Address 2")
                {
                }
                column(City_CompanyInformation; City)
                {
                }
                column(County_CompanyInformation; County)
                {
                }
                column(CountryNameINF_CompanyInformation; "Country Name INF")
                {
                }
                column(CountryRegionCode_CompanyInformation; "Country/Region Code")
                {
                }
                column(PostCode_CompanyInformation; "Post Code")
                {
                }
                column(EMail_CompanyInformation; "E-Mail")
                {
                }
                // column(HomePage_CompanyInformation; "Home Page")
                // {
                // }
                column(PhoneNo_CompanyInformation; "Phone No.")
                {
                }
                column(FaxNo_CompanyInformation; "Fax No.")
                {
                }
            }
            dataitem("Sales Line"; "Sales Line")
            {
                DataItemLink = "Document Type" = field("Document Type"), "Document No." = field("No.");

                column(LineNo_SalesLine; "Line No.")
                {
                }
                column(UnitofMeasure_SalesLine; "Unit of Measure")
                {
                }
                column(UnitofMeasureCode_SalesLine; ErkHoldingBasicFunctions.GetTranslatedUnitOfMeasureDescription("Unit of Measure Code", 'ENU'))
                {
                }
                column(Description_SalesLine; ExportManagement.GetItemTranslation("No.", "Variant Code", 'ENU'))
                {
                }
                column(Description2_SalesLine; "Description 2")
                {
                }
                column(No_SalesLine; "No.")
                {
                }
                column(VariantCode_SalesLine; "Variant Code")
                {
                }
                column(Quantity_SalesLine; Quantity)
                {
                }
                column(UnitPrice_SalesLine; "Unit Price")
                {
                }
                column(CurrencyCode_SalesLine; "Currency Code")
                {
                }
                column(AmountIncludingVAT_SalesLine; "Amount Including VAT")
                {
                }
                column(Brand; ExportManagement.GetBrandFromItemAndVariantCode("No.", "Variant Code"))
                {
                }
                column(Specification; ExportManagement.GetSpecificationFromItemAndVariantCode("No.", "Variant Code"))
                {
                }
                column(CountryofOrigin; ErkHoldingBasicFunctions.GetCountryRegionNameInCurrentLanguage(ErkHoldingBasicFunctions.GetCountryOfOriginFromItemNo("No.")))
                {
                }
            }
            dataitem("Bank Account"; "Bank Account")
            {
                DataItemLink = "No." = field("Company Bank Account Code");
                CalcFields = "Bank Name INF", "Branch Name INF";

                column(No_BankAccount; "No.")
                {
                }
                column(Name_BankAccount; Name)
                {
                }
                column(IBAN_BankAccount; IBAN)
                {
                }
                column(SWIFTCode_BankAccount; "SWIFT Code")
                {
                }
                column(BankCodeINF_BankAccount; "Bank Code INF")
                {
                }
                column(BankNameINF_BankAccount; "Bank Name INF")
                {
                }
                column(BranchCodeINF_BankAccount; "Branch Code INF")
                {
                }
                column(BranchNameINF_BankAccount; "Branch Name INF")
                {
                }
            }
        }
    }
    trigger OnInitReport()
    begin
        CompanyInformation.GetRecordOnce();
    end;

    var
        ExportManagement: Codeunit "Export Management ERK";
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
        VoyageMangement: Codeunit "Voyage Mangement ERK";
}
