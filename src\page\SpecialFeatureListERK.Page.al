page 60006 "Special Feature List ERK"
{
    ApplicationArea = ExportManagementERK;
    Caption = 'Special Feature List ERK';
    PageType = List;
    SourceTable = "Special Feature ERK";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Code; Rec.Code)
                {
                }
                field(Description; Rec.Description)
                {
                }
            }
        }
    }
}
