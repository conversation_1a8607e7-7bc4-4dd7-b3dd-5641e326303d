page 60110 "VATWitholdBusPostingGrAPI ERK"
{
    PageType = API;
    Caption = 'VAT Withhold Bus. Posting Group API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'vatWitholdBusPostingGroup';
    EntitySetName = 'vatWitholdBusPostingGroup';
    SourceTable = "VAT WitholdBusPostingGroup INF";
    DelayedInsert = true;
    ODataKeyFields = SystemId;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(id; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(code; Rec.Code)
                {
                    Caption = 'Code';
                }
                field(description; Rec.Description)
                {
                    Caption = 'Description';
                }
                field(lastModifiedDateTime; Rec.SystemModifiedAt)
                {
                    Caption = 'Last Modified Date Time';
                }
            }
        }
    }
}
