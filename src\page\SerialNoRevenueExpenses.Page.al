page 60033 "Serial No. Revenue/Expenses"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Serial No. Revenue/Expenses';
    PageType = List;
    SourceTable = "Serial No. Revenue/Expense ERK";
    UsageCategory = Lists;

    //Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Car Carrier Rev/Exp Line No."; Rec."Car Carrier Rev/Exp Line No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Unposted Invoice No."; Rec."Unposted Invoice No.")
                {
                }
                field("Posted Invoice No."; Rec."Posted Invoice No.")
                {
                }
                field("Invoice Line No."; Rec."Invoice Line No.")
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Source No."; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                // field("External Document No."; Rec."External Document No.")
                // {
                //     ToolTip = 'Specifies the value of the External Document No. field.';
                // }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field(Amount; Rec.Amount)
                {
                }
                field("Amount (ACY)"; Rec."Amount (ACY)")
                {
                }
                // field("Invoice Date"; Rec."Invoice Date")
                // {
                //     ToolTip = 'Specifies the value of the Invoice Date field.';
                // }
                // field("Invoice Line No."; Rec."Invoice Line No.")
                // {
                //     ToolTip = 'Specifies the value of the Invoice Line No. field.';
                // }
                // field("Invoice No."; Rec."Invoice No.")
                // {
                //     ToolTip = 'Specifies the value of the Invoice No. field.';
                // }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}
