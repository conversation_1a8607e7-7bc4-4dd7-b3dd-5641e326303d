codeunit 60021 PurchaseOrderAPISubscriberERK
{
    EventSubscriberInstance = Manual;

    var
        PurchaseOrderAggregate: Record "Purchase Order Entity Buffer";

    procedure SetSalesLineInvoiceLineAggregate(Rec: Record "Purchase Order Entity Buffer")
    begin
        PurchaseOrderAggregate := Rec;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnBeforeInsertEvent, '', false, false)]
    local procedure OnBeforeInsertPurchaseOrder(var Rec: Record "Purchase Header")
    begin
        if Rec.IsTemporary() then
            exit;
        UpdatePurchaseHeader(Rec);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnBeforeModifyEvent, '', false, false)]
    local procedure OnBeforeModifyPurchaseOrder(var Rec: Record "Purchase Header")
    begin
        if Rec.IsTemporary() then
            exit;
        UpdatePurchaseHeader(Rec);
    end;

    local procedure UpdatePurchaseHeader(var PurchaseHeader: Record "Purchase Header")
    begin
        PurchaseHeader."Your Reference" := PurchaseOrderAggregate."EBA Your Reference ERK";

    end;



}