tableextension 60025 "E-Invoice Header Ext. ERK" extends "E-Invoice Header INF"
{
    fields
    {
        field(60000; "EBA Status ERK"; Enum "EBA Status ERK")
        {
            Caption = 'EBA Status';
            ToolTip = 'Specifies EBA Status field.';
            InitValue = 1;
            //Editable = false;

        }
    }



    keys
    {
        // Add changes to keys here
    }

    fieldgroups
    {
        // Add changes to field groups here
    }

    procedure GetPDFFileData() PDFFileData: Text
    var
        TypeHelper: Codeunit "Type Helper";
        InStr: InStream;

    begin
        CalcFields("Invoice View PDF File");
        "Invoice View PDF File".CreateInStream(InStr, TextEncoding::UTF8);
        if not TypeHelper.TryReadAsTextWithSeparator(InStr, TypeHelper.LFSeparator(), PDFFileData) then
            PDFFileData := 'Error reading PDF file data.';
    end;
}