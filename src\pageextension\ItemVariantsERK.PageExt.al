pageextension 60005 "Item Variants ERK" extends "Item Variants"
{
    layout
    {
        addlast(Control1)
        {
            field("Packaging Type ERK"; Rec."Packaging Type ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
            field("Special Feature ERK"; Rec."Special Feature ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
            field("Brand ERK"; Rec."Brand ERK")
            {
                ApplicationArea = ExportManagementERK;
            }
            field("Tariff No. ERK"; Rec."Tariff No. ERK")
            {
                ApplicationArea = All;
            }
            field("Country/Region of Origin ERK"; Rec."Country/Region of Origin ERK")
            {
                ApplicationArea = All;
            }
        }
    }
}
