page 60099 "Inbound E-Invoices API ERK"
{
    PageType = API;
    Caption = 'Inbound E-Inv. List API';
    APIPublisher = 'infotek';
    APIGroup = 'eh';
    APIVersion = 'v1.0';
    EntityName = 'inboundEInvoices';
    EntitySetName = 'inboundEInvoices';
    SourceTable = "E-Invoice Header INF";
    SourceTableView = sorting("E-Invoice No.") where("Document Type" = filter("Purchase Invoice" | "Sales Cr. Memo"), "Get Detail Invoice Info" = const(true));
    DelayedInsert = true;
    ODataKeyFields = SystemId;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field(id; Rec.SystemId)
                {
                    Caption = 'Id';
                }
                field(ebaStatusERK; Rec."EBA Status ERK")
                {
                    Caption = 'EBA Status';
                }
                field(supplierIDSchemaID; Rec."Supplier ID Schema ID")
                {
                    Caption = 'Supplier ID Schema ID';
                }
                field(supplierID; Rec."Supplier ID")
                {
                    Caption = 'Supplier ID';
                }
#pragma warning disable LC0063
                field(eInvoiceNo; Rec."E-Invoice No.")
#pragma warning restore LC0063
                {
                    Caption = 'E-Invoice No.';
                }
                field(sourceType; Rec."Source Type")
                {
                    Caption = 'Source Type';
                }
#pragma warning disable LC0063
                field(sourceNo; Rec."Source No.")
#pragma warning restore LC0063
                {
                    Caption = 'Source No.';
                }

                field(supplierName; Rec."Supplier Name")
                {
                    Caption = 'Supplier Name';
                }
                field(supplierPersonName; Rec."Supplier Person Name")
                {
                    Caption = 'Supplier Person Name';
                }
                field(supplierPersonSurname; Rec."Supplier Person Surname")
                {
                    Caption = 'Supplier Person Surname';
                }
                field(postingDate; Rec."Posting Date")
                {
                    Caption = 'Posting Date';
                }
                field(issueDate; Rec."Issue Date")
                {
                    Caption = 'Issue Date';
                }
                field(issueTime; Rec."Issue Time")
                {
                    Caption = 'Issue Time';
                }
                field(ublLineExtensionAmount; Rec."UBL Line Extension Amount")
                {
                    Caption = 'UBL Line Extension Amount';
                }
                field(ublTaxExclusiveAmount; Rec."UBL Tax Exclusive Amount")
                {
                    Caption = 'UBL Tax Exclusive Amount';
                }
                field(ublTaxInclusiveAmount; Rec."UBL Tax Inclusive Amount")
                {
                    Caption = 'UBL Tax Inclusive Amount';
                }
                field(inbUBLPaymentAmount; Rec."Inb. UBL Payment Amount")
                {
                    Caption = 'Inb. UBL Payment Amount';
                }
                field(invoiceTypeCode; Rec."Invoice Type Code")
                {
                    Caption = 'Invoice Type Code';
                }
                field(paymentTermsCode; Rec."Payment Terms Code")
                {
                    Caption = 'Payment Terms Code';
                }
                field(documentCurrencyCode; Rec."Document Currency Code")
                {
                    Caption = 'Document Currency Code';
                }
                field(ublDocCurrCode; Rec."UBL Doc. Curr. Code")
                {
                    Caption = 'UBL Doc. Currency Code';
                }
                field(lastModifiedDateTime; Rec.SystemModifiedAt)
                {
                    Caption = 'SystemModifiedAt';
                }
                field(invoiceViewPDFFile; Rec."Invoice View PDF File")
                {
                    Caption = 'Invoice View PDF File';
                }
            }
        }
    }

    // trigger OnAfterGetCurrRecord()
    // begin
    //     Rec.CalcFields("Invoice View PDF File");
    //     Clear(Rec."Invoice View PDF File");
    //     Rec.Modify(true);
    // end;
}