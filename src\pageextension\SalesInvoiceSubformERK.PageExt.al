pageextension 60034 "Sales Invoice Subform ERK" extends "Sales Invoice Subform"
{
    layout
    {
        modify("Variant Code")
        {
            Visible = true;
        }
        modify("Bin Code")
        {
            Visible = true;
        }
        modify("Unit Cost (LCY)")
        {
            Visible = true;
        }
        addafter(Description)
        {
            field("Vessel Load/Unload Type ERK"; Rec."Vessel Load/Unload Type ERK")
            {
                ApplicationArea = All;
                Editable = Rec.Type = Rec.Type::"Representative COI INF";
            }
            field("Load Type ERK"; Rec."Load Type ERK")
            {
                ApplicationArea = All;
                Editable = Rec.Type = Rec.Type::"Representative COI INF";
            }
        }
        addafter(Quantity)
        {
            field("Distributed Quantity ERK"; Rec."Distributed Quantity ERK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addfirst("&Line")
        {
            action("OpenVehicleDistributionWorksheet ERK")
            {
                ApplicationArea = All;
                Caption = 'Open Vehicle Distribution Worksheet';
                Image = AccountingPeriods;
                ToolTip = 'Executes the Open Vehicle Distribution Worksheet action.';
                Enabled = not SalesHeaderCreatedFromCarCarrier;

                trigger OnAction()
                var
                    SalesHeader: Record "Sales Header";
                begin
                    SalesHeader.Get(Rec."Document Type", Rec."Document No.");

                    VehicleRevExpManagement.CreateVehicleRevenueExpenseWorksheet(Rec."Document No.",
                                                                                 Rec."Line No.",
                                                                                 Enum::"Voyage Line Detail Type ERK"::Revenue,
                                                                                 Rec."No.",
                                                                                 Rec."Variant Code",
                                                                                 '',
                                                                                 SalesHeader."Sell-to Customer No.",
                                                                                 SalesHeader."Your Reference",
                                                                                 SalesHeader."Posting Date",
                                                                                 SalesHeader."Currency Code",
                                                                                 SalesHeader."External Document No.",
                                                                                 Rec."Line Amount",
                                                                                 true,
                                                                                 SalesHeader."Shortcut Dimension 1 Code");
                end;
            }
        }
    }

    trigger OnAfterGetCurrRecord()
    var
        SalesHeader: Record "Sales Header";
    begin
        if SalesHeader.Get(Rec."Document Type", Rec."Document No.") then
            SalesHeaderCreatedFromCarCarrier := SalesHeader."Created From Car Carrier ERK"
        else
            SalesHeaderCreatedFromCarCarrier := false;
    end;

    var
        VehicleRevExpManagement: Codeunit "Vehicle Rev./Exp. Management";
        SalesHeaderCreatedFromCarCarrier: Boolean;
}
