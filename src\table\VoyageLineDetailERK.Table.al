table 60012 "Voyage Line Detail ERK"
{
    Caption = 'Voyage Line Detail';
    DataClassification = CustomerContent;
    DrillDownPageId = "Voyage Line Detail List ERK";
    LookupPageId = "Voyage Line Detail List ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document Line No. field.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(4; "No."; Code[20])
        {
            Caption = 'No.';
            TableRelation = Item."No."; // where(Type = const(Service));
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                Rec."Variant Code" := '';
                if Item.Get(Rec."No.") then begin
                    Rec.Validate(Description, Item.Description);
                    Rec.Validate("Unit of Measure Code", Item."Base Unit of Measure");
                    Rec.Validate("Unit Price", Item."Unit Price");
                end
                else begin
                    Rec.Validate(Description, '');
                    Rec.Validate("Unit of Measure Code", '');
                    Rec.Validate("Unit Price", 0);
                end;
            end;
        }
        field(5; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(6; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DecimalPlaces = 0 : 3;
            ToolTip = 'Specifies the value of the Quantity field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Price", 0));
            end;
        }
        field(7; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            TableRelation = "Unit of Measure".Code;
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(8; "Unit Price"; Decimal)
        {
            Caption = 'Unit Price';
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the value of the Unit Price field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Price", 0));
            end;
        }
        field(9; "Line Amount"; Decimal)
        {
            Caption = 'Line Amount';
            Editable = false;
            ToolTip = 'Specifies the value of the Line Amount field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount (ACY)", ExportManagement.ConvertAmountToACY(Rec."Posting Date", Rec."Currency Code", Rec."Line Amount"));
            end;
        }
        field(10; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency;
            ToolTip = 'Specifies the value of the Currency Code field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Price", 0));
            end;
            //Editable = false;
            // FieldClass = FlowField;
            // CalcFormula = lookup("Voyage Line ERK"."Currency Code" where("Document No." = field("Document No."), "Line No." = field("Document Line No.")));
        }
        field(11; "Invoice No."; Code[20])
        {
            Caption = 'Invoice No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Sales Invoice No. field.';
        }
        field(12; "Invoice Line No."; Integer)
        {
            Caption = 'Invoice Line No.';
            Editable = false;
            BlankZero = false;
            ToolTip = 'Specifies the value of the Sales Invoice Line No. field.';
        }
        field(13; "External Document No."; Code[35])
        {
            Caption = 'External Document No.';
            ToolTip = 'Specifies the value of the External Document No. field.';
        }
        field(14; "Variant Code"; Code[10])
        {
            Caption = 'Variant No.';
            TableRelation = "Item Variant".Code where("Item No." = field("No."));
            ToolTip = 'Specifies the value of the Variant No. field.';
            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                Rec.TestField("Invoice No.", '');
                if ItemVariant.Get("No.", "Variant Code") then
                    Description := ItemVariant.Description
                else
                    Description := '';
            end;
        }
        // field(15; "Department Code"; Code[20])
        // {
        //     Caption = 'Department Code';
        //     TableRelation = "Dimension Value".Code where("Dimension Code" = const('Departman'));
        // }
        // field(16; "Location Code"; Code[10])
        // {
        //     Caption = 'Location Code';
        //     TableRelation = Location.Code;
        // }
        // field(17; "Fuel Consumption Quantity (LT)"; Decimal)
        // {
        //     Caption = 'Fuel Consumption Quantity (LT)';
        // }
        // field(18; "Service Area Code"; Code[20])
        // {
        //     Caption = 'Service Area Code';
        //     TableRelation = "Dimension Value".Code where("Dimension Code" = const('HİZMETKONUMU'));
        // }
        field(19; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Amount", VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Price", 0));
            end;
        }
        field(20; Type; Enum "Voyage Line Detail Type ERK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
            trigger OnValidate()
            var
                VoyageLine: Record "Voyage Line ERK";
            begin
                if Rec.Type = Rec.Type::Revenue then begin
                    VoyageLine.Get(Rec."Document No.", Rec."Document Line No.");
                    Rec.Validate("Source No.", VoyageLine."Customer No.");
                end
                else
                    if Rec.Type = Rec.Type::Expense then begin
                        Rec."Source No." := '';
                        Rec."Source Name" := '';
                    end;
            end;
        }
        field(21; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = if (Type = const(Expense)) Vendor
            else if (Type = const(Revenue)) Customer;
            ToolTip = 'Specifies the value of the Vendor No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
                Vendor: Record Vendor;
            begin
                if Type = Type::Revenue then begin
                    Customer.Get("Source No.");
                    "Source Name" := Customer.Name;
                    "Currency Code" := Customer."Currency Code";
                end
                else
                    if Type = Type::Expense then begin
                        Vendor.Get("Source No.");
                        "Source Name" := Vendor.Name;
                        "Currency Code" := Vendor."Currency Code";
                    end;
            end;
        }
        field(15; "Source Name"; Text[100])
        {
            Caption = 'Source Name';
            ToolTip = 'Specifies the value of the Source Name field.';
        }
        field(16; "Is Cancelled"; Boolean)
        {
            Caption = 'Is Cancelled';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = exist("Cancelled Document" where("Cancelled Doc. No." = field("Invoice No.")));
            ToolTip = 'Specifies the value of the Is Cancelled field.';
        }
        field(17; "Line Amount (ACY)"; Decimal)
        {
            Caption = 'Line Amount (ACY)';
            Editable = false;
            ToolTip = 'Specifies the value of the Line Amount (ACY) field.';
        }
        field(18; Posted; Boolean)
        {
            Caption = 'Posted';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = exist("Value Entry" where("Document No." = field("Invoice No.")));
            ToolTip = 'Specifies the value of the Posted field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
        }
        key(Key2; Type)
        {
        }
    }
    trigger OnInsert()
    var
        VoyageLineDetail: Record "Voyage Line Detail ERK";
    begin
        VoyageLineDetail.SetRange("Document No.", Rec."Document No.");
        VoyageLineDetail.SetRange("Document Line No.", Rec."Document Line No.");
        if VoyageLineDetail.FindLast() then
            Rec."Line No." := VoyageLineDetail."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnModify()
    begin
        TestField("Invoice No.", '');
    end;

    trigger OnDelete()
    begin
        Rec.TestField("Invoice No.", '');
    end;

    var
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        ExportManagement: Codeunit "Export Management ERK";
}
