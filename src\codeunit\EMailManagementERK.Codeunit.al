codeunit 60012 "E-Mail Management ERK"
{
    TableNo = "Job Queue Entry";

    trigger OnRun()
    var
        InvalidParameterStringErr: Label 'Invalid Report No.';
        ReportNo: Integer;
    begin
        Evaluate(ReportNo, Rec."Parameter String");
        case Rec."Parameter String" of
            '60003': //Ankes Report
                SendEMail_Ankes(ReportNo);
            '60006': //Logistics Daily Report
                SendEMail_LogisticsDailyReport(ReportNo);
            '120', '60011', '322': //120 Aged Account Receivables
                SendEMail_120_AgedAccountReceivables(ReportNo);
            else
                Error(InvalidParameterStringErr);
        end;
    end;

    procedure SendEMail_Ankes(ReportNo: Integer)
    var
        TempBlob: Codeunit "Temp Blob";
        Email: Codeunit Email;
        EmailMessage: Codeunit "Email Message";
        NoReportSendingSetupFoundErr: Label 'Report Sending Setup not found.';
        ReportInStream: InStream;
        ReportOutStream: OutStream;
        BCCList: List of [Text];
        CCList: List of [Text];
        RecipientList: List of [Text];
        FileName: Text[250];
        SubjectText: Text[250];
        BodyText: Text[250];
        FileExtension: Text;
        ReportFormatValue: ReportFormat;
        ContentType: Text[250];
    begin
        ReportSendingSetup.Reset();
        ReportSendingSetup.SetRange("Company Name", CompanyName());
        ReportSendingSetup.SetRange("Report ID", ReportNo);
        if not ReportSendingSetup.FindSet(false) then
            Error(NoReportSendingSetupFoundErr);
        repeat
            Clear(RecipientList);
            Clear(TempBlob);
            Clear(Email);
            Clear(EmailMessage);
            Clear(ReportInStream);
            Clear(ReportOutStream);
            RecipientList := ReportSendingSetup."E-Mail Address".Split(';');
            TempBlob.CreateOutStream(ReportOutStream);

            case ReportSendingSetup."Report Export Type" of
                ReportSendingSetup."Report Export Type"::PDF:
                    begin
                        ReportFormatValue := ReportFormatValue::Pdf;
                        FileExtension := '.pdf';
                        ContentType := 'PDF';
                    end;
                ReportSendingSetup."Report Export Type"::Excel:
                    begin
                        ReportFormatValue := ReportFormatValue::Excel;
                        FileExtension := '.xlsx';
                        ContentType := 'XLSX';
                    end;
                ReportSendingSetup."Report Export Type"::Word:
                    begin
                        ReportFormatValue := ReportFormatValue::Word;
                        FileExtension := '.docx';
                        ContentType := 'DOCX';
                    end;
                else begin
                    ReportFormatValue := ReportFormatValue::Pdf;
                    FileExtension := '.pdf';
                    ContentType := 'PDF';
                end;
            end;

            Report.SaveAs(ReportNo, '', ReportFormatValue, ReportOutStream);
            TempBlob.CreateInStream(ReportInStream);
            SubjectText := Format(WorkDate()) + ' - ' + CompanyProperty.DisplayName() + ' - Ankes Raporu';
            FileName := Format(WorkDate()) + ' - ' + CompanyProperty.DisplayName() + ' - Ankes Raporu' + FileExtension;
            BodyText := SubjectText;
            EmailMessage.Create(RecipientList, SubjectText, BodyText, false, CCList, BCCList);
            EmailMessage.AddAttachment(FileName, ContentType, ReportInStream);
            Email.Send(EmailMessage);
        until ReportSendingSetup.Next() = 0;
    end;

    procedure SendEMail_LogisticsDailyReport(ReportNo: Integer)
    var
        TempBlob: Codeunit "Temp Blob";
        Email: Codeunit Email;
        EmailMessage: Codeunit "Email Message";
        NoReportSendingSetupFoundErr: Label 'Report Sending Setup not found.';
        ReportInStream: InStream;
        ReportOutStream: OutStream;
        BCCList: List of [Text];
        CCList: List of [Text];
        RecipientList: List of [Text];
        FileName: Text[250];
        SubjectText: Text[250];
        BodyText: Text[250];
        FileExtension: Text;
        ReportFormatValue: ReportFormat;
        ContentType: Text[250];
    begin
        ReportSendingSetup.Reset();
        ReportSendingSetup.SetRange("Company Name", CompanyName());
        ReportSendingSetup.SetRange("Report ID", ReportNo);
        if not ReportSendingSetup.FindSet(false) then
            Error(NoReportSendingSetupFoundErr);
        repeat
            Clear(RecipientList);
            Clear(TempBlob);
            Clear(Email);
            Clear(EmailMessage);
            Clear(ReportInStream);
            Clear(ReportOutStream);
            RecipientList := ReportSendingSetup."E-Mail Address".Split(';');
            TempBlob.CreateOutStream(ReportOutStream);

            case ReportSendingSetup."Report Export Type" of
                ReportSendingSetup."Report Export Type"::PDF:
                    begin
                        ReportFormatValue := ReportFormatValue::Pdf;
                        FileExtension := '.pdf';
                        ContentType := 'PDF';
                    end;
                ReportSendingSetup."Report Export Type"::Excel:
                    begin
                        ReportFormatValue := ReportFormatValue::Excel;
                        FileExtension := '.xlsx';
                        ContentType := 'XLSX';
                    end;
                ReportSendingSetup."Report Export Type"::Word:
                    begin
                        ReportFormatValue := ReportFormatValue::Word;
                        FileExtension := '.docx';
                        ContentType := 'DOCX';
                    end;
                else begin
                    ReportFormatValue := ReportFormatValue::Pdf;
                    FileExtension := '.pdf';
                    ContentType := 'PDF';
                end;
            end;

            Report.SaveAs(ReportNo, '', ReportFormatValue, ReportOutStream);
            TempBlob.CreateInStream(ReportInStream);
            SubjectText := Format(WorkDate()) + ' - ' + CompanyProperty.DisplayName() + ' - Antrepo Durum Raporu';
            FileName := Format(WorkDate()) + ' - ' + CompanyProperty.DisplayName() + ' - Antrepo Durum Raporu' + FileExtension;
            BodyText := SubjectText;
            EmailMessage.Create(RecipientList, SubjectText, BodyText, false, CCList, BCCList);
            EmailMessage.AddAttachment(FileName, ContentType, ReportInStream);
            Email.Send(EmailMessage);
        until ReportSendingSetup.Next() = 0;
    end;

    procedure SendEMail_120_AgedAccountReceivables(ReportNo: Integer)
    var
        NoReportSendingSetupFoundErr: Label 'Report Sending Setup not found.';
    begin
        ReportSendingSetup.Reset();
        ReportSendingSetup.SetRange("Company Name", CompanyName());
        ReportSendingSetup.SetRange("Report ID", ReportNo);
        if not ReportSendingSetup.FindSet(false) then
            Error(NoReportSendingSetupFoundErr);
        repeat
            ReportSendingSetupLoopForReport120(ReportNo);
        until ReportSendingSetup.Next() = 0;
    end;

    local procedure ReportSendingSetupLoopForReport120(ReportNo: Integer)
    var
        AllObjWithCaption: Record AllObjWithCaption;
        TempBlob: Codeunit "Temp Blob";
        Email: Codeunit Email;
        EmailMessage: Codeunit "Email Message";
        ReportInStream: InStream;
        ReportOutStream: OutStream;
        BCCList: List of [Text];
        CCList: List of [Text];
        RecipientList: List of [Text];
        FileName: Text[250];
        SubjectText: Text[250];
        BodyText: Text[250];
        FileExtension: Text;
        ReportFormatValue: ReportFormat;
        ContentType: Text[250];
    begin
        AllObjWithCaption.Get(AllObjWithCaption."Object Type"::Report, ReportNo);

        Clear(TempBlob);
        Clear(Email);
        Clear(EmailMessage);
        Clear(ReportInStream);
        Clear(ReportOutStream);
        RecipientList := ReportSendingSetup."E-Mail Address".Split(';');

        case ReportSendingSetup."Report Export Type" of
            ReportSendingSetup."Report Export Type"::PDF:
                begin
                    ReportFormatValue := ReportFormatValue::Pdf;
                    FileExtension := '.pdf';
                    ContentType := 'PDF';
                end;
            ReportSendingSetup."Report Export Type"::Excel:
                begin
                    ReportFormatValue := ReportFormatValue::Excel;
                    FileExtension := '.xlsx';
                    ContentType := 'XLSX';
                end;
            ReportSendingSetup."Report Export Type"::Word:
                begin
                    ReportFormatValue := ReportFormatValue::Word;
                    FileExtension := '.docx';
                    ContentType := 'DOCX';
                end;
            else begin
                ReportFormatValue := ReportFormatValue::Pdf;
                FileExtension := '.pdf';
                ContentType := 'PDF';
            end;
        end;

        TempBlob.CreateOutStream(ReportOutStream);
        Report.SaveAs(ReportNo, ReportSendingSetup.Parameters, ReportFormatValue, ReportOutStream);
        TempBlob.CreateInStream(ReportInStream);
        SubjectText := Format(WorkDate()) + ' - ' + CompanyProperty.DisplayName() + AllObjWithCaption."Object Caption";
        FileName := Format(WorkDate()) + ' - ' + CompanyProperty.DisplayName() + AllObjWithCaption."Object Caption" + FileExtension;
        BodyText := SubjectText;
        EmailMessage.Create(RecipientList, SubjectText, BodyText, false, CCList, BCCList);
        EmailMessage.AddAttachment(FileName, ContentType, ReportInStream);
        Email.Send(EmailMessage);
    end;

    var
        ReportSendingSetup: Record "Report Sending Setup ERK";
}
