table 60066 "Driver Information ERK"
{
    Caption = 'Driver Information';
    DataClassification = CustomerContent;
    DrillDownPageId = "Driver Information List ERK";
    LookupPageId = "Driver Information List ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the unique identifier of the Driver Information record.';
            NotBlank = true;
        }
        field(2; "License Plate No."; Code[20])
        {
            Caption = 'License Plate No.';
            ToolTip = 'Specifies the license plate of the vehicle.';
            TableRelation = "Temporary License Plate ERK"."No.";
        }
        field(3; "Driver Full Name"; Text[100])
        {
            Caption = 'Driver Full Name';
            ToolTip = 'Specifies the full name of the driver.';
            NotBlank = true;
        }
        field(4; "Document Date"; Date)
        {
            Caption = 'Document Date';
            ToolTip = 'Specifies the date of the document.';
        }
    }
    keys
    {
        key(PK; "Driver Full Name", "License Plate No.", "Document No.")
        {
            Clustered = true;
        }
    }
    fieldgroups
    {
        fieldgroup(DropDown; "Driver Full Name", "License Plate No.")
        {
        }
    }
}