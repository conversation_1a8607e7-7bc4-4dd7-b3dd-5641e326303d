page 60009 "Container Card ERK"
{
    ApplicationArea = All;
    Caption = 'Container Card';
    PageType = Card;
    SourceTable = "Container Header ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("Export No."; Rec."Export No.")
                {
                }
                field("No."; Rec."No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
            }
            part(Lines; "Container Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Container No." = field("No."), "Export No." = field("Export No.");
                UpdatePropagation = Both;
            }
            group(Totals)
            {
                Caption = 'Totals';

                field("Total Box Quantity"; Rec."Total Box Quantity")
                {
                }
                field("Total Net Weight (KG)"; Rec."Total Net Weight (KG)")
                {
                }
                field("Total Gross Weight (KG)"; Rec."Total Gross Weight (KG)")
                {
                }
                field("Total Palette Quantity"; Rec."Total Palette Quantity")
                {
                }
            }
        }
    }
    actions
    {
        area(Reporting)
        {
            action(PackingList)
            {
                Caption = 'Packing List';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PrintReport;
                PromotedOnly = true;
                ToolTip = 'Executes the Packing List action.';

                trigger OnAction()
                begin
                    Rec.SetRecFilter();
                    Report.Run(Report::"Packing List ERK", true, true, Rec);
                end;
            }
        }
    }
    trigger OnAfterGetCurrRecord()
    begin
        CurrPage.Lines.Page.SetExportNo(Rec."Export No.");
    end;
}
