tableextension 60031 "Sales Line ERK" extends "Sales Line"
{
    fields
    {
        field(60000; "Load Type ERK"; Code[20])
        {
            Caption = 'Load Type';
            TableRelation = "Parent Load Type ERK".Code;
            ToolTip = 'Specifies the value of the Load Type field.';
        }
        field(60001; "Vessel Load/Unload Type ERK"; Code[10])
        {
            Caption = 'Vessel Loading/Unloading Type';
            TableRelation = "Item Variant".Code where("Item No." = const('HZMT0017'));
            ToolTip = 'Specifies the value of the Vessel Loading/Unloading Type field.';
        }
        field(60002; "Distributed Quantity ERK"; Integer)
        {
            Caption = 'Distributed Quantity';
            ToolTip = 'Specifies the quantity of distributed vehicles.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Serial No. Revenue/Expense ERK" where("Unposted Invoice No." = field("Document No."), "Invoice Line No." = field("Line No.")));
        }
    }
}
