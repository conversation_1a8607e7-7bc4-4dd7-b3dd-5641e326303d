page 60005 "Export Lines ERK"
{
    ApplicationArea = ExportManagementERK;
    Caption = 'Export Lines';
    PageType = List;
    SourceTable = "Export Line ERK";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Purchase Shipment Method Code"; Rec."Purchase Shipment Method Code")
                {
                }
                field("Blanket Sales Order No."; Rec."Blanket Sales Order No.")
                {
                }
                field("Purchase Currency Code"; Rec."Purchase Currency Code")
                {
                }
                field("Purchase Due Date"; Rec."Purchase Due Date")
                {
                }
                field("Purchase Receipt No."; Rec."Purchase Receipt No.")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Vendor No."; Rec."Vendor No.")
                {
                }
                field("Vendor Name"; Rec."Vendor Name")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field(Brand; Rec.Brand)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field("Unit Price"; Rec."Unit Price")
                {
                }
                field("Unit Cost"; Rec."Unit Cost")
                {
                }
                field("Purchase Line Amount"; Rec."Purchase Line Amount")
                {
                }
                field("Sales Line Amount"; Rec."Sales Line Amount")
                {
                }
                field("Country/Region of Origin Code"; Rec."Country/Region of Origin Code")
                {
                }
                field("Load Quantity"; Rec."Load Quantity")
                {
                }
                field("Loading Date"; Rec."Loading Date")
                {
                }
                field("Purchase Date"; Rec."Purchase Date")
                {
                }
                field("Sales Currency Code"; Rec."Sales Currency Code")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }
}
