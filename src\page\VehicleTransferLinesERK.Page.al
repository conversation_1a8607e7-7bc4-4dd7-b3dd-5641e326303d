page 60094 "Vehicle Transfer Lines ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle Transfer Lines';
    PageType = List;
    SourceTable = "Vehicle Transfer Line ERK";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Operation Type"; Rec."Operation Type")
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                    Visible = false;
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                field("From Location Code"; Rec."From Location Code")
                {
                }
                field("From Bin Code"; Rec."From Bin Code")
                {
                    //Visible = false;
                }
                field("Header To Location Code"; Rec."Header To Location Code")
                {
                }
                field("Header To Bin Code"; Rec."Header To Bin Code")
                {
                }
                field("To Location Code"; Rec."To Location Code")
                {
                    Visible = false;
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                    Visible = false;
                }
                field(Processed; Rec.Processed)
                {
                }
                field("Processed By"; Rec."Processed By")
                {
                    Visible = false;
                }
                field("Processed At"; Rec."Processed At")
                {
                    Visible = false;
                }
                field("In-Transit"; Rec."In-Transit")
                {
                }
                // field(TSE; Rec.TSE)
                // {
                //     ToolTip = 'Specifies the value of the TSE field.';
                // }
                field("In-Transit By"; Rec."In-Transit By")
                {
                }
                field("In-Transit At"; Rec."In-Transit At")
                {
                }
            }
        }
    }
}
