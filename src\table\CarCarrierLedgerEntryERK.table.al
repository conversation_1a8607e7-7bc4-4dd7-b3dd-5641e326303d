table 60026 "Car Carrier Ledger Entry ERK"
{
    Caption = 'Car Carrier Ledger Entry';
    DataClassification = CustomerContent;
    LookupPageId = "Car Carrier Ledger Entries ERK";
    DrillDownPageId = "Car Carrier Ledger Entries ERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            TableRelation = "Car Carrier Header ERK"."No.";
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            TableRelation = "Serial No. Information"."Serial No.";
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(4; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = "Voyage Account ERK"."No.";
            ToolTip = 'Specifies the value of the Customer No. field.';
        }
        field(5; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(6; "Loading Port"; Code[10])
        {
            Caption = 'Loading Port';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Loading Port field.';
        }
        field(7; "Discharge Port"; Code[10])
        {
            Caption = 'Discharge Port';
            TableRelation = "Entry/Exit Point".Code;
            ToolTip = 'Specifies the value of the Discharge Port field.';
        }
        field(8; "Loading DateTime"; DateTime)
        {
            Caption = 'Loading DateTime';
            Editable = false;
            ToolTip = 'Specifies the value of the Loading DateTime field.';
        }
        field(9; "Discharge DateTime"; DateTime)
        {
            Caption = 'Discharge DateTime';
            Editable = false;
            ToolTip = 'Specifies the value of the Discharge DateTime field.';
        }
        field(10; "From Document No."; Code[20])
        {
            Caption = 'From Document No.';
            TableRelation = "Car Carrier Header ERK"."No.";
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the From Document No. field.';
        }
        field(11; "Model Code"; Code[40])
        {
            Caption = 'Model Code';
            TableRelation = "Model ERK".Code;
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Carline Code field.';
        }
        field(12; "Loading Port Description"; Text[100])
        {
            Caption = 'Loading Port Description';
            ToolTip = 'Specifies the value of the Loading Port Description field.';
        }
        field(13; "Discharge Port Description"; Text[100])
        {
            Caption = 'Discharge Port Description';
            ToolTip = 'Specifies the value of the Discharge Port Description field.';
        }
        field(14; "Loading to Discharge Desc."; Text[203])
        {
            Caption = 'Loading to Discharge Desciption';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Loading to Discharge Desciption field.';
        }
        field(15; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            TableRelation = "Ship ERK"."No.";
            ToolTip = 'Specifies the value of the Ship No. field.';
        }
        field(16; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(17; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the value of the Document Line No. field.';
        }
        field(18; "Document Line Detail No."; Integer)
        {
            Caption = 'Document Line Detail No.';
            ToolTip = 'Specifies the value of the Document Line Detail No. field.';
        }
        field(19; "Discharge Port Line No."; Integer)
        {
            Caption = 'Discharge Port Line No.';
            TableRelation = "Car Carrier Line ERK"."Line No." where("Document No." = field("Document No."));
            ToolTip = 'Specifies the value of the Discharge Port Line No. field.';
        }
        field(20; "Loading-Discharge Date-Time"; Text[100])
        {
            Caption = 'Loading-Discharge Date-Time';
            ToolTip = 'Specifies the value of the Loading-Discharge Date-Time field.';
        }
        field(21; "Car Carrier Order No."; Code[20])
        {
            Caption = 'Car Carrier Order No.';
            ToolTip = 'Specifies the value of the Car Carrier Order No. field.';
        }
        field(22; "CC Order Load Detail Line No."; Integer)
        {
            Caption = 'Car Carrier Order Load Detail Line No.';
            ToolTip = 'Specifies the value of the Car Carrier Order Load Detail Line No. field.';
        }
        field(23; "Footprint (m2)"; Decimal)
        {
            FieldClass = FlowField;
            CalcFormula = lookup("Serial No. Information"."Footprint (m2) ERK" where("Serial No." = field("Serial No.")));
            Editable = false;
            Caption = 'Footprint (m2)';
            AllowInCustomizations = Always;
        }
        field(24; "Volume (m3)"; Decimal)
        {
            Caption = 'Volume (m3)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Serial No. Information"."Volume (m3) ERK" where("Serial No." = field("Serial No.")));
            AllowInCustomizations = Always;
        }
        field(25; "To Document No."; Code[20])
        {
            Caption = 'To Document No.';
            TableRelation = "Car Carrier Header ERK"."No.";
            ToolTip = 'Specifies the value of the To Document No. field.';
        }
        field(26; "Truck Plate ERK"; Code[10])
        {
            Caption = 'Truck Plate';
            ToolTip = 'Specifies the truck plate used for loading this vehicle.';
        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
        key(SK; "Document No.", "Serial No.")
        {
            Unique = true;
        }
        key(SK2; "Customer No.")
        {
        }
        key(SK3; "Document No.", "Document Line No.", "Document Line Detail No.")
        {
        }
        key(SK4; "Document No.", "Document Line No.", "Loading Port")
        {
        }
        key(SK5; "Document No.", "Document Line No.", "Discharge Port", "Discharge DateTime")
        {
        }
        key(SK6; "Document No.", "Loading Port", "Document Line No.")
        {
        }
    }
    trigger OnInsert()
    var
        VehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    begin
        if VehicleLedgerEntry.FindLast() then
            Rec."Entry No." := VehicleLedgerEntry."Entry No." + 1
        else
            Rec."Entry No." := 1;
    end;
}
