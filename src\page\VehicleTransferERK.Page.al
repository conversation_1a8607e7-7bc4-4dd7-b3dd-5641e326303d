page 60046 "Vehicle Transfer ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle Transfer';
    PageType = Card;
    SourceTable = "Vehicle Transfer Header ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus ERK")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Serial No.');
                end;
            }
            field("Serial No."; Rec."Serial No.")
            {
                trigger OnValidate()
                begin
                    //CurrPage.Update(true);
                    CurrPage.SetFieldFocus.SetFocusOnField('Serial No.');
                end;
            }
            field("Last Succesful Serial No."; Rec."Last Successful Serial No.")
            {
                Editable = false;
                QuickEntry = false;
            }
            // group(BarcodeReading)
            // {
            //     Caption = 'Barcode Reading';
            //     Visible = true;
            //     // field("Serial No."; Rec."Serial No.")
            //     // {
            //     //     ToolTip = 'Specifies the value of the Serial No. field.';
            //     //     ExtendedDatatype = Barcode;
            //     // }
            // }
            group(General)
            {
                Caption = 'General';
                Visible = AllVisible or ManualAllVisible;

                field("No."; Rec."No.")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";
                }
                field("Operation Type"; Rec."Operation Type")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Shipping Agent Code"; Rec."Shipping Agent Code")
                {
                }
                field("Car Carrier No."; Rec."Car Carrier No.")
                {
                    QuickEntry = false;
                }
                field("Ship Name1"; Rec."Ship Name")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";
                    //Editable = false;
                }
                field("Target Address Finder"; Rec."Target Address Finder")
                {
                    QuickEntry = false;
                }
                field("To Location Code"; Rec."To Location Code")
                {
                    QuickEntry = false;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                    QuickEntry = false;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("From Location Code"; Rec."From Location Code")
                {
                }
                field("From Bin Code"; Rec."From Bin Code")
                {
                }
                field(ToBinCodeQuantity; VehicleTransferManagement.GetBinVehicleQtyFromLocationAndBinCode(Rec."To Location Code", Rec."To Bin Code"))
                {
                    Caption = 'To Bin Code Quantity';
                    ToolTip = 'Specifies the value of the To Bin Code Quantity field.';
                    QuickEntry = false;
                }
                field(ToBinCodeCapacity; VehicleTransferManagement.GetBinCapacityFromLocationAndBinCode(Rec."To Location Code", Rec."To Bin Code"))
                {
                    Caption = 'To Bin Code Capacity';
                    ToolTip = 'Specifies the value of the To Bin Code Capacity field.';
                    QuickEntry = false;
                }
                field(Completed; Rec.Completed)
                {
                    QuickEntry = false;
                }
                field("Lines Locked"; Rec."Lines Locked")
                {
                }
                field(Released; Rec.Released)
                {
                }
                field(T1; Rec.T1)
                {
                }
                field("Total Quantity"; Rec."Total Quantity")
                {
                    QuickEntry = false;
                }
                field("Total Processed Quantity"; Rec."Total Processed Quantity")
                {
                    QuickEntry = false;
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                    QuickEntry = false;
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                    QuickEntry = false;
                }
            }
            group(Discharge)
            {
                Caption = 'Discharge';
                Visible = ((DischargeVisible) and (not ManualAllVisible));

                field("No. DischargeLoadingVisible"; Rec."No.")
                {
                    QuickEntry = false;
                    Editable = false;
                }
                field("Operation Type DischargeLoadingVisible"; Rec."Operation Type")
                {
                    QuickEntry = false;
                    Editable = false;

                    trigger OnValidate()
                    begin
                        //CurrPage.Update();
                    end;
                }
                field("Ship Name"; Rec."Ship Name")
                {
                    QuickEntry = false;
                    Editable = not Rec.Released;
                    //Editable = false;
                }
                field("To Location Code DischargeLoadingVisible"; Rec."To Location Code")
                {
                    QuickEntry = false;
                    Editable = false;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                // field("Set Addressing Filter"; Rec."Set Addressing Filter")
                // {
                //     ToolTip = 'Specifies the value of the Set Addressing Filter field.';
                // }

                field("To Bin Code DischargeLoadingVisible"; Rec."To Bin Code")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Total Quantity DischargeLoadingVisible"; Rec."Total Quantity")
                {
                    QuickEntry = false;
                }
                field("Total Processed Quantity DischargeLoadingVisible"; Rec."Total Processed Quantity")
                {
                    QuickEntry = false;
                }
            }
            group(Addressing)
            {
                Caption = 'Addressing';
                Visible = ((AddressingVisible) and (not ManualAllVisible));

                field("Target Address Finder Addressing"; Rec."Target Address Finder")
                {
                    QuickEntry = false;
                    //Visible = TargetAddressFinderVisible;
                }
                field("To Bin Code Addressing"; Rec."To Bin Code")
                {
                    QuickEntry = false;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                // field("No. Addressing"; Rec."No.")
                // {
                //     ToolTip = 'Specifies the value of the No. field.';
                //     QuickEntry = false;
                // }
                // field("Operation Type Addressing"; Rec."Operation Type")
                // {
                //     ToolTip = 'Specifies the value of the Operation Type field.';
                //     QuickEntry = false;
                //     trigger OnValidate()
                //     begin
                //         //CurrPage.Update(true);
                //     end;
                // }
                field("To Location Code Addressing"; Rec."To Location Code")
                {
                    QuickEntry = false;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field(ToBinCodeQuantitAddressingy; VehicleTransferManagement.GetBinVehicleQtyFromLocationAndBinCode(Rec."To Location Code", Rec."To Bin Code"))
                {
                    Caption = 'To Bin Code Quantity';
                    ToolTip = 'Specifies the value of the To Bin Code Quantity field.';
                    QuickEntry = false;
                    //Visible = ToBinCodeQuantityVisible;
                }
                field(ToBinCodeCapacityAddressing; VehicleTransferManagement.GetBinCapacityFromLocationAndBinCode(Rec."To Location Code", Rec."To Bin Code"))
                {
                    Caption = 'To Bin Code Capacity';
                    ToolTip = 'Specifies the value of the To Bin Code Capacity field.';
                    QuickEntry = false;
                    //Visible = ToBinCodeCapacity;
                }
            }
            group(CustomsExitTransfer)
            {
                Caption = 'Customs Exit/Transfer';
                Visible = ((CustomsExitTransferVisible) and (not ManualAllVisible));

                field("No. CustomsExitTransfer"; Rec."No.")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";
                }
                field("Operation Type CustomsExitTransfer"; Rec."Operation Type")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";

                    trigger OnValidate()
                    begin
                        //CurrPage.Update(true);
                    end;
                }
                field("To Location Code CustomsExitTransfer"; Rec."To Location Code")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("To Bin Code CustomsExitTransfer"; Rec."To Bin Code")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Ship Name2"; Rec."Ship Name")
                {
                    QuickEntry = false;
                    Editable = not Rec."Lines Locked";
                    //Editable = false;
                }
            }
            group(ExitVisible)
            {
                Caption = 'Exit Group';
                Visible = ((ExitGroupVisible) and (not ManualAllVisible));
                field("No. ExitGroup"; Rec."No.")
                {
                    QuickEntry = false;
                    Editable = false;
                }
                field("Operation Type ExitGroup"; Rec."Operation Type")
                {
                    QuickEntry = false;
                    Editable = false;
                }
                field("To Location Code ExitGroup"; Rec."To Location Code")
                {
                    QuickEntry = false;
                }
                field("To Bin Code ExitGroup"; Rec."To Bin Code")
                {
                    QuickEntry = false;
                }
                field("Ship Name3"; Rec."Ship Name")
                {
                    QuickEntry = false;
                    Editable = false;
                }
            }
            part(Lines; "Vehicle Transfer Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Visible = AllVisible or CustomsExitTransferVisible or ManualAllVisible;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(PreviousBinCode)
            {
                Caption = 'Previous Bin Code';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PreviousRecord;
                ToolTip = 'Executes the Previous Bin Code action.';

                trigger OnAction()
                begin
                    VehicleTransferManagement.SetPreviousBinCode(Rec);
                end;
            }
            action(NextBinCode)
            {
                Caption = 'Next Bin Code';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NextRecord;
                ToolTip = 'Executes the Next Bin Code action.';

                trigger OnAction()
                begin
                    VehicleTransferManagement.SetNextAvailableBinCode(Rec, true);
                    //CurrPage.Update(true);
                end;
            }
            action(VehicleTransferLedgerEntries)
            {
                Caption = 'Vehicle Transfer Ledger Entries';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = EntriesList;
                RunObject = page "Vehicle Transfer Ledg. Entries";
                RunPageLink = "Document No." = field("No.");
                ToolTip = 'Executes the Vehicle Ledger Entries action.';
            }
            action(DetailedView)
            {
                Caption = 'Detailed View';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ViewDetails;
                ToolTip = 'Executes the Detailed View action.';

                trigger OnAction()
                begin
                    if ManualAllVisible then
                        ManualAllVisible := false
                    else
                        ManualAllVisible := true;
                    //CurrPage.Update(true);
                end;
            }
        }
        area(Reporting)
        {
            action(GrupageDocumentERK)
            {
                Caption = 'Print Grupage Document';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Report;
                ToolTip = 'Executes the Grupage Document action.';

                trigger OnAction()
                var
                    VehicleTransferHeader: Record "Vehicle Transfer Header ERK";
                begin
                    CurrPage.SetSelectionFilter(VehicleTransferHeader);
                    Report.Run(Report::"Grupage Document ERK", true, true, VehicleTransferHeader);
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        SetVisibilities();
    end;

    trigger OnOpenPage()
    begin
        AllVisible := true;
    end;

    local procedure SetVisibilities()
    begin
        case Rec."Operation Type" of
            Rec."Operation Type"::Discharge,
            Rec."Operation Type"::Loading,
            Rec."Operation Type"::"Vehicle Entry",
            Rec."Operation Type"::"Dealer Dispatch",
            Rec."Operation Type"::"Stock-Taking",
            Rec."Operation Type"::"Dispatch Preparation":
                begin
                    DischargeVisible := true;
                    AllVisible := false;
                    AddressingVisible := false;
                    CustomsExitTransferVisible := false;
                    ExitGroupVisible := false;
                end;
            Rec."Operation Type"::Addressing:
                begin
                    AddressingVisible := true;
                    AllVisible := false;
                    DischargeVisible := false;
                    CustomsExitTransferVisible := false;
                    ExitGroupVisible := false;
                end;
            Rec."Operation Type"::Transfer,
            Rec."Operation Type"::"PDI Entry",
            Rec."Operation Type"::Wash,
            Rec."Operation Type"::"Nav Entry",
            Rec."Operation Type"::"Customs Exit":
                begin
                    CustomsExitTransferVisible := true;
                    AllVisible := false;
                    DischargeVisible := false;
                    AddressingVisible := false;
                    ExitGroupVisible := false;
                end;
            Rec."Operation Type"::"Nav Exit",
            Rec."Operation Type"::"PDI Exit",
            Rec."Operation Type"::"Damage Exit":
                begin
                    ExitGroupVisible := true;
                    AllVisible := false;
                    DischargeVisible := false;
                    AddressingVisible := false;
                    CustomsExitTransferVisible := false;

                end;
        end;
    end;

    trigger OnClosePage()
    begin
        Rec.CalcFields("Total Quantity", "Total Processed Quantity");
        if (Rec."Total Quantity" = Rec."Total Processed Quantity") and (Rec."Total Quantity" <> 0) then begin
            Rec.Validate(Completed, true);
            Rec.Modify(true);
        end;
        if (Rec."Operation Type" = Rec."Operation Type"::"Customs Exit") and not Rec."Lines Locked" then begin
            Rec.Validate("Lines Locked", true);
            Rec.Modify(true);
        end;
    end;

    var
        VehicleTransferManagement: Codeunit "Vehicle Transfer Management";
        AllVisible: Boolean;
        ManualAllVisible: Boolean;
        DischargeVisible: Boolean;
        AddressingVisible: Boolean;
        CustomsExitTransferVisible: Boolean;
        ExitGroupVisible: Boolean;
}
