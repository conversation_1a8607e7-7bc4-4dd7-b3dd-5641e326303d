page 60048 "Model List ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Model List';
    PageType = List;
    SourceTable = "Model ERK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Brand Code"; Rec."Brand Code")
                {
                }
                field(Code; Rec.Code)
                {
                }
                field("Width (mm)"; Rec."Width (mm)")
                {
                }
                field("Length  (mm)"; Rec."Length (mm)")
                {
                }
                field("Height  (mm)"; Rec."Height (mm)")
                {
                }
                field("Gross Weight (kg)"; Rec."Gross Weight (kg)")
                {
                }
                field("Area (m2)"; Rec."Area (m2)")
                {
                }
                field("Volume (m3)"; Rec."Volume (m3)")
                {
                }
            }
        }
    }
}
