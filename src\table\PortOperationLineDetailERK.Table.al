table 60022 "Port Operation Line Detail ERK"
{
    Caption = 'Port Operation Line Detail';
    DataClassification = CustomerContent;
    LookupPageId = "Port Operation Line DetailsERK";
    DrillDownPageId = "Port Operation Line DetailsERK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Document Line No. field.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(4; Type; Enum "Voyage Line Detail Type ERK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
            trigger OnValidate()
            var
                PortOperationLine: Record "Port Operation Line ERK";
            begin
                case Rec.Type of
                    Rec.Type::Revenue:
                        begin
                            PortOperationLine.Get(Rec."Document No.", Rec."Document Line No.");
                            Rec.Validate("Source No.", PortOperationLine."Bill-to Customer No.");
                        end;
                    Rec.Type::Expense:
                        begin
                            Rec."Source No." := '';
                            Rec."Source Name" := '';
                        end;
                    Rec.Type::"Load Entry", Rec.Type::"Load Exit", Rec.Type::"Load Transfer":
                        begin
                            PortOperationLine.Get(Rec."Document No.", Rec."Document Line No.");
                            Rec.Validate("No.", PortOperationLine."Load Item No.");
                        end;
                end;
                PortOperationManagement.SetDefaultConsuptionInformationForLogistics(Rec);
            end;
        }
        field(5; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
            trigger OnValidate()
            begin
                if (Format("Next Invoice Period") <> '') and ("Posting Date" <> 0D) then
                    "Next Invoice Date" := CalcDate("Next Invoice Period", "Posting Date");

                // Update price from contract when posting date changes
                if Type = Type::Revenue then
                    PortOperationContractMngt.PopulateLineDetailFromContract(Rec, '');
            end;
        }
        field(6; "No."; Code[20])
        {
            Caption = 'No.';
            TableRelation = if (Type = filter(Revenue | Expense)) Item."No." where(Type = const(Service))
            else
            Item."No.";
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
                PortOperationLine: Record "Port Operation Line ERK";
            begin
                Rec."Variant Code" := '';
                Description := ExportManagement.GetItemTranslation("No.", "Variant Code", '');
                if Item.Get("No.") then begin
                    "Unit of Measure Code" := Item."Base Unit of Measure";
                    "VAT Prod. Posting Group" := Item."VAT Prod. Posting Group";
                    "VAT With. Prod. Posting Group" := Item."VAT Wthld Prod Posting Grp INF";
                end
                else begin
                    "Unit of Measure Code" := '';
                    "VAT Prod. Posting Group" := '';
                    "VAT With. Prod. Posting Group" := '';
                end;
                ErkHoldingSetup.GetRecordOnce();
                if "No." = ErkHoldingSetup."Load Item No." then begin
                    PortOperationLine.Get(Rec."Document No.", Rec."Document Line No.");
                    Rec.Validate("Variant Code", PortOperationLine."Variant Code");
                    Rec.Validate("Sub Load Type", PortOperationLine."Sub Load Type");
                end;
                PortOperationContractMngt.PopulateLineDetailFromContract(Rec, '');
            end;
        }
        field(7; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
            trigger OnValidate()
            begin
                Description := ExportManagement.GetItemTranslation("No.", "Variant Code", '');
                PortOperationContractMngt.PopulateLineDetailFromContract(Rec, '');
            end;
        }
        field(8; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(9; "Shortcut Dimension 1 Code"; Code[20])
        {
            CaptionClass = '1,2,1';
            Caption = 'Shortcut Dimension 1 Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1), Blocked = const(false));
            ToolTip = 'Specifies the value of the Shortcut Dimension 1 Code field.';
            trigger OnValidate()
            begin
                PortOperationManagement.UpdateCommonOperationField(Rec);
                PortOperationManagement.SetDefaultConsuptionInformationForLogistics(Rec);
                Rec.CalcFields("Department Name");
            end;
        }
        field(40; "Department Name"; Text[100])
        {
            Caption = 'Department Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Dimension Value".Name where("Global Dimension No." = const(1), Blocked = const(false), Code = field("Shortcut Dimension 1 Code")));
            ToolTip = 'Specifies the value of the Department Name field.';
        }
        field(10; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Location Code field.';
            trigger OnValidate()
            var
                Bin: Record Bin;
            begin
                Bin.SetRange("Location Code", "Location Code");
                if Bin.Find('-') and (Bin.Next() <> 0) then
                    exit;

                Bin.FindFirst();
                Validate("Bin Code", Bin.Code);
            end;
        }
        field(11; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DecimalPlaces = 0 : 3;
            ToolTip = 'Specifies the value of the Quantity field.';
            trigger OnValidate()
            begin
                case Type of
                    "Voyage Line Detail Type ERK"::"Load Exit":
                        Rec.Quantity := -Abs(Rec.Quantity);
                end;
                Rec."Line Amount" := VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Price", Rec."Duration (Days)");
                "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Operation Date", "Currency Code", "Line Amount");
            end;
        }
        field(12; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            TableRelation = "Item Unit of Measure".Code where("Item No." = field("No."));
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(13; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency;
            ToolTip = 'Specifies the value of the Currency Code field.';
            trigger OnValidate()
            begin
                "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Operation Date", "Currency Code", "Line Amount");
            end;
        }
        field(14; "Unit Price"; Decimal)
        {
            Caption = 'Unit Price';
            DecimalPlaces = 2 : 5;
            AutoFormatType = 2;
            AutoFormatExpression = "Currency Code";
            ToolTip = 'Specifies the value of the Unit Price field.';
            trigger OnValidate()
            begin
                Rec."Line Amount" := VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Price", Rec."Duration (Days)");
                "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Operation Date", "Currency Code", "Line Amount");
            end;
        }
        field(15; "Line Amount"; Decimal)
        {
            Caption = 'Line Amount';
            Editable = false;
            AutoFormatType = 1;
            AutoFormatExpression = "Currency Code";
            ToolTip = 'Specifies the value of the Line Amount field.';
        }
        field(16; "Line Amount (ACY)"; Decimal)
        {
            Caption = 'Line Amount (ACY)';
            Editable = false;
            AutoFormatType = 1;
            ToolTip = 'Specifies the value of the Line Amount (ACY) field.';
        }
        field(17; "Invoice No."; Code[20])
        {
            Caption = 'Invoice No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Invoice No. field.';
        }
        field(18; "External Document No."; Code[35])
        {
            Caption = 'External Document No.';
            ToolTip = 'Specifies the value of the External Document No. field.';
        }
        field(19; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = if (Type = const(Expense)) Vendor
            else if (Type = const(Revenue)) Customer;
            ToolTip = 'Specifies the value of the Source No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
                Vendor: Record Vendor;
            begin
                if Type in [Type::Revenue, Type::"Load Transfer"] then begin
                    Customer.Get("Source No.");
                    "Source Name" := Customer.Name;
                    "Currency Code" := Customer."Currency Code";
                end
                else
                    if Type = Type::Expense then begin
                        Vendor.Get("Source No.");
                        "Source Name" := Vendor.Name;
                        "Currency Code" := Vendor."Currency Code";
                    end;
            end;
        }
        field(20; "Source Name"; Text[100])
        {
            Caption = 'Source Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Name field.';
        }
        field(21; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Location Code"));
            ToolTip = 'Specifies the value of the Bin Code field.';
        }
        field(22; "Sub Load Type"; Code[20])
        {
            Caption = 'Sub Load Type';
            TableRelation = "Sub Load Type ERK";
            ToolTip = 'Specifies the value of the Load Type field.';
        }
        field(23; "VAT Prod. Posting Group"; Code[20])
        {
            Caption = 'VAT Prod. Posting Group';
            TableRelation = "VAT Product Posting Group";
            ToolTip = 'Specifies the value of the VAT Prod. Posting Group field.';
        }
        field(24; "Next Invoice Date"; Date)
        {
            Caption = 'Next Invoice Date';
            ToolTip = 'Specifies the value of the Next Invoice Date field.';
            trigger OnValidate()
            begin
                if (Format("Next Invoice Period") <> '') and ("Next Invoice Date" <> 0D) then
                    Evaluate("Next Invoice Period", '<' + Format("Next Invoice Date" - "Posting Date") + 'D>');
            end;
        }
        field(25; "Next Invoice Period"; DateFormula)
        {
            Caption = 'Next Invoice Period';
            ToolTip = 'Specifies the value of the Next Invoice Period field.';
            trigger OnValidate()
            begin
                if (Format("Next Invoice Period") <> '') and ("Posting Date" <> 0D) then
                    "Next Invoice Date" := CalcDate("Next Invoice Period", "Posting Date");
            end;
        }
        field(26; Processed; Boolean)
        {
            Caption = 'Processed';
            ToolTip = 'Specifies the value of the Processed field.';
        }
        field(27; "Invoice Comment"; Text[80])
        {
            Caption = 'Invoice Comment';
            ToolTip = 'Specifies the value of the Invoice Comment field.';
        }
        field(28; "Operation Date"; Date)
        {
            Caption = 'Operation Date';
            ToolTip = 'Specifies the value of the Operation Date field.';
            trigger OnValidate()
            begin
                PortOperationManagement.SetDefaultConsuptionInformationForLogistics(Rec);
                "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Operation Date", "Currency Code", "Line Amount");
            end;
        }
        field(29; "Regime Type"; Enum "Regime Type ERK")
        {
            Caption = 'Regime Type';
            ToolTip = 'Specifies the value of the Regime Type field.';
        }
        field(30; "Transfer Customer Name"; Text[100])
        {
            Caption = 'Transfer Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Transfer Customer Name field.';
        }
        field(31; "Processed Load"; Code[10])
        {
            Caption = 'Processed Load';
            TableRelation = "Item Variant".Code where("Item No." = field("Load Item No."));
            ToolTip = 'Specifies the value of the Processed Load field.';
        }
        field(32; "Load Item No."; Code[20])
        {
            Caption = 'Load Item No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Load Item No. field.';
        }
        field(33; "Ship No."; Code[10])
        {
            Caption = 'Ship No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Port Operation Header ERK"."Ship No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship No. field.';
        }
        field(34; "Declaration No."; Code[20])
        {
            Caption = 'Declaration No.';
            ToolTip = 'Specifies the value of the Declaration No. field.';
        }
        field(35; "VAT With. Prod. Posting Group"; Code[10])
        {
            Caption = 'VAT Withholding Prod. Posting Group';
            TableRelation = "VAT Withold Prod Post Grp INF".Code;
            ToolTip = 'Specifies the value of the VAT Withholding Prod. Posting Group field.';
        }
        field(36; "Consumption Location Code"; Code[10])
        {
            Caption = 'Consumption Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Consumption Location Code field.';
        }
        field(37; "Consumption Bin Code"; Code[20])
        {
            Caption = 'Consumption Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Consumption Location Code"));
            ToolTip = 'Specifies the value of the Consumption Bin Code field.';
        }
        field(38; "Is Cancelled"; Boolean)
        {
            Caption = 'Is Cancelled';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = exist("Cancelled Document" where("Cancelled Doc. No." = field("Invoice No.")));
            ToolTip = 'Specifies the value of the Is Cancelled field.';
        }
        field(39; "Parent Load Type"; Code[20])
        {
            Caption = 'Parent Load Type';
            TableRelation = "Parent Load Type ERK".Code;
            ToolTip = 'Specifies the value of the Parent Load Type field.';
        }
        field(41; "Duration (Days)"; Integer)
        {
            Caption = 'Duration (Days)';
            ToolTip = 'Specify the duration in days for the selected operation.';
            trigger OnValidate()
            begin
                Rec."Line Amount" := VoyageMangement.CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Rec.Quantity, Rec."Unit Price", Rec."Duration (Days)");
                "Line Amount (ACY)" := ExportManagement.ConvertAmountToACY("Operation Date", "Currency Code", "Line Amount");
            end;
        }
    }
    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        PortOperationLineDetail: Record "Port Operation Line Detail ERK";
        PortOperationLine: Record "Port Operation Line ERK";
    begin
        PortOperationLineDetail.SetRange("Document No.", Rec."Document No.");
        PortOperationLineDetail.SetRange("Document Line No.", Rec."Document Line No.");
        if PortOperationLineDetail.FindLast() then
            Rec."Line No." := PortOperationLineDetail."Line No." + 10000
        else
            Rec."Line No." := 10000;
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Load Item No.");
        "Load Item No." := ErkHoldingSetup."Load Item No.";
        PortOperationLine.Get(Rec."Document No.", Rec."Document Line No.");
        Rec.Validate("Shortcut Dimension 1 Code", PortOperationLine."Shortcut Dimension 1 Code");
        Rec.Validate("Processed Load", PortOperationLine."Variant Code");
        Rec.Validate("Parent Load Type", PortOperationLine."Parent Load Type");
        Rec.Validate("Sub Load Type", PortOperationLine."Sub Load Type");
    end;

    trigger OnModify()
    begin
        TestField(Processed, false);
        TestField("Invoice No.", '');
    end;

    var
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        ExportManagement: Codeunit "Export Management ERK";
        VoyageMangement: Codeunit "Voyage Mangement ERK";
        PortOperationManagement: Codeunit "Port Operation Management ERK";
        PortOperationContractMngt: Codeunit "Port Operation Contract Mngt.";
}
