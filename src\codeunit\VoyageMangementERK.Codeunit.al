codeunit 60003 "Voyage Mangement ERK"
{
    procedure GetEntryExitDescriptionFromEntryExitCode(EntryExitCode: Code[10]): Text[100]
    var
        EntryExitPoint: Record "Entry/Exit Point";
    begin
        if not EntryExitPoint.Get(EntryExitCode) then
            exit('');
        exit(EntryExitPoint.Description);
    end;

    procedure GetCustomerNameFromCustomerNo(CustomerNo: Code[20]): Text[100]
    var
        Customer: Record Customer;
    begin
        if Customer.Get(CustomerNo) then
            exit(Customer.Name);
        exit('');
    end;

    procedure CalculateLineAmountFromQuantityAndUnitPriceAndDuration(Quantity: Decimal; UnitPrice: Decimal; Duration: Integer): Decimal
    begin
        if Duration = 0 then
            exit(Quantity * UnitPrice);

        exit(UnitPrice * Quantity * Duration);
    end;

    procedure CreateNewSalesInvoiceFromSelectedLines(var VoyageLineDetail: Record "Voyage Line Detail ERK")
    var
        VoyageLine: Record "Voyage Line ERK";
        VoyageHeader: Record "Voyage Header ERK";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        SalesCommentLine: Record "Sales Comment Line";
        Err01Err: Label 'Line No.: %1 is already in Sales Invoice No.: %2', Comment = '%1="Voyage Line Detail ERK"."Line No."; %2="Voyage Line Detail ERK"."Invoice No."';
        SalesLineLineNo: Integer;
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Department Dimension Code");
        //ErkHoldingSetup.TestField("Ro-Ro Dimension Value Code");
        VoyageLineDetail.FindSet(false);
        VoyageHeader.Get(VoyageLineDetail."Document No.");
        VoyageHeader.TestField("Department Code");
        repeat
            if VoyageLineDetail."Invoice No." <> '' then
                Error(Err01Err, VoyageLineDetail."Line No.", VoyageLineDetail."Invoice No.");
        until VoyageLineDetail.Next() = 0;
        VoyageLineDetail.FindSet(true);
        VoyageLine.Get(VoyageLineDetail."Document No.", VoyageLineDetail."Document Line No.");
        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", VoyageLine."Customer No.");
        SalesHeader.Validate("Currency Code", VoyageLine."Currency Code");
        SalesHeader.Validate("Posting Date", VoyageLineDetail."Posting Date");
        CreateDimensionValueCode(VoyageLineDetail."Document No.");
        SalesHeader.Modify(true);
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        if SalesLine.FindLast() then
            SalesLineLineNo := SalesLine."Line No." + 10000
        else
            SalesLineLineNo := 10000;
        repeat
            VoyageLineDetail.TestField("Posting Date");
            VoyageLineDetail.TestField(Type, VoyageLineDetail.Type::Revenue);
            VoyageLineDetail.TestField(Quantity);
            VoyageLineDetail.TestField("Unit Price");
            Item.Get(VoyageLineDetail."No.");
            if Item.IsVariantMandatory() then
                VoyageLineDetail.TestField("Variant Code");
            SalesLine.Init();
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Line No." := SalesLineLineNo;
            SalesLine.Insert(true);
            SalesLine.Validate(Type, SalesLine.Type::Item);
            SalesLine.Validate("No.", VoyageLineDetail."No.");
            SalesLine.Validate("Variant Code", VoyageLineDetail."Variant Code");
            SalesLine.Validate(Quantity, VoyageLineDetail.Quantity);
            SalesLine.Validate("Unit of Measure Code", VoyageLineDetail."Unit of Measure Code");
            SalesLine.Validate("Unit Price", VoyageLineDetail."Unit Price");
            SalesLine.Validate("Invoice Line Combine Type INF", SalesHeader."DefInvoiceLineCombineType INF");
            //SalesLine.Validate("Shortcut Dimension 1 Code", ErkHoldingSetup."Ro-Ro Dimension Value Code");
            SalesLine.Validate("Shortcut Dimension 1 Code", VoyageHeader."Department Code");
            SalesLine.Validate("Shortcut Dimension 2 Code", VoyageLineDetail."Document No.");
            SalesLine.Modify(true);
            VoyageLineDetail."Invoice No." := SalesHeader."No.";
            VoyageLineDetail."Invoice Line No." := SalesLine."Line No.";
            VoyageLineDetail.Modify(false);
            SalesLineLineNo += 10000;
        until VoyageLineDetail.Next() = 0;

        VoyageLine.CalcFields("Consignee Name");
        SalesCommentLine.Init();
        SalesCommentLine."Document Type" := SalesHeader."Document Type";
        SalesCommentLine."No." := SalesHeader."No.";
        SalesCommentLine."Line No." := 10000;
        SalesCommentLine.Date := SalesHeader."Posting Date";
        SalesCommentLine.Comment := CopyStr('Alıcı Bilgisi: ' + VoyageLine."Consignee Name", 1, MaxStrLen(SalesCommentLine.Comment));
        SalesCommentLine."Transfer to E-Invoice INF" := true;
        SalesCommentLine.Insert(true);
        SalesCommentLine.Init();
        SalesCommentLine."Document Type" := SalesHeader."Document Type";
        SalesCommentLine."No." := SalesHeader."No.";
        SalesCommentLine."Line No." := 20000;
        SalesCommentLine.Date := SalesHeader."Posting Date";
        SalesCommentLine.Comment := CopyStr('Sefer Bilgisi: ' + VoyageHeader.Description, 1, MaxStrLen(SalesCommentLine.Comment));
        SalesCommentLine."Transfer to E-Invoice INF" := true;
        SalesCommentLine.Insert(true);
        SalesCommentLine.Init();
        SalesCommentLine."Document Type" := SalesHeader."Document Type";
        SalesCommentLine."No." := SalesHeader."No.";
        SalesCommentLine."Line No." := 30000;
        SalesCommentLine.Date := SalesHeader."Posting Date";
        SalesCommentLine.Comment := 'Ürün Cinsi: ' + VoyageLine."Load Type";
        SalesCommentLine."Transfer to E-Invoice INF" := true;
        SalesCommentLine.Insert(true);
        PageManagement.PageRun(SalesHeader);
    end;

    procedure CreateDimensionValueCode(DimensionValueCode: Code[20])
    var
        DimensionValue: Record "Dimension Value";
    begin
        GeneralLedgerSetup.GetRecordOnce();
        GeneralLedgerSetup.TestField("Global Dimension 2 Code");
        if not DimensionValue.Get(GeneralLedgerSetup."Global Dimension 2 Code", DimensionValueCode) then begin
            DimensionValue.Init();
            DimensionValue."Dimension Code" := GeneralLedgerSetup."Global Dimension 2 Code";
            DimensionValue.Code := DimensionValueCode;
            DimensionValue.Insert(true);
        end;
    end;

    local procedure ClearSalesInvoiceFieldsOnVoyageLineDetail(SalesLine: Record "Sales Line")
    var
        VoyageLineDetail: Record "Voyage Line Detail ERK";
    begin
        VoyageLineDetail.SetRange("Invoice No.", SalesLine."Document No.");
        VoyageLineDetail.SetRange("Invoice Line No.", SalesLine."Line No.");
        if not VoyageLineDetail.FindFirst() then
            exit;
        VoyageLineDetail."Invoice No." := '';
        VoyageLineDetail."Invoice Line No." := 0;
        VoyageLineDetail.Modify(false);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure OnDeleteOnBeforeTestStatusOpen(var SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    begin
        ClearSalesInvoiceFieldsOnVoyageLineDetail(SalesLine);
        ClearConsumptionInvoiceNoFieldOnVoyageExpense(SalesLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting(var SalesHeader: Record "Sales Header"; var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; var SkipDelete: Boolean; CommitIsSuppressed: Boolean; EverythingInvoiced: Boolean; var TempSalesLineGlobal: Record "Sales Line" temporary)
    var
        SalesLine: Record "Sales Line";
        VoyageLineDetail: Record "Voyage Line Detail ERK";
        VoyageExpense: Record "Voyage Expense ERK";
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if not SalesLine.FindSet(false) then
            exit;
        repeat
            VoyageLineDetail.SetRange("Invoice No.", SalesLine."Document No.");
            VoyageLineDetail.SetRange("Invoice Line No.", SalesLine."Line No.");
            if VoyageLineDetail.FindFirst() then begin
                VoyageLineDetail."Invoice No." := SalesInvoiceHeader."No.";
                VoyageLineDetail.Modify(false);
            end;
        until SalesLine.Next() = 0;
        VoyageExpense.SetRange("Purchase Invoice No.", SalesHeader."No.");
        VoyageExpense.ModifyAll("Purchase Invoice No.", SalesInvoiceHeader."No.", false);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnBeforeDeleteAfterPosting, '', false, false)]
    local procedure OnBeforeDeleteAfterPosting_PurchasePost(var PurchaseHeader: Record "Purchase Header"; var PurchInvHeader: Record "Purch. Inv. Header"; var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var SkipDelete: Boolean; CommitIsSupressed: Boolean; var TempPurchLine: Record "Purchase Line" temporary; var TempPurchLineGlobal: Record "Purchase Line" temporary; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    begin
        UpdatePostedPurchaseInvoiceNoForVoyageExpense(PurchaseHeader, PurchInvHeader);
        UpdatePostedPurchaseInvoiceNoForVoyageLineDetail(PurchaseHeader, PurchInvHeader);
    end;

    local procedure UpdatePostedPurchaseInvoiceNoForVoyageExpense(var PurchaseHeader: Record "Purchase Header"; var PurchInvHeader: Record "Purch. Inv. Header")
    var
        PurchaseLine: Record "Purchase Line";
        VoyageExpense: Record "Voyage Expense ERK";
    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetRange(Type, PurchaseLine.Type::Item);
        if not PurchaseLine.FindSet(false) then
            exit;
        repeat
            VoyageExpense.SetRange("Purchase Invoice No.", PurchaseLine."Document No.");
            VoyageExpense.SetRange("Purchase Invoice Line No.", PurchaseLine."Line No.");
            if VoyageExpense.FindFirst() then begin
                VoyageExpense."Purchase Invoice No." := PurchInvHeader."No.";
                VoyageExpense."Vendor Invoice No." := PurchInvHeader."Vendor Invoice No.";
                VoyageExpense.Modify(false);
            end;
        until PurchaseLine.Next() = 0;
    end;

    local procedure UpdatePostedPurchaseInvoiceNoForVoyageLineDetail(var PurchaseHeader: Record "Purchase Header"; var PurchInvHeader: Record "Purch. Inv. Header")
    var
        PurchaseLine: Record "Purchase Line";
        VoyageLineDetail: Record "Voyage Line Detail ERK";
    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetRange(Type, PurchaseLine.Type::Item);
        if not PurchaseLine.FindSet(false) then
            exit;
        repeat
            VoyageLineDetail.SetRange("Invoice No.", PurchaseLine."Document No.");
            if VoyageLineDetail.FindFirst() then begin
                VoyageLineDetail."Invoice No." := PurchInvHeader."No.";
                VoyageLineDetail."External Document No." := PurchInvHeader."Vendor Invoice No.";
                VoyageLineDetail.Modify(false);
            end;
        until PurchaseLine.Next() = 0;
    end;

    procedure SetVoyageLineEditable(VoyageLine: Record "Voyage Line ERK"; var IsVoyageLineEditable: Boolean)
    var
        VoyageLineDetail: Record "Voyage Line Detail ERK";
    begin
        VoyageLineDetail.SetRange("Document No.", VoyageLine."Document No.");
        VoyageLineDetail.SetRange("Document Line No.", VoyageLine."Line No.");
        if VoyageLineDetail.IsEmpty() then
            IsVoyageLineEditable := true
        else
            IsVoyageLineEditable := false;
    end;

    procedure CreatePurchaseInvoiceFromVoyageHeader(VoyageHeader: Record "Voyage Header ERK")
    var
        PurchaseHeader: Record "Purchase Header";
    begin
        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Invoice;
        PurchaseHeader.Insert(true);
        PageManagement.PageRun(PurchaseHeader);
    end;

    procedure CreatePurchaseInvoiceFromVoyageExpense(var VoyageExpense: Record "Voyage Expense ERK")
    var
        ErrorErr: Label 'There is different %2 selected than %2 %1. All selected lines must be from same %2.', Comment = '%1="Voyage Expense ERK"."Vendor No."; %2=FieldCaption("Vendor No.")';
    begin
        VoyageExpense.FindFirst();
        VoyageExpense.SetFilter("Vendor No.", '<>%1', VoyageExpense."Vendor No.");
        if not VoyageExpense.IsEmpty() then
            Error(ErrorErr, VoyageExpense."Vendor No.", VoyageExpense.FieldCaption("Vendor No."));
        VoyageExpense.SetRange("Vendor No.");
        VoyageExpense.FindFirst();
        VoyageExpense.SetFilter("Currency Code", '<>%1', VoyageExpense."Currency Code");
        if not VoyageExpense.IsEmpty() then
            Error(ErrorErr, VoyageExpense."Currency Code", VoyageExpense.FieldCaption("Currency Code"));
        VoyageExpense.SetRange("Currency Code");
        VoyageExpense.FindFirst();
        if VoyageExpense."Consumption Line" then
            CreateConsumptionInvoiceFromVoyageExpense(VoyageExpense)
        else
            CreatePurchaseHeaderAndLinesForVoyageExpense(VoyageExpense);
    end;

    local procedure CreatePurchaseHeaderAndLinesForVoyageExpense(var VoyageExpense: Record "Voyage Expense ERK")
    var
        VoyageHeader: Record "Voyage Header ERK";
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        PurchaseLineLineNo: Integer;
    begin
        ErkHoldingSetup.GetRecordOnce();
        VoyageExpense.FindSet(true);
        VoyageHeader.Get(VoyageExpense."Document No.");
        VoyageHeader.TestField("Department Code");
        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Invoice;
        PurchaseHeader.Insert(true);
        PurchaseHeader.Validate("Buy-from Vendor No.", VoyageExpense."Vendor No.");
        PurchaseHeader.Validate("Currency Code", VoyageExpense."Currency Code");
        CreateDimensionValueCode(VoyageExpense."Document No.");
        PurchaseHeader.Validate("Posting Date", VoyageExpense."Posting Date");
        PurchaseHeader.Validate("Vendor Invoice No.", VoyageExpense."Vendor Invoice No.");
        PurchaseHeader.Modify(true);
        PurchaseLineLineNo := 10000;
        repeat
            VoyageExpense.TestField("Purchase Invoice No.", '');
            VoyageExpense.TestField("Vendor Invoice No.");
            VoyageExpense.TestField("Unit Cost");
            VoyageExpense.TestField(Quantity);
            Item.Get(VoyageExpense."No.");
            if Item.IsVariantMandatory() then
                VoyageExpense.TestField("Variant Code");
            PurchaseLine.Init();
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Line No." := PurchaseLineLineNo;
            PurchaseLine.Insert(true);
            PurchaseLine.Validate(Type, PurchaseLine.Type::Item);
            PurchaseLine.Validate("No.", VoyageExpense."No.");
            PurchaseLine.Validate("Variant Code", VoyageExpense."Variant Code");
            PurchaseLine.Validate(Description, VoyageExpense.Description);
            PurchaseLine.Validate(Quantity, VoyageExpense.Quantity);
            PurchaseLine.Validate("Unit of Measure Code", VoyageExpense."Unit of Measure Code");
            PurchaseLine.Validate("Direct Unit Cost", VoyageExpense."Unit Cost");
            //PurchaseLine.Validate("Shortcut Dimension 1 Code", ErkHoldingSetup."Ro-Ro Dimension Value Code");
            PurchaseLine.Validate("Shortcut Dimension 1 Code", VoyageHeader."Department Code");
            PurchaseLine.Validate("Shortcut Dimension 2 Code", VoyageExpense."Document No.");
            PurchaseLine.Modify(true);
            PurchaseLineLineNo += 10000;
            VoyageExpense."Purchase Invoice No." := PurchaseLine."Document No.";
            VoyageExpense."Purchase Invoice Line No." := PurchaseLine."Line No.";
            VoyageExpense.Modify(false);
        until VoyageExpense.Next() = 0;
        PageManagement.PageRun(PurchaseHeader);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnDeleteOnBeforeTestStatusOpen, '', false, false)]
    local procedure OnDeleteOnBeforeTestStatusOpen_PurchaseLine(var PurchaseLine: Record "Purchase Line"; var IsHandled: Boolean)
    begin
        ClearPurchaseInvoiceFieldsOnVoyageExpense(PurchaseLine);
        ClearPurchaseInvoiceFieldsOnVoyageLineDetail(PurchaseLine);
    end;

    local procedure ClearPurchaseInvoiceFieldsOnVoyageLineDetail(PurchaseLine: Record "Purchase Line")
    var
        VoyageLineDetail: Record "Voyage Line Detail ERK";
    begin
        VoyageLineDetail.SetRange("Invoice No.", PurchaseLine."Document No.");
        if not VoyageLineDetail.FindFirst() then
            exit;
        VoyageLineDetail."Invoice No." := '';
        VoyageLineDetail.Modify(false);
    end;

    local procedure ClearPurchaseInvoiceFieldsOnVoyageExpense(PurchaseLine: Record "Purchase Line")
    var
        VoyageExpense: Record "Voyage Expense ERK";
    begin
        VoyageExpense.SetRange("Purchase Invoice No.", PurchaseLine."Document No.");
        VoyageExpense.SetRange("Purchase Invoice Line No.", PurchaseLine."Line No.");
        if not VoyageExpense.FindFirst() then
            exit;
        VoyageExpense."Purchase Invoice No." := '';
        VoyageExpense."Purchase Invoice Line No." := 0;
        VoyageExpense.Modify(true);
    end;

    procedure CreateIFOConsumptionExpenseLine(VoyageHeader: Record "Voyage Header ERK")
    var
        VoyageExpense: Record "Voyage Expense ERK";
        Customer: Record Customer;
        ConsumptionErr: Label 'You must enter at least one fuel consumption quantity';
        FuelExpenseLinesCreatedMsg: Label 'IFO Consumption Expense Line are created. Please create the invoice from the Voyage Expense Lines.';
        IFOAlreadyExistErr: Label 'There is already a fuel consumption expense line for IFO. Please delete it before creating a new one.';
    begin
        if (VoyageHeader."Consumption IFO Quantity" = 0) and (VoyageHeader."Consumption MGO Quantity" = 0) then
            Error(ConsumptionErr);
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("IFO Item No.");
        ErkHoldingSetup.TestField("Consumption Customer No.");
        ErkHoldingSetup.TestField("Department Dimension Code");
        //ErkHoldingSetup.TestField("Ro-Ro Dimension Value Code");
        VoyageHeader.TestField("Department Code");
        Customer.Get(ErkHoldingSetup."Consumption Customer No.");
        if VoyageHeader."Consumption IFO Quantity" <> 0 then begin
            VoyageExpense.SetRange("Document No.", VoyageHeader."No.");
            VoyageExpense.SetRange("No.", ErkHoldingSetup."IFO Item No.");
            if not VoyageExpense.IsEmpty() then
                Error(IFOAlreadyExistErr);
            VoyageExpense.Init();
            VoyageExpense."Document No." := VoyageHeader."No.";
            VoyageExpense.Insert(true);
            VoyageExpense.Validate("No.", ErkHoldingSetup."IFO Item No.");
            VoyageExpense."Vendor No." := Customer."No.";
            VoyageExpense.Validate(Quantity, VoyageHeader."Consumption IFO Quantity");
            VoyageExpense.Validate("Unit Cost", 0);
            VoyageExpense.Validate("Consumption Line", true);
            VoyageExpense.Modify(true);
        end;
        Message(FuelExpenseLinesCreatedMsg);
    end;

    procedure CreateMGOConsumptionExpenseLine(VoyageHeader: Record "Voyage Header ERK")
    var
        VoyageExpense: Record "Voyage Expense ERK";
        Customer: Record Customer;
        ConsumptionErr: Label 'You must enter at least one fuel consumption quantity';
        FuelExpenseLinesCreatedMsg: Label 'MGO Consumption Expense Line are created. Please create the invoice from the Voyage Expense Lines.';
        MGOAlreadyExistErr: Label 'There is already a fuel consumption expense line for MGO. Please delete it before creating a new one.';
    begin
        if (VoyageHeader."Consumption IFO Quantity" = 0) and (VoyageHeader."Consumption MGO Quantity" = 0) then
            Error(ConsumptionErr);
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("MGO Item No.");
        ErkHoldingSetup.TestField("Consumption Customer No.");
        ErkHoldingSetup.TestField("Department Dimension Code");
        VoyageHeader.TestField("Department Code");
        //ErkHoldingSetup.TestField("Ro-Ro Dimension Value Code");
        Customer.Get(ErkHoldingSetup."Consumption Customer No.");
        if VoyageHeader."Consumption MGO Quantity" <> 0 then begin
            VoyageExpense.SetRange("Document No.", VoyageHeader."No.");
            VoyageExpense.SetRange("No.", ErkHoldingSetup."MGO Item No.");
            if not VoyageExpense.IsEmpty() then
                Error(MGOAlreadyExistErr);
            VoyageExpense.Init();
            VoyageExpense."Document No." := VoyageHeader."No.";
            VoyageExpense.Insert(true);
            VoyageExpense.Validate("No.", ErkHoldingSetup."MGO Item No.");
            VoyageExpense."Vendor No." := Customer."No.";
            VoyageExpense.Validate(Quantity, VoyageHeader."Consumption MGO Quantity");
            VoyageExpense.Validate("Unit Cost", 0);
            VoyageExpense.Validate("Consumption Line", true);
            VoyageExpense.Modify(true);
        end;
        Message(FuelExpenseLinesCreatedMsg);
    end;

    procedure CreateHireExpenseLine(VoyageHeader: Record "Voyage Header ERK")
    var
        Customer: Record Customer;
        VoyageExpense: Record "Voyage Expense ERK";
        Ship: Record "Ship ERK";
        HireExpenseLinesCreatedMsg: Label 'Hire Voyage Expense Line is created. Please create the invoice from the Voyage Expense Lines.';
        HireLineAlreadyExistErr: Label 'There is already a hire expense line for this voyage. Please delete it before creating a new one.';
    begin
        ErkHoldingSetup.GetRecordOnce();
        ErkHoldingSetup.TestField("Hire Item No.");
        ErkHoldingSetup.TestField("Consumption Customer No.");
        VoyageExpense.SetRange("Document No.", VoyageHeader."No.");
        VoyageExpense.SetRange("No.", ErkHoldingSetup."Hire Item No.");
        if not VoyageExpense.IsEmpty() then
            Error(HireLineAlreadyExistErr);
        Customer.Get(ErkHoldingSetup."Consumption Customer No.");
        Ship.Get(VoyageHeader."Ship No.");
        Ship.TestField("Contract Unit Cost");
        VoyageExpense.Init();
        VoyageExpense."Document No." := VoyageHeader."No.";
        VoyageExpense.Insert(true);
        VoyageExpense.Validate("No.", ErkHoldingSetup."Hire Item No.");
        VoyageExpense."Vendor No." := Customer."No.";
        VoyageExpense.Validate(Quantity, VoyageHeader."Ending Date" - VoyageHeader."Loading Port Arrival Date");
        VoyageExpense.Validate("Unit Cost", Ship."Contract Unit Cost");
        VoyageExpense.Validate("Unit of Measure Code", 'GUN');
        VoyageExpense.Validate("Currency Code", Ship."Contract Currency Code");
        VoyageExpense.Validate("Consumption Line", true);
        VoyageExpense.Modify(true);
        Message(HireExpenseLinesCreatedMsg);
    end;

    local procedure CreateConsumptionInvoiceFromVoyageExpense(var VoyageExpense: Record "Voyage Expense ERK")
    var
        VoyageHeader: Record "Voyage Header ERK";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        SalesLineNo: Integer;
    begin
        VoyageExpense.FindSet(true);
        ErkHoldingSetup.GetRecordOnce();
        VoyageHeader.Get(VoyageExpense."Document No.");
        VoyageHeader.TestField("Department Code");
        SalesLineNo := 0;
        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", VoyageExpense."Vendor No.");
        CreateDimensionValueCode(VoyageExpense."Document No.");
        SalesHeader.Validate("External Document No.", SalesHeader."No.");
        SalesHeader.Validate("Posting Date", VoyageExpense."Posting Date");
        SalesHeader.Modify(true);
        repeat
            VoyageExpense.TestField("Unit Cost");
            VoyageExpense.TestField(Quantity);
            SalesLineNo += 10000;
            SalesLine.Init();
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Line No." := SalesLineNo;
            SalesLine.Insert(true);
            SalesLine.Validate(Type, SalesLine.Type::Item);
            SalesLine.Validate("No.", VoyageExpense."No.");
            SalesLine.Validate("Variant Code", VoyageExpense."Variant Code");
            SalesLine.Validate("Location Code", VoyageHeader."Ship No.");
            SalesLine.Validate(Quantity, VoyageExpense.Quantity);
            SalesLine.Validate("Unit of Measure Code", VoyageExpense."Unit of Measure Code");
            SalesLine.Validate("Bin Code", VoyageHeader."Ship No.");
            SalesLine.Validate("Invoice Line Combine Type INF", SalesHeader."DefInvoiceLineCombineType INF");
            SalesLine.Validate("Unit Price", 0);
            //SalesLine.Validate("Shortcut Dimension 1 Code", ErkHoldingSetup."Ro-Ro Dimension Value Code");
            SalesLine.Validate("Shortcut Dimension 1 Code", VoyageHeader."Department Code");
            SalesLine.Validate("Shortcut Dimension 2 Code", VoyageExpense."Document No.");
            SalesLine.Modify(true);
            VoyageExpense.Validate("Purchase Invoice No.", SalesHeader."No.");
            VoyageExpense.Validate("Purchase Invoice Line No.", SalesLine."Line No.");
            VoyageExpense.Modify(false);
        until VoyageExpense.Next() = 0;
        PageManagement.PageRun(SalesHeader);
    end;

    procedure CalculateVoyageSalesAmountACY(VoyageHeader: Record "Voyage Header ERK"): Decimal
    var
        ValueEntry: Record "Value Entry";
        ReturnValue: Decimal;
    begin
        ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
        ValueEntry.SetFilter("Sales Amount (Actual)", '<>0', ValueEntry."Sales Amount (Actual)");
        ValueEntry.SetRange("Global Dimension 2 Code", VoyageHeader."No.");
        if not ValueEntry.FindSet() then
            exit(0);
        repeat
            ReturnValue += ExportManagement.ConvertAmountToACY(ValueEntry."Posting Date", '', ValueEntry."Sales Amount (Actual)");
        until ValueEntry.Next() = 0;
        exit(ReturnValue);
    end;

    procedure CalculateVoyageLineRevenueAmountACY(VoyageLine: Record "Voyage Line ERK"): Decimal
    var
        VoyageLineDetail: Record "Voyage Line Detail ERK";
        ACYValue: Decimal;
    begin
        ACYValue := 0;
        VoyageLineDetail.SetRange("Document No.", VoyageLine."Document No.");
        VoyageLineDetail.SetRange("Document Line No.", VoyageLine."Line No.");
        VoyageLineDetail.SetRange(Type, VoyageLineDetail.Type::Revenue);
        if not VoyageLineDetail.FindSet(false) then
            exit(ACYValue);
        repeat
            ACYValue += ExportManagement.ConvertAmountToACY(VoyageLineDetail."Posting Date", VoyageLineDetail."Currency Code", VoyageLineDetail."Line Amount");
        until VoyageLineDetail.Next() = 0;
        exit(ACYValue);
    end;

    procedure CalculateVoyageExpenseDistrubitionACY(VoyageLine: Record "Voyage Line ERK"): Decimal
    var
        ValueEntry: Record "Value Entry";
        VoyageLineDetail: Record "Voyage Line Detail ERK";
        ReturnValue: Decimal;
        TotalRevenue: Decimal;
        LineRevenue: Decimal;
    begin
        ReturnValue := 0;
        TotalRevenue := 0;
        LineRevenue := CalculateVoyageLineRevenueAmountACY(VoyageLine);
        ValueEntry.SetRange("Global Dimension 2 Code", VoyageLine."Document No.");
        ValueEntry.SetRange("Shortcut Dimension 3 Code", '');
        ValueEntry.SetRange("Sales Amount (Actual)", 0);
        if not ValueEntry.FindSet(false) then
            exit(ReturnValue);
        repeat
            ReturnValue += Abs(ValueEntry."Cost Amount (Actual) (ACY)") + Abs(ValueEntry."Cost Amount (Non-Invtbl.)(ACY)");
        until ValueEntry.Next() = 0;
        VoyageLineDetail.SetRange("Document No.", VoyageLine."Document No.");
        VoyageLineDetail.SetRange(Type, VoyageLineDetail.Type::Revenue);
        if VoyageLineDetail.FindSet(false) then
            repeat
                TotalRevenue += ExportManagement.ConvertAmountToACY(VoyageLineDetail."Posting Date", VoyageLineDetail."Currency Code", VoyageLineDetail."Line Amount");
            until VoyageLineDetail.Next() = 0;
        if (TotalRevenue = 0) or (LineRevenue = 0) then
            exit(ReturnValue);
        exit(ReturnValue / TotalRevenue * LineRevenue);
    end;

    procedure CreatePurchaseInvoiceFromVoyageLineDetail(var VoyageLineDetail: Record "Voyage Line Detail ERK")
    var
        ErrorErr: Label 'There is different %2 selected than %2 %1. All selected lines must be from same %2.', Comment = '%1="Voyage Expense ERK"."Vendor No."; %2=FieldCaption("Vendor No.")';
    begin
        VoyageLineDetail.FindFirst();
        VoyageLineDetail.SetFilter("Source No.", '<>%1', VoyageLineDetail."Source No.");
        if not VoyageLineDetail.IsEmpty() then
            Error(ErrorErr, VoyageLineDetail."Source No.", VoyageLineDetail.FieldCaption("Source No."));
        VoyageLineDetail.SetRange("Source No.");
        VoyageLineDetail.FindFirst();
        VoyageLineDetail.SetFilter("Currency Code", '<>%1', VoyageLineDetail."Currency Code", VoyageLineDetail.FieldCaption("Currency Code"));
        if not VoyageLineDetail.IsEmpty() then
            Error(ErrorErr, VoyageLineDetail."Currency Code", VoyageLineDetail.FieldCaption("Currency Code"));
        VoyageLineDetail.SetRange("Currency Code");
        CreatePurchaseHeaderAndLinesForVoyageLinedDetails(VoyageLineDetail);
    end;

    local procedure CreatePurchaseHeaderAndLinesForVoyageLinedDetails(var VoyageLineDetail: Record "Voyage Line Detail ERK")
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        VoyageHeader: Record "Voyage Header ERK";
        PurchaseLineLineNo: Integer;
    begin
        ErkHoldingSetup.GetRecordOnce();
        VoyageLineDetail.FindSet(true);
        VoyageHeader.Get(VoyageLineDetail."Document No.");
        VoyageHeader.TestField("Department Code");

        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Invoice;
        PurchaseHeader.Insert(true);
        PurchaseHeader.Validate("Buy-from Vendor No.", VoyageLineDetail."Source No.");
        PurchaseHeader.Validate("Currency Code", VoyageLineDetail."Currency Code");
        CreateDimensionValueCode(VoyageLineDetail."Document No.");
        PurchaseHeader.Validate("Vendor Invoice No.", VoyageLineDetail."External Document No.");
        PurchaseHeader.Validate("Posting Date", VoyageLineDetail."Posting Date");
        PurchaseHeader.Modify(true);
        PurchaseLineLineNo := 10000;
        repeat
            VoyageLineDetail.TestField("Posting Date");
            VoyageLineDetail.TestField("Invoice No.", '');
            VoyageLineDetail.TestField(Type, VoyageLineDetail.Type::Expense);
            VoyageLineDetail.TestField("External Document No.");
            VoyageLineDetail.TestField(Quantity);
            VoyageLineDetail.TestField("Unit Price");
            Item.Get(VoyageLineDetail."No.");
            if Item.IsVariantMandatory() then
                VoyageLineDetail.TestField("Variant Code");
            PurchaseLine.Init();
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Line No." := PurchaseLineLineNo;
            PurchaseLine.Insert(true);
            PurchaseLine.Validate(Type, PurchaseLine.Type::Item);
            PurchaseLine.Validate("No.", VoyageLineDetail."No.");
            PurchaseLine.Validate("Variant Code", VoyageLineDetail."Variant Code");
            PurchaseLine.Validate(Quantity, VoyageLineDetail.Quantity);
            PurchaseLine.Validate("Unit of Measure Code", VoyageLineDetail."Unit of Measure Code");
            PurchaseLine.Validate("Direct Unit Cost", VoyageLineDetail."Unit Price");
            PurchaseLine.Validate(Description, VoyageLineDetail.Description);
            //PurchaseLine.Validate("Shortcut Dimension 1 Code", ErkHoldingSetup."Ro-Ro Dimension Value Code");
            PurchaseLine.Validate("Shortcut Dimension 1 Code", VoyageHeader."Department Code");
            PurchaseLine.Validate("Shortcut Dimension 2 Code", VoyageLineDetail."Document No.");
            PurchaseLine.Modify(true);
            PurchaseLineLineNo += 10000;
            VoyageLineDetail."Invoice No." := PurchaseLine."Document No.";
            VoyageLineDetail."Invoice Line No." := PurchaseLine."Line No.";
            VoyageLineDetail.Modify(false);
        until VoyageLineDetail.Next() = 0;
        PageManagement.PageRun(PurchaseHeader);
    end;

    local procedure ClearConsumptionInvoiceNoFieldOnVoyageExpense(var SalesLine: Record "Sales Line")
    var
        VoyageExpense: Record "Voyage Expense ERK";
    begin
        if SalesLine."Shortcut Dimension 2 Code" = '' then
            exit;
        VoyageExpense.SetRange("Purchase Invoice No.", SalesLine."Document No.");
        if VoyageExpense.IsEmpty() then
            exit;
        VoyageExpense.ModifyAll("Purchase Invoice No.", '', false);
    end;
    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterPostSalesDoc, '', false, false)]
    // local procedure OnAfterPostSalesDoc(var SalesHeader: Record "Sales Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; SalesShptHdrNo: Code[20]; RetRcpHdrNo: Code[20]; SalesInvHdrNo: Code[20]; SalesCrMemoHdrNo: Code[20]; CommitIsSuppressed: Boolean; InvtPickPutaway: Boolean; var CustLedgerEntry: Record "Cust. Ledger Entry"; WhseShip: Boolean; WhseReceiv: Boolean; PreviewMode: Boolean)
    // begin
    //     ClearVoyageExpenseFieldsOnSalesCancellation(SalesHeader);
    //     ClearVoyageLineDetailFieldsOnSalesCancellation(SalesHeader);
    // end;
    // local procedure ClearVoyageExpenseFieldsOnSalesCancellation(var SalesHeader: Record "Sales Header")
    // var
    //     VoyageExpense: Record "Voyage Expense ERK";
    // begin
    //     if SalesHeader."Document Type" <> SalesHeader."Document Type"::"Credit Memo" then
    //         exit;
    //     if not SalesHeader."Cancel INF" then
    //         exit;
    //     VoyageExpense.SetRange("Purchase Invoice No.", SalesHeader."Applies-to Doc. No.");
    //     VoyageExpense.ModifyAll("Vendor Invoice No.", '', false);
    //     VoyageExpense.ModifyAll("Purchase Invoice Line No.", 0, false);
    //     VoyageExpense.ModifyAll("Purchase Invoice No.", '', false);
    // end;
    // local procedure ClearVoyageLineDetailFieldsOnSalesCancellation(var SalesHeader: Record "Sales Header")
    // var
    //     VoyageLineDetail: Record "Voyage Line Detail ERK";
    // begin
    //     if SalesHeader."Document Type" <> SalesHeader."Document Type"::"Credit Memo" then
    //         exit;
    //     if not SalesHeader."Cancel INF" then
    //         exit;
    //     VoyageLineDetail.SetRange("Invoice No.", SalesHeader."Applies-to Doc. No.");
    //     VoyageLineDetail.ModifyAll("External Document No.", '', false);
    //     VoyageLineDetail.ModifyAll("Invoice Line No.", 0, false);
    //     VoyageLineDetail.ModifyAll("Invoice No.", '', false);
    // end;
    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPostPurchaseDoc, '', false, false)]
    // local procedure "Purch.-Post_OnAfterPostPurchaseDoc"(var PurchaseHeader: Record "Purchase Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; PurchRcpHdrNo: Code[20]; RetShptHdrNo: Code[20]; PurchInvHdrNo: Code[20]; PurchCrMemoHdrNo: Code[20]; CommitIsSupressed: Boolean)
    // begin
    //     ClearVoyageExpenseFieldsOnPurchaseCancellation(PurchaseHeader);
    //     ClearVoyageLineDetailsFieldsOnPurchaseCancellation(PurchaseHeader);
    // end;
    // local procedure ClearVoyageExpenseFieldsOnPurchaseCancellation(var PurchaseHeader: Record "Purchase Header")
    // var
    //     VoyageExpense: Record "Voyage Expense ERK";
    // begin
    //     if PurchaseHeader."Document Type" <> PurchaseHeader."Document Type"::"Credit Memo" then
    //         exit;
    //     if not PurchaseHeader."Cancel INF" then
    //         exit;
    //     VoyageExpense.SetRange("Purchase Invoice No.", PurchaseHeader."Applies-to Doc. No.");
    //     VoyageExpense.ModifyAll("Purchase Invoice Line No.", 0, false);
    //     VoyageExpense.ModifyAll("Vendor Invoice No.", '', false);
    //     VoyageExpense.ModifyAll("Purchase Invoice No.", '', false);
    // end;
    // local procedure ClearVoyageLineDetailsFieldsOnPurchaseCancellation(var PurchaseHeader: Record "Purchase Header")
    // var
    //     VoyageLineDetail: Record "Voyage Line Detail ERK";
    // begin
    //     if PurchaseHeader."Document Type" <> PurchaseHeader."Document Type"::"Credit Memo" then
    //         exit;
    //     if not PurchaseHeader."Cancel INF" then
    //         exit;
    //     VoyageLineDetail.SetRange("Invoice No.", PurchaseHeader."Applies-to Doc. No.");
    //     VoyageLineDetail.ModifyAll("Invoice Line No.", 0, false);
    //     VoyageLineDetail.ModifyAll("External Document No.", '', false);
    //     VoyageLineDetail.ModifyAll("Invoice No.", '', false);
    // end;

    var
        Item: Record Item;
        ErkHoldingSetup: Record "Erk Holding Setup ERK";
        GeneralLedgerSetup: Record "General Ledger Setup";
        ExportManagement: Codeunit "Export Management ERK";
        PageManagement: Codeunit "Page Management";
}
