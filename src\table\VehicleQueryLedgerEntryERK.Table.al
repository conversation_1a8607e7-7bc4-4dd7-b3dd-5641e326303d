table 60043 "Vehicle Query Ledger Entry ERK"
{
    Caption = 'Vehicle Query Ledger Entry';
    DataClassification = CustomerContent;
    DrillDownPageId = "Vehicle Query Ledger Entries";
    LookupPageId = "Vehicle Query Ledger Entries";

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            ToolTip = 'Specifies the value of the Entry No. field.';
        }
        field(2; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
            ToolTip = 'Specifies the value of the Serial No. field.';
        }
        field(3; "Customs Declaration No."; Code[20])
        {
            Caption = 'Customs Declaration No.';
            ToolTip = 'Specifies the value of the Customs Declaration No. field.';
        }
        field(4; "Customs Declaration Line No."; Integer)
        {
            Caption = 'Customs Declaration Line No.';
            ToolTip = 'Specifies the value of the Customs Declaration Line No. field.';
        }
        field(5; "Summary Declaration No."; Code[20])
        {
            Caption = 'Summary Declaration No.';
            ToolTip = 'Specifies the value of the Summary Declaration No. field.';
        }
        field(6; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(7; Status; Enum "Veh. Query Ledger Entry Status")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        VehicleQueryLedgerEntry: Record "Vehicle Query Ledger Entry ERK";
    begin
        if VehicleQueryLedgerEntry.FindLast() then
            Rec."Entry No." := VehicleQueryLedgerEntry."Entry No." + 1
        else
            Rec."Entry No." := 1;
    end;
}
