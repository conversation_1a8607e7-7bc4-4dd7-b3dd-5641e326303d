pageextension 60022 "General Journal ERK" extends "General Journal"
{
    layout
    {
        addafter("External Document No.")
        {
            field("Your Reference ERK"; Rec."Your Reference")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Your Reference field.';
            }
        }
        modify("External Document No.")
        {
            Visible = true;

            trigger OnAssistEdit()
            var
                SalesHeader: Record "Sales Header";
            begin
                SalesHeader.SetRange("Document Type", SalesHeader."Document Type"::"Blanket Order");
                SalesHeader.SetRange("Sell-to Customer No.", Rec."Account No.");
                if Page.RunModal(Page::"Blanket Sales Orders", SalesHeader) = Action::LookupOK then
                    Rec.Validate("External Document No.", SalesHeader."No.");
            end;
        }
        addlast(Control1)
        {
            field("GIB Document Desc. INF ERK"; Rec."GIB Document Desc. INF")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the GIB Document Desc. field.';
            }
        }
        modify("GIB Document Desc. INF")
        {
            Visible = false;
        }
    }
}
