codeunit 60011 "Upgrade Management ERK"
{
    Subtype = Upgrade;
    Access = Internal;
    Permissions = tabledata "Sales Invoice Line" = RIMD;

    trigger OnUpgradePerCompany()
    begin
        // NavApp.GetCurrentModuleInfo(myAppInfo);
        // UpdatePortDescriptionsCarCarrierLedgerEntryAppVersion230047();
        // UpdateShipNoAndShipNameCarCarrierLedgerEntryAppVersion230047();
        // UpdateDocumentLineNoAndDocumentDetailLineNoCarCarrierLedgerEntryAppVersion230047();
        //UpdateRepresentiveLines();
    end;
    // local procedure UpdateDocumentLineNoAndDocumentDetailLineNoCarCarrierLedgerEntryAppVersion230047()
    // var
    //     CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    //     CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
    // begin
    //     CarCarrierLineDetail.SetFilter("Loaded Quantity", '>0');
    //     if not CarCarrierLineDetail.FindSet() then
    //         exit;
    //     repeat
    //         CarCarrierLedgerEntry.SetRange("Document No.", CarCarrierLineDetail."Document No.");
    //         CarCarrierLedgerEntry.SetRange("Customer No.", CarCarrierLineDetail."Customer No.");
    //         CarCarrierLedgerEntry.SetRange("Loading Port", CarCarrierLineDetail."Loading Port");
    //         CarCarrierLedgerEntry.SetRange("Discharge Port", CarCarrierLineDetail."Discharge Port");
    //         CarCarrierLedgerEntry.ModifyAll("Document Line No.", CarCarrierLineDetail."Document Line No.", false);
    //         CarCarrierLedgerEntry.ModifyAll("Document Line Detail No.", CarCarrierLineDetail."Line No.", false);
    //     until CarCarrierLineDetail.Next() = 0;
    // end;
    // local procedure UpdateShipNoAndShipNameCarCarrierLedgerEntryAppVersion230047()
    // var
    //     CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    //     CarCarrierHeader: Record "Car Carrier Header ERK";
    // begin
    //     if myAppInfo.AppVersion = Version.Create(23, 0, 0, 47) then
    //         exit;
    //     if myAppInfo.DataVersion = Version.Create(23, 0, 0, 47) then begin
    //         CarCarrierLedgerEntry.SetRange("Ship No.", '');
    //         if not CarCarrierLedgerEntry.FindSet(true) then
    //             exit;
    //         repeat
    //             if CarCarrierHeader.Get(CarCarrierLedgerEntry."Document No.") then begin
    //                 CarCarrierLedgerEntry."Ship No." := CarCarrierHeader."Ship No.";
    //                 CarCarrierLedgerEntry."Ship Name" := CarCarrierHeader."Ship Name";
    //                 CarCarrierLedgerEntry.Modify(false);
    //             end;
    //         until CarCarrierLedgerEntry.Next() = 0;
    //     end;
    // end;
    // local procedure UpdatePortDescriptionsCarCarrierLedgerEntryAppVersion230047()
    // var
    //     CarCarrierLedgerEntry: Record "Car Carrier Ledger Entry ERK";
    //     EntryExitPoint: Record "Entry/Exit Point";
    // begin
    //     if myAppInfo.DataVersion = Version.Create(23, 0, 0, 47) then begin
    //         CarCarrierLedgerEntry.SetRange("Loading to Discharge Desc.", '');
    //         if not CarCarrierLedgerEntry.FindSet(true) then
    //             exit;
    //         repeat
    //             if EntryExitPoint.Get(CarCarrierLedgerEntry."Loading Port") then
    //                 CarCarrierLedgerEntry."Loading Port Description" := EntryExitPoint.Description;
    //             if EntryExitPoint.Get(CarCarrierLedgerEntry."Discharge Port") then
    //                 CarCarrierLedgerEntry."Discharge Port Description" := EntryExitPoint.Description;
    //             CarCarrierLedgerEntry."Loading to Discharge Desc." := CarCarrierLedgerEntry."Loading Port Description" + ' - ' + CarCarrierLedgerEntry."Discharge Port Description";
    //             CarCarrierLedgerEntry.Modify(false);
    //         until CarCarrierLedgerEntry.Next() = 0;
    //     end;
    // end;
    // var
    //     myAppInfo: ModuleInfo;

    // local procedure UpdateRepresentiveLines()
    // var
    //     SalesInvoiceLine: Record "Sales Invoice Line";
    //     SalesLine: Record "Sales Line";
    // begin
    //     SalesInvoiceLine.Reset();
    //     SalesInvoiceLine.SetRange(Type, 71018700);
    //     if SalesInvoiceLine.FindSet() then
    //         SalesInvoiceLine.ModifyAll(Type, SalesInvoiceLine.Type::"Representative COI INF");

    //     SalesLine.Reset();
    //     SalesLine.SetRange(Type, 71018700);
    //     if SalesLine.FindSet() then
    //         SalesLine.ModifyAll(Type, SalesLine.Type::"Representative COI INF");
    // end;
}
