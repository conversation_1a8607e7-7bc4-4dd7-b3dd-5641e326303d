tableextension 60000 "Purchase Line ERK" extends "Purchase Line"
{
    fields
    {
        field(60000; "Export No. ERK"; Code[20])
        {
            Caption = 'Export No.';
            TableRelation = "Export Header ERK"."No." where(Completed = const(false));
            ToolTip = 'Specifies the value of the Export No. field.';
            trigger OnValidate()
            var
                ExportLine: Record "Export Line ERK";
                ErrorErr: Label 'Blanket Purchase Order Line Qty. to Recevice can not be greater than %1. Its value is %2', Comment = '%1=Outstanding Qty.; %2="Purchase Line"."Qty. to Receive"';
            begin
                if Rec."Export No. ERK" = '' then
                    exit;
                if Rec.Type <> Rec.Type::Item then
                    exit;
                if Rec."Export No. ERK" = 'DUMMY' then
                    exit;
                ExportLine.SetRange("Document No.", Rec."Export No. ERK");
                ExportLine.SetRange("Item No.", Rec."No.");
                ExportLine.SetRange("Variant Code", Rec."Variant Code");
                ExportLine.FindFirst();
                if (ExportLine."Load Quantity" - ExportLine."Quantity Received") < Rec."Qty. to Receive" then
                    Error(ErrorErr, ExportLine."Load Quantity" - ExportLine."Quantity Received", Rec."Qty. to Receive");
                Rec."Export Line No. ERK" := ExportLine."Line No.";
            end;
        }
        field(60001; "Export Line No. ERK"; Integer)
        {
            Caption = 'Export Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Export Line No. field.';
        }
        field(60002; "Currency Code ERK"; Code[10])
        {
            Caption = 'Currency Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purchase Header"."Currency Code" where("No." = field("Document No."), "Document Type" = field("Document Type")));
            AllowInCustomizations = Always;
        }
        field(60004; "Distributed Quantity ERK"; Integer)
        {
            Caption = 'Distributed Quantity';
            ToolTip = 'Specifies the quantity of distributed vehicles.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Serial No. Revenue/Expense ERK" where("Unposted Invoice No." = field("Document No."), "Invoice Line No." = field("Line No.")));
        }
    }
}
