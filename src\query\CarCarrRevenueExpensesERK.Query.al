query 60009 "Car Carr. Revenue/Expenses ERK"
{
    Caption = 'Car Carr. Revenue/Expenses';
    APIGroup = 'erkHoldingCustomization';
    APIPublisher = 'infotek';
    APIVersion = 'v1.0';
    EntityName = 'carCarrRevenueExpense';
    EntitySetName = 'carCarrRevenueExpenses';
    QueryType = API;

    elements
    {
        dataitem(carCarrRevenueExpenseERK; "Car Carr. Revenue/Expense ERK")
        {
            column(carCarrierEndingDateTime; "Car Carrier Ending Date-Time")
            {
            }
            column(carCarrierStartingDateTime; "Car Carrier Starting Date-Time")
            {
            }
            column(consumptionAmountCalculated; "Consumption Amount Calculated")
            {
            }
            column(currencyCode; "Currency Code")
            {
            }
            column(description; Description)
            {
            }
            column(distrubutedQuantity; "Distrubuted Quantity")
            {
            }
            column(documentNo; "Document No.")
            {
            }
            column(externalDocumentNo; "External Document No.")
            {
            }
            column(lineAmount; "Line Amount")
            {
            }
            column(lineAmountACY; "Line Amount (ACY)")
            {
            }
            column(lineNo; "Line No.")
            {
            }
            column(no; "No.")
            {
            }
            column(notes; Notes)
            {
            }
            column(plannedEndingDate; "Planned Ending Date")
            {
            }
            column(plannedStartingDate; "Planned Starting Date")
            {
            }
            column(portCode; "Port Code")
            {
            }
            column(portDescription; "Port Description")
            {
            }
            column(postedInvoiceNo; "Posted Invoice No.")
            {
            }
            column(postingDate; "Posting Date")
            {
            }
            column(quantity; Quantity)
            {
            }
            column(shipName; "Ship Name")
            {
            }
            column(shipNo; "Ship No.")
            {
            }
            column(sourceCarCarrOrderLineNo; "Source Car Carr.Order Line No.")
            {
            }
            column(sourceCarCarrierOrderNo; "Source Car Carrier Order No.")
            {
            }
            column(sourceName; "Source Name")
            {
            }
            column(sourceNo; "Source No.")
            {
            }
            column(sourceProformaLineNo; "Source Proforma Line No.")
            {
            }
            column(sourceRealizedLineNo; "Source Realized Line No.")
            {
            }
            column(status; Status)
            {
            }
            column(systemCreatedAt; SystemCreatedAt)
            {
            }
            column(systemCreatedBy; SystemCreatedBy)
            {
            }
            column(systemId; SystemId)
            {
            }
            column(systemModifiedAt; SystemModifiedAt)
            {
            }
            column(systemModifiedBy; SystemModifiedBy)
            {
            }
            column("type"; "Type")
            {
            }
            column(unitPriceCost; "Unit Price/Cost")
            {
            }
            column(unpostedInvoiceNo; "Unposted Invoice No.")
            {
            }
            column(uoMCode; "UoM Code")
            {
            }
            column(variantCode; "Variant Code")
            {
            }
            column(yourReference; "Your Reference")
            {
            }
        }
    }

    trigger OnBeforeOpen()
    begin
    end;
}