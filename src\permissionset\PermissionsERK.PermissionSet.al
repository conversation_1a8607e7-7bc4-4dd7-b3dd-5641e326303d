permissionset 60000 "Permissions ERK"
{
    Assignable = true;
    Caption = 'Erk Holding Customizations', MaxLength = 30;
    Permissions = tabledata "Container Header ERK" = RIMD,
        tabledata "Container Line ERK" = RIMD,
        tabledata "Car Carrier Header ERK" = RIMD,
        tabledata "Car Carrier Line Detail ERK" = RIMD,
        tabledata "Car Carrier Line ERK" = RIMD,
        tabledata "Erk Holding Setup ERK" = RIMD,
        tabledata "Export Header ERK" = RIMD,
        tabledata "Export Ledger Entry ERK" = RIMD,
        tabledata "Export Line ERK" = RIMD,
        tabledata "Load Type ERK" = RIMD,
        tabledata "Packaging Type ERK" = RIMD,
        tabledata "Ship ERK" = RIMD,
        tabledata "Special Feature ERK" = RIMD,
        tabledata "Voyage Account ERK" = RIMD,
        tabledata "Voyage Expense ERK" = RIMD,
        tabledata "Voyage Header ERK" = RIMD,
        tabledata "Voyage Line Detail ERK" = RIMD,
        tabledata "Voyage Line ERK" = RIMD,
        table "Container Header ERK" = X,
        table "Container Line ERK" = X,
        table "Car Carrier Header ERK" = X,
        table "Car Carrier Line Detail ERK" = X,
        table "Car Carrier Line ERK" = X,
        table "Erk Holding Setup ERK" = X,
        table "Export Header ERK" = X,
        table "Export Ledger Entry ERK" = X,
        table "Export Line ERK" = X,
        table "Load Type ERK" = X,
        table "Packaging Type ERK" = X,
        table "Ship ERK" = X,
        table "Special Feature ERK" = X,
        table "Voyage Account ERK" = X,
        table "Voyage Expense ERK" = X,
        table "Voyage Header ERK" = X,
        table "Voyage Line Detail ERK" = X,
        table "Voyage Line ERK" = X,
        report "Commercial Invoice ERK" = X,
        report "Packing List ERK" = X,
        report "Proforma Invoice ERK" = X,
        codeunit "Company - Extension Map. Mngt." = X,
        codeunit "Erk Holding Basic Functions" = X,
        codeunit "Export Management ERK" = X,
        codeunit "Voyage Mangement ERK" = X,
        page "Apply Payment ERK" = X,
        page "Apply to Blanket Sales Order" = X,
        page "Container Card ERK" = X,
        page "Container List ERK" = X,
        page "Container Subpage ERK" = X,
        page "Car Carrier ERK" = X,
        page "Car Carrier Line Details ERK" = X,
        page "Car Carrier List ERK" = X,
        page "Car Carrier Subpage ERK" = X,
        page "Erk Holding Setup ERK" = X,
        page "Export Card ERK" = X,
        page "Export Ledger Entries ERK" = X,
        page "Export Lines ERK" = X,
        page "Export List ERK" = X,
        page "Export Subpage ERK" = X,
        page "Item Selection ERK" = X,
        page "Load Type ERK" = X,
        page "Packaging Type List ERK" = X,
        page "Ship Card ERK" = X,
        page "Ship List ERK" = X,
        page "Special Feature List ERK" = X,
        page "Voyage Account Card ERK" = X,
        page "Voyage Account List ERK" = X,
        page "Voyage Card ERK" = X,
        page "Voyage Expense List ERK" = X,
        page "Voyage Line Detail List ERK" = X,
        page "Voyage Lines ERK" = X,
        page "Voyage List ERK" = X,
        page "Voyage Subpage ERK" = X,
        page "Inbound E-Invoices API ERK" = X,
        query "Blanket Sales Header ERK" = X,
        query "Export Header ERK" = X,
        query "Export Line ERK" = X,
        tabledata "Port Operation Header ERK" = RIMD,
        tabledata "Port Operation Line ERK" = RIMD,
        table "Port Operation Header ERK" = X,
        table "Port Operation Line ERK" = X,
        tabledata "Port Operation Line Detail ERK" = RIMD,
        table "Port Operation Line Detail ERK" = X,
        tabledata "Parent Load Type ERK" = RIMD,
        tabledata "Sub Load Type ERK" = RIMD,
        table "Parent Load Type ERK" = X,
        table "Sub Load Type ERK" = X,
        page "Port Operation Card ERK" = X,
        page "Port Operation Line DetailsERK" = X,
        page "Port Operation List ERK" = X,
        page "Port Operation Subpage ERK" = X,
        codeunit "Port Operation Management ERK" = X,
        page "Parent Load Types ERK" = X,
        page "Sub Load Types ERK" = X,
        tabledata "Load Transer ERK" = RIMD,
        table "Load Transer ERK" = X,
        page "Load Transfer ERK" = X,
        tabledata "Car Carrier Ledger Entry ERK" = RIMD,
        table "Car Carrier Ledger Entry ERK" = X,
        page "Car Carrier Ledger Entries ERK" = X,
        codeunit "Car Carrier Management ERK" = X,
        tabledata "Loading/Discharging Worksheet" = RIMD,
        table "Loading/Discharging Worksheet" = X,
        page "Loading/Discharging Worksheet" = X,
        tabledata "Vehicle Transfer Ledger Entry" = RIMD,
        table "Vehicle Transfer Ledger Entry" = X,
        page "Transfer Pop-Up ERK" = X,
        page "Vehicle Transfer Ledg. Entries" = X,
        codeunit "Vehicle Transfer Management" = X,
        tabledata "Vehicle Transfer Header ERK" = RIMD,
        table "Vehicle Transfer Header ERK" = X,
        tabledata "Vehicle Transfer Line ERK" = RIMD,
        table "Vehicle Transfer Line ERK" = X,
        page "Vehicle Transfer ERK" = X,
        page "Vehicle Transfer Subpage ERK" = X,
        page "Vehicle Transfers ERK" = X,
        tabledata "Model ERK" = RIMD,
        table "Model ERK" = X,
        page "Model List ERK" = X,
        tabledata "Car Carrier Freight Price ERK" = RIMD,
        table "Car Carrier Freight Price ERK" = X,
        page "Car Carrier Freight Price List" = X,
        tabledata "Vehicle Operation Activ. Cue" = RIMD,
        table "Vehicle Operation Activ. Cue" = X,
        page "Vehicle Operation Activities" = X,
        page "Vehicle Operation Role Center" = X,
        page "Vehicle Query ERK" = X,
        tabledata "Customs Operation Header ERK" = RIMD,
        tabledata "Customs Operation Line ERK" = RIMD,
        table "Customs Operation Header ERK" = X,
        table "Customs Operation Line ERK" = X,
        page "Customs Operation Document ERK" = X,
        page "Customs Operation Documents" = X,
        page "Customs Operation Subpage ERK" = X,
        page "Customs Operation Worksheet" = X,
        codeunit "Customs Operation Management" = X,
        tabledata "PDI Header ERK" = RIMD,
        tabledata "PDI Line ERK" = RIMD,
        table "PDI Header ERK" = X,
        table "PDI Line ERK" = X,
        tabledata "PDI Check List ERK" = RIMD,
        table "PDI Check List ERK" = X,
        page "PDI Check List ERK" = X,
        codeunit "PDI Management ERK" = X,
        page "PDI Document ERK" = X,
        page "PDI Document Subpage ERK" = X,
        page "PDI Documents ERK" = X,
        page "Port Operation Lines ERK" = X,
        tabledata "Car Carr. Revenue/Expense ERK" = RIMD,
        table "Car Carr. Revenue/Expense ERK" = X,
        tabledata "Serial No. Revenue/Expense ERK" = RIMD,
        table "Serial No. Revenue/Expense ERK" = X,
        page "Car Carr. Revenue/Expenses ERK" = X,
        page "Serial No. Revenue/Expenses" = X,
        tabledata "Vehicle Operation ERK" = RIMD,
        table "Vehicle Operation ERK" = X,
        page "Customs Exit ERK" = X,
        page "Customs Operation Lines ERK" = X,
        tabledata "Vehicle Size ERK" = RIMD,
        table "Vehicle Size ERK" = X,
        page "Vehicle Size List ERK" = X,
        tabledata "Auto.Vehicle Transfer Setup" = RIMD,
        table "Auto.Vehicle Transfer Setup" = X,
        page "Auto.Vehicle Transfer Setup" = X,
        tabledata "Vehicle Query Ledger Entry ERK" = RIMD,
        table "Vehicle Query Ledger Entry ERK" = X,
        page "Vehicle Query Ledger Entries" = X,
        tabledata "Vehicle Fuel Ledger Entry ERK" = RIMD,
        table "Vehicle Fuel Ledger Entry ERK" = X,
        page "Vehicle Fuel Ledger Entries" = X,
        page "Vehicle Add. Operation ERK" = X,
        tabledata "Ship Type - Dimension Setup" = RIMD,
        table "Ship Type - Dimension Setup" = X,
        page "Ship Type - Dimension Setup" = X,
        tabledata "Port User ERK" = RIMD,
        table "Port User ERK" = X,
        codeunit "Serial No. Management ERK" = X,
        page "Port Users ERK" = X,
        page "Update Serial No. Information" = X,
        page "Vehicle Transfer Lines - Edit" = X,
        page "Vehicle Distribution List ERK" = X,
        tabledata "Vehicle Rev/Exp Worksheet Line" = RIMD,
        tabledata "Vehicle Rev/Exp. Worksheet Hdr" = RIMD,
        table "Vehicle Rev/Exp Worksheet Line" = X,
        table "Vehicle Rev/Exp. Worksheet Hdr" = X,
        codeunit "Vehicle Rev./Exp. Management" = X,
        page "Vehicle Rev/Exp Wksh. Subpage" = X,
        page "Vehicle Rev/Exp. Worksheet ERK" = X,
        page "Car Carrier Lines ERK" = X,
        codeunit "Upgrade Management ERK" = X,
        report "Ankes Report ERK" = X,
        page "CCLE - Edit ERK" = X,
        tabledata "Job Title ERK" = RIMD,
        table "Job Title ERK" = X,
        page "Job Titles ERK" = X,
        tabledata "Position Level ERK" = RIMD,
        table "Position Level ERK" = X,
        page "Position Levels ERK" = X,
        report "Item Charge Summary ERK" = X,
        codeunit "E-Mail Management ERK" = X,
        tabledata "Report Sending Setup ERK" = RIMD,
        table "Report Sending Setup ERK" = X,
        page "Report Sending Setup ERK" = X,
        report "Customs Entry - Exit ERK" = X,
        report "Logistics Daily Stock Report" = X,
        tabledata "Car Carrier Order Header ERK" = RIMD,
        tabledata "Car Carrier Order Line ERK" = RIMD,
        table "Car Carrier Order Header ERK" = X,
        table "Car Carrier Order Line ERK" = X,
        page "Car Carrier Order ERK" = X,
        page "Car Carrier Order Subpage ERK" = X,
        page "Car Carrier Orders ERK" = X,
        codeunit "Job Queue Management ERK" = X,
        report "Vehicle Discharge Report ERK" = X,
        report "Log. Daily Stock by Location" = X,
        query "Sales Invoice Line ERK" = X,
        codeunit "Document Attachment Mngmt. ERK" = X,
        tabledata "Model Version ERK" = RIMD,
        table "Model Version ERK" = X,
        page "Model Version List ERK" = X,
        tabledata "Car Carrier Order Line Dtl ERK" = RIMD,
        table "Car Carrier Order Line Dtl ERK" = X,
        report "Shipping Order ERK" = X,
        codeunit "Car Carrier Order Mngt. ERK" = X,
        page "Car Carrier Order Line Dtl ERK" = X,
        tabledata "Car Carrier Order Vehicle ERK" = RIMD,
        table "Car Carrier Order Vehicle ERK" = X,
        page "Car Carrier Order Vehicles ERK" = X,
        tabledata "CC Order Load Type ERK" = RIMD,
        table "CC Order Load Type ERK" = X,
        page "CC Order Load Types ERK" = X,
        page "Export List Report ERK" = X,
        page "Import Pre-Load Vehicles ERK" = X,
        page "Car Carrier Order Lines ERK" = X,
        page "Car Carrier Report ERK" = X,
        report "Aged Accounts Receivable ERK" = X,
        page "Vehicle Operation ERK" = X,
        tabledata "Fuel Card ERK" = RIMD,
        tabledata "Fuel Station ERK" = RIMD,
        table "Fuel Card ERK" = X,
        table "Fuel Station ERK" = X,
        page "Fuel Card List ERK" = X,
        page "Fuel Station List ERK" = X,
        page "Vehicle Transfer Lines ERK" = X,
        query "Car Carrier API ERK" = X,
        tabledata "Serial No. - Message Setup ERK" = RIMD,
        table "Serial No. - Message Setup ERK" = X,
        page "Serial No. - Message Setup ERK" = X,
        report "Grupage Document ERK" = X,
        tabledata "Grupage Import ERK" = RIMD,
        table "Grupage Import ERK" = X,
        page "Grupage Import Worksheet ERK" = X,
        report "Vehicle Notes ERK" = X,
        codeunit "Note Management ERK" = X,
        codeunit "Consumption Calc. Job Que ERK" = X,
        tabledata "Port Cluster ERK" = RIMD,
        table "Port Cluster ERK" = X,
        page "Port Clusters ERK" = X,
        query "Car Carrier Line API ERK" = X,
        tabledata "Contract ERK" = RIMD,
        table "Contract ERK" = X,
        page "Contract Subpage ERK" = X,
        page "Contracts ERK" = X,
        tabledata "Car Carrier Line Report ERK" = RIMD,
        table "Car Carrier Line Report ERK" = X,
        codeunit "Car Carrier Line Report ERK" = X,
        query "Car Carrier Line Report ERK" = X,
        codeunit "StormGeo Management ERK" = X,
        report "Easy Reconciliation Cst. ERK" = X,
        report "Easy Reconciliation Vnd. ERK" = X,
        page "Car Carr. Order Lines EIE ERK" = X,
        tabledata "Driver Information ERK" = RIMD,
        table "Driver Information ERK" = X,
        page "Driver Information List ERK" = X,
        report "Temporary Traffic Document ERK" = X,
        tabledata "Temporary License Plate ERK" = RIMD,
        table "Temporary License Plate ERK" = X,
        page "Temporary License Plates ERK" = X,
        page "Pending App. Custom Dec. Que." = X,
        codeunit "Internal Voucher Mngt. ERK" = X,
        query "G/L Entries ERK" = X,
        query "Value Entries ERK" = X,
        report "Vehicle History ERK" = X,
        query "Car Carr. Revenue/Expenses ERK" = X,
        report "Vendor Turnover Report ERK" = X,
        page "Bank Branch INF API ERK" = X,
        page "Bank Clearing St. API ERK" = X,
        page "Bank INF API ERK" = X,
        page "CustomerBankAccAPI ERK" = X,
        page "CustomerPostingGrAPI ERK" = X,
        page "Customers API ERK" = X,
        page "EInvoiceProfile API ERK" = X,
        page "GenBusPostingGrAPI ERK" = X,
        page "Post Codes API ERK" = X,
        page "SalespersonPurchaser API ERK" = X,
        page "Swift API ERK" = X,
        page "VATBusPostingGrAPI ERK" = X,
        page "VATWitholdBusPostingGrAPI ERK" = X,
        page "VendorBankAccAPI ERK" = X,
        page "VendorPostingGrAPI ERK" = X,
        page "Vendors API ERK" = X,
        codeunit "EBA Integration Mngt. ERK" = X,
        page "PurchaseOrderInvoice API ERK" = X,
        tabledata "Vehicle Add. Operation ERK" = RIMD,
        table "Vehicle Add. Operation ERK" = X,
        tabledata "Restricted Location-Oper. Type" = RIMD,
        table "Restricted Location-Oper. Type" = X,
        page "Restricted Location-Oper. Type" = X,
        page "Post Value Entry to G/L ERK" = X,
        page "Value Entry - Edit ERK" = X,
        tabledata "Port Operation Contract Header" = RIMD,
        tabledata "Port Operation Contract Line" = RIMD,
        table "Port Operation Contract Header" = X,
        table "Port Operation Contract Line" = X,
        page "Port Operation Contract ERK" = X,
        page "Port Operation Contract Subpag" = X,
        page "Port Operation Contracts ERK" = X,
        page "Item Application Entry - Edit" = X,
        page "SNI - Yalova ERK" = X,
        page "VTLE - Yalova ERK" = X,
        page "Customs Declaration Form List" = X,
        report "Export Sales Invoice List ERK" = X,
        tabledata "Car Carrier Line Handled ERK" = RIMD,
        table "Car Carrier Line Handled ERK" = X,
        report "Aged Accounts Payable ERK" = X,
        page "Car Carrier Line Handled ERK" = X,
        tabledata "In-Transit Setup ERK" = RIMD,
        table "In-Transit Setup ERK" = X,
        page "In-Transit Setup ERK" = X,
        query "Vehicle Transfer Ledger Entry" = X,
        query "Serial No. Information ERK" = X,
        page "Update Dimension Code ERK" = X,
        tabledata "PDI Line Result ERK" = RIMD,
        table "PDI Line Result ERK" = X,
        page "PDI Line Result List ERK" = X,
        report "PDI Report ERK" = X,
        page "Shipping Agents Lite ERK" = X,
        tabledata "Transfer Neg. Balance ERK" = RIMD,
        table "Transfer Neg. Balance ERK" = X,
        report "Transfer Negative Balances ERK" = X,
        page "Transfer Neg. Balance ERK" = X,
        tabledata "Logistics Operation Cue ERK" = RIMD,
        table "Logistics Operation Cue ERK" = X,
        page "Logistics Dept. Role Center" = X,
        page "Logistics Operation Activities" = X,
        report "ExchRate Adj. Acc. Detail ERK" = X,
        page "ItemCategories API ERK" = X,
        page "Items API ERK" = X,
        page "Perron Bins ERK" = X,
        page "Serial No. Information Perron" = X,
        codeunit "Grupage Management ERK" = X,
        codeunit "Port Operation Contract Mngt." = X,
        codeunit "PortOprLineDtl.Creation Job Q." = X,
        tabledata "Port Service Header ERK" = RIMD,
        tabledata "Port Service Line ERK" = RIMD,
        table "Port Service Header ERK" = X,
        table "Port Service Line ERK" = X,
        page "Port Service Card ERK" = X,
        page "Port Service Lines ERK" = X,
        page "Port Service List ERK" = X,
        page "Port Service Subpage ERK" = X,
        codeunit "Attachment Handler ERK" = X,
        page "Serial No. Entry Dialog ERK" = X,
        tabledata "Invoicing Period Setup ERK" = RIMD,
        table "Invoicing Period Setup ERK" = X,
        page "Invoicing Period Setup List" = X,
        page "PDI Lines ERK" = X,
        tabledata "Dredge Header ERK" = RIMD,
        tabledata "Dredge Line ERK" = RIMD,
        table "Dredge Header ERK" = X,
        table "Dredge Line ERK" = X,
        codeunit "Dredge Management ERK" = X,
        page "Dredge Document ERK" = X,
        page "Dredge Document List ERK" = X,
        page "Dredge Lines ERK" = X,
        page "Dredge Subpage ERK" = X,
        codeunit PurchaseOrderAPISubscriberERK = X,
        page "Purchase Order Lines API ERK" = X,
        page "Purchase Orders API ERK" = X,
        codeunit "Car Carr Rev/Exp Rename ERK" = X,
        page "CCRE - Edit ERK" = X,
        page "Sales CrMemo API ERK" = X;
}