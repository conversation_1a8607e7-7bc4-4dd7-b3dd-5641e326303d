page 60069 "Vehicle Add. Operation ERK"
{
    ApplicationArea = All;
    Caption = 'Vehicle Additional Operation';
    PageType = StandardDialog;
    SourceTable = "Vehicle Add. Operation ERK";
    SourceTableTemporary = true;
    UsageCategory = Tasks;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                ShowCaption = false;
                field("Operation Type"; Rec."Operation Type")
                {
                }
                field("Serial No."; Rec."Serial No.")
                {
                }
                group(Fueling)
                {
                    Visible = Rec."Operation Type" = Rec."Operation Type"::Fueling;
                    Caption = 'Fueling';
                    field("Fuel Type"; Rec."Fuel Type")
                    {
                    }
                    field("Quantity (LT)"; Rec."Quantity (LT)")
                    {
                        Editable = Rec."Fuel Type" <> Rec."Fuel Type"::Electric;
                    }
                    field("Card ID"; Rec."Card ID")
                    {
                        Editable = Rec."Fuel Type" = Rec."Fuel Type"::Electric;
                    }
                    field("Charge Station ID"; Rec."Charge Station ID")
                    {
                        Editable = Rec."Fuel Type" = Rec."Fuel Type"::Electric;
                    }
                    field(kWh; Rec.kWh)
                    {
                        Editable = Rec."Fuel Type" = Rec."Fuel Type"::Electric;
                    }
                }
                group(Tire)
                {
                    Visible = Rec."Operation Type" = Rec."Operation Type"::Tire;
                    Caption = 'Tire';
                    field("Tire Location"; Rec."Tire Location")
                    {
                    }
                }
                group(Battery)
                {
                    Visible = Rec."Operation Type" = Rec."Operation Type"::Battery;
                    Caption = 'Battery';
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.Init();
        Rec.Insert(true);
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction = CloseAction::OK then
            VehicleTransferManagement.InsertVehicleFuelLedgerEntry(Rec);
    end;

    var
        VehicleTransferManagement: Codeunit "Vehicle Transfer Management";
}
