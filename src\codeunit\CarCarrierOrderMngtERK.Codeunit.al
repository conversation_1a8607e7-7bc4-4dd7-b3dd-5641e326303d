codeunit 60015 "Car Carrier Order Mngt. ERK"
{
    procedure RegisterPreLoadVehicles(var TempCarCarrierOrderVehicle: Record "Car Carrier Order Vehicle ERK" temporary; var CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK")
    var
        CarCarrierOrderVehicle: Record "Car Carrier Order Vehicle ERK";
        SuccesMsg: Label '%1 Vehicles imported succesfully.', Comment = '%1="Car Carrier Order Vehicle ERK".Count()';
    begin
        TempCarCarrierOrderVehicle.FindSet();
        repeat
            CarCarrierOrderVehicle.Init();
            CarCarrierOrderVehicle."Document No." := CarCarrierOrderLineDtl."Document No.";
            //CarCarrierOrderVehicle."Document Line No." := CarCarrierOrderLineDtl."Document Line No.";
            CarCarrierOrderVehicle."Document Line Detail No." := CarCarrierOrderLineDtl."Line No.";
            CarCarrierOrderVehicle."Serial No." := TempCarCarrierOrderVehicle."Serial No.";
            CarCarrierOrderVehicle."Pre-Load" := true;
            CarCarrierOrderVehicle.Insert(true);
        until TempCarCarrierOrderVehicle.Next() = 0;
        Message(SuccesMsg, TempCarCarrierOrderVehicle.Count());
    end;

    procedure UpdateCarCarrierOrderLineStatusOnLoading(var CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    var
        CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
        SetStatusLoaded: Boolean;
    begin
        CarCarrierOrderLineDtl.SetAutoCalcFields("Loaded Quantity");
        CarCarrierOrderLineDtl.SetRange("Document No.", CarCarrierLineDetail."Order No.");
        CarCarrierOrderLineDtl.SetRange("Planned Car Carrier No.", CarCarrierLineDetail."Document No.");
        if CarCarrierOrderLineDtl.FindSet(false) then begin
            SetStatusLoaded := true;
            repeat
                if CarCarrierOrderLineDtl."Loaded Quantity" = 0 then
                    SetStatusLoaded := false;
            until CarCarrierOrderLineDtl.Next() = 0;
        end;
        if SetStatusLoaded then begin
            CarCarrierOrderLine.SetRange("Document No.", CarCarrierLineDetail."Order No.");
            CarCarrierOrderLine.SetRange("Planned Car Carrier No.", CarCarrierLineDetail."Document No.");
            CarCarrierOrderLine.ModifyAll(Status, CarCarrierOrderLine.Status::Loaded, true);
        end;
    end;

    procedure UpdateCarCarrierOrderLineStatusOnDischarge(var CarCarrierLineDetail: Record "Car Carrier Line Detail ERK")
    var
        CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK";
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
        SetStatusDischarged: Boolean;
    begin
        CarCarrierOrderLineDtl.SetAutoCalcFields("Discharged Quantity");
        CarCarrierOrderLineDtl.SetRange("Document No.", CarCarrierLineDetail."Order No.");
        CarCarrierOrderLineDtl.SetRange("Planned Car Carrier No.", CarCarrierLineDetail."Document No.");
        if CarCarrierOrderLineDtl.FindSet(false) then begin
            SetStatusDischarged := true;
            repeat
                if CarCarrierOrderLineDtl."Discharged Quantity" = 0 then
                    SetStatusDischarged := false;
            until CarCarrierOrderLineDtl.Next() = 0;
        end;
        if SetStatusDischarged then begin
            CarCarrierOrderLine.SetRange("Document No.", CarCarrierLineDetail."Order No.");
            CarCarrierOrderLine.SetRange("Planned Car Carrier No.", CarCarrierLineDetail."Document No.");
            CarCarrierOrderLine.ModifyAll(Status, CarCarrierOrderLine.Status::Discharged, true);
        end;
    end;

    procedure SetStatusCompleted(var CarCarrierOrderHeader: Record "Car Carrier Order Header ERK")
    var
        CarCarrierOrderLine: Record "Car Carrier Order Line ERK";
    begin
        CarCarrierOrderLine.SetRange("Document No.", CarCarrierOrderHeader."No.");
        if not CarCarrierOrderLine.IsEmpty() then begin
            CarCarrierOrderLine.SetFilter(Status, '<>%1', CarCarrierOrderLine.Status::Discharged);
            if CarCarrierOrderLine.IsEmpty() then
                CarCarrierOrderHeader.Validate(Status, CarCarrierOrderHeader.Status::Completed)
            else
                CarCarrierOrderHeader.Validate(Status, CarCarrierOrderHeader.Status::"In Progress");
            CarCarrierOrderHeader.Modify(true);
        end;
    end;

    procedure CheckCarCarrierOrderLineDetailSelectedInCarCarrierLineDetail(CarCarrierOrderLineDtl: Record "Car Carrier Order Line Dtl ERK")
    var
        CarCarrierLineDetail: Record "Car Carrier Line Detail ERK";
        CanNotModifyErr: Label 'Since this line is selected in Car Carrier No.: %1, you cannot make any changes.', Comment = '%1="Car Carrier Line Detail ERK"."Document No."';
    begin
        CarCarrierLineDetail.SetRange("Document No.", CarCarrierOrderLineDtl."Planned Car Carrier No.");
        CarCarrierLineDetail.SetRange("Order No.", CarCarrierOrderLineDtl."Document No.");
        CarCarrierLineDetail.SetRange("Order Load Detail Line No.", CarCarrierOrderLineDtl."Line No.");
        if CarCarrierLineDetail.FindFirst() then
            Error(CanNotModifyErr, CarCarrierLineDetail."Document No.");
    end;

    // procedure CreateUpdateCarCarrierRevenueExpenseFromCarCarrierOrderLine(CarCarrierOrderLine: Record "Car Carrier Order Line ERK"; xCarCarrierOrderLine: Record "Car Carrier Order Line ERK")
    // var
    //     CarCarrierOrderHeader: Record "Car Carrier Order Header ERK";
    //     CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    // begin
    //     if CarCarrierOrderLine."Planned Car Carrier No." = '' then
    //         exit;
    //     if xCarCarrierOrderLine."Planned Car Carrier No." <> CarCarrierOrderLine."Planned Car Carrier No." then
    //         DeleteCarCarrierRevenueExpenseLinesFromCarCarrierOrderLine(xCarCarrierOrderLine);
    //     CarCarrierOrderHeader.Get(CarCarrierOrderLine."Document No.");
    //     ErkHoldingSetup.GetRecordOnce();
    //     CarCarrierOrderLine.TestField("Est. Revenue Amount");
    //     CarCarrRevenueExpense.SetRange("Document No.", CarCarrierOrderLine."Planned Car Carrier No.");
    //     CarCarrRevenueExpense.SetRange("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //     CarCarrRevenueExpense.SetRange("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //     CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."Est. Revenue Item No.");
    //     if CarCarrRevenueExpense.FindFirst() then begin
    //         CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrierOrderLine."Est. Revenue Amount");
    //         CarCarrRevenueExpense.Validate("Currency Code", CarCarrierOrderLine."Est. Revenue Currency Code");
    //     end
    //     else begin
    //         CarCarrRevenueExpense.Init();
    //         CarCarrRevenueExpense."Document No." := CarCarrierOrderLine."Planned Car Carrier No.";
    //         CarCarrRevenueExpense.Insert(true);
    //         CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::"Estimated Revenue");
    //         CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."Est. Revenue Item No.");
    //         CarCarrRevenueExpense.Validate(Quantity, 1);
    //         CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrierOrderLine."Est. Revenue Amount");
    //         CarCarrRevenueExpense.Validate("Currency Code", CarCarrierOrderLine."Est. Revenue Currency Code");
    //         CarCarrRevenueExpense.Validate("Posting Date", CarCarrierOrderHeader."Order Date");
    //         CarCarrRevenueExpense.Validate("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //         CarCarrRevenueExpense.Validate("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //         CarCarrRevenueExpense.Modify(true);
    //     end;
    //     //CarCarrierOrderLine.TestField("Est. Fuel Cost Amount");
    //     if CarCarrierOrderLine."Est. Fuel Cost Amount" > 0 then begin
    //         CarCarrRevenueExpense.SetRange("Document No.", CarCarrierOrderLine."Planned Car Carrier No.");
    //         CarCarrRevenueExpense.SetRange("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //         CarCarrRevenueExpense.SetRange("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //         CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."Est. Fuel Item No.");
    //         if CarCarrRevenueExpense.FindFirst() then begin
    //             CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrierOrderLine."Est. Fuel Cost Amount");
    //             CarCarrRevenueExpense.Validate("Currency Code", CarCarrierOrderLine."Est. Fuel Cost Currency Code");
    //         end
    //         else begin
    //             CarCarrRevenueExpense.Init();
    //             CarCarrRevenueExpense."Document No." := CarCarrierOrderLine."Planned Car Carrier No.";
    //             CarCarrRevenueExpense.Insert(true);
    //             CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::"Estimated Expense");
    //             CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."Est. Fuel Item No.");
    //             CarCarrRevenueExpense.Validate(Quantity, 1);
    //             CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrierOrderLine."Est. Fuel Cost Amount");
    //             CarCarrRevenueExpense.Validate("Currency Code", CarCarrierOrderLine."Est. Fuel Cost Currency Code");
    //             CarCarrRevenueExpense.Validate("Posting Date", CarCarrierOrderHeader."Order Date");
    //             CarCarrRevenueExpense.Validate("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //             CarCarrRevenueExpense.Validate("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //             CarCarrRevenueExpense.Modify(true);
    //         end;
    //     end;
    //     //CarCarrierOrderLine.TestField("Est. Hire Cost Amount");
    //     if CarCarrierOrderLine."Est. Hire Cost Amount" > 0 then begin
    //         CarCarrRevenueExpense.SetRange("Document No.", CarCarrierOrderLine."Planned Car Carrier No.");
    //         CarCarrRevenueExpense.SetRange("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //         CarCarrRevenueExpense.SetRange("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //         CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."Est. Hire Item No.");
    //         if CarCarrRevenueExpense.FindFirst() then begin
    //             CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrierOrderLine."Est. Hire Cost Amount");
    //             CarCarrRevenueExpense.Validate("Currency Code", CarCarrierOrderLine."Est. Hire Cost Currency Code");
    //         end
    //         else begin
    //             CarCarrRevenueExpense.Init();
    //             CarCarrRevenueExpense."Document No." := CarCarrierOrderLine."Planned Car Carrier No.";
    //             CarCarrRevenueExpense.Insert(true);
    //             CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::"Estimated Expense");
    //             CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."Est. Hire Item No.");
    //             CarCarrRevenueExpense.Validate(Quantity, 1);
    //             CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrierOrderLine."Est. Hire Cost Amount");
    //             CarCarrRevenueExpense.Validate("Currency Code", CarCarrierOrderLine."Est. Hire Cost Currency Code");
    //             CarCarrRevenueExpense.Validate("Posting Date", CarCarrierOrderHeader."Order Date");
    //             CarCarrRevenueExpense.Validate("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //             CarCarrRevenueExpense.Validate("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //             CarCarrRevenueExpense.Modify(true);
    //         end;
    //     end;
    //     //CarCarrierOrderLine.TestField("Est. Other Cost Amount");
    //     if CarCarrierOrderLine."Est. Other Cost Amount" > 0 then begin
    //         CarCarrRevenueExpense.SetRange("Document No.", CarCarrierOrderLine."Planned Car Carrier No.");
    //         CarCarrRevenueExpense.SetRange("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //         CarCarrRevenueExpense.SetRange("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //         CarCarrRevenueExpense.SetRange("No.", ErkHoldingSetup."Est. Other Cost Item No.");
    //         if CarCarrRevenueExpense.FindFirst() then begin
    //             CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrierOrderLine."Est. Other Cost Amount");
    //             CarCarrRevenueExpense.Validate("Currency Code", CarCarrierOrderLine."Est. Other Cost Currency Code");
    //         end
    //         else begin
    //             CarCarrRevenueExpense.Init();
    //             CarCarrRevenueExpense."Document No." := CarCarrierOrderLine."Planned Car Carrier No.";
    //             CarCarrRevenueExpense.Insert(true);
    //             CarCarrRevenueExpense.Validate(Type, CarCarrRevenueExpense.Type::"Estimated Expense");
    //             CarCarrRevenueExpense.Validate("No.", ErkHoldingSetup."Est. Other Cost Item No.");
    //             CarCarrRevenueExpense.Validate(Quantity, 1);
    //             CarCarrRevenueExpense.Validate("Unit Price/Cost", CarCarrierOrderLine."Est. Other Cost Amount");
    //             CarCarrRevenueExpense.Validate("Currency Code", CarCarrierOrderLine."Est. Other Cost Currency Code");
    //             CarCarrRevenueExpense.Validate("Posting Date", CarCarrierOrderHeader."Order Date");
    //             CarCarrRevenueExpense.Validate("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //             CarCarrRevenueExpense.Validate("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //             CarCarrRevenueExpense.Modify(true);
    //         end;
    //     end;
    // end;

    // procedure DeleteCarCarrierRevenueExpenseLinesFromCarCarrierOrderLine(CarCarrierOrderLine: Record "Car Carrier Order Line ERK")
    // var
    //     CarCarrRevenueExpense: Record "Car Carr. Revenue/Expense ERK";
    // begin
    //     // if CarCarrierOrderLine."Planned Car Carrier No." <> '' then
    //     //     exit;
    //     CarCarrRevenueExpense.SetRange("Source Car Carrier Order No.", CarCarrierOrderLine."Document No.");
    //     CarCarrRevenueExpense.SetRange("Source Car Carr.Order Line No.", CarCarrierOrderLine."Line No.");
    //     CarCarrRevenueExpense.DeleteAll(true);
    // end;

    // var
    //     ErkHoldingSetup: Record "Erk Holding Setup ERK";
}
