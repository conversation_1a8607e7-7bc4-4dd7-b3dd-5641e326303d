report 60008 "Log. Daily Stock by Location"
{
    ApplicationArea = All;
    Caption = 'Logistics Daily Stock by Location';
    UsageCategory = History;

    dataset
    {
        dataitem(PortOperationHeaderERK; "Port Operation Header ERK")
        {
            column(No; "No.")
            {
            }
            column(Description; Description)
            {
            }
            column(ShipNo; "Ship No.")
            {
            }
            column(ShipName; "Ship Name")
            {
            }
            column(PortCode; "Port Code")
            {
            }
            column(ThirdParty; "Third Party")
            {
            }
            column(PortName; "Port Name")
            {
            }
            column(Completed; Completed)
            {
            }
            column(DeclaredQuantityTon; "Declared Quantity (Ton)")
            {
            }
            column(CommonOperation; "Common Operation")
            {
            }
            column(OperationStartingTime; "Operation Starting Time")
            {
            }
            column(OperationEndingTime; "Operation Ending Time")
            {
            }
            column(ActualDockingTime; "Actual Docking Time")
            {
            }
            column(ActualDepartureTime; "Actual Departure Time")
            {
            }
            column(DockNo; "Dock No.")
            {
            }
            column(ActualTonnage; "Actual Tonnage")
            {
            }
            column(DeparmentName; "Deparment Name")
            {
            }
            dataitem("Port Operation Line ERK"; "Port Operation Line ERK")
            {
                DataItemLink = "Document No." = field("No.");

                column(BilltoCustomerName_PortOperationLineERK; "Bill-to Customer Name")
                {
                }
                column(BilltoCustomerNo_PortOperationLineERK; "Bill-to Customer No.")
                {
                }
                column(Completed_PortOperationLineERK; Completed)
                {
                }
                column(DocumentNo_PortOperationLineERK; "Document No.")
                {
                }
                column(ExpenseAmountACY_PortOperationLineERK; "Expense Amount (ACY)")
                {
                }
                column(FirstEntryDate_PortOperationLineERK; "First Entry Date")
                {
                }
                column(LineNo_PortOperationLineERK; "Line No.")
                {
                }
                column(LoadItemNo_PortOperationLineERK; "Load Item No.")
                {
                }
                column(LoadOwnerName_PortOperationLineERK; "Load Owner Name")
                {
                }
                column(LoadOwnerNo_PortOperationLineERK; "Load Owner No.")
                {
                }
                column(ParentLoadType_PortOperationLineERK; "Parent Load Type")
                {
                }
                column(RemainingQuantity_PortOperationLineERK; "Remaining Quantity")
                {
                }
                column(RevenueAmountACY_PortOperationLineERK; "Revenue Amount (ACY)")
                {
                }
                column(ShortcutDimension1Code_PortOperationLineERK; "Shortcut Dimension 1 Code")
                {
                }
                column(SubLoadType_PortOperationLineERK; "Sub Load Type")
                {
                }
                column(TotalEntryQuantity_PortOperationLineERK; "Total Entry Quantity")
                {
                }
                column(TotalExitQuantity_PortOperationLineERK; "Total Exit Quantity")
                {
                }
                column(TotalScrapQuantity_PortOperationLineERK; "Total Scrap Quantity")
                {
                }
                column(TotalTransferQuantity_PortOperationLineERK; "Total Transfer Quantity")
                {
                }
                column(VariantCode_PortOperationLineERK; "Variant Code")
                {
                }
                dataitem("Port Operation Line Detail ERK"; "Port Operation Line Detail ERK")
                {
                    DataItemLink = "Document No." = field("Document No."), "Document Line No." = field("Line No.");
                    DataItemTableView = where("Is Cancelled" = const(false));
                    CalcFields = "Is Cancelled";

                    column(BinCode_PortOperationLineDetailERK; "Bin Code")
                    {
                    }
                    column(ConsumptionBinCode_PortOperationLineDetailERK; "Consumption Bin Code")
                    {
                    }
                    column(ConsumptionLocationCode_PortOperationLineDetailERK; "Consumption Location Code")
                    {
                    }
                    column(CurrencyCode_PortOperationLineDetailERK; "Currency Code")
                    {
                    }
                    column(DeclarationNo_PortOperationLineDetailERK; "Declaration No.")
                    {
                    }
                    column(Description_PortOperationLineDetailERK; Description)
                    {
                    }
                    column(DocumentLineNo_PortOperationLineDetailERK; "Document Line No.")
                    {
                    }
                    column(DocumentNo_PortOperationLineDetailERK; "Document No.")
                    {
                    }
                    column(ExternalDocumentNo_PortOperationLineDetailERK; "External Document No.")
                    {
                    }
                    column(InvoiceComment_PortOperationLineDetailERK; "Invoice Comment")
                    {
                    }
                    column(InvoiceNo_PortOperationLineDetailERK; "Invoice No.")
                    {
                    }
                    column(LineAmount_PortOperationLineDetailERK; "Line Amount")
                    {
                    }
                    column(LineAmountACY_PortOperationLineDetailERK; "Line Amount (ACY)")
                    {
                    }
                    column(LineNo_PortOperationLineDetailERK; "Line No.")
                    {
                    }
                    column(LoadItemNo_PortOperationLineDetailERK; "Load Item No.")
                    {
                    }
                    column(LocationCode_PortOperationLineDetailERK; "Location Code")
                    {
                    }
                    column(NextInvoiceDate_PortOperationLineDetailERK; "Next Invoice Date")
                    {
                    }
                    column(NextInvoicePeriod_PortOperationLineDetailERK; "Next Invoice Period")
                    {
                    }
                    column(No_PortOperationLineDetailERK; "No.")
                    {
                    }
                    column(OperationDate_PortOperationLineDetailERK; "Operation Date")
                    {
                    }
                    column(PostingDate_PortOperationLineDetailERK; "Posting Date")
                    {
                    }
                    column(Processed_PortOperationLineDetailERK; Processed)
                    {
                    }
                    column(ProcessedLoad_PortOperationLineDetailERK; "Processed Load")
                    {
                    }
                    column(Quantity_PortOperationLineDetailERK; Quantity)
                    {
                    }
                    column(RegimeType_PortOperationLineDetailERK; "Regime Type")
                    {
                    }
                    column(ShipNo_PortOperationLineDetailERK; "Ship No.")
                    {
                    }
                    column(ShortcutDimension1Code_PortOperationLineDetailERK; "Shortcut Dimension 1 Code")
                    {
                    }
                    column(SourceName_PortOperationLineDetailERK; "Source Name")
                    {
                    }
                    column(SourceNo_PortOperationLineDetailERK; "Source No.")
                    {
                    }
                    column(SubLoadType_PortOperationLineDetailERK; "Sub Load Type")
                    {
                    }
                    column(TransferCustomerName_PortOperationLineDetailERK; "Transfer Customer Name")
                    {
                    }
                    column(Type_PortOperationLineDetailERK; "Type")
                    {
                    }
                    column(UnitofMeasureCode_PortOperationLineDetailERK; "Unit of Measure Code")
                    {
                    }
                    column(UnitPrice_PortOperationLineDetailERK; "Unit Price")
                    {
                    }
                    column(VariantCode_PortOperationLineDetailERK; "Variant Code")
                    {
                    }
                    column(VATProdPostingGroup_PortOperationLineDetailERK; "VAT Prod. Posting Group")
                    {
                    }
                    column(VATWithProdPostingGroup_PortOperationLineDetailERK; "VAT With. Prod. Posting Group")
                    {
                    }
                    column(IsCancelled_PortOperationLineDetailERK; "Is Cancelled")
                    {
                    }
                }
            }
        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(processing)
    //         {
    //         }
    //     }
    // }
}
