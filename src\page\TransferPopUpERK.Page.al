page 60043 "Transfer Pop-Up ERK"
{
    ApplicationArea = ErkPortERK;
    Caption = 'Transfer Pop-Up';
    PageType = ConfirmationDialog;

    //SourceTable = "Vehicle Ledger Entry ERK";
    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field(CarCarrierNo; CarCarrierNo)
                {
                    Caption = 'To Car Carrier No.';
                    ToolTip = 'Specifies the value of the CarCarrierNo field.';

                    trigger OnLookup(var Text: Text): Boolean
                    var
                        CarCarrierHeader: Record "Car Carrier Header ERK";
                    begin
                        if Page.RunModal(Page::"Car Carrier List ERK", CarCarrierHeader) = Action::LookupOK then
                            CarCarrierNo := CarCarrierHeader."No."
                    end;
                }
            }
        }
    }
    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        TransferredQtyMsg: Label '%1 Vehicles has been transferred to Car Carrier No.: %2', Comment = '%1="Car Carrier Ledger Entry ERK".Count(); %2=CarCarrierNo';
    begin
        if CloseAction = CloseAction::Yes then begin
            VehicleLedgerEntry.FindSet(true);
            repeat
                VehicleLedgerEntry."From Document No." := VehicleLedgerEntry."Document No.";
                VehicleLedgerEntry."Document No." := CarCarrierNo;
                VehicleLedgerEntry.Modify(false);
            until VehicleLedgerEntry.Next() = 0;
        end;
        Message(TransferredQtyMsg, VehicleLedgerEntry.Count(), CarCarrierNo);
    end;

    procedure SetVehicleLedgerEntryFilter(var parVehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK")
    begin
        parVehicleLedgerEntry.FindSet(false);
        repeat
            VehicleLedgerEntry.Get(parVehicleLedgerEntry."Entry No.");
            VehicleLedgerEntry.Mark(true);
        until parVehicleLedgerEntry.Next() = 0;
        VehicleLedgerEntry.MarkedOnly(true);
    end;

    var
        VehicleLedgerEntry: Record "Car Carrier Ledger Entry ERK";
        CarCarrierNo: Code[20];
}
