pageextension 60012 "Vendor Card ERK" extends "Vendor Card"
{
    layout
    {
        addafter("Purchaser Code")
        {
            field("Purchaser Name ERK"; Rec."Purchaser Name ERK")
            {
                ApplicationArea = All;
            }
            field("Erk Holding Intercompany ERK"; Rec."Erk Holding Intercompany ERK")
            {
                ApplicationArea = All;
            }
        }
        addlast(General)
        {
            field("Global Dimension 1 Code ERK"; Rec."Global Dimension 1 Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Global Dimension 1 Code field.';
            }
            field("Global Dimension 2 Code ERK"; Rec."Global Dimension 2 Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Global Dimension 2 Code field.';
            }
            field("Transfer Reverse Amount ERK"; Rec."Transfer Reverse Amount ERK")
            {
                ApplicationArea = All;
            }
        }
        modify(Name)
        {
            ShowMandatory = true;
        }
        modify(Address)
        {
            ShowMandatory = true;
        }
        modify(City)
        {
            ShowMandatory = true;
        }
        modify("Phone No.")
        {
            ShowMandatory = true;
        }
        modify("Vendor Posting Group")
        {
            ShowMandatory = true;
        }
        modify("Payment Terms Code")
        {
            ShowMandatory = true;
        }
        modify("Country/Region Code")
        {
            ShowMandatory = true;
        }
        // modify("Payment Method Code")
        // {
        //     ShowMandatory = true;
        // }
        modify("VAT Registration No.")
        {
            ShowMandatory = true;
        }
        modify("Gen. Bus. Posting Group")
        {
            ShowMandatory = true;
        }
        modify("Post Code")
        {
            ShowMandatory = true;
        }
        modify(County)
        {
            ShowMandatory = true;
        }
        modify("E-Mail")
        {
            ShowMandatory = true;
        }
        modify("Tax Area Code")
        {
            ShowMandatory = true;
        }
        modify("VAT Bus. Posting Group")
        {
            ShowMandatory = true;
        }
        modify("Partner Type")
        {
            ShowMandatory = true;
        }
        modify("E-Invoice Profile ID INF")
        {
            ShowMandatory = true;
        }
    }
}
