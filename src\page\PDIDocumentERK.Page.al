page 60059 "PDI Document ERK"
{
    ApplicationArea = All;
    Caption = 'PDI Document';
    PageType = Document;
    SourceTable = "PDI Header ERK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = not Rec.Saved;
                field("No."; Rec."No.")
                {
                    Editable = false;
                }
                field("Serial No."; Rec."Serial No.")
                {
                    Editable = false;
                }
                field("Location Code"; Rec."Location Code")
                {
                    Editable = false;
                }
                field("Operation Starting Date-Time"; Rec."Operation Starting Date-Time")
                {
                    Editable = false;
                }
                field("Operation Ending Date-Time"; Rec."Operation Ending Date-Time")
                {
                    Editable = false;
                }
                field("Assigned User ID"; Rec."Responsible Name")
                {
                    Editable = not Rec."Saved Atleast Once";
                }
                field("Line No."; Rec."Line No.")
                {
                    Editable = not Rec."Saved Atleast Once";
                }
                field("Manufacturer Code"; Rec."Brand Code")
                {
                    Editable = not Rec."Saved Atleast Once";
                }
                field("Carline Code"; Rec."Model Code")
                {
                    Editable = not Rec."Saved Atleast Once";
                }
                field("Color Name"; Rec."Color Name")
                {
                    Editable = not Rec."Saved Atleast Once";
                }
                field("Fuel Type"; Rec."Fuel Type")
                {
                    Editable = not Rec."Saved Atleast Once";
                }
                field(Notes; Rec.Notes)
                {
                    Editable = not Rec.Saved;
                }
                field("To Bin Code"; Rec."To Bin Code")
                {
                    Editable = not Rec."Saved Atleast Once";
                }
                field(Damaged; Rec.Damaged)
                {
                    Editable = not Rec."Saved Atleast Once";
                }
                field(Closed; Rec.Saved)
                {
                    Editable = false;
                }
                field("Created By"; CreatedByUserName)
                {
                    Caption = 'Created By User Name';
                    ToolTip = 'Specifies the user who created the record.';
                    Editable = false;
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                    Editable = false;
                }
                field("Modified By"; ModifiedByUserName)
                {
                    Caption = 'Modified by User Name';
                    ToolTip = 'Specifies the user who last modified the record.';
                    Editable = false;
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
            }
            part(Lines; "PDI Document Subpage ERK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Editable = not Rec.Saved;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreatePDILines)
            {
                Caption = 'Create PDI Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Line;
                PromotedOnly = true;
                Visible = false;
                ToolTip = 'Executes the Create PDI Lines action.';

                trigger OnAction()
                begin
                    PDIManagement.CreatePDILinesFromPDICheckList(Rec);
                end;
            }
            action(Save)
            {
                Caption = 'Save', Comment = 'TRK="Kapat"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Completed;
                ToolTip = 'Executes the Save action.';
                Enabled = not Rec.Saved;

                trigger OnAction()
                begin
                    PDIManagement.SavePDIDocument(Rec);
                    CurrPage.Update(false);
                end;
            }
            action(Reopen)
            {
                Caption = 'Reopen';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReOpen;
                ToolTip = 'Reopens the PDI document for edits.';
                Enabled = Rec.Saved;

                trigger OnAction()
                var
                    ConfirmMsg: Label 'Do you want to reopen this PDI document?';
                begin
                    if not Confirm(ConfirmMsg, false) then
                        exit;

                    Rec.Validate(Saved, false);
                    Rec.Modify(true);
                    CurrPage.Update(false);
                end;
            }
            action(Attachments)
            {
                Caption = 'Attachments';
                Image = Attach;
                ToolTip = 'Add a file as an attachment. You can attach images as well as documents.';

                trigger OnAction()
                var
                    SerialNoInformation: Record "Serial No. Information";
                    DocumentAttachmentDetails: Page "Document Attachment Details";
                    RecRef: RecordRef;
                begin
                    SerialNoInformation.SetRange("Serial No.", Rec."Serial No.");
                    SerialNoInformation.FindFirst();
                    RecRef.GetTable(SerialNoInformation);
                    DocumentAttachmentDetails.OpenForRecRef(RecRef);
                    DocumentAttachmentDetails.RunModal();
                end;
            }
        }
    }

    trigger OnAfterGetRecord()
    begin
        // Get user names from security IDs
        CreatedByUserName := BasicFunctions.GetUserNameFromSecurityId(Rec.SystemCreatedBy);
        ModifiedByUserName := BasicFunctions.GetUserNameFromSecurityId(Rec.SystemModifiedBy);
    end;

    var
        PDIManagement: Codeunit "PDI Management ERK";
        BasicFunctions: Codeunit "Erk Holding Basic Functions";
        CreatedByUserName: Code[50];
        ModifiedByUserName: Code[50];
}
