tableextension 60012 "Entry/Exit Point ERK" extends "Entry/Exit Point"
{
    fields
    {
        field(60000; "Location Code ERK"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(60001; "Is Europe ERK"; Boolean)
        {
            Caption = 'Is Europe';
            ToolTip = 'Specifies the value of the Is Europe field.';
        }
        field(60002; "Port Cluster Code ERK"; Code[10])
        {
            Caption = 'Port Cluster Code';
            TableRelation = "Port Cluster ERK".Code;
            ToolTip = 'Specifies the value of the Port Cluster Code field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Port Cluster Description ERK");
            end;
        }
        field(60003; "Port Cluster Description ERK"; Text[100])
        {
            Caption = 'Port Cluster Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Port Cluster ERK".Description where(Code = field("Port Cluster Code ERK")));
            ToolTip = 'Specifies the value of the Port Cluster Description field.';
        }
    }
}
