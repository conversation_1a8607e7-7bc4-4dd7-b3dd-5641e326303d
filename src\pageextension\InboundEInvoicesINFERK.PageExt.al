pageextension 60048 "Inbound E-Invoices INF ERK" extends "Inbound E-Invoices INF"
{
    layout
    {
        addafter("Source No.")
        {
            // field("SourceName ERK"; GetSourceNameFromEInvoiceHeader(Rec))
            // {
            //     ApplicationArea = All;
            //     Caption = 'Source Name';
            //     ToolTip = 'Specifies the value of the Source Name field.';
            // }
            field("Source Name ERK"; ErkHoldingBasicFunctions.GetVendorNameFromVendorNo(Rec."Source No."))
            {
                ApplicationArea = All;
                Caption = 'Source Name';
                ToolTip = 'Specifies the value of the Source Name field.';
            }

        }
    }
    // local procedure GetSourceNameFromEInvoiceHeader(EInvoiceHeader: Record "E-Invoice Header INF"): Text[100]
    // var
    //     Customer: Record Customer;
    //     Vendor: Record Vendor;
    // begin
    //     if EInvoiceHeader."Source Type" = EInvoiceHeader."Source Type"::Customer then begin
    //         Customer.Get(EInvoiceHeader."Source No.");
    //         exit(Customer.Name);
    //     end
    //     else begin
    //         Vendor.Get(EInvoiceHeader."Source No.");
    //         exit(Vendor.Name);
    //     end
    // end;
    var
        ErkHoldingBasicFunctions: Codeunit "Erk Holding Basic Functions";
}
